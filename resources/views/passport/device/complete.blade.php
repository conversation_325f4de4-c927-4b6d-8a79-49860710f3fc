<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.display_name') }} - 授权完成</title>
    <style>
        :root {
            --primary: #3b82f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f3f4f6;
            --border: #e5e7eb;
            --success: #22c55e;
            --error: #ef4444;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary: #60a5fa;
                --text-primary: #f3f4f6;
                --text-secondary: #9ca3af;
                --bg-primary: #1f2937;
                --bg-secondary: #374151;
                --border: #4b5563;
                --success: #10b981;
                --error: #f87171;
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 500px;
            width: 100%;
            margin: 0 auto;
        }

        .card {
            background-color: var(--bg-primary);
            border: 1px solid var(--border);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            padding: 2rem;
            text-align: center;
        }

        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .icon.success {
            background-color: rgba(34, 197, 94, 0.1);
            color: var(--success);
        }

        .icon.error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error);
        }

        h1 {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
        }

        .subtitle {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            font-size: 1rem;
            line-height: 1.5;
        }

        .message {
            background-color: var(--bg-secondary);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }

        .message-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .message-text {
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border);
        }

        .btn-secondary:hover {
            background-color: var(--border);
        }

        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        @media (max-width: 640px) {
            .card {
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }

            .buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }

        .auto-close {
            margin-top: 1rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .countdown {
            font-weight: 600;
            color: var(--primary);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            @if(isset($success) && $success)
                <div class="icon success">
                    ✓
                </div>
                
                <h1>授权成功</h1>
                <p class="subtitle">
                    您已成功授权设备访问您的账户
                </p>

                <div class="message">
                    <div class="message-title">接下来要做什么？</div>
                    <div class="message-text">
                        您可以安全地关闭此窗口，并返回到您的设备继续使用应用程序。
                    </div>
                </div>

                <div class="auto-close">
                    此页面将在 <span class="countdown" id="countdown">10</span> 秒后自动关闭
                </div>
            @else
                <div class="icon error">
                    ✗
                </div>
                
                <h1>授权失败</h1>
                <p class="subtitle">
                    很抱歉，设备授权过程中出现了问题
                </p>

                <div class="message">
                    <div class="message-title">可能的原因</div>
                    <div class="message-text">
                        • 用户代码已过期或无效<br>
                        • 授权请求被用户拒绝<br>
                        • 系统出现临时错误
                    </div>
                </div>

                <div class="buttons">
                    <a href="{{ url('/device') }}" class="btn btn-primary">重新尝试</a>
                    <a href="javascript:window.close()" class="btn btn-secondary">关闭窗口</a>
                </div>
            @endif
        </div>
    </div>

    @if(isset($success) && $success)
        <script>
            let countdown = 10;
            const countdownElement = document.getElementById('countdown');
            
            const timer = setInterval(() => {
                countdown--;
                countdownElement.textContent = countdown;
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    window.close();
                }
            }, 1000);
        </script>
    @endif
</body>
</html>
