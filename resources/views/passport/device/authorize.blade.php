<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.display_name') }} - 设备授权</title>
    <style>
        :root {
            --primary: #3b82f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f3f4f6;
            --border: #e5e7eb;
            --success: #22c55e;
            --error: #ef4444;
            --warning: #f59e0b;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary: #60a5fa;
                --text-primary: #f3f4f6;
                --text-secondary: #9ca3af;
                --bg-primary: #1f2937;
                --bg-secondary: #374151;
                --border: #4b5563;
                --success: #10b981;
                --error: #f87171;
                --warning: #fbbf24;
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 600px;
            width: 100%;
            margin: 0 auto;
        }

        .card {
            background-color: var(--bg-primary);
            border: 1px solid var(--border);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }

        .header {
            padding: 2rem 2rem 1rem;
            text-align: center;
            border-bottom: 1px solid var(--border);
        }

        .client-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 1rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--bg-secondary);
        }

        .client-icon img {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            object-fit: cover;
        }

        .client-icon .fallback {
            width: 48px;
            height: 48px;
            background-color: var(--primary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        h1 {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .app-name {
            color: var(--primary);
            font-weight: 600;
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            line-height: 1.5;
        }

        .content {
            padding: 2rem;
        }

        .device-info {
            background-color: var(--bg-secondary);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .device-info h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .info-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .info-value {
            color: var(--text-primary);
            font-weight: 500;
            font-family: 'Courier New', monospace;
        }

        .scopes-section {
            margin-bottom: 2rem;
        }

        .scopes-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .scope-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 0.75rem;
            padding: 0.75rem;
            background-color: var(--bg-secondary);
            border-radius: 6px;
        }

        .scope-icon {
            color: var(--success);
            margin-right: 0.75rem;
            margin-top: 0.125rem;
            flex-shrink: 0;
        }

        .scope-description {
            color: var(--text-primary);
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .warning-section {
            background-color: rgba(245, 158, 11, 0.1);
            border: 1px solid var(--warning);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .warning-title {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: var(--warning);
            margin-bottom: 0.5rem;
        }

        .warning-icon {
            margin-right: 0.5rem;
        }

        .warning-text {
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-approve {
            background-color: var(--success);
            color: white;
        }

        .btn-approve:hover {
            background-color: #16a34a;
            transform: translateY(-1px);
        }

        .btn-deny {
            background-color: var(--error);
            color: white;
        }

        .btn-deny:hover {
            background-color: #dc2626;
            transform: translateY(-1px);
        }

        .btn:disabled {
            background-color: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .d-none {
            display: none;
        }

        @media (max-width: 640px) {
            .header, .content {
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }

            .buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="header">
                <div class="client-icon">
                    @if($client->icon_url)
                        <img src="{{ $client->icon_url }}" alt="{{ $client->name }}">
                    @else
                        <div class="fallback">
                            📱
                        </div>
                    @endif
                </div>
                
                <h1>
                    设备授权请求<br>
                    <span class="app-name">{{ $client->name }}</span>
                </h1>
                
                <p class="subtitle">
                    该应用程序正在请求访问您的账户权限
                </p>
            </div>

            <div class="content">
                <div class="device-info">
                    <h3>设备信息</h3>
                    <div class="info-row">
                        <span class="info-label">用户代码:</span>
                        <span class="info-value">{{ $request->user_code ?? 'N/A' }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">客户端ID:</span>
                        <span class="info-value">{{ substr($client->getKey(), 0, 8) }}...</span>
                    </div>
                    @if($client->getIncrementing())
                        <div class="info-row">
                            <span class="info-label">客户端类型:</span>
                            <span class="info-value">
                                @if($client->confidential)
                                    机密客户端
                                @else
                                    公共客户端
                                @endif
                            </span>
                        </div>
                    @endif
                </div>

                @if(count($scopes) > 0)
                    <div class="scopes-section">
                        <h3 class="scopes-title">该应用程序将被允许:</h3>
                        @foreach($scopes as $scope)
                            <div class="scope-item">
                                <span class="scope-icon">✓</span>
                                <span class="scope-description">{{ $scope->description }}</span>
                            </div>
                        @endforeach
                    </div>
                @endif

                <div class="warning-section">
                    <div class="warning-title">
                        <span class="warning-icon">⚠️</span>
                        请注意
                    </div>
                    <div class="warning-text">
                        只有在您确认这是您尝试连接的设备时，才应授权此请求。授权后，该应用程序将能够代表您执行上述操作。
                    </div>
                </div>

                <div class="buttons">
                    <button class="btn btn-approve" onclick="approve()" id="approve-btn">
                        <span class="loading" id="approve-loading"></span>
                        <span id="approve-text">授权</span>
                    </button>
                    <button class="btn btn-deny" onclick="deny()" id="deny-btn">
                        <span class="loading" id="deny-loading"></span>
                        <span id="deny-text">拒绝</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Approve Form -->
        <form class="d-none" method="post" action="{{ route('passport.device.authorizations.approve') }}" id="approve-form">
            @csrf
            <input type="hidden" name="state" value="{{ $request->state }}">
            <input type="hidden" name="client_id" value="{{ $client->getKey() }}">
            <input type="hidden" name="auth_token" value="{{ $authToken }}">
        </form>

        <!-- Deny Form -->
        <form class="d-none" method="post" action="{{ route('passport.device.authorizations.deny') }}" id="deny-form">
            @csrf
            @method('DELETE')
            <input type="hidden" name="state" value="{{ $request->state }}">
            <input type="hidden" name="client_id" value="{{ $client->getKey() }}">
            <input type="hidden" name="auth_token" value="{{ $authToken }}">
        </form>
    </div>

    <script>
        function approve() {
            const btn = document.getElementById('approve-btn');
            const loading = document.getElementById('approve-loading');
            const text = document.getElementById('approve-text');
            const denyBtn = document.getElementById('deny-btn');
            
            btn.disabled = true;
            denyBtn.disabled = true;
            loading.style.display = 'block';
            text.textContent = '授权中...';
            
            document.getElementById('approve-form').submit();
        }

        function deny() {
            const btn = document.getElementById('deny-btn');
            const loading = document.getElementById('deny-loading');
            const text = document.getElementById('deny-text');
            const approveBtn = document.getElementById('approve-btn');
            
            btn.disabled = true;
            approveBtn.disabled = true;
            loading.style.display = 'block';
            text.textContent = '拒绝中...';
            
            document.getElementById('deny-form').submit();
        }

        // 防止重复提交
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const buttons = document.querySelectorAll('.btn');
                    buttons.forEach(btn => btn.disabled = true);
                });
            });
        });
    </script>
</body>
</html>
