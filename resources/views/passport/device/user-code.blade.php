<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.display_name') }} - 设备验证</title>
    <style>
        :root {
            --primary: #3b82f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f3f4f6;
            --border: #e5e7eb;
            --success: #22c55e;
            --error: #ef4444;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary: #60a5fa;
                --text-primary: #f3f4f6;
                --text-secondary: #9ca3af;
                --bg-primary: #1f2937;
                --bg-secondary: #374151;
                --border: #4b5563;
                --success: #10b981;
                --error: #f87171;
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 500px;
            width: 100%;
            margin: 0 auto;
        }

        .card {
            background-color: var(--bg-primary);
            border: 1px solid var(--border);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            padding: 2rem;
            text-align: center;
        }

        .logo {
            width: 64px;
            height: 64px;
            margin: 0 auto 1.5rem;
            background-color: var(--primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        h1 {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .subtitle {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            font-size: 1rem;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border);
            border-radius: 8px;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-size: 1rem;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            letter-spacing: 0.1em;
            text-transform: uppercase;
        }

        .input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .input.is-invalid {
            border-color: var(--error);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .error-message {
            color: var(--error);
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        .btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
            transform: translateY(-1px);
        }

        .btn-primary:disabled {
            background-color: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        .help-text {
            margin-top: 1.5rem;
            padding: 1rem;
            background-color: var(--bg-secondary);
            border-radius: 8px;
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .device-icon {
            font-size: 1.5rem;
        }

        @media (max-width: 640px) {
            .card {
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }
        }

        .d-none {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="logo">
                <span class="device-icon">📱</span>
            </div>
            
            <h1>设备验证</h1>
            <p class="subtitle">
                请在下方输入您设备上显示的用户代码以继续授权过程
            </p>

            @if ($errors->any())
                <div class="error-message">
                    @foreach ($errors->all() as $error)
                        {{ $error }}
                    @endforeach
                </div>
            @endif

            <form method="GET" action="{{ route('passport.device.authorizations.authorize') }}" id="user-code-form">
                <div class="form-group">
                    <label for="user_code">用户代码</label>
                    <input 
                        type="text" 
                        name="user_code" 
                        id="user_code" 
                        class="input @if($errors->has('user_code')) is-invalid @endif"
                        placeholder="例如: ABCD-EFGH"
                        value="{{ old('user_code') }}"
                        maxlength="9"
                        pattern="[A-Z0-9]{4}-[A-Z0-9]{4}"
                        autocomplete="off"
                        required
                    >
                    @if ($errors->has('user_code'))
                        <div class="error-message">
                            {{ $errors->first('user_code') }}
                        </div>
                    @endif
                </div>

                <button type="submit" class="btn btn-primary" id="submit-btn">
                    继续
                </button>
            </form>

            <div class="help-text">
                <strong>需要帮助？</strong><br>
                确保您已在设备上启动了授权流程，并且输入的代码与设备上显示的完全一致。代码通常格式为 XXXX-XXXX。
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const userCodeInput = document.getElementById('user_code');
            const submitBtn = document.getElementById('submit-btn');

            // 格式化用户输入
            userCodeInput.addEventListener('input', function(e) {
                let value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
                
                if (value.length > 4) {
                    value = value.substring(0, 4) + '-' + value.substring(4, 8);
                }
                
                e.target.value = value;
                
                // 验证格式
                const isValid = /^[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(value) || /^[A-Z0-9]{1,4}$/.test(value);
                submitBtn.disabled = !isValid || value.length < 4;
            });

            // 防止粘贴非法字符
            userCodeInput.addEventListener('paste', function(e) {
                setTimeout(() => {
                    userCodeInput.dispatchEvent(new Event('input'));
                }, 0);
            });

            // 自动提交完整代码
            userCodeInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && userCodeInput.value.length === 9) {
                    document.getElementById('user-code-form').submit();
                }
            });
        });
    </script>
</body>
</html>
