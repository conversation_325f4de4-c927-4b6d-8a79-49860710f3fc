<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.display_name') }} - 授权</title>
    <style>
        :root {
            --primary: #3b82f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f3f4f6;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary: #60a5fa;
                --text-primary: #f3f4f6;
                --text-secondary: #9ca3af;
                --bg-primary: #1f2937;
                --bg-secondary: #374151;
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: Arial, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1000px;
            width: 100%;
            margin: 0 auto;
        }

        .content {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            gap: 40px;
        }

        .text-section {
            text-align: center;
        }

        .text-section h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .text-section .app-name {
            color: var(--primary);
        }

        .text-section .description {
            color: var(--text-secondary);
            font-size: 1.1rem;
            line-height: 1.5;
        }

        .scopes-section {
            width: 100%;
            max-width: 400px;
        }

        .scopes {
            margin-bottom: 1.5rem;
        }

        .scopes p {
            font-size: 1rem;
            line-height: 1.5;
        }

        .scopes .title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.375rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .btn-approve {
            background-color: #22c55e;
            color: white;
        }

        .btn-approve:hover {
            background-color: #16a34a;
        }

        .btn-danger {
            background-color: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background-color: #dc2626;
        }

        .loading-section {
            text-align: center;
        }

        .loading-section h1 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--text-secondary);
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .d-none {
            display: none;
        }

        @media (max-width: 768px) {
            .content {
                flex-direction: column;
                gap: 20px;
            }

            .text-section h1 {
                font-size: 2rem;
            }

            .text-section .description {
                font-size: 0.9rem;
            }

            .scopes-section {
                max-width: 100%;
            }

            .scopes p {
                font-size: 0.9rem;
            }

            .buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }

            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        @if (!$client->trusted)
            <div class="content">
                <div class="text-section">
                    <h1>
                        应用授权 <br />
                        <span class="app-name">{{ $client->name }}</span>
                    </h1>
                    <div class="description">
                        @if (!empty($client->description))
                            {{ $client->description }}
                        @endif
                    </div>
                </div>

                <div class="scopes-section">
                    <!-- Scope List -->
                    @if (count($scopes) > 0)
                        <div class="scopes">
                            <p class="title">此应用程序将被允许:</p>
                            <div>
                                @foreach ($scopes as $scope)
                                    <p>{{ $scope->description }}</p>
                                @endforeach
                            </div>
                        </div>
                    @endif
                    <div class="buttons">
                        <button class="btn btn-approve" onclick="accept()">授权</button>
                        <button class="btn btn-danger" onclick="deny()">取消</button>
                    </div>
                </div>
            </div>
        @else
            <div class="loading-section">
                <h1>正在继续...</h1>
                <div class="spinner"></div>
            </div>
            <script>
                setTimeout(() => {
                    accept()
                }, 100)
            </script>
        @endif

        <!-- Authorize Button -->
        <form class="d-none" method="post" action="{{ route('passport.authorizations.approve') }}" id="authorize-form">
            @csrf
            <input type="hidden" name="state" value="{{ $request->state }}">
            <input type="hidden" name="client_id" value="{{ $client->getKey() }}">
            <input type="hidden" name="auth_token" value="{{ $authToken }}">
        </form>

        <!-- Cancel Button -->
        <form class="d-none" method="post" action="{{ route('passport.authorizations.deny') }}" id="cancel-form">
            @csrf
            @method('DELETE')
            <input type="hidden" name="state" value="{{ $request->state }}">
            <input type="hidden" name="client_id" value="{{ $client->getKey() }}">
            <input type="hidden" name="auth_token" value="{{ $authToken }}">
        </form>

        <script>
            function accept() {
                document.getElementById('authorize-form').submit();
            }

            function deny() {
                document.getElementById('cancel-form').submit();
            }
        </script>
    </div>
</body>
</html>