<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" @class(['dark' => ($appearance ?? 'system') == 'dark'])>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    {{-- Inline script to detect system dark mode preference and apply it immediately --}}
    <script>
        (function () {
            const appearance = '{{ $appearance ?? "system" }}';

            if (appearance === 'system') {
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                if (prefersDark) {
                    document.documentElement.classList.add('dark');
                }
            }
        })();
    </script>

    {{-- Inline style to set the HTML background color based on our theme in app.css --}}
    <style>
        html {
            background-color: oklch(1 0 0);
        }

        html.dark {
            background-color: oklch(0.145 0 0);
        }
    </style>

    <title inertia>一键部署，弹性扩容，自动伸缩，容器化部署平台 - {{ config('app.display_name', 'Leaflow') }}</title>

    {{--
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png"> --}}
    <link rel="icon" href="/favicon.svg" type="image/svg+xml">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="theme-color" content="#ffffff">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta name="description"
        content="欢迎回来 Leaflow利飞 ，我们提供高效的容器化部署解决方案。这里提供按需付费，内置自动扩缩容服务，拥有高可用性的多地集群。让您把钱花在刀刃上，为自己使用的资源付费。">
    <meta name="keywords"
        content="Leaflow, 利飞, 容器化, 云服务, Docker, 自动扩缩容, 容器化部署, PaaS, SaaS, IaaS, 云原生, K8S, 香港K8S, 成都K8S, 香港集群, 成都集群, 高可用, 无限流量, 免费试用, 宝塔面板, 1Panel, MySQL, AI运维, Heroku, 阿里云, 腾讯云, 分钟计费, 容器化部署">
    <meta name="robots" content="index,follow">
    <meta name="googlebot" content="NOODP">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta property="og:title" content="一键部署，弹性扩容，自动伸缩，容器化部署平台  - Leaflow利飞">
    <meta property="og:description" content="AI服务器运维，一键部署容器化应用，按分钟计费">
    <meta property="og:image" content="<https://leaflow.net/assets/og.svg>">
    <meta property="og:url" content="<https://leaflow.net>">
    <meta property="og:type" content="website">
    <meta name="author" content="LiferGroup">
    <meta name="copyright" content="{{ date('Y') }} 中山市利飞科技有限公司">

    {{--
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" /> --}}

    {{-- @php
    $user = auth('web')->user();
    $permissions = 0; // 默认值为0

    if ($user) {
    // 获取角色名称
    $roles = $user->getRoleNames();

    // 获取所有权限名称
    $permissionsList = $user->getAllPermissions()->pluck('name');

    // 构建权限数组
    $permissionsArray = [
    'roles' => $roles,
    'permissions' => $permissionsList,
    ];

    // 将权限数组编码为 JSON
    $permissions = json_encode($permissionsArray);
    }
    @endphp --}}

    {{--
    <script type="text/javascript">
        window.App = {
            user: {!! $user? json_encode($user): 'null' !!
        },
            csrfToken: "{{ csrf_token() }}",
                permissions: { !!$permissions!! }
        }
    </script> --}}

    {{--
    <script>
        (function () {
            const workspaceId = '{{ getPermissionsTeamId() }}';
            if (workspaceId) {
                window.App.workspaceId = workspaceId;
            }
        })();
    </script> --}}

    <!-- Scripts -->
    <script src="/desk-sdk.js"></script>
    @routes
    @vite(['resources/js/app.ts', "resources/js/pages/{$page['component']}.vue"])
    @inertiaHead
</head>

<body class="font-sans antialiased">
    @inertia
</body>

</html>