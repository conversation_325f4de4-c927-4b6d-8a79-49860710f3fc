import type { ResourceChangeEvent } from '@/stores/resourcesStore';
import type { StatefulSet } from '@/types';
import type { WindowInstance, WindowType } from '@/types/window';
import mitt from 'mitt';

export type ApiErrorPayload = {
    message: string;
    errors?: Record<string, any>;
};

export type TerminalInstance = {
    id: string;
    podName: string;
    containerName: string;
    mode: 'shell' | 'attach';
    title: string;
    isConnected: boolean;
    isMinimized: boolean;
    zIndex: number;
};

export type CommandExecutionResult = {
    success: boolean;
    output: string;
    exitCode?: number;
    error?: string;
};

// 定义事件类型
export type Events = {
    // 资源变更事件（通用）
    'resource:changed': ResourceChangeEvent;
    'resource:refresh': void; // 触发资源刷新
    'resource:force-refresh': void; // 强制触发所有资源刷新
    'resource:refresh:complete': void; // 资源刷新完成
    'resource:refresh:error': Error; // 资源刷新失败
    'resource:store:cleared': void; // 资源存储已清空
    'resource:store:loaded': void; // 资源存储已加载完成

    // 工作区相关事件
    'workspace:switched': number; // workspace id
    'workspace:changed': import('@/types').Workspace | null;
    'workspace:switching': { from: string | null; to: string | null }; // 工作区切换中
    'workspace:switch:complete': string; // 工作区切换完成，传入新的 namespace

    // 向后兼容的旧事件（逐步废弃）
    'statefulset:updated': StatefulSet;
    'statefulset:deleted': string;
    'api:error': ApiErrorPayload;

    // 窗口管理相关事件
    'window:open': { type: WindowType; data?: Record<string, any>; options?: Partial<WindowInstance> };
    'window:close': string; // window id
    'window:minimize': string; // window id
    'window:maximize': string; // window id
    'window:restore': string; // window id
    'window:focus': string; // window id
    'window:update': { id: string; updates: Partial<WindowInstance> };
    'window:toggle': WindowType;

    // 终端相关事件（保持向后兼容）
    'terminal:open': { podName: string; containerName?: string; mode?: 'shell' | 'attach' };
    'terminal:close': string; // terminal id
    'terminal:minimize': string; // terminal id
    'terminal:maximize': string; // terminal id
    'terminal:focus': string; // terminal id
    'terminal:command:execute': { terminalId: string; command: string };
    'terminal:command:result': { terminalId: string; result: CommandExecutionResult };

    // AI 助手相关事件（保持向后兼容）
    'ai-assistant:toggle': void;
    'ai-assistant:open': void;
    'ai-assistant:close': void;
    'ai-assistant:new-conversation': void;
    'ai-assistant:message-received': { content: string };

    // 全局搜索相关事件
    'global-search:close': void;

    // 监听状态事件
    'listener:status:changed': { isListening: boolean; namespace: string | null; error: string | null };
    'listener:fetching:changed': boolean; // 是否正在获取资源

    // RPC Events
    'rpc:request': any;
    'rpc:response': any;

    // SDK/Test Events
    'sdk:ping': any;
    'sdk:custom': any;

    // 其他事件类型可以在这里添加
};

// 创建事件总线实例
const eventBus = mitt<Events>();

export default eventBus;
