import type { ClusterPricing } from '@/types';

/**
 * 计算每小时价格（8位小数点）
 * 与后端保持一致：先计算每分钟价格，再乘以60
 *
 * @param monthlyPrice 月价格
 * @returns 每小时价格字符串
 */
export function calculateHourlyPrice(monthlyPrice: string | number): string {
    const monthly = parseFloat(monthlyPrice.toString());
    // 月价格除以 (30天 * 24小时 * 60分钟) = 43200分钟
    const perMinute = monthly / 43200;
    // 每分钟价格乘以60得到每小时价格
    const hourly = perMinute * 60;
    return hourly.toFixed(8);
}

/**
 * 格式化价格显示
 *
 * @param price 价格
 * @param withSymbol 是否包含货币符号
 * @returns 格式化后的价格字符串
 */
export function formatPrice(price: string | number, withSymbol: boolean = true): string {
    const formatted = parseFloat(price.toString()).toFixed(2);
    return withSymbol ? `¥ ${formatted}` : formatted;
}

/**
 * 格式化高精度价格显示（用于每小时价格）
 *
 * @param price 价格
 * @param withSymbol 是否包含货币符号
 * @returns 格式化后的价格字符串
 */
export function formatPrecisePrice(price: string | number, withSymbol: boolean = true): string {
    const formatted = parseFloat(price.toString()).toFixed(8);
    return withSymbol ? `¥ ${formatted}` : formatted;
}

/**
 * 获取集群定价信息的格式化显示
 *
 * @param pricing 集群定价配置
 * @returns 定价信息对象
 */
export function getClusterPricingInfo(pricing: ClusterPricing) {
    return {
        memory: {
            monthly: formatPrice(pricing.memory_price_per_gb),
            hourly: formatPrecisePrice(calculateHourlyPrice(pricing.memory_price_per_gb)),
        },
        cpu: {
            monthly: formatPrice(pricing.cpu_price_per_core),
            hourly: formatPrecisePrice(calculateHourlyPrice(pricing.cpu_price_per_core)),
        },
        storage: {
            monthly: formatPrice(pricing.storage_price_per_gb),
            hourly: formatPrecisePrice(calculateHourlyPrice(pricing.storage_price_per_gb)),
        },
    };
}
