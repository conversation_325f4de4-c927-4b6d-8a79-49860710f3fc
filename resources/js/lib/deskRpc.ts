import eventBus from '@/lib/eventBus';

interface RpcRequest {
    id: string;
    method: string;
    payload: any;
    sender: string;
}
interface RpcResponse {
    id: string;
    payload: any;
    error?: any;
}

const pending: Record<string, { resolve: Function; reject: Function }> = {};

function generateId() {
    return `${Date.now().toString(36)}-${Math.random().toString(36).slice(2, 8)}`;
}

// listen responses
(eventBus as any).on('rpc:response', (resp: RpcResponse) => {
    const waiter = pending[resp.id];
    if (waiter) {
        if (resp.error) waiter.reject(resp.error);
        else waiter.resolve(resp.payload);
        delete pending[resp.id];
    }
});

export function deskRequest(method: string, payload?: any, timeout = 10000): Promise<any> {
    return new Promise((resolve, reject) => {
        const id = generateId();
        pending[id] = { resolve, reject };
        (eventBus as any).emit('rpc:request', { id, method, payload });
        if (timeout) {
            setTimeout(() => {
                if (pending[id]) {
                    delete pending[id];
                    reject(new Error('RPC timeout'));
                }
            }, timeout);
        }
    });
}

// utility to respond
export function deskProvide(method: string, handler: (payload: any) => any | Promise<any>) {
    (eventBus as any).on('rpc:request', async (req: RpcRequest) => {
        if (req.method !== method) return;
        try {
            const result = await handler(req.payload);
            (eventBus as any).emit('rpc:response', { id: req.id, payload: result });
        } catch (e) {
            const errMsg = typeof e === 'object' && e && 'message' in e ? (e as any).message : String(e);
            (eventBus as any).emit('rpc:response', { id: req.id, error: errMsg });
        }
    });
}
