import { useWorkspaceStore } from '@/stores/workspaceStore';
import type { VariableContext, VariableResolver } from '@/types/yaml';

/**
 * 变量解析器实现
 * 支持解析模板字符串中的变量，格式：${variableName}
 */
export class YamlVariableResolver implements VariableResolver {
    private workspaceStore = useWorkspaceStore();

    /**
     * 获取预定义的变量上下文
     */
    private getBuiltinContext(): VariableContext {
        const workspace = this.workspaceStore.currentWorkspace;

        const context = {
            // 工作空间相关变量
            namespace: workspace?.namespace || '',
            workspace_id: workspace?.id || '',
            workspace_name: workspace?.name || '',
            cluster_id: workspace?.cluster_id || '',
            cluster_name: workspace?.cluster?.name || '',

            // TODO: 用户相关变量（如果需要的话）
            // user_id: window.App?.user?.id || '',
            // user_name: window.App?.user?.name || '',
            // user_email: window.App?.user?.email || '',

            // 其他常用变量
            timestamp: Date.now().toString(),
            date: new Date().toISOString().split('T')[0], // YYYY-MM-DD
            datetime: new Date().toISOString(),
        };

        console.log('context', context);

        return context;
    }

    /**
     * 解析模板字符串中的变量
     * @param template 包含变量的模板字符串
     * @param context 额外的变量上下文
     * @returns 解析后的字符串
     */
    resolve(template: string, context?: VariableContext): string {
        if (typeof template !== 'string') {
            return template;
        }

        const builtinContext = this.getBuiltinContext();
        const mergedContext = { ...builtinContext, ...context };

        // 使用正则表达式匹配 ${variableName} 格式的变量
        return template.replace(/\$\{([^}]+)\}/g, (match, variableName) => {
            const trimmedName = variableName.trim();

            if (trimmedName in mergedContext) {
                const value = mergedContext[trimmedName];
                return value !== null && value !== undefined ? String(value) : '';
            }

            // 如果变量不存在，保持原样或返回空字符串
            console.warn(`变量 "${trimmedName}" 未定义，保持原样`);
            return match;
        });
    }

    /**
     * 递归解析对象中的所有字符串值
     * @param obj 要解析的对象
     * @param context 变量上下文
     * @returns 解析后的对象
     */
    resolveObject(obj: any, context?: VariableContext): any {
        if (typeof obj === 'string') {
            return this.resolve(obj, context);
        }

        if (Array.isArray(obj)) {
            return obj.map((item) => this.resolveObject(item, context));
        }

        if (obj && typeof obj === 'object') {
            const resolved: any = {};
            for (const [key, value] of Object.entries(obj)) {
                resolved[key] = this.resolveObject(value, context);
            }
            return resolved;
        }

        return obj;
    }

    /**
     * 获取所有可用的变量名称
     * @returns 变量名称数组
     */
    getAvailableVariables(): string[] {
        const context = this.getBuiltinContext();
        return Object.keys(context).sort();
    }

    /**
     * 获取变量的描述信息
     * @returns 变量描述映射
     */
    getVariableDescriptions(): Record<string, string> {
        return {
            namespace: '当前工作空间的命名空间',
            workspace_id: '当前工作空间 ID',
            workspace_name: '当前工作空间名称',
            cluster_id: '当前集群 ID',
            cluster_name: '当前集群名称',
            user_id: '当前用户 ID',
            user_name: '当前用户名称',
            user_email: '当前用户邮箱',
            timestamp: '当前时间戳（毫秒）',
            date: '当前日期（YYYY-MM-DD）',
            datetime: '当前日期时间（ISO 格式）',
        };
    }
}

/**
 * 创建变量解析器实例
 */
export function createVariableResolver(): VariableResolver {
    return new YamlVariableResolver();
}
