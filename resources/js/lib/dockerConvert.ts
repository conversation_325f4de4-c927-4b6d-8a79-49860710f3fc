// 新增文件: 解析 Docker Run 与 Docker Compose 并转换为平台专用 YAML
import * as yaml from 'js-yaml';

/**
 * 端口定义
 */
interface PortMapping {
    hostPort: number | null;
    containerPort: number;
    protocol: 'TCP' | 'UDP';
}

/**
 * 环境变量定义
 */
interface EnvVar {
    name: string;
    value: string;
}

/**
 * 卷挂载定义
 */
interface VolumeMount {
    mountPath: string;
    storageName: string;
    readOnly: boolean;
}

/**
 * 清理名称，使其符合 Kubernetes 资源命名规范
 */
function sanitizeName(name: string): string {
    return name
        .toLowerCase()
        .replace(/[^a-z0-9-]/g, '-')
        .replace(/^-+|-+$/g, '')
        .replace(/-+/g, '-')
        .substring(0, 63);
}

/**
 * 解析端口映射字符串
 */
function parsePortMapping(portStr: string): PortMapping {
    const parts = portStr.split(':');
    let hostPort: number | null = null;
    let containerPort: number;
    let protocol: 'TCP' | 'UDP' = 'TCP';

    if (parts.length === 1) {
        // 只有容器端口
        containerPort = parseInt(parts[0]);
    } else if (parts.length === 2) {
        // 主机端口:容器端口
        hostPort = parseInt(parts[0]);
        containerPort = parseInt(parts[1]);
    } else {
        // 可能包含协议
        containerPort = parseInt(parts[parts.length - 1]);
        if (parts.length > 1) {
            hostPort = parseInt(parts[0]);
        }
    }

    // 检查协议（简单检查）
    if (portStr.toLowerCase().includes('udp')) {
        protocol = 'UDP';
    }

    return { hostPort, containerPort, protocol };
}

/**
 * 解析卷挂载字符串
 */
function parseVolume(volumeStr: string): VolumeMount {
    const parts = volumeStr.split(':');
    let mountPath: string;
    let storageName: string;
    let readOnly = false;

    if (parts.length === 1) {
        // 只有挂载路径，创建一个默认存储名
        mountPath = parts[0];
        storageName = sanitizeName(`vol-${mountPath.replace(/[^a-zA-Z0-9]/g, '-')}`);
    } else if (parts.length === 2) {
        // 主机路径:容器路径 或 存储名:容器路径
        storageName = sanitizeName(parts[0]);
        mountPath = parts[1];
    } else {
        // 可能包含选项
        storageName = sanitizeName(parts[0]);
        mountPath = parts[1];
        if (parts[2] && parts[2].includes('ro')) {
            readOnly = true;
        }
    }

    return { mountPath, storageName, readOnly };
}

/**
 * 检查是否为 docker run 命令
 */
function isDockerRun(content: string): boolean {
    return content.trim().toLowerCase().startsWith('docker run');
}

/**
 * 检查是否为 docker compose 文件
 */
function isDockerCompose(content: string): boolean {
    try {
        const parsed = yaml.load(content);
        return Boolean(parsed && typeof parsed === 'object' && 'services' in parsed);
    } catch {
        return false;
    }
}

/**
 * 将 docker run 命令转换为平台 YAML
 */
export function convertDockerRunToYaml(command: string): string {
    // 移除开头的 docker run
    const cleaned = command.trim().replace(/^docker\s+run\s+/, '');

    // 使用简单的正则分割，考虑到引号包裹的参数
    const tokens: string[] = cleaned.match(/(?:[^\s"']+|"[^"]*"|'[^']*')+/g) || [];

    let image = '';
    let name = '';

    const envs: EnvVar[] = [];
    const ports: PortMapping[] = [];
    const volumes: VolumeMount[] = [];

    const consumeNext = (i: number, defaultValue?: string): { value: string | null; nextIndex: number } => {
        // token[i] is flag, token[i+1] should be value unless flag is --opt=value
        if (defaultValue !== undefined) {
            return { value: defaultValue, nextIndex: i };
        }
        if (i + 1 < tokens.length) {
            return { value: tokens[i + 1].replace(/["']/g, ''), nextIndex: i + 1 };
        }
        return { value: null, nextIndex: i };
    };

    for (let i = 0; i < tokens.length; i++) {
        let tok = tokens[i].replace(/["']/g, '');

        // --name or --name=foo
        if (tok === '--name' || tok === '-—name') {
            const { value, nextIndex } = consumeNext(i);
            if (value) name = value;
            i = nextIndex;
            continue;
        }
        if (tok.startsWith('--name=')) {
            name = tok.split('=')[1];
            continue;
        }

        // env
        if (tok === '-e' || tok === '--env') {
            const { value, nextIndex } = consumeNext(i);
            if (value) {
                const [k, v = ''] = value.split('=');
                envs.push({ name: k, value: v });
            }
            i = nextIndex;
            continue;
        }
        if (tok.startsWith('-e')) {
            const pair = tok.slice(2);
            const [k, v = ''] = pair.split('=');
            envs.push({ name: k, value: v });
            continue;
        }
        if (tok.startsWith('--env=')) {
            const pair = tok.split('=')[1];
            const [k, v = ''] = pair.split('=');
            envs.push({ name: k, value: v });
            continue;
        }

        // ports
        if (tok === '-p' || tok === '--publish') {
            const { value, nextIndex } = consumeNext(i);
            if (value) {
                ports.push(parsePortMapping(value));
            }
            i = nextIndex;
            continue;
        }
        if (tok.startsWith('-p')) {
            ports.push(parsePortMapping(tok.slice(2)));
            continue;
        }
        if (tok.startsWith('--publish=')) {
            ports.push(parsePortMapping(tok.split('=')[1]));
            continue;
        }

        // volumes
        if (tok === '-v' || tok === '--volume') {
            const { value, nextIndex } = consumeNext(i);
            if (value) {
                volumes.push(parseVolume(value));
            }
            i = nextIndex;
            continue;
        }
        if (tok.startsWith('-v')) {
            volumes.push(parseVolume(tok.slice(2)));
            continue;
        }
        if (tok.startsWith('--volume=')) {
            volumes.push(parseVolume(tok.split('=')[1]));
            continue;
        }

        // 如果是第一个非参数 token，视为镜像名
        if (!tok.startsWith('-') && !image) {
            image = tok;
            // 可能接下来还有命令参数，这里暂不处理
        }
    }

    if (!image) {
        throw new Error('无法解析镜像名');
    }

    const deploymentName = sanitizeName(name || image.split(':')[0]);

    const docs: any[] = [];

    // 先处理存储
    const seenStorages = new Set<string>();
    volumes.forEach((vol) => {
        if (!seenStorages.has(vol.storageName)) {
            docs.push({
                kind: 'Storage',
                name: vol.storageName,
                size: 1024, // 默认 1Gi
            });
            seenStorages.add(vol.storageName);
        }
    });

    // 构建 Deployment 文档
    const deploymentDoc: any = {
        kind: 'Deployment',
        name: deploymentName,
        replicas: 1,
        image_pull_secrets: [],
        containers: [
            {
                name: deploymentName,
                image,
                working_dir: '',
                command: [],
                args: [],
                ports: ports.map((p) => ({
                    name: `port${p.containerPort}`,
                    container_port: p.containerPort,
                    protocol: p.protocol,
                })),
                env: envs,
                env_from_configmap: [],
                env_from_secret: [],
                resources: {
                    cpu: 1000,
                    memory: 1024,
                },
                volume_mounts: volumes.map((v) => ({
                    mount_path: v.mountPath,
                    storage_name: v.storageName,
                    sub_path: '',
                    read_only: v.readOnly,
                })),
                configmap_mounts: [],
                secret_mounts: [],
            },
        ],
    };

    docs.push(deploymentDoc);

    // 构建 Service 文档（如果存在端口）
    if (ports.length > 0) {
        docs.push({
            kind: 'Service',
            name: deploymentName,
            type: 'ClusterIP',
            target_workload_type: 'Deployment',
            target_workload_name: deploymentName,
            ports: ports.map((p) => ({
                name: `port${p.containerPort}`,
                port: p.hostPort || p.containerPort,
                target_port: p.containerPort,
                protocol: p.protocol,
            })),
            session_affinity: 'None',
            external_traffic_policy: 'Cluster',
        });
    }

    return docsToYaml(docs);
}

/**
 * 将 docker compose YAML 转换为平台 YAML
 */
export function convertDockerComposeToYaml(composeContent: string): string {
    const compose = yaml.load(composeContent);
    if (!compose || typeof compose !== 'object' || !('services' in compose)) {
        throw new Error('无效的 Docker Compose 内容');
    }

    const services: Record<string, any> = (compose as any).services;

    const docs: any[] = [];
    const seenStorages = new Set<string>();

    Object.entries(services).forEach(([serviceName, service]) => {
        const image: string = service.image;
        const deploymentName = sanitizeName(serviceName);

        // 解析环境变量
        const envs: EnvVar[] = [];
        if (Array.isArray(service.environment)) {
            (service.environment as string[]).forEach((e) => {
                const [k, v = ''] = e.split('=');
                envs.push({ name: k, value: v });
            });
        } else if (typeof service.environment === 'object' && service.environment !== null) {
            Object.entries(service.environment as Record<string, string>).forEach(([k, v]) => {
                envs.push({ name: k, value: v as string });
            });
        }

        // 解析端口
        const ports: PortMapping[] = [];
        if (Array.isArray(service.ports)) {
            (service.ports as any[]).forEach((p) => {
                if (typeof p === 'string') {
                    ports.push(parsePortMapping(p));
                } else if (typeof p === 'number') {
                    ports.push({ hostPort: p, containerPort: p, protocol: 'TCP' });
                }
            });
        }

        // 解析卷
        const volumes: VolumeMount[] = [];
        if (Array.isArray(service.volumes)) {
            (service.volumes as string[]).forEach((v) => {
                volumes.push(parseVolume(v));
            });
        }

        // 创建存储文档
        volumes.forEach((vol) => {
            if (!seenStorages.has(vol.storageName)) {
                docs.push({
                    kind: 'Storage',
                    name: vol.storageName,
                    size: 1024,
                });
                seenStorages.add(vol.storageName);
            }
        });

        // Deployment 文档
        const deploymentDoc: any = {
            kind: 'Deployment',
            name: deploymentName,
            replicas: 1,
            image_pull_secrets: [],
            containers: [
                {
                    name: deploymentName,
                    image,
                    working_dir: '',
                    command: [],
                    args: [],
                    ports: ports.map((p) => ({
                        name: `port${p.containerPort}`,
                        container_port: p.containerPort,
                        protocol: p.protocol,
                    })),
                    env: envs,
                    env_from_configmap: [],
                    env_from_secret: [],
                    resources: {
                        cpu: 1000,
                        memory: 1024,
                    },
                    volume_mounts: volumes.map((v) => ({
                        mount_path: v.mountPath,
                        storage_name: v.storageName,
                        sub_path: '',
                        read_only: v.readOnly,
                    })),
                    configmap_mounts: [],
                    secret_mounts: [],
                },
            ],
        };
        docs.push(deploymentDoc);

        // Service 文档
        if (ports.length > 0) {
            docs.push({
                kind: 'Service',
                name: deploymentName,
                type: 'ClusterIP',
                target_workload_type: 'Deployment',
                target_workload_name: deploymentName,
                ports: ports.map((p) => ({
                    name: `port${p.containerPort}`,
                    port: p.hostPort || p.containerPort,
                    target_port: p.containerPort,
                    protocol: p.protocol,
                })),
                session_affinity: 'None',
                external_traffic_policy: 'Cluster',
            });
        }
    });

    return docsToYaml(docs);
}

/**
 * 根据内容自动转换（如果可能）
 */
export function convertContentToCustomYaml(content: string): string {
    if (isDockerRun(content)) {
        try {
            return convertDockerRunToYaml(content);
        } catch (e) {
            console.error(e);
            // 转换失败，返回原始内容
            return content;
        }
    }

    if (isDockerCompose(content)) {
        try {
            return convertDockerComposeToYaml(content);
        } catch (e) {
            console.error(e);
            return content;
        }
    }

    // 默认返回原始内容
    return content;
}

/**
 * 将文档数组转换为 YAML 字符串
 */
function docsToYaml(docs: any[]): string {
    return docs.map((d) => yaml.dump(d, { skipInvalid: true })).join('---\n');
}
