export interface PageSnapshot {
    url: string;
    title: string;
    viewport: {
        width: number;
        height: number;
    };
    domTree: DOMNode;
    timestamp: number;
}

export interface ElementChange {
    selector: string;
    changeType: 'added' | 'removed' | 'modified';
    before?: DOMNode;
    after?: DOMNode;
}

export interface PageDiff {
    hasChanges: boolean;
    changes: ElementChange[];
    newElements: DOMNode[];
    removedElements: DOMNode[];
    modifiedElements: Array<{ before: DOMNode; after: DOMNode }>;
    summary: string;
}

export interface AccessibilityInfo {
    title?: string;
    ariaLabel?: string;
    ariaLabelledBy?: string;
    ariaDescribedBy?: string;
    ariaRole?: string;
    ariaExpanded?: boolean;
    ariaSelected?: boolean;
    ariaChecked?: boolean;
    ariaDisabled?: boolean;
    ariaHidden?: boolean;
    ariaLive?: string;
    ariaLevel?: number;
    ariaSetSize?: number;
    ariaPosInSet?: number;
    tabIndex?: number;
    alt?: string;
    placeholder?: string;
}

export interface ElementAttributes {
    disabled?: boolean;
    value?: string;
    type?: string;
    checked?: boolean;
    href?: string;
    src?: string;
    alt?: string;
    name?: string;
    id?: string;
    className?: string;
}

export interface SelectOption {
    value: string;
    text: string;
    selected: boolean;
}

export interface DOMNode {
    nodeType: 'button' | 'input' | 'select' | 'link' | 'img' | 'form' | 'list' | 'listitem' | 'region' | 'text' | 'generic';
    selector: string;
    isVisible: boolean;
    text?: string;
    children?: DOMNode[];
    attributes?: ElementAttributes;
    accessibility?: AccessibilityInfo;
    options?: SelectOption[];
}

// 全局状态缓存
let lastPageSnapshot: PageSnapshot | null = null;

export function serializeDOMTree(root = document.body): PageSnapshot {
    const generateSelector = (element: Element): string => {
        // 优先使用 ID
        if (element.id) {
            return `#${CSS.escape(element.id)}`;
        }

        // 使用 data-ai-id（如果存在）
        const dataId = element.getAttribute('data-ai-id');
        if (dataId) {
            return `[data-ai-id="${CSS.escape(dataId)}"]`;
        }

        // 使用 name 属性（对于表单元素）
        const name = element.getAttribute('name');
        if (name && ['input', 'select', 'textarea'].includes(element.tagName.toLowerCase())) {
            return `${element.tagName.toLowerCase()}[name="${CSS.escape(name)}"]`;
        }

        // 构建路径
        const path: string[] = [];
        let current: Element | null = element;

        while (current && current !== document.body && path.length < 4) {
            let selector = current.tagName.toLowerCase();

            // 添加一个主要类名
            if (current.className && typeof current.className === 'string') {
                const classes = current.className
                    .trim()
                    .split(/\s+/)
                    .filter((cls) => cls);
                if (classes.length > 0) {
                    selector += `.${CSS.escape(classes[0])}`;
                }
            }

            // 添加 :nth-child（如果需要）
            if (current.parentElement) {
                const siblings = Array.from(current.parentElement.children);
                const sameTagSiblings = siblings.filter((s) => s.tagName === current!.tagName);
                if (sameTagSiblings.length > 1) {
                    const index = siblings.indexOf(current) + 1;
                    selector += `:nth-child(${index})`;
                }
            }

            path.unshift(selector);
            current = current.parentElement;
        }

        if (current === document.body) {
            path.unshift('body');
        }

        // 返回最短的唯一选择器
        return path.slice(-3).join(' > ');
    };

    const extractAccessibilityInfo = (element: Element): AccessibilityInfo => {
        const accessibility: AccessibilityInfo = {};

        // 基本无障碍属性
        const title = element.getAttribute('title');
        if (title) accessibility.title = title;

        const ariaLabel = element.getAttribute('aria-label');
        if (ariaLabel) accessibility.ariaLabel = ariaLabel;

        const ariaLabelledBy = element.getAttribute('aria-labelledby');
        if (ariaLabelledBy) accessibility.ariaLabelledBy = ariaLabelledBy;

        const ariaDescribedBy = element.getAttribute('aria-describedby');
        if (ariaDescribedBy) accessibility.ariaDescribedBy = ariaDescribedBy;

        const role = element.getAttribute('role');
        if (role) accessibility.ariaRole = role;

        // 布尔值 ARIA 属性
        const ariaExpanded = element.getAttribute('aria-expanded');
        if (ariaExpanded !== null) accessibility.ariaExpanded = ariaExpanded === 'true';

        const ariaSelected = element.getAttribute('aria-selected');
        if (ariaSelected !== null) accessibility.ariaSelected = ariaSelected === 'true';

        const ariaChecked = element.getAttribute('aria-checked');
        if (ariaChecked !== null) accessibility.ariaChecked = ariaChecked === 'true';

        const ariaDisabled = element.getAttribute('aria-disabled');
        if (ariaDisabled !== null) accessibility.ariaDisabled = ariaDisabled === 'true';

        const ariaHidden = element.getAttribute('aria-hidden');
        if (ariaHidden !== null) accessibility.ariaHidden = ariaHidden === 'true';

        // 其他 ARIA 属性
        const ariaLive = element.getAttribute('aria-live');
        if (ariaLive) accessibility.ariaLive = ariaLive;

        const ariaLevel = element.getAttribute('aria-level');
        if (ariaLevel) accessibility.ariaLevel = parseInt(ariaLevel, 10);

        const ariaSetSize = element.getAttribute('aria-setsize');
        if (ariaSetSize) accessibility.ariaSetSize = parseInt(ariaSetSize, 10);

        const ariaPosInSet = element.getAttribute('aria-posinset');
        if (ariaPosInSet) accessibility.ariaPosInSet = parseInt(ariaPosInSet, 10);

        // tabIndex
        const tabIndex = element.getAttribute('tabindex');
        if (tabIndex !== null) accessibility.tabIndex = parseInt(tabIndex, 10);

        // 特定元素的无障碍属性
        if (element.tagName.toLowerCase() === 'img') {
            const alt = (element as HTMLImageElement).alt;
            if (alt) accessibility.alt = alt;
        }

        if (['input', 'textarea'].includes(element.tagName.toLowerCase())) {
            const placeholder = (element as HTMLInputElement).placeholder;
            if (placeholder) accessibility.placeholder = placeholder;
        }

        return Object.keys(accessibility).length > 0 ? accessibility : {};
    };

    const serializeNode = (node: Node): DOMNode | null => {
        if (node.nodeType !== Node.ELEMENT_NODE) {
            return null;
        }

        const element = node as Element;

        // 跳过不需要的元素
        if (['SCRIPT', 'STYLE', 'META', 'LINK'].includes(element.tagName)) {
            return null;
        }

        const tagName = element.tagName.toLowerCase();
        const isVisible = (() => {
            const style = window.getComputedStyle(element);
            const rect = element.getBoundingClientRect();
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0' && rect.width > 0 && rect.height > 0;
        })();

        // 确定节点类型
        let nodeType: DOMNode['nodeType'] = 'generic';
        if (tagName === 'button') {
            nodeType = 'button';
        } else if (tagName === 'ul' || tagName === 'ol') {
            nodeType = 'list';
        } else if (tagName === 'li') {
            nodeType = 'listitem';
        } else if (tagName === 'img') {
            nodeType = 'img';
        } else if (tagName === 'section' || (tagName === 'div' && element.getAttribute('role') === 'region')) {
            nodeType = 'region';
        } else if (tagName === 'select') {
            nodeType = 'select';
        } else if (tagName === 'input') {
            nodeType = 'input';
        } else if (tagName === 'a') {
            nodeType = 'link';
        } else if (tagName === 'form') {
            nodeType = 'form';
        }

        // 提取文本内容
        const textContent = element.textContent?.replace(/\s+/g, ' ').trim().slice(0, 100) || '';

        // 特殊处理：将连续的文本节点拆分为单独的子节点
        const children: DOMNode[] = [];
        Array.from(node.childNodes).forEach((child) => {
            if (child.nodeType === Node.ELEMENT_NODE) {
                const serializedChild = serializeNode(child);
                if (serializedChild) {
                    children.push(serializedChild);
                }
            } else if (child.nodeType === Node.TEXT_NODE && child.textContent?.trim()) {
                // 文本节点作为独立的 generic 节点
                children.push({
                    nodeType: 'text',
                    selector: `${generateSelector(element)}::text`,
                    text: child.textContent.replace(/\s+/g, ' ').trim().slice(0, 50),
                    isVisible,
                    children: [],
                });
            }
        });

        // 构建节点
        const nodeInfo: DOMNode = {
            nodeType,
            selector: generateSelector(element),
            isVisible,
        };

        // 添加文本内容（如果存在）
        if (textContent && nodeType !== 'generic') {
            nodeInfo.text = textContent;
        }

        // 添加子节点
        if (children.length > 0) {
            nodeInfo.children = children;
        }

        // 提取无障碍信息
        const accessibility = extractAccessibilityInfo(element);
        if (Object.keys(accessibility).length > 0) {
            nodeInfo.accessibility = accessibility;
        }

        // 添加特定属性（根据节点类型）
        const attributes: ElementAttributes = {};

        // 通用属性
        if (element.id) attributes.id = element.id;
        if (element.className && typeof element.className === 'string') {
            attributes.className = element.className;
        }

        if (nodeType === 'button' || nodeType === 'input') {
            const inputElement = element as HTMLButtonElement | HTMLInputElement;
            attributes.disabled = inputElement.disabled;

            if ('value' in inputElement) attributes.value = inputElement.value;
            if ('type' in inputElement) attributes.type = inputElement.type;
            if ('checked' in inputElement) attributes.checked = inputElement.checked;
            if ('name' in inputElement) attributes.name = inputElement.name;
        } else if (nodeType === 'select') {
            const selectElement = element as HTMLSelectElement;
            attributes.disabled = selectElement.disabled;
            attributes.value = selectElement.value;
            attributes.name = selectElement.name;

            nodeInfo.options = Array.from(selectElement.options).map(
                (opt): SelectOption => ({
                    value: opt.value,
                    text: opt.textContent?.trim() || '',
                    selected: opt.selected,
                }),
            );
        } else if (nodeType === 'img') {
            const imgElement = element as HTMLImageElement;
            attributes.src = imgElement.src;
            attributes.alt = imgElement.alt;
        } else if (nodeType === 'link') {
            const linkElement = element as HTMLAnchorElement;
            attributes.href = linkElement.href;
        } else if (nodeType === 'region') {
            const label = element.getAttribute('aria-label') || element.getAttribute('title');
            if (label) {
                nodeInfo.text = label;
            }
        }

        // 只有在有属性时才添加 attributes 对象
        if (Object.keys(attributes).length > 0) {
            nodeInfo.attributes = attributes;
        }

        return nodeInfo;
    };

    const domTree = serializeNode(root);
    if (!domTree) {
        throw new Error('Failed to serialize DOM tree');
    }

    return {
        url: location.href,
        title: document.title,
        viewport: {
            width: window.innerWidth,
            height: window.innerHeight,
        },
        domTree,
        timestamp: Date.now(),
    };
}

// 计算页面差异
export function calculatePageDiff(oldSnapshot: PageSnapshot | null, newSnapshot: PageSnapshot): PageDiff {
    if (!oldSnapshot) {
        return {
            hasChanges: true,
            changes: [],
            newElements: [],
            removedElements: [],
            modifiedElements: [],
            summary: '首次加载页面',
        };
    }

    const changes: ElementChange[] = [];
    const newElements: DOMNode[] = [];
    const removedElements: DOMNode[] = [];
    const modifiedElements: Array<{ before: DOMNode; after: DOMNode }> = [];

    // 创建元素映射
    const oldElementsMap = new Map<string, DOMNode>();
    const newElementsMap = new Map<string, DOMNode>();

    const collectElements = (node: DOMNode, map: Map<string, DOMNode>): void => {
        map.set(node.selector, node);
        if (node.children) {
            node.children.forEach((child) => collectElements(child, map));
        }
    };

    collectElements(oldSnapshot.domTree, oldElementsMap);
    collectElements(newSnapshot.domTree, newElementsMap);

    // 检查新增的元素
    for (const [selector, element] of newElementsMap) {
        if (!oldElementsMap.has(selector)) {
            newElements.push(element);
            changes.push({
                selector,
                changeType: 'added',
                after: element,
            });
        }
    }

    // 检查删除的元素
    for (const [selector, element] of oldElementsMap) {
        if (!newElementsMap.has(selector)) {
            removedElements.push(element);
            changes.push({
                selector,
                changeType: 'removed',
                before: element,
            });
        }
    }

    // 检查修改的元素
    for (const [selector, newElement] of newElementsMap) {
        const oldElement = oldElementsMap.get(selector);
        if (oldElement && !deepEqual(oldElement, newElement)) {
            modifiedElements.push({ before: oldElement, after: newElement });
            changes.push({
                selector,
                changeType: 'modified',
                before: oldElement,
                after: newElement,
            });
        }
    }

    const hasChanges = changes.length > 0;
    let summary = '';

    if (!hasChanges) {
        summary = '页面无变化';
    } else {
        const parts: string[] = [];
        if (newElements.length > 0) parts.push(`新增 ${newElements.length} 个元素`);
        if (removedElements.length > 0) parts.push(`删除 ${removedElements.length} 个元素`);
        if (modifiedElements.length > 0) parts.push(`修改 ${modifiedElements.length} 个元素`);
        summary = parts.join('，');
    }

    return {
        hasChanges,
        changes,
        newElements,
        removedElements,
        modifiedElements,
        summary,
    };
}

// 深度比较函数
function deepEqual(obj1: unknown, obj2: unknown): boolean {
    if (obj1 === obj2) return true;

    if (obj1 == null || obj2 == null) return false;

    if (typeof obj1 !== typeof obj2) return false;

    if (typeof obj1 !== 'object') return obj1 === obj2;

    if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

    if (Array.isArray(obj1)) {
        const arr1 = obj1 as unknown[];
        const arr2 = obj2 as unknown[];
        if (arr1.length !== arr2.length) return false;
        for (let i = 0; i < arr1.length; i++) {
            if (!deepEqual(arr1[i], arr2[i])) return false;
        }
        return true;
    }

    const keys1 = Object.keys(obj1 as Record<string, unknown>);
    const keys2 = Object.keys(obj2 as Record<string, unknown>);

    if (keys1.length !== keys2.length) return false;

    for (const key of keys1) {
        if (!keys2.includes(key)) return false;
        if (!deepEqual((obj1 as Record<string, unknown>)[key], (obj2 as Record<string, unknown>)[key])) return false;
    }

    return true;
}

// 获取页面快照并计算差异
export function getPageSnapshotWithDiff(): { snapshot: PageSnapshot; diff: PageDiff } {
    const currentSnapshot = serializeDOMTree();
    const diff = calculatePageDiff(lastPageSnapshot, currentSnapshot);

    // 更新缓存
    lastPageSnapshot = currentSnapshot;

    return {
        snapshot: currentSnapshot,
        diff,
    };
}

// 获取当前页面快照
export function getCurrentPageSnapshot(): PageSnapshot {
    return serializeDOMTree();
}

// 设置页面快照（用于初始化或重置）
export function setPageSnapshot(snapshot: PageSnapshot): void {
    lastPageSnapshot = snapshot;
}

// 交互元素接口
export interface InteractiveElement {
    selector: string;
    type: DOMNode['nodeType'];
    text?: string;
    attributes: ElementAttributes;
    accessibility?: AccessibilityInfo;
    options?: SelectOption[];
}

// getInteractiveElements 函数，生成扁平化的交互元素列表
export function getInteractiveElements(): InteractiveElement[] {
    const elements: InteractiveElement[] = [];

    const walkTree = (node: DOMNode): void => {
        if (
            node.nodeType === 'button' ||
            node.nodeType === 'input' ||
            node.nodeType === 'select' ||
            node.nodeType === 'listitem' ||
            node.nodeType === 'link'
        ) {
            elements.push({
                selector: node.selector,
                type: node.nodeType,
                text: node.text,
                attributes: node.attributes || {},
                accessibility: node.accessibility,
                options: node.options || undefined,
            });
        }
        if (node.children) {
            node.children.forEach(walkTree);
        }
    };

    const domTree = serializeDOMTree().domTree;
    walkTree(domTree);

    return elements;
}
