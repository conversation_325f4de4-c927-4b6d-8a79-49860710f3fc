import { getPageSnapshotWithDiff, serializeDOMTree, setPageSnapshot, type PageDiff } from './webContent.js';

export interface ElementInfo {
    selector: string;
    tagName: string;
    type?: string;
    text?: string;
    value?: string;
    placeholder?: string;
    href?: string;
    src?: string;
    alt?: string;
    title?: string;
    disabled?: boolean;
    checked?: boolean;
    selected?: boolean;
    options?: Array<{ value: string; text: string; selected: boolean }>;
    position: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    isVisible: boolean;
    isInteractable: boolean;
}

export interface ToolResult {
    success: boolean;
    message: string;
    changes?: PageDiff;
}

export class WebTools {
    private static generateSelector(element: Element): string {
        // 生成唯一的CSS选择器
        let selector = '';
        let current = element as Element | null;

        while (current && current !== document.body) {
            let currentSelector = current.tagName.toLowerCase();

            // 添加ID（如果有）
            if (current.id) {
                currentSelector += `#${current.id}`;
                selector = currentSelector + (selector ? ' > ' + selector : '');
                break;
            }

            // 添加类名（如果有）
            if (current.className && typeof current.className === 'string') {
                const classes = current.className
                    .trim()
                    .split(/\s+/)
                    .filter((cls) => cls);
                if (classes.length > 0) {
                    currentSelector += '.' + classes.join('.');
                }
            }

            // 添加nth-child（如果需要）
            if (current.parentElement) {
                const siblings = Array.from(current.parentElement.children);
                const index = siblings.indexOf(current);
                if (siblings.filter((s) => s.tagName === current!.tagName).length > 1) {
                    currentSelector += `:nth-child(${index + 1})`;
                }
            }

            selector = currentSelector + (selector ? ' > ' + selector : '');
            current = current.parentElement;
        }

        return selector || element.tagName.toLowerCase();
    }

    private static isVisible(element: Element): boolean {
        const style = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();

        return (
            style.display !== 'none' &&
            style.visibility !== 'hidden' &&
            style.opacity !== '0' &&
            rect.width > 0 &&
            rect.height > 0 &&
            rect.top < window.innerHeight &&
            rect.bottom > 0 &&
            rect.left < window.innerWidth &&
            rect.right > 0
        );
    }

    private static isInteractable(element: Element): boolean {
        const tagName = element.tagName.toLowerCase();
        const type = (element as HTMLInputElement).type?.toLowerCase();

        // 检查是否是可交互的元素
        const interactableTags = ['button', 'a', 'input', 'textarea', 'select', 'option'];
        const interactableTypes = [
            'button',
            'submit',
            'reset',
            'checkbox',
            'radio',
            'file',
            'text',
            'email',
            'password',
            'search',
            'url',
            'tel',
            'number',
            'date',
            'time',
            'datetime-local',
            'month',
            'week',
            'color',
            'range',
        ];

        if (interactableTags.includes(tagName)) {
            if (tagName === 'input' && type && !interactableTypes.includes(type)) {
                return false;
            }
            return true;
        }

        // 检查是否有点击事件监听器或者可点击属性
        const hasClickHandler = !!(
            element.getAttribute('onclick') ||
            element.getAttribute('role') === 'button' ||
            element.getAttribute('tabindex') !== null ||
            window.getComputedStyle(element).cursor === 'pointer'
        );

        return hasClickHandler;
    }

    private static extractElementInfo(element: Element): ElementInfo {
        const rect = element.getBoundingClientRect();
        const tagName = element.tagName.toLowerCase();
        const inputElement = element as HTMLInputElement;
        const selectElement = element as HTMLSelectElement;

        const info: ElementInfo = {
            selector: this.generateSelector(element),
            tagName,
            position: {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height,
            },
            isVisible: this.isVisible(element),
            isInteractable: this.isInteractable(element),
        };

        // 提取文本内容
        if (element.textContent && element.textContent.trim()) {
            info.text = element.textContent.trim().substring(0, 200);
        }

        // 提取特定属性
        if (inputElement.type) info.type = inputElement.type;
        if (inputElement.value !== undefined) info.value = inputElement.value;
        if (inputElement.placeholder) info.placeholder = inputElement.placeholder;
        if (inputElement.disabled !== undefined) info.disabled = inputElement.disabled;
        if (inputElement.checked !== undefined) info.checked = inputElement.checked;

        // 处理链接
        if (tagName === 'a') {
            const linkElement = element as HTMLAnchorElement;
            if (linkElement.href) info.href = linkElement.href;
        }

        // 处理图片
        if (tagName === 'img') {
            const imgElement = element as HTMLImageElement;
            if (imgElement.src) info.src = imgElement.src;
            if (imgElement.alt) info.alt = imgElement.alt;
        }

        // 处理select元素
        if (tagName === 'select') {
            info.options = Array.from(selectElement.options).map((option) => ({
                value: option.value,
                text: option.text,
                selected: option.selected,
            }));
        }

        // 提取通用属性
        if (element.getAttribute('title')) info.title = element.getAttribute('title')!;

        return info;
    }

    // 执行操作前保存页面状态
    private static savePageState(): void {
        const snapshot = serializeDOMTree();
        setPageSnapshot(snapshot);
    }

    static getPageContent(): string {
        const { snapshot } = getPageSnapshotWithDiff();
        return JSON.stringify(snapshot);
    }

    static async navigateToPage(page: string): Promise<ToolResult> {
        try {
            // 页面路由映射
            const pageRoutes: Record<string, string> = {
                dashboard: '/',
                deployments: '/deployments',
                statefulsets: '/statefulsets',
                services: '/services',
                ingresses: '/ingresses',
                storages: '/storages',
                configmaps: '/configmaps',
                secrets: '/secrets',
                hpa: '/hpa',
                pods: '/pods',
                workspaces: '/workspaces',
                balance: '/balance',
            };

            const route = pageRoutes[page];
            if (!route) {
                return {
                    success: false,
                    message: `未知的页面: ${page}`,
                };
            }

            // 保存操作前的页面状态
            this.savePageState();

            // 使用 Inertia.js 进行导航
            const { router } = await import('@inertiajs/vue3');
            router.visit(route);

            // 等待页面加载
            await new Promise((resolve) => setTimeout(resolve, 1000));

            return {
                success: true,
                message: `成功导航到 ${page} 页面`,
            };
        } catch (error) {
            return {
                success: false,
                message: `导航失败: ${error instanceof Error ? error.message : '未知错误'}`,
            };
        }
    }

    static async clickElement(selector: string): Promise<ToolResult> {
        try {
            const element = document.querySelector(selector);
            if (!element) {
                return {
                    success: false,
                    message: `未找到选择器为 "${selector}" 的元素`,
                };
            }

            // 保存操作前的页面状态
            this.savePageState();

            // 滚动到元素位置
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 等待一小段时间确保滚动完成
            await new Promise((resolve) => setTimeout(resolve, 300));

            // 触发点击事件
            const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
            });
            element.dispatchEvent(event);

            // 等待页面可能的变化
            await new Promise((resolve) => setTimeout(resolve, 500));

            return {
                success: true,
                message: `成功点击了元素 "${selector}"`,
            };
        } catch (error) {
            return {
                success: false,
                message: `点击元素时出错: ${error instanceof Error ? error.message : '未知错误'}`,
            };
        }
    }

    static async typeText(selector: string, text: string): Promise<ToolResult> {
        try {
            const element = document.querySelector(selector) as HTMLInputElement | HTMLTextAreaElement;
            if (!element) {
                return {
                    success: false,
                    message: `未找到选择器为 "${selector}" 的元素`,
                };
            }

            const tagName = element.tagName.toLowerCase();
            if (!['input', 'textarea'].includes(tagName)) {
                return {
                    success: false,
                    message: `元素 "${selector}" 不是输入框`,
                };
            }

            // 保存操作前的页面状态
            this.savePageState();

            // 聚焦元素
            element.focus();

            // 清空现有内容
            element.value = '';

            // 输入新文本
            element.value = text;

            // 触发输入事件
            const inputEvent = new Event('input', { bubbles: true });
            element.dispatchEvent(inputEvent);

            const changeEvent = new Event('change', { bubbles: true });
            element.dispatchEvent(changeEvent);

            // 等待可能的页面变化
            await new Promise((resolve) => setTimeout(resolve, 200));

            return {
                success: true,
                message: `成功在元素 "${selector}" 中输入了文本: "${text}"`,
            };
        } catch (error) {
            return {
                success: false,
                message: `输入文本时出错: ${error instanceof Error ? error.message : '未知错误'}`,
            };
        }
    }

    static async selectOption(selector: string, value: string): Promise<ToolResult> {
        try {
            const element = document.querySelector(selector) as HTMLSelectElement;
            if (!element) {
                return {
                    success: false,
                    message: `未找到选择器为 "${selector}" 的元素`,
                };
            }

            if (element.tagName.toLowerCase() !== 'select') {
                return {
                    success: false,
                    message: `元素 "${selector}" 不是下拉选择框`,
                };
            }

            // 查找选项
            const option = Array.from(element.options).find((opt) => opt.value === value || opt.text === value);
            if (!option) {
                return {
                    success: false,
                    message: `在选择框中未找到值为 "${value}" 的选项`,
                };
            }

            // 保存操作前的页面状态
            this.savePageState();

            // 选择选项
            element.value = option.value;

            // 触发变化事件
            const changeEvent = new Event('change', { bubbles: true });
            element.dispatchEvent(changeEvent);

            // 等待可能的页面变化
            await new Promise((resolve) => setTimeout(resolve, 200));

            return {
                success: true,
                message: `成功在选择框 "${selector}" 中选择了选项 "${option.text}"`,
            };
        } catch (error) {
            return {
                success: false,
                message: `选择选项时出错: ${error instanceof Error ? error.message : '未知错误'}`,
            };
        }
    }

    static async checkCheckbox(selector: string, checked: boolean): Promise<ToolResult> {
        try {
            const element = document.querySelector(selector) as HTMLInputElement;
            if (!element) {
                return {
                    success: false,
                    message: `未找到选择器为 "${selector}" 的元素`,
                };
            }

            if (element.type !== 'checkbox') {
                return {
                    success: false,
                    message: `元素 "${selector}" 不是复选框`,
                };
            }

            // 保存操作前的页面状态
            this.savePageState();

            // 设置选中状态
            element.checked = checked;

            // 触发变化事件
            const changeEvent = new Event('change', { bubbles: true });
            element.dispatchEvent(changeEvent);

            // 等待可能的页面变化
            await new Promise((resolve) => setTimeout(resolve, 200));

            return {
                success: true,
                message: `成功${checked ? '选中' : '取消选中'}了复选框 "${selector}"`,
            };
        } catch (error) {
            return {
                success: false,
                message: `操作复选框时出错: ${error instanceof Error ? error.message : '未知错误'}`,
            };
        }
    }

    static async scrollToElement(selector: string): Promise<ToolResult> {
        try {
            const element = document.querySelector(selector);
            if (!element) {
                return {
                    success: false,
                    message: `未找到选择器为 "${selector}" 的元素`,
                };
            }

            // 保存操作前的页面状态
            this.savePageState();

            element.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 等待滚动完成
            await new Promise((resolve) => setTimeout(resolve, 500));

            return {
                success: true,
                message: `成功滚动到元素 "${selector}"`,
            };
        } catch (error) {
            return {
                success: false,
                message: `滚动到元素时出错: ${error instanceof Error ? error.message : '未知错误'}`,
            };
        }
    }

    static async waitForElement(selector: string, timeout: number = 5000): Promise<ToolResult> {
        const startTime = Date.now();

        // 保存操作前的页面状态
        this.savePageState();

        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element && this.isVisible(element)) {
                return {
                    success: true,
                    message: `元素 "${selector}" 已出现`,
                };
            }
            await new Promise((resolve) => setTimeout(resolve, 100));
        }

        return {
            success: false,
            message: `等待元素 "${selector}" 超时（${timeout}ms）`,
        };
    }
}

// 定义工具函数
export const webToolDefinitions = [
    {
        name: 'navigate_to_page',
        description: '导航到平台的指定页面',
        parameters: {
            type: 'object',
            properties: {
                page: {
                    type: 'string',
                    description: '要导航到的页面',
                    enum: [
                        'dashboard',
                        'deployments',
                        'statefulsets',
                        'services',
                        'ingresses',
                        'storages',
                        'configmaps',
                        'secrets',
                        'hpa',
                        'pods',
                        'workspaces',
                        'balance',
                    ],
                },
            },
            required: ['page'],
        },
    },
    {
        name: 'click_element',
        description: '点击页面上的指定元素',
        parameters: {
            type: 'object',
            properties: {
                selector: {
                    type: 'string',
                    description: '要点击的元素的CSS选择器',
                },
            },
            required: ['selector'],
        },
    },
    {
        name: 'type_text',
        description: '在输入框中输入文本',
        parameters: {
            type: 'object',
            properties: {
                selector: {
                    type: 'string',
                    description: '输入框的CSS选择器',
                },
                text: {
                    type: 'string',
                    description: '要输入的文本',
                },
            },
            required: ['selector', 'text'],
        },
    },
    {
        name: 'select_option',
        description: '在下拉选择框中选择选项',
        parameters: {
            type: 'object',
            properties: {
                selector: {
                    type: 'string',
                    description: '选择框的CSS选择器',
                },
                value: {
                    type: 'string',
                    description: '要选择的选项值或文本',
                },
            },
            required: ['selector', 'value'],
        },
    },
    {
        name: 'check_checkbox',
        description: '选中或取消选中复选框',
        parameters: {
            type: 'object',
            properties: {
                selector: {
                    type: 'string',
                    description: '复选框的CSS选择器',
                },
                checked: {
                    type: 'boolean',
                    description: '是否选中复选框',
                },
            },
            required: ['selector', 'checked'],
        },
    },
    {
        name: 'scroll_to_element',
        description: '滚动到指定元素',
        parameters: {
            type: 'object',
            properties: {
                selector: {
                    type: 'string',
                    description: '要滚动到的元素的CSS选择器',
                },
            },
            required: ['selector'],
        },
    },
    {
        name: 'wait_for_element',
        description: '等待指定元素出现在页面上',
        parameters: {
            type: 'object',
            properties: {
                selector: {
                    type: 'string',
                    description: '要等待的元素的CSS选择器',
                },
                timeout: {
                    type: 'number',
                    description: '等待超时时间（毫秒），默认5000',
                },
            },
            required: ['selector'],
        },
    },
];

// 工具执行器
export async function executeWebTool(toolName: string, parameters: any): Promise<ToolResult> {
    // 延迟 3 秒等页面加载
    await new Promise((resolve) => setTimeout(resolve, 3000));

    switch (toolName) {
        case 'get_page_content':
            return {
                success: true,
                message: '获取页面内容成功',
                changes: {
                    hasChanges: false,
                    changes: [],
                    newElements: [],
                    removedElements: [],
                    modifiedElements: [],
                    summary: '页面内容获取',
                },
            };
        case 'navigate_to_page':
            return await WebTools.navigateToPage(parameters.page);
        case 'click_element':
            return await WebTools.clickElement(parameters.selector);
        case 'type_text':
            return await WebTools.typeText(parameters.selector, parameters.text);
        case 'select_option':
            return await WebTools.selectOption(parameters.selector, parameters.value);
        case 'check_checkbox':
            return await WebTools.checkCheckbox(parameters.selector, parameters.checked);
        case 'scroll_to_element':
            return await WebTools.scrollToElement(parameters.selector);
        // case 'wait_for_element':
        // return await WebTools.waitForElement(parameters.selector, parameters.timeout);
        default:
            throw new Error(`未知的工具: ${toolName}`);
    }
}
