import eventBus from './eventBus';

const WINDOW_ID = `win-${Date.now().toString(36)}`;
const processedMessages = new Set<string>();
let isEmittingFromBridge = false;

/**
 * Propagates a message through the window hierarchy.
 * This function is the core of cross-window communication.
 */
function propagate(msg: any) {
    const messageString = JSON.stringify(msg);

    if (window.self !== window.top) {
        // I'm in an iframe, my only job is to send the event to the top window.
        if (window.top) {
            window.top.postMessage(messageString, '*');
        }
    } else {
        // I'm the top window (the hub). My job is to broadcast to all child iframes.
        document.querySelectorAll('iframe').forEach((iframe) => {
            try {
                iframe.contentWindow?.postMessage(messageString, '*');
            } catch (e) {
                console.error('Desk bridge broadcast error:', e);
            }
        });
    }
}

/**
 * Initializes the iframe communication bridge.
 * Its core responsibility is to synchronize events between the main application's
 * eventBus and any iframed windows using postMessage.
 */
export function initIframeBridge() {
    // 1. Listen for messages coming FROM other windows.
    window.addEventListener('message', (event) => {
        let msg;
        try {
            if (typeof event.data !== 'string') return;
            msg = JSON.parse(event.data);
        } catch (e) {
            return;
        }

        if (!msg || !msg.__desk) return;
        if (processedMessages.has(msg.messageId)) return;
        processedMessages.add(msg.messageId);

        // Set a flag to indicate that the next emit is synthetic and should not be re-propagated.
        isEmittingFromBridge = true;
        eventBus.emit(msg.name, msg.payload);
        isEmittingFromBridge = false; // Reset the flag immediately after.

        // If this window is the top-level hub, it must still relay the message
        // to OTHER iframes so they all get the event. The original sender will
        // be skipped by the `processedMessages` check in their own listeners.
        if (window.self === window.top) {
            propagate(msg);
        }
    });

    // 2. Listen for local eventBus events and propagate them to other windows.
    eventBus.on('*', (type, payload) => {
        // If the flag is set, it means this event originated from a postMessage,
        // so we must not propagate it again, which would cause an infinite loop.
        if (isEmittingFromBridge) {
            return;
        }

        if (typeof type !== 'string' || type.startsWith('hook:')) {
            return; // Ignore internal or non-string events
        }

        const message = {
            __desk: true,
            senderId: WINDOW_ID,
            messageId: `msg-${Date.now().toString(36)}-${Math.random().toString(36).slice(2, 6)}`,
            name: type,
            payload: payload,
        };

        processedMessages.add(message.messageId);
        propagate(message);
    });

    console.log(`PAAS-NEW: iFrame bridge initialized for ${window.self === window.top ? 'TOP' : 'IFRAME'} window.`);
}
