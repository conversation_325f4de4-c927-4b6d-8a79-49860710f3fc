import { useResourceListener } from '@/composables/useResourceListener';
import eventBus from '@/lib/eventBus';
import { useResourcesStore } from '@/stores/resourcesStore';
import { useWorkspaceStore } from '@/stores/workspaceStore';
import type { Workspace } from '@/types';
import { ref, watch } from 'vue';

/**
 * 资源管理器
 * 负责协调工作区切换和资源监听，提供统一的管理接口
 */
class ResourceManager {
    private resourceListener: ReturnType<typeof useResourceListener> | null = null;
    private workspaceStore: ReturnType<typeof useWorkspaceStore> | null = null;
    private resourcesStore: ReturnType<typeof useResourcesStore> | null = null;
    private initialized = ref(false);
    private enableDebugLog: boolean;

    // 记录已注册的监听器
    private registeredListeners: (() => void)[] = [];

    constructor(options: { enableDebugLog?: boolean } = {}) {
        this.enableDebugLog = options.enableDebugLog || false;
    }

    /**
     * 初始化资源管理器
     */
    initialize() {
        if (this.initialized.value) {
            if (this.enableDebugLog) {
                console.warn('资源管理器已经初始化');
            }
            return;
        }

        // 初始化存储
        this.workspaceStore = useWorkspaceStore();
        this.resourcesStore = useResourcesStore();

        // 创建资源监听器
        this.resourceListener = useResourceListener({
            autoStart: false,
            enableDebugLog: this.enableDebugLog,
        });

        // 监听工作区变更
        this.setupWorkspaceWatcher();

        // 监听事件总线
        this.setupEventBusListeners();

        this.initialized.value = true;

        if (this.enableDebugLog) {
            console.log('资源管理器初始化完成');
        }

        // 如果当前已有工作区，立即开始监听
        if (this.workspaceStore.currentWorkspace) {
            this.handleWorkspaceChange(this.workspaceStore.currentWorkspace);
        }
    }

    /**
     * 设置工作区变更监听器
     */
    private setupWorkspaceWatcher() {
        if (!this.workspaceStore) return;

        // 监听当前工作区的变化
        const stopWatcher = watch(
            () => this.workspaceStore!.currentWorkspace,
            (newWorkspace, oldWorkspace) => {
                // 如果 same，不处理
                if (newWorkspace?.id === oldWorkspace?.id) {
                    return;
                }

                if (this.enableDebugLog) {
                    console.log('工作区变更:', {
                        from: oldWorkspace?.name || 'null',
                        to: newWorkspace?.name || 'null',
                    });
                }

                this.handleWorkspaceChange(newWorkspace);
            },
            { immediate: false }, // 不立即执行，因为初始化时会手动调用
        );

        this.registeredListeners.push(stopWatcher);
    }

    /**
     * 设置事件总线监听器
     */
    private setupEventBusListeners() {
        if (!this.workspaceStore) return;

        // 监听工作区切换事件（如果其他地方触发）
        const handleWorkspaceSwitch = (workspaceId: number) => {
            if (!this.workspaceStore) return;
            const workspace = this.workspaceStore.workspaces.find((w) => w.id === workspaceId);
            if (workspace) {
                this.handleWorkspaceChange(workspace);
            }
        };

        // 监听资源刷新事件
        const handleResourceRefresh = () => {
            if (this.workspaceStore?.currentWorkspace && this.resourceListener) {
                this.resourceListener.reconnect(this.workspaceStore.currentWorkspace);
            }
        };

        // 注册事件监听器
        eventBus.on('workspace:switched', handleWorkspaceSwitch);
        eventBus.on('resource:refresh', handleResourceRefresh);

        // 记录清理函数
        this.registeredListeners.push(() => {
            eventBus.off('workspace:switched', handleWorkspaceSwitch);
            eventBus.off('resource:refresh', handleResourceRefresh);
        });
    }

    /**
     * 处理工作区变更
     */
    private handleWorkspaceChange(newWorkspace: Workspace | null) {
        if (!this.resourceListener) {
            console.error('资源监听器未初始化');
            return;
        }

        if (this.enableDebugLog) {
            console.log('处理工作区变更:', newWorkspace?.name || 'null');
        }

        // 切换资源监听
        this.resourceListener.switchWorkspace(newWorkspace);

        // 发送工作区切换事件
        eventBus.emit('workspace:changed', newWorkspace);
    }

    /**
     * 手动刷新资源监听
     */
    refresh() {
        if (this.enableDebugLog) {
            console.log('手动刷新资源监听');
        }

        if (this.resourceListener && this.workspaceStore?.currentWorkspace) {
            this.resourceListener.reconnect(this.workspaceStore.currentWorkspace);
        }
    }

    /**
     * 获取当前状态
     */
    getStatus() {
        return {
            initialized: this.initialized.value,
            currentWorkspace: this.workspaceStore?.currentWorkspace || null,
            resourcesLoaded: this.resourcesStore?.isLoaded || false,
            isListening: this.resourceListener?.isListening.value || false,
            listeningNamespace: this.resourceListener?.currentNamespace.value || null,
            error: this.resourceListener?.error.value || null,
            resourceCounts: this.resourcesStore?.getResourceCounts || {},
        };
    }

    /**
     * 获取指定类型的资源
     */
    getResources(resourceType: keyof import('@/stores/resourcesStore').ResourceCollections) {
        if (!this.resourcesStore) return [];
        return this.resourcesStore.getResourcesByType(resourceType);
    }

    /**
     * 销毁资源管理器
     */
    destroy() {
        if (this.enableDebugLog) {
            console.log('销毁资源管理器');
        }

        // 停止资源监听
        if (this.resourceListener) {
            this.resourceListener.stopListening();
        }

        // 清理所有监听器
        this.registeredListeners.forEach((cleanup) => {
            try {
                cleanup();
            } catch (err) {
                console.warn('清理监听器时出错:', err);
            }
        });
        this.registeredListeners = [];

        // 重置状态
        this.initialized.value = false;
        this.resourceListener = null;
    }

    /**
     * 启用/禁用调试日志
     */
    setDebugLog(enable: boolean) {
        this.enableDebugLog = enable;
    }
}

// 创建全局实例
const resourceManager = new ResourceManager({
    enableDebugLog: import.meta.env.DEV, // 开发环境启用调试日志
});

export default resourceManager;

// 导出类型，供其他地方使用
export type { ResourceManager };
