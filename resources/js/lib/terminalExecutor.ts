import axios from '@/lib/axios';
import type { CommandExecutionResult } from '@/lib/eventBus';

export interface ExecuteCommandOptions {
    podName: string;
    containerName?: string;
    command: string;
    timeout?: number;
    mode?: 'shell' | 'attach';
}

export class TerminalExecutor {
    /**
     * 在指定的 Pod 中执行命令
     */
    static async executeCommand(options: ExecuteCommandOptions): Promise<CommandExecutionResult> {
        const { podName, containerName, command, timeout = 30000, mode = 'shell' } = options;

        try {
            // 通过 API 执行命令
            const response = await axios.post(`/api/pods/${podName}/exec`, {
                container: containerName,
                command: command,
                timeout: timeout / 1000, // 转换为秒
                mode: mode,
            });

            return {
                success: response.data.exit_code === 0,
                output: response.data.output || '',
                exitCode: response.data.exit_code || 0,
            };
        } catch (error: any) {
            return {
                success: false,
                output: '',
                exitCode: 1,
                error: error.response?.data?.message || error.message || 'Command execution failed',
            };
        }
    }

    /**
     * 批量执行命令
     */
    static async executeBatch(commands: ExecuteCommandOptions[]): Promise<CommandExecutionResult[]> {
        const results: CommandExecutionResult[] = [];

        for (const command of commands) {
            const result = await this.executeCommand(command);
            results.push(result);

            // 如果命令失败，可以选择是否继续执行后续命令
            if (!result.success) {
                console.warn(`Command failed: ${command.command}`, result);
            }
        }

        return results;
    }

    /**
     * 检查 Pod 是否可用
     */
    static async checkPodAvailability(podName: string): Promise<boolean> {
        try {
            const response = await axios.get(`/api/pods/${podName}`);
            return response.data.status === 'Running';
        } catch (error) {
            return false;
        }
    }

    /**
     * 获取 Pod 的容器列表
     */
    static async getContainers(podName: string): Promise<string[]> {
        try {
            const response = await axios.get(`/api/pods/${podName}`);
            return response.data.containers?.map((c: any) => c.name) || [];
        } catch (error) {
            return [];
        }
    }

    /**
     * 常用命令快捷方式
     */
    static async getSystemInfo(podName: string, containerName?: string): Promise<CommandExecutionResult> {
        return this.executeCommand({
            podName,
            containerName,
            command: 'uname -a && cat /etc/os-release',
        });
    }

    static async getProcessList(podName: string, containerName?: string): Promise<CommandExecutionResult> {
        return this.executeCommand({
            podName,
            containerName,
            command: 'ps aux',
        });
    }

    static async getDiskUsage(podName: string, containerName?: string): Promise<CommandExecutionResult> {
        return this.executeCommand({
            podName,
            containerName,
            command: 'df -h',
        });
    }

    static async getMemoryUsage(podName: string, containerName?: string): Promise<CommandExecutionResult> {
        return this.executeCommand({
            podName,
            containerName,
            command: 'free -h',
        });
    }

    static async getNetworkInfo(podName: string, containerName?: string): Promise<CommandExecutionResult> {
        return this.executeCommand({
            podName,
            containerName,
            command: 'ip addr show',
        });
    }

    static async getEnvironmentVariables(podName: string, containerName?: string): Promise<CommandExecutionResult> {
        return this.executeCommand({
            podName,
            containerName,
            command: 'env | sort',
        });
    }

    static async testConnectivity(podName: string, target: string, containerName?: string): Promise<CommandExecutionResult> {
        return this.executeCommand({
            podName,
            containerName,
            command: `ping -c 3 ${target}`,
        });
    }

    static async listFiles(podName: string, path: string = '/', containerName?: string): Promise<CommandExecutionResult> {
        return this.executeCommand({
            podName,
            containerName,
            command: `ls -la ${path}`,
        });
    }

    static async getFileContent(podName: string, filePath: string, containerName?: string): Promise<CommandExecutionResult> {
        return this.executeCommand({
            podName,
            containerName,
            command: `cat ${filePath}`,
        });
    }

    static async tailLogs(podName: string, logPath: string, lines: number = 100, containerName?: string): Promise<CommandExecutionResult> {
        return this.executeCommand({
            podName,
            containerName,
            command: `tail -n ${lines} ${logPath}`,
        });
    }
}
