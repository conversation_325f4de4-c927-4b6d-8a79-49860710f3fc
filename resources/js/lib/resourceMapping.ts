import type { ResourceTypeMapping } from '@/types/yaml';

// 资源类型映射配置
export const RESOURCE_TYPE_MAPPINGS: ResourceTypeMapping[] = [
    {
        kind: 'Storage',
        apiEndpoint: '/api/storages',
        resourceStoreKey: 'storages',
    },
    {
        kind: 'Deployment',
        apiEndpoint: '/api/deployments',
        resourceStoreKey: 'deployments',
    },
    {
        kind: 'StatefulSet',
        apiEndpoint: '/api/statefulsets',
        resourceStoreKey: 'statefulsets',
    },
    {
        kind: 'Service',
        apiEndpoint: '/api/services',
        resourceStoreKey: 'services',
    },
    {
        kind: 'Ingress',
        apiEndpoint: '/api/ingresses',
        resourceStoreKey: 'ingresses',
    },
    {
        kind: 'ConfigMap',
        apiEndpoint: '/api/configmaps',
        resourceStoreKey: 'configmaps',
    },
    {
        kind: 'Secret',
        apiEndpoint: '/api/secrets',
        resourceStoreKey: 'secrets',
    },
    {
        kind: 'GenericSecret',
        apiEndpoint: '/api/secrets/generic',
        resourceStoreKey: 'secrets',
        deleteApiEndpoint: '/api/secrets', // 使用通用删除路由
    },
    {
        kind: 'DockerRegistrySecret',
        apiEndpoint: '/api/secrets/docker-registry',
        resourceStoreKey: 'secrets',
        deleteApiEndpoint: '/api/secrets', // 使用通用删除路由
    },
    {
        kind: 'TlsSecret',
        apiEndpoint: '/api/secrets/tls',
        resourceStoreKey: 'secrets',
        deleteApiEndpoint: '/api/secrets', // 使用通用删除路由
    },
    {
        kind: 'BasicAuthSecret',
        apiEndpoint: '/api/secrets/basic-auth',
        resourceStoreKey: 'secrets',
        deleteApiEndpoint: '/api/secrets', // 使用通用删除路由
    },
    {
        kind: 'SshAuthSecret',
        apiEndpoint: '/api/secrets/ssh-auth',
        resourceStoreKey: 'secrets',
        deleteApiEndpoint: '/api/secrets', // 使用通用删除路由
    },
    {
        kind: 'HorizontalPodAutoscaler',
        apiEndpoint: '/api/hpas',
        resourceStoreKey: 'horizontalpodautoscalers',
    },
];

// 根据 kind 获取资源类型映射
export function getResourceMapping(kind: string): ResourceTypeMapping | null {
    return RESOURCE_TYPE_MAPPINGS.find((mapping) => mapping.kind === kind) || null;
}

// 获取所有支持的资源类型
export function getSupportedResourceKinds(): string[] {
    return RESOURCE_TYPE_MAPPINGS.map((mapping) => mapping.kind);
}

// 验证资源类型是否支持
export function isResourceKindSupported(kind: string): boolean {
    return RESOURCE_TYPE_MAPPINGS.some((mapping) => mapping.kind === kind);
}
