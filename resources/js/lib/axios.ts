import axios from 'axios';
import eventBus from './eventBus';

// 设置基础 URL
axios.defaults.baseURL = window.location.origin;

// 设置默认头部
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
axios.defaults.withCredentials = true;

// 响应拦截器
axios.interceptors.response.use(
    (response) => response,
    (error) => {
        console.log('api:error', error);
        eventBus.emit('api:error', {
            message: error.response?.data?.message || '请求失败',
            errors: error.response?.data?.errors,
        });
        return Promise.reject(error);
    },
);

export default axios;
