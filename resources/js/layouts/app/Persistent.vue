<template>
    <Toaster class="pointer-events-auto" />

    <slot />

    <!-- 统一窗口管理器 -->
    <WindowManager ref="windowManagerRef" v-if="isDesktop" />

    <!-- 窗口任务栏 -->
    <WindowTaskbar
        v-if="windows.length > 0 && isDesktop"
        :windows="windows"
        :active-window-id="activeWindowId"
        :position="'bottom'"
        @window-click="handleTaskbarWindowClick"
        @window-close="handleTaskbarWindowClose"
    />
</template>

<script setup lang="ts">
import { Toaster } from '@/components/ui/sonner';
import WindowManager from '@/components/WindowManager.vue';
import WindowTaskbar from '@/components/windows/WindowTaskbar.vue';
import eventBus, { type ApiErrorPayload } from '@/lib/eventBus';
import resourceManager from '@/lib/resourceManager';
import { useWorkspaceStore } from '@/stores/workspaceStore';
import type { Auth } from '@/types';
import { usePage } from '@inertiajs/vue3';
import { AlertCircle, CheckCircle } from 'lucide-vue-next';
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { toast } from 'vue-sonner';
import 'vue-sonner/style.css';

const isDesktop = computed(() => window.parent === window);
console.log('isDesktop', isDesktop.value);
const workspaceStore = useWorkspaceStore();

const page = usePage();
const auth = computed(() => page.props.auth as Auth);
const user = computed(() => auth.value?.user);
const workspace = computed(() => auth.value?.workspace);

// 窗口管理器引用
const windowManagerRef = ref<InstanceType<typeof WindowManager>>();

// 窗口状态 - 从WindowManager获取
const windows = computed(() => windowManagerRef.value?.windows || []);
const activeWindowId = computed(() => windowManagerRef.value?.activeWindowId || null);

// 按键设置常量
const KEYS = {
    AI_ASSISTANT: {
        TOGGLE_KEY: '/',
        CLOSE_KEY: 'Escape',
        DOUBLE_PRESS_INTERVAL: 500, // 双击间隔（毫秒）
    },
};

// 全局变量记录上一次按键时间
let lastKeyPressTime = 0;
let lastKeyPressed = '';

// 检查是否有输入元素处于聚焦状态
const isInputFocused = () => {
    const activeElement = document.activeElement;
    const inputTags = ['INPUT', 'TEXTAREA', 'SELECT'];
    const isContentEditable = activeElement?.hasAttribute('contenteditable');

    return activeElement && (inputTags.includes(activeElement.tagName) || isContentEditable);
};

// 添加全局按键事件监听器
const handleKeyDown = (event: KeyboardEvent) => {
    // 处理双击 / 键
    if (event.key === KEYS.AI_ASSISTANT.TOGGLE_KEY && !isInputFocused()) {
        const now = Date.now();
        const timeDiff = now - lastKeyPressTime;

        if (lastKeyPressed === KEYS.AI_ASSISTANT.TOGGLE_KEY && timeDiff < KEYS.AI_ASSISTANT.DOUBLE_PRESS_INTERVAL) {
            // 双击 /，打开助手
            event.preventDefault();
            eventBus.emit('ai-assistant:toggle');
            lastKeyPressTime = 0;
            lastKeyPressed = '';
        } else {
            lastKeyPressTime = now;
            lastKeyPressed = KEYS.AI_ASSISTANT.TOGGLE_KEY;
        }
    } else {
        // 如果按下的不是 / 键，重置记录
        if (event.key !== KEYS.AI_ASSISTANT.TOGGLE_KEY) {
            lastKeyPressed = event.key;
        }
    }

    // Escape键关闭助手
    if (event.key === KEYS.AI_ASSISTANT.CLOSE_KEY) {
        eventBus.emit('ai-assistant:close');
    }
};

// 任务栏事件处理
const handleTaskbarWindowClick = (windowId: string) => {
    if (windowManagerRef.value) {
        const window = windows.value.find((w) => w.id === windowId);
        if (window) {
            if (window.isMinimized) {
                windowManagerRef.value.restoreWindow(windowId);
            } else if (activeWindowId.value === windowId) {
                windowManagerRef.value.minimizeWindow(windowId);
            } else {
                windowManagerRef.value.focusWindow(windowId);
            }
        }
    }
};

const handleTaskbarWindowClose = (windowId: string) => {
    if (windowManagerRef.value) {
        windowManagerRef.value.closeWindow(windowId);
    }
};

// 在全局范围监听按键事件
onMounted(() => {
    document.addEventListener('keydown', handleKeyDown);
    eventBus.on('api:error', handleApiError);

    // 初始化资源管理器
    resourceManager.initialize();

    // 如果未登录，则不获取工作空间
    if (!user.value) {
        return;
    }

    // 如果没有当前工作空间，则尝试从存储中获取
    if (!workspaceStore.currentWorkspace) {
        workspaceStore.fetchWorkspaces();
    }
});

onBeforeUnmount(() => {
    document.removeEventListener('keydown', handleKeyDown);
    eventBus.off('api:error', handleApiError);
});

const handleApiError = (payload: ApiErrorPayload) => {
    let description = '';
    if (payload.errors) {
        description = Object.values(payload.errors).flat().join('<br>');
    }

    toast.error(payload.message, {
        icon: AlertCircle,
        description,
        class: 'error-toast',
    });
};

watch(
    () => page.props.flash,
    (newFlash: any) => {
        if (newFlash?.success) {
            toast.success(newFlash.success, {
                icon: CheckCircle,
                description: '操作成功',
                class: 'success-toast',
            });
        }

        if (newFlash?.error) {
            toast.error(newFlash.error, {
                icon: AlertCircle,
                description: '发生错误',
                class: 'error-toast',
            });
        }

        if (newFlash?.message) {
            toast(newFlash.message, {
                icon: AlertCircle,
                description: '提示',
                class: 'info-toast',
            });
        }
    },
    { immediate: true, deep: true },
);

// Errors
watch(
    () => page.props.errors,
    (errors: any) => {
        if (errors && Object.keys(errors).length > 0) {
            for (const key in errors) {
                toast.error(errors[key]);
            }
        }
    },
    { immediate: true, deep: true },
);

const handleTeamEvent = (notification: any) => {
    console.log('team.events', notification);
};

const handlePlainTextNotification = (notification: any) => {
    let icon = AlertCircle;
    const message = notification.title ?? '提示';
    const description = notification.message ?? '未知消息';

    switch (notification.event) {
        case 'success':
            icon = CheckCircle;
            break;
    }

    toast.success(message, {
        icon: icon,
        description: description,
        class: notification.event + '-toast',
    });
};
</script>
