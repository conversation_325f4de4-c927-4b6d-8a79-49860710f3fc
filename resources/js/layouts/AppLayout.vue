<script setup lang="ts">
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import { computed } from 'vue';
import type { BreadcrumbItemType } from '../types';
import Base from './Base.vue';

interface Props {
    breadcrumbs?: BreadcrumbItemType[];
    hideHeader?: boolean;
}

withDefaults(defineProps<Props>(), {
    breadcrumbs: () => [],
    hideHeader: false,
});

const isDesktop = computed(() => window.parent === window);
</script>

<template>
    <Base>
        <slot v-if="!isDesktop" />

        <AppSidebarLayout v-else :breadcrumbs="breadcrumbs">
            <slot />
        </AppSidebarLayout>
    </Base>
</template>
