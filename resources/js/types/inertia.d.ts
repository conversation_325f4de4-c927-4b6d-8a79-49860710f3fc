import { Page } from '@inertiajs/core';
import type { User, Workspace } from './index';

// 为 Ziggy 的 route 函数定义类型
declare module 'ziggy-js' {
    export interface Config {
        url: string;
        port: number | null;
        defaults: Record<string, any>;
        routes: Record<string, any>;
    }

    export type RouteParam = string | number;
    export type RouteParamsWithQueryOverload = Record<string, any>;

    export default function route(): string;
    export default function route(name: string, params?: RouteParamsWithQueryOverload | RouteParam, absolute?: boolean, config?: Config): string;
}

declare global {
    function route(): string;
    function route(name: string, params?: any, absolute?: boolean): string;

    interface InertiaPageProps {
        auth: {
            user: {
                id: number;
                name: string;
                email: string;
                current_workspace_id?: number;
                [key: string]: any;
            };
        };
        flash?: any;
        errors?: any;

        [key: string]: any;
    }

    interface Window {
        route: typeof route;
    }
}

declare module 'vue' {
    interface ComponentCustomProperties {
        $route: typeof route;
        $page: Page<InertiaPageProps>;
    }
}

declare module '@inertiajs/core' {
    interface PageProps {
        auth: {
            user: User;
            workspace: Workspace | null;
        };
        flash: any;
        errors?: any;
    }
}

declare module '@inertiajs/vue3' {
    export declare function usePage<T>(): Page<T>;
}

export {};
