export interface ConfigMap {
    name: string;
    namespace: string;
    uid: string;
    resource_version: string;
    created_at: string;
    labels: Record<string, string>;
    annotations: Record<string, string>;
    type: string;
    data: Record<string, string>;
    binary_data: any[];
    data_keys: string[];
    binary_data_keys: string[];
    data_count: number;
}

export interface CreateConfigMapData {
    name: string;
    data: Record<string, string>;
    labels?: Record<string, string>;
}

// ConfigMap 环境变量引用
export interface EnvFromConfigMap {
    configmap_name: string;
    key?: string; // 如果为空，引用整个 ConfigMap
    env_name?: string; // 环境变量名，默认使用 key
}

// ConfigMap 文件挂载项
export interface ConfigMapMountItem {
    key: string;
    path: string;
}

export interface ConfigMapMount {
    configmap_name: string;
    mount_path: string;
    items?: ConfigMapMountItem[]; // 如果为空，挂载整个 ConfigMap
    default_mode?: string; // 文件权限（八进制字符串）
}
