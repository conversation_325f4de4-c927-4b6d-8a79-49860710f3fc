export interface WindowPosition {
    x: number;
    y: number;
}

export interface WindowSize {
    width: number;
    height: number;
}

export interface BaseWindow {
    id: string;
    type: WindowType;
    title: string;
    position: WindowPosition;
    size: WindowSize;
    zIndex: number;
    isMinimized: boolean;
    isMaximized: boolean;
    isVisible: boolean;
    isDraggable: boolean;
    isResizable: boolean;
    minSize?: WindowSize;
    maxSize?: WindowSize;
    icon?: string;
    data?: Record<string, any>;
}

export type WindowType = 'terminal' | 'ai-assistant' | 'file-manager' | 'editor' | 'browser' | 'custom';

export interface TerminalWindow extends BaseWindow {
    type: 'terminal';
    data: {
        podName: string;
        containerName: string;
        mode: 'shell' | 'attach';
        isConnected: boolean;
    };
}

export interface AIAssistantWindow extends BaseWindow {
    type: 'ai-assistant';
    data: {
        conversationId?: string;
        isLoading: boolean;
    };
}

export type WindowInstance = TerminalWindow | AIAssistantWindow | BaseWindow;

export interface WindowManagerState {
    windows: WindowInstance[];
    activeWindowId: string | null;
    nextZIndex: number;
    taskbarVisible: boolean;
}

export interface WindowManagerOptions {
    defaultPosition?: WindowPosition;
    defaultSize?: WindowSize;
    stackOffset?: number;
}
