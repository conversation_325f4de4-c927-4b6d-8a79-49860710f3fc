export type PaymentMethod = {
    identifier: string;
    name: string;
    description: string;
    enabled: boolean;
    supports_refund: boolean;
    requires_callback: boolean;
};

export type TopUpRecord = {
    id: number;
    transaction_number: string;
    amount: string;
    remaining_amount: string;
    status: 'pending' | 'completed' | 'failed' | 'refunded' | 'partial_refunded';
    payment_method: string;
    remark: string | null;
    completed_at: string | null;
    created_at: string;
    updated_at: string;
};

export type BillingRecord = {
    id: number;
    workspace_id: number;
    cluster_id: number;
    user_id: number;
    billing_start_at: string;
    billing_end_at: string;
    resource_usage: {
        memory_mi?: number;
        cpu_m?: number;
        storage_gi?: number;
        loadbalancer_count?: number;
    };
    memory_cost: string;
    cpu_cost: string;
    storage_cost: string;
    loadbalancer_cost: string;
    total_cost: string;
    status: 'pending' | 'charged' | 'failed';
    charged_at?: string;
    created_at: string;
    updated_at: string;
    workspace_name?: string;
};
