import type { ComputedRef, Ref } from 'vue';

export interface YamlDocument {
    kind: string;
    name: string;
    [key: string]: any;
}

export interface YamlValidationResult {
    isValid: boolean;
    error?: string;
    documents?: YamlDocument[];
}

export interface ApplyResult {
    index: number;
    api: string;
    method: string;
    status: 'success' | 'error';
    data?: any;
    error?: string;
}

export interface CurrentApplyingItem {
    api: string;
    method: string;
}

// 资源类型映射
export interface ResourceTypeMapping {
    kind: string;
    apiEndpoint: string;
    resourceStoreKey: string;
    deleteApiEndpoint?: string; // 可选的自定义删除路由
}

// 变量解析相关接口
export interface VariableContext {
    [key: string]: any;
}

export interface VariableResolver {
    resolve(template: string, context?: VariableContext): string;
    resolveObject(obj: any, context?: VariableContext): any;
    getAvailableVariables(): string[];
}

export interface UseYamlApplyReturn {
    yamlContent: Ref<string>;
    isSubmitting: Ref<boolean>;
    hasWorkspace: ComputedRef<boolean>;
    currentProgress: Ref<number>;
    totalProgress: Ref<number>;
    progressPercentage: ComputedRef<number>;
    currentApplyingItem: Ref<CurrentApplyingItem | null>;
    defaultYaml: string;
    initializeDefaultYaml: () => void;
    validateYaml: (content: string) => YamlValidationResult;
    submitYaml: (onSuccess?: () => void) => Promise<void>;
    applyYaml: (onSuccess?: () => void) => Promise<void>;
    deleteYaml: (onSuccess?: () => void) => Promise<void>;
    variableResolver: VariableResolver;
}
