export interface HPAMetric {
    type: 'Resource' | 'Pods' | 'Object' | 'External';
    resource_name?: string;
    target_type?: 'Utilization' | 'AverageValue' | 'Value';
    target_value?: string | number;
    resource?: {
        name: string;
        target: {
            type: 'Utilization' | 'AverageValue';
            average_utilization?: number;
            average_value?: string | number;
        };
    };
    metric_name?: string;
    selector?: Record<string, any>;
    object_api_version?: string;
    object_kind?: string;
    object_name?: string;
}

export interface HPAScaleTargetRef {
    apiVersion: string;
    kind: string;
    name: string;
}

export interface HPACondition {
    type: string;
    status: string;
    reason?: string;
    message?: string;
    lastTransitionTime?: string;
}

export interface HPACurrentMetric {
    type: string;
    resource?: {
        name: string;
        current: {
            averageUtilization?: number;
            averageValue?: string;
        };
    };
}

export interface HorizontalPodAutoscaler {
    name: string;
    namespace: string;
    created_at: string;
    resource_version: string;
    scale_target_ref: HPAScaleTargetRef;
    min_replicas: number;
    max_replicas: number;
    metrics: HPAMetric[];
    behavior?: any;
    current_replicas?: number;
    desired_replicas?: number;
    current_metrics: HPACurrentMetric[];
    last_scale_time?: string;
    conditions: HPACondition[];
    status: string;
    current_cpu_utilization?: number;
    current_memory_utilization?: number;
    target_cpu_utilization?: number;
    target_memory_utilization?: number;
}

export interface CreateHPAData {
    name: string;
    scale_target_ref: {
        kind: string;
        name: string;
        api_version: string;
    };
    target_type: string;
    target_name: string;
    min_replicas: number;
    max_replicas: number;
    metrics: HPAMetric[];
    behavior?: {
        scale_up: {
            stabilization_window_seconds: number;
        };
        scale_down: {
            stabilization_window_seconds: number;
        };
    };
    labels?: Record<string, string>;
    [key: string]: any; // 支持 Inertia.js 表单的索引签名
}

export interface ScalableWorkload {
    kind: string;
    name: string;
    api_version: string;
    current_replicas?: number;
    ready_replicas?: number;
    status?: string;
}
