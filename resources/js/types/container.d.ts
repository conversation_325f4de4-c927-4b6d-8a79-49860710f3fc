import type { ConfigMapMount, EnvFromConfigMap } from './configmap';
import type { EnvFromSecret, SecretMount } from './secret';
import type { VolumeMount } from './storage';

export interface ContainerPort {
    name?: string;
    container_port: number;
    protocol?: string;
}

export interface EnvVar {
    name: string;
    value: string;
}

export interface Resources {
    memory: number | string;
    cpu: number | string;
}

export interface HttpHeader {
    name: string;
    value: string;
}

export interface HealthProbe {
    type: 'http' | 'tcp' | 'exec';
    initial_delay_seconds?: number;
    period_seconds?: number;
    timeout_seconds?: number;
    success_threshold?: number;
    failure_threshold?: number;
    // HTTP 探针配置
    http_path?: string;
    http_port?: number;
    http_scheme?: 'HTTP' | 'HTTPS';
    http_headers?: HttpHeader[];
    // TCP 探针配置
    tcp_port?: number;
    // Exec 探针配置
    exec_command?: string[];
}

export interface Container {
    name: string;
    image: string;
    working_dir?: string; // 工作目录
    command?: string[]; // Container command
    args?: string[]; // Container args
    ports?: ContainerPort[];
    env?: EnvVar[];
    env_from_configmap?: EnvFromConfigMap[]; // 从 ConfigMap 引用的环境变量
    env_from_secret?: EnvFromSecret[]; // 从 Secret 引用的环境变量
    volume_mounts?: VolumeMount[];
    configmap_mounts?: ConfigMapMount[]; // ConfigMap 文件挂载
    secret_mounts?: SecretMount[]; // Secret 文件挂载
    resources?: Resources;
    // 健康检查探针
    liveness_probe?: HealthProbe;
    readiness_probe?: HealthProbe;
    startup_probe?: HealthProbe;
    image_pull_policy?: 'Always' | 'Never' | 'IfNotPresent';
    stdin?: boolean;
    tty?: boolean;
}
