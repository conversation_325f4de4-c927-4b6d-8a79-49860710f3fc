export interface IpPool {
    name: string;
    range: string;
    description?: string;
    allocation_strategy: 'sequential' | 'random' | 'least_used';
}

export interface IpPoolInfo {
    id: number;
    name: string;
    description?: string;
    ip_version: string;
    ip_version_label: string;
    sharing_strategy: string;
    sharing_strategy_label: string;
    driver: string;
    stats: {
        total_ips: number;
        used_ips: number;
        available_ips: number;
        utilization_rate: number;
    };
    is_available: boolean;
    utilization_rate: number;
}
