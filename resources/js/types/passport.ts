export interface AccessToken {
    id: string;
    name: string;
    scopes: string[];
    revoked: boolean;
    created_at: string;
    last_used_at?: string;
    expires_at?: string;
    client: {
        id: string;
        name: string;
    };
}

export interface PassportClient {
    id: string;
    name: string;
    redirect: string;
    revoked: boolean;
    trusted: boolean;
    secret?: string;
    icon_url?: string;
    is_pkce_client: boolean;
    supports_device_flow: boolean;
    created_at: string;
    updated_at: string;
}
