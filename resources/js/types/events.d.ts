export interface K8sEvent {
    name?: string;
    type: string;
    namespace?: string;
    kind?: string;
    reason: string;
    message: string;
    timestamp: string;
    first_timestamp?: string | null;
    last_timestamp?: string | null;
    count: number;
    uid?: string;
    resource_version?: string;
    age?: string;
    is_recent?: boolean;
    source?: string;
    involved_object?: {
        kind: string;
        name: string;
        uid: string;
    };
}

export interface ContainerMetrics {
    name: string;
    usage: {
        cpu: string;
        memory: string;
    };
}

export interface NamespaceMetrics {
    [podName: string]: import('./pod').PodMetrics;
}

export interface SimplePodMetrics {
    cpu: string;
    memory: string;
}

export interface PodWarningStatus {
    has_warnings: boolean;
    warning_events: K8sEvent[];
    warning_count: number;
}

export interface EnrichedPod {
    name: string;
    namespace: string;
    status: string;
    phase: string;
    pod_ip: string | null;
    containers: any[];
    conditions: any[];
    start_time: string | null;
    restart_count: number;
    created_at: string | null;
    events: K8sEvent[];
    metrics: SimplePodMetrics;
    warningStatus: PodWarningStatus;
    age: string;
}

export interface PodMetricsData {
    name: string;
    namespace: string;
    uid: string | null;
    resource_version: string | null;
    created_at: string;
    labels: Record<string, string>;
    annotations: Record<string, string>;
    type: string;
    timestamp: string;
    window: string;
    containers: ContainerMetrics[];
}
