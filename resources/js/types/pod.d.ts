import type { ContainerPort, EnvVar, Resources } from './container';
import type { VolumeMount } from './storage';

export interface PodContainer {
    name: string;
    image: string;
    ports: ContainerPort[];
    env: EnvVar[];
    resources: Resources;
    volumeMounts: VolumeMount[];
    ready: boolean;
    restartCount: number;
    state: string;
    lastState?: any;
}

export interface PodCondition {
    type: string;
    status: string;
    reason?: string;
    message?: string;
    lastTransitionTime?: string;
    lastProbeTime?: string | null;
}

export interface PodEvent {
    type: string;
    reason: string;
    message: string;
    first_timestamp?: string;
    last_timestamp?: string;
    count: number;
    source: string;
    involved_object?: {
        kind: string;
        name: string;
        uid: string;
    };
}

export interface PodMetrics {
    timestamp?: string;
    window?: string;
    containers: {
        name: string;
        usage: {
            cpu: string;
            memory: string;
        };
    }[];
}

export interface Pod {
    name: string;
    namespace: string;
    uid: string;
    resource_version: string;
    created_at: string;
    labels: Record<string, string>;
    annotations: Record<string, string>;
    type: string;
    status: string;
    phase: string;
    pod_ip?: string;
    host_ip?: string;
    node_name?: string;
    qos_class?: string;
    containers: PodContainer[];
    init_containers?: PodContainer[];
    conditions: PodCondition[];
    start_time?: string;
    deletion_timestamp?: string;
    restart_count: number;
    events?: PodEvent[];
    metrics?: PodMetrics;
}
