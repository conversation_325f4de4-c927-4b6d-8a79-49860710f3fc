import type { Container } from './container';
import type { VolumeClaimTemplate } from './storage';

export interface Deployment {
    name: string;
    namespace: string;
    uid: string;
    resource_version: string;
    created_at: string;
    labels: Record<string, string>;
    annotations: Record<string, string>;
    type: string;
    containers: Container[];
    ports: Array<{
        container_name: string;
        port_name?: string;
        port: number;
        protocol: string;
    }>;
    replicas: number;
    ready_replicas?: number;
    available_replicas?: number;
    unavailable_replicas?: number;
    updated_replicas?: number;
    selector?: Record<string, string>;
    strategy: string;
    status: string;
    conditions: Array<{
        type: string;
        status: string;
        lastUpdateTime?: string;
        lastTransitionTime?: string;
        reason?: string;
        message?: string;
    }>;
    volumes?: any[];
    image_pull_secrets?: string[];
    restart_policy?: string;
}

export interface CreateDeploymentData {
    name: string;
    replicas?: number;
    containers: Container[];
    volumes?: any[];
    image_pull_secrets?: string[];
    labels?: Record<string, string>;
}

export interface StatefulSet {
    name: string;
    namespace: string;
    uid: string;
    resource_version: string;
    created_at: string;
    labels: Record<string, string>;
    annotations: Record<string, string>;
    type: string;
    replicas: number;
    ready_replicas?: number;
    selector: Record<string, string>;
    service_name: string;
    update_strategy: string;
    status: string;
    containers: Container[];
    volumes?: any[];
    image_pull_secrets?: string[];
    volume_claim_templates?: VolumeClaimTemplate[];
}

export interface CreateStatefulSetData {
    name: string;
    replicas?: number;
    service_name?: string;
    containers: Container[];
    volumes?: any[];
    image_pull_secrets?: string[];
    volume_claim_templates?: VolumeClaimTemplate[];
    labels?: Record<string, string>;
}
