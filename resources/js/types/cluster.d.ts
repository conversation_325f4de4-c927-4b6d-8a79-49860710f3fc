export interface Cluster {
    id: number;
    name: string;
    server_url: string;
    billing_enabled: boolean;
    created_at: string;
    updated_at: string;
    pricing?: ClusterPricing;
}

export interface ClusterPricing {
    memory_price_per_gb: string;
    cpu_price_per_core: string;
    storage_price_per_gb: string;
    description?: string;
}

export interface ResourcePricing {
    id: number;
    resource_id: number;
    resource_type: string;
    unit_type: string;
    price_per_unit_per_hour: string;
    effective_date: string;
    description?: string;
    created_at: string;
    updated_at: string;
}

export interface PriceCalculationRequest {
    memory_mi: number;
    cpu_m: number;
    storage_gi: number;
    loadbalancer_count?: number;
}

export interface PriceCalculationResponse {
    resources: PriceCalculationRequest;
    breakdown: {
        memory_price_per_minute: string;
        cpu_price_per_minute: string;
        storage_price_per_minute: string;
        loadbalancer_price_per_minute?: string;
    };
    total: {
        per_minute: string;
        per_hour: string;
        per_day: string;
        per_month: string;
    };
    formatted: {
        per_minute: string;
        per_hour: string;
        per_day: string;
        per_month: string;
    };
}
