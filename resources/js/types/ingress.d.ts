export interface IngressRule {
    host: string;
    http: {
        paths: IngressPath[];
    };
}

export interface IngressPath {
    path: string;
    pathType: string;
    backend: {
        service: {
            name: string;
            port: {
                number: number | undefined;
            };
        };
    };
}

export interface IngressTLS {
    hosts: string[];
    secretName?: string;
}

export interface Ingress {
    name: string;
    namespace: string;
    uid: string;
    resource_version: string;
    created_at: string;
    labels: Record<string, string>;
    annotations: Record<string, string>;
    type: string;
    className: string;
    rules: IngressRule[];
    tls?: IngressTLS[];
    loadBalancerIngress?: string;
    status: string;
    domains: string[];
    has_tls: boolean;
    tls_domains: string[];
    access_urls: string[];
}

export interface CreateIngressData {
    name: string;
    rules: IngressRule[];
    tls?: IngressTLS[];
    labels?: Record<string, string>;
}
