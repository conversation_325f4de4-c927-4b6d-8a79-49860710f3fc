export interface Secret {
    name: string;
    namespace: string;
    uid: string;
    resource_version: string;
    created_at: string;
    labels: Record<string, string>;
    annotations: Record<string, string>;
    type: 'SSH' | 'BasicAuth' | 'Opaque' | 'DockerConfig' | 'TLS' | string;
    data_keys: string[];
    data_count: number;
}

export interface CreateGenericSecretData {
    name: string;
    data: Record<string, string>;
    labels?: Record<string, string>;
}

export interface CreateDockerRegistrySecretData {
    name: string;
    server: string;
    username: string;
    password: string;
    email?: string;
    labels?: Record<string, string>;
}

export interface CreateTlsSecretData {
    name: string;
    cert: string;
    key: string;
    labels?: Record<string, string>;
}

export interface CreateBasicAuthSecretData {
    name: string;
    username: string;
    password: string;
    labels?: Record<string, string>;
}

export interface CreateSshAuthSecretData {
    name: string;
    ssh_private_key: string;
    labels?: Record<string, string>;
}

// Secret 环境变量引用
export interface EnvFromSecret {
    secret_name: string;
    key?: string; // 如果为空，引用整个 Secret
    env_name?: string; // 环境变量名，默认使用 key
}

// Secret 文件挂载项
export interface SecretMountItem {
    key: string;
    path: string;
}

export interface SecretMount {
    secret_name: string;
    mount_path: string;
    items?: SecretMountItem[]; // 如果为空，挂载整个 Secret
    default_mode?: string; // 文件权限（八进制字符串）
}
