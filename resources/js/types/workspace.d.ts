import type { Cluster } from './cluster';

export interface Workspace {
    id: number;
    user_id: number;
    cluster_id: number;
    name: string;
    status: 'pending' | 'creating' | 'deleting' | 'active' | 'suspended' | 'failed';
    namespace: string;
    suspended_at?: string;
    suspension_reason?: string;
    overdue_amount?: string;
    last_overdue_at?: string;
    created_at: string;
    updated_at: string;
    cluster: Cluster;
}

export interface CreateWorkspaceData {
    name: string;
    cluster_id: number;
}
