export interface ServicePort {
    name?: string;
    port: number;
    target_port?: number;
    node_port?: number;
    protocol: string;
}

export interface Service {
    name: string;
    namespace: string;
    uid: string;
    resource_version: string;
    type: string;
    service_type: string;
    cluster_ip?: string;
    external_ips?: string[] | null;
    load_balancer_ip?: string;
    load_balancer_class?: string | null;
    ports: Array<ServicePort>;
    session_affinity: string;
    external_traffic_policy: string;
    status: string;
    external_addresses?: string[];
    has_external_access: boolean;
    workload_type: string | null;
    workload_name: string | null;
    created_at: string;
    updated_at?: string;
}

export interface CreateServiceData {
    name: string;
    type: string;
    target_workload_type: string;
    target_workload_name: string;
    ports: {
        name?: string;
        port: number;
        target_port?: number;
        protocol: string;
    }[];
    session_affinity?: string;
    external_traffic_policy?: string;
    ip_pool_id?: string;
    labels?: Record<string, string>;
}

export interface WorkloadOption {
    type: 'Deployment' | 'StatefulSet';
    name: string;
    ports: {
        container_name: string;
        port_name?: string;
        port: number;
        protocol: string;
    }[];
    selector: Record<string, string>;
}
