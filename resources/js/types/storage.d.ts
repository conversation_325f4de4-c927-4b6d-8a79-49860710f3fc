export interface Storage {
    name: string;
    namespace: string;
    uid: string;
    resource_version: string;
    created_at: string;
    labels: Record<string, string>;
    annotations: Record<string, string>;
    type: string;
    size: string;
    status: string;
    capacity: string;
    storage_class?: string;
    access_modes: string[];
    volume_name?: string;
    conditions: any[];
    is_bound: boolean;
    is_read_write: boolean;
    is_read_write_many: boolean;
    formatted_size: string;
}

export interface CreateStorageData {
    name: string;
    size: number;
    labels?: Record<string, string>;
}

export interface VolumeMount {
    mount_path: string;
    name?: string;
    storage_name?: string;
    sub_path?: string;
    read_only?: boolean;
}

export interface VolumeClaimTemplate {
    name: string;
    size: number;
}
