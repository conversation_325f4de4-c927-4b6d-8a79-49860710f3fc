export default {
    welcome: {
        title: 'One-Click Deploy, Elastic Scaling, Auto-Scaling',
        nav: {
            login: 'Login',
            register: 'Get Started',
        },
        hero: {
            title: 'One-Click Deploy',
            subtitle:
                'Say goodbye to complex traditional cloud server configurations. No need to purchase and maintain servers. Containerized deployment with auto-scaling lets your applications adapt to demand, truly achieving pay-as-you-use.',
            feature1: 'Container startup 10x faster than VMs',
            feature2: 'Auto-scaling saves up to 60% costs',
            feature3: 'Zero-downtime smooth upgrades',
            buttons: {
                start: 'Get Started',
                calculator: 'Price Calculator',
                learn: 'Learn More',
            },
        },
        comparison: {
            title: 'Say Goodbye to Traditional Cloud Servers, Embrace Containerized Future',
            subtitle: 'Unlike traditional VPS/ECS/CVM, we provide true pay-as-you-use containerized platform',
            traditional: {
                title: 'Traditional Cloud Servers',
                feature1: 'Need to purchase fixed-spec servers',
                feature2: 'Manual environment setup and deployment',
                feature3: 'Low resource utilization, cost waste',
                feature4: 'Requires professional DevOps staff',
            },
            modern: {
                title: 'Modern Containerized Platform',
                feature1: 'Pay-as-you-use, elastic scaling',
                feature2: 'One-click deployment, automated operations',
                feature3: 'High resource utilization, cost optimized',
                feature4: 'No professional DevOps knowledge required',
            },
        },
        features: {
            autoScale: {
                title: 'Auto Scaling',
                description: 'Automatically adjust application replicas based on traffic, scale up during peaks, scale down during lows',
            },
            selfHealing: {
                title: 'Self-Healing',
                description: 'Automatically restart crashed applications to ensure continuous service availability',
            },
            rollingUpdate: {
                title: 'Rolling Updates',
                description: 'Zero-downtime smooth upgrades, new version starts before old version shuts down',
            },
            loadBalancer: {
                title: 'Auto Load Balancing',
                description: 'Multi-replica automatic load balancing, no need to purchase additional load balancers',
            },
            clusterStorage: {
                title: 'Cluster Storage',
                description: 'Shared storage between applications, mount anytime, simple and easy to use',
            },
            manifestDeploy: {
                title: 'Manifest Deployment',
                description: 'One-click deployment of complex applications through manifest files',
            },
            highAvailability: {
                title: 'High Availability Deployment',
                description: 'Multi-node deployment ensures application high availability',
            },
            elasticBilling: {
                title: 'Elastic Billing',
                description: 'Charged based on actual usage, billed according to scaled usage during expansion',
            },
        },
        aiAssistant: {
            badge: 'Revolutionary AI Assistant',
            title: 'Let AI Become Your',
            titleHighlight: 'DevOps Expert',
            subtitle:
                'Say goodbye to complex operation interfaces. Simply describe your needs in natural language, and the AI assistant will automatically complete all operations for you',
            progress: 'Execution Progress',
            features: {
                naturalLanguage: {
                    title: 'Natural Language Interaction',
                    description: 'No need to learn complex commands, describe your needs in everyday language:',
                    example: '"Help me deploy a WordPress website"',
                },
                webOperation: {
                    title: 'Smart Web Operations',
                    description:
                        'AI can automatically click buttons, fill forms, and select configuration options, just like an experienced DevOps engineer operating for you',
                },
                contextUnderstanding: {
                    title: 'Context Understanding',
                    description:
                        'AI can understand the current page state and adjust operation strategies based on actual conditions, ensuring every step is precise',
                },
                smartSuggestions: {
                    title: 'Smart Suggestions',
                    description:
                        'When encountering problems, AI will proactively provide best practice recommendations to help you make wise technical decisions',
                },
            },
            demo: {
                title: 'AI Assistant Demo',
                userMessage: 'I want to deploy a MySQL database',
                aiResponse: "Great! I'll help you deploy a MySQL database. I will:",
                steps: [
                    'Navigate to StatefulSet page',
                    'Fill in database configuration',
                    'Set up storage volume mounting',
                    'Configure network access',
                ],
                processing: 'AI is automatically operating the page...',
                completed: ['Navigated to StatefulSet page', 'Filled in MySQL configuration'],
                current: 'Configuring storage mounting...',
            },
            useCases: {
                title: 'What Can AI Assistant Do for You?',
                subtitle: 'Here are some common use cases',
                quickWebsite: {
                    title: 'Quick Website Setup',
                    description: '"Help me build a WordPress website, including database and domain configuration"',
                },
                databaseDeploy: {
                    title: 'Database Deployment',
                    description: '"I need a Redis cache server with 2GB memory configuration"',
                },
                configOptimization: {
                    title: 'Configuration Optimization',
                    description: '"Help me check application performance and see if scaling is needed"',
                },
                troubleshooting: {
                    title: 'Troubleshooting',
                    description: '"My application is inaccessible, help me check what\'s wrong"',
                },
                securityConfig: {
                    title: 'Security Configuration',
                    description: '"Help me configure HTTPS certificates and security policies"',
                },
                microservices: {
                    title: 'Microservices Architecture',
                    description: '"Help me deploy a microservices architecture, including API gateway"',
                },
            },
            cta: {
                experience: 'Experience AI Assistant Now',
                learnMore: 'Learn More Features',
            },
        },
        applications: {
            title: 'Rich Application Ecosystem, Ready to Use',
            subtitle: 'Supports Docker containerized applications, covering databases, web services, microservices and various scenarios',
            tag1: 'Manifest file deployment support',
            tag2: 'Enterprise-grade security',
            tag3: 'Multi-environment support',
        },
        pricing: {
            title: 'Smart Price Comparison',
            subtitle: 'Calculate your resource costs in real-time and compare pricing differences across major platforms',
            tabs: {
                docker: 'Docker PaaS Comparison',
                cloud: 'Traditional Cloud Comparison',
            },
            docker: {
                competitor: {
                    title: 'Heroku Cloud Platform',
                    subtitle: 'Fixed dynos, monthly billing',
                    standardDyno: 'Standard-1X Dyno',
                    postgres: 'Standard-0 PostgreSQL',
                    redis: 'Premium-0 Redis',
                    scheduler: 'Scheduler Add-on',
                    monitoring: 'Metrics Monitoring',
                    total: 'Total',
                    note: '* Fixed dyno sizes, must upgrade entire plan when exceeding limits',
                },
                our: {
                    title: 'Smart Container Platform',
                    subtitle: 'Elastic scaling, pay-as-you-use',
                    deployment: 'Application Deployment',
                    database: 'Database Service',
                    cache: 'Cache Service',
                    cronjobs: 'Cron Jobs',
                    monitoring: 'Monitoring Service',
                    total: 'Total',
                    payAsUse: 'Pay-as-you-use',
                    free: 'Free',
                    note: 'Save 79% costs, K8s native support, true elastic scaling',
                },
            },
            cloud: {
                traditional: {
                    title: 'Traditional Cloud Servers',
                    subtitle: 'Fixed specs, monthly billing',
                    server: '2-core 4GB cloud server',
                    loadBalancer: 'Load balancer',
                    storage: 'Cloud storage 100GB',
                    bandwidth: 'Bandwidth & Traffic',
                    total: 'Total',
                    note: '* Even with only 30% resource utilization, still need to pay full price',
                },
                modern: {
                    title: 'Modern Containerized Platform',
                    subtitle: 'Elastic scaling, pay-as-you-use',
                    resources: 'Basic resource usage',
                    loadBalancer: 'Auto load balancing',
                    storage: 'Cluster storage',
                    bandwidth: 'Bandwidth costs',
                    total: 'Total',
                    payAsUse: 'Pay-as-you-use',
                    free: 'Free',
                    zero: 'HK$0',
                    note: 'Save costs, auto-scale during high traffic, billed based on actual usage',
                },
            },
        },
        calculator: {
            title: 'Price Calculator',
            subtitle: 'Calculate deployment costs in real-time based on your resource requirements',
            note: 'Transparent billing, accurate to the minute',
            selectCluster: 'Select Cluster',
            resourceConfig: 'Resource Configuration',
            memory: 'Memory (MB)',
            memoryNote: 'Minimum 512MB, maximum 1TB, must be multiples of 512',
            cpu: 'CPU (m)',
            cpuNote: 'Minimum 500m, maximum 128 cores, must be multiples of 500 (1000m = 1 core)',
            storage: 'Storage (GB)',
            storageNote: 'Optional, maximum 10TB',
            loadBalancer: 'Load Balancer Count',
            loadBalancerNote: 'Optional, maximum 10',
            quickConfig: 'Quick Configuration',
            lightApp: 'Light Application',
            standardApp: 'Standard Application',
            performanceApp: 'High Performance Application',
            pricePreview: 'Price Preview',
            configFirst: 'Configure resources to view price preview',
            register: 'Register',
            registerNote: 'Register to start deploying applications',
        },
        cta: {
            title: 'Say Goodbye to Server Operations, Focus on Business Development',
            subtitle: 'Use {name} to make application deployment simple and efficient',
            feature1: 'Free trial',
            feature2: 'Rapid deployment',
            feature3: 'Pay-as-you-use',
            feature4: 'Professional support',
            startFree: 'Start Free Now',
            login: 'Already have an account? Login',
            note: 'No payment method required, register to start using',
        },
        footer: {
            copyright: '© {year} {name}.',
            privacy: 'Privacy Policy',
            terms: 'Terms of Service',
            contact: 'Contact Us',
        },
    },
    dashboard: {
        charts: {
            memory: 'Memory',
            cpu: 'CPU',
            storage: 'Storage',
        },
    },
    common: {
        loading: 'Calculating...',
    },
};
