export default {
    welcome: {
        title: '一键部署，弹性扩容，自动伸缩',
        nav: {
            login: '登录',
            register: '开始使用',
        },
        hero: {
            title: '一键部署',
            subtitle: '告别传统云服务器繁琐配置，无需购买和维护服务器。容器化部署，自动扩缩容，让您的应用随需而变，真正实现按需付费。',
            feature1: '容器启动速度比虚拟机快 10 倍',
            feature2: '自动扩缩容，成本节省高达 60%',
            feature3: '0 宕机时间平滑升级',
            buttons: {
                start: '立即开始',
                calculator: '价格计算器',
                learn: '了解更多',
            },
        },
        comparison: {
            title: '告别传统云服务器，拥抱容器化未来',
            subtitle: '与传统 VPS/ECS/CVM 不同，我们提供真正的按需付费容器化平台',
            traditional: {
                title: '传统云服务器',
                feature1: '需要购买固定规格服务器',
                feature2: '手动配置环境和部署',
                feature3: '资源利用率低，成本浪费',
                feature4: '需要专业运维人员',
            },
            modern: {
                title: '现代容器化平台',
                feature1: '按需付费，弹性扩缩容',
                feature2: '一键部署，自动化运维',
                feature3: '资源利用率高，成本优化',
                feature4: '无需专业运维知识',
            },
        },
        features: {
            autoScale: {
                title: '自动扩缩容',
                description: '根据访问量自动调节应用副本数量，高峰扩容，低谷缩容',
            },
            selfHealing: {
                title: '应用自愈',
                description: '应用崩溃自动重启，确保服务持续可用',
            },
            rollingUpdate: {
                title: '滚动更新',
                description: '0 宕机时间平滑升级，新版本启动后再关闭旧版本',
            },
            loadBalancer: {
                title: '自动负载均衡',
                description: '多副本自动负载均衡，无需额外购买负载均衡器',
            },
            clusterStorage: {
                title: '集群存储',
                description: '应用间共享存储，随时挂载，简单易用',
            },
            manifestDeploy: {
                title: '清单部署',
                description: '通过清单文件一键部署复杂应用',
            },
            highAvailability: {
                title: '高可用部署',
                description: '多节点部署，确保应用高可用性',
            },
            elasticBilling: {
                title: '弹性计费',
                description: '按实际使用量计费，扩容时按扩容后的用量收费',
            },
        },
        aiAssistant: {
            badge: '革命性 AI 助手',
            title: '让 AI 成为您的',
            titleHighlight: '运维专家',
            subtitle: '告别复杂的操作界面，只需用自然语言描述需求，AI 助手将自动为您完成所有操作',
            progress: '执行进度',
            features: {
                naturalLanguage: {
                    title: '自然语言交互',
                    description: '无需学习复杂命令，用日常语言描述需求：',
                    example: '"帮我部署一个 WordPress 网站"',
                },
                webOperation: {
                    title: '智能网页操作',
                    description: 'AI 可以自动点击按钮、填写表单、选择配置项，就像一个经验丰富的运维工程师在为您操作',
                },
                contextUnderstanding: {
                    title: '上下文理解',
                    description: 'AI 能理解当前页面状态，根据实际情况调整操作策略，确保每一步都精准无误',
                },
                smartSuggestions: {
                    title: '智能建议',
                    description: '遇到问题时，AI 会主动提供最佳实践建议，帮助您做出明智的技术决策',
                },
            },
            demo: {
                title: 'AI 助手演示',
                userMessage: '我想部署一个 MySQL 数据库',
                aiResponse: '好的！我来帮您部署 MySQL 数据库。我会：',
                steps: ['导航到有状态集页面', '填写数据库配置信息', '设置存储卷挂载', '配置网络访问'],
                processing: 'AI 正在自动操作页面...',
                completed: ['已导航到有状态集页面', '已填写 MySQL 配置'],
                current: '正在配置存储挂载...',
            },
            useCases: {
                title: 'AI 助手能为您做什么？',
                subtitle: '以下是一些常见的使用场景',
                quickWebsite: {
                    title: '快速建站',
                    description: '"帮我搭建一个 WordPress 网站，包括数据库和域名配置"',
                },
                databaseDeploy: {
                    title: '数据库部署',
                    description: '"我需要一个 Redis 缓存服务器，内存配置 2GB"',
                },
                configOptimization: {
                    title: '配置优化',
                    description: '"帮我检查应用性能，看看是否需要扩容"',
                },
                troubleshooting: {
                    title: '故障排查',
                    description: '"我的应用无法访问，帮我检查一下是什么问题"',
                },
                securityConfig: {
                    title: '安全配置',
                    description: '"帮我配置 HTTPS 证书和安全策略"',
                },
                microservices: {
                    title: '微服务架构',
                    description: '"帮我部署一套微服务架构，包括API网关"',
                },
            },
            cta: {
                experience: '立即体验 AI 助手',
                learnMore: '了解更多功能',
            },
        },
        applications: {
            title: '丰富的应用生态，开箱即用',
            subtitle: '支持 Docker 容器化应用，涵盖数据库、Web 服务、微服务等各类场景',
            tag1: '支持清单文件部署',
            tag2: '企业级安全',
            tag3: '多环境支持',
        },
        pricing: {
            title: '智能价格对比器',
            subtitle: '实时计算您的资源成本，直观对比各大平台的价格差异',
            tabs: {
                docker: 'Docker PaaS 对比',
                cloud: '传统云计算对比',
            },
            docker: {
                competitor: {
                    title: 'Heroku 云平台',
                    subtitle: '固定实例，按月付费',
                    standardDyno: 'Standard-1X 实例',
                    postgres: 'Standard-0 PostgreSQL',
                    redis: 'Premium-0 Redis',
                    scheduler: 'Scheduler 定时任务',
                    monitoring: 'Metrics 监控服务',
                    total: '总计',
                    note: '* 固定实例规格，超出限制需要升级整个套餐',
                },
                our: {
                    title: '智能容器平台',
                    subtitle: '弹性扩缩，按需付费',
                    deployment: '应用部署实例',
                    database: '数据库服务',
                    cache: '缓存服务',
                    cronjobs: '定时任务',
                    monitoring: '监控服务',
                    total: '总计',
                    payAsUse: '按量收费',
                    free: '免费',
                    note: '节省 79% 成本，K8s 原生支持，真正的弹性扩缩容',
                },
            },
            cloud: {
                traditional: {
                    title: '传统云服务器',
                    subtitle: '固定规格，按月付费',
                    server: '2核4G云服务器',
                    loadBalancer: '负载均衡器',
                    storage: '云存储 100GB',
                    bandwidth: '带宽流量费',
                    total: '总计',
                    note: '* 即使资源利用率只有 30%，仍需支付全额费用',
                },
                modern: {
                    title: '现代容器化平台',
                    subtitle: '弹性扩缩，按需付费',
                    resources: '基础资源使用',
                    loadBalancer: '自动负载均衡',
                    storage: '集群存储',
                    bandwidth: '带宽费用',
                    total: '总计',
                    payAsUse: '按量收费',
                    free: '免费',
                    zero: 'HK$0',
                    note: '节省成本，访问量大时自动扩容，按实际用量计费',
                },
            },
        },
        calculator: {
            title: '价格计算器',
            subtitle: '根据您的资源需求，实时计算部署成本',
            note: '透明计费，精确到分钟',
            selectCluster: '选择集群',
            resourceConfig: '资源配置',
            memory: '内存 (MB)',
            memoryNote: '最小 512MB，最大 1TB，必须为 512 的倍数',
            cpu: 'CPU (m)',
            cpuNote: '最小 500m，最大 128核，必须为 500 的倍数 (1000m = 1核)',
            storage: '存储 (GB)',
            storageNote: '可选，最大 10TB',
            loadBalancer: '负载均衡器数量',
            loadBalancerNote: '可选，最大 10 个',
            quickConfig: '快速配置',
            lightApp: '轻量应用',
            standardApp: '标准应用',
            performanceApp: '高性能应用',
            pricePreview: '费用预览',
            configFirst: '配置资源后查看费用预览',
            register: '注册',
            registerNote: '注册即可开始部署应用',
        },
        cta: {
            title: '告别服务器运维，专注业务发展',
            subtitle: '使用 {name}，让应用部署变得简单高效',
            feature1: '免费试用',
            feature2: '极速部署',
            feature3: '按需计费',
            feature4: '专业支持',
            startFree: '立即开始免费使用',
            login: '已有账号？登录',
            note: '无需绑定支付方式，注册即可开始使用',
        },
        footer: {
            copyright: '© {year} {name}.',
            privacy: '隐私政策',
            terms: '服务条款',
            contact: '联系我们',
        },
    },
    dashboard: {
        charts: {
            memory: '内存',
            cpu: 'CPU',
            storage: '存储',
        },
    },
    common: {
        loading: '计算中...',
    },
};
