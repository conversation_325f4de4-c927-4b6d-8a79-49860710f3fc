import { createI18n } from 'vue-i18n';
import en from './locales/en-US';
import zh from './locales/zh-CN';

// 从 localStorage 获取保存的语言设置，默认为中文
const getInitialLocale = () => {
    if (typeof window !== 'undefined') {
        const saved = localStorage.getItem('locale');
        if (saved && ['zh-CN', 'en-US'].includes(saved)) {
            return saved;
        }
    }
    return 'zh-CN';
};

export const i18n = createI18n({
    legacy: false,
    locale: getInitialLocale(),
    fallbackLocale: 'en-US',
    messages: {
        'zh-CN': zh,
        'en-US': en,
    },
});

export default i18n;
