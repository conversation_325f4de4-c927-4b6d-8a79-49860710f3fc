<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { useWorkspaceStore } from '@/stores/workspaceStore';
import type { Auth, Workspace } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { AlertCircle, Loader2, Plus, RefreshCw, RotateCcw, Server } from 'lucide-vue-next';
import { toast } from 'sonner';
import { ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    workspaces: Workspace[];
    auth: Auth;
}

const props = defineProps<Props>();

const retryingWorkspaces = ref(new Set<number>());

const getStatusVariant = (status: string) => {
    switch (status) {
        case 'active':
            return 'default';
        case 'pending':
        case 'creating':
            return 'secondary';
        case 'deleting':
            return 'outline';
        case 'suspended':
        case 'failed':
            return 'destructive';
        default:
            return 'secondary';
    }
};

const getStatusText = (status: string) => {
    switch (status) {
        case 'active':
            return '正常';
        case 'pending':
            return '待激活';
        case 'creating':
            return '创建中';
        case 'deleting':
            return '删除中';
        case 'suspended':
            return '已暂停';
        case 'failed':
            return '创建失败';
        default:
            return '未知';
    }
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};

const retryCreateNamespace = async (workspace: Workspace) => {
    if (retryingWorkspaces.value.has(workspace.id)) {
        return;
    }

    retryingWorkspaces.value.add(workspace.id);

    try {
        await axios.post(`/api/workspaces/${workspace.id}/retry-namespace`);

        toast.success('重试创建成功', {
            description: '已重新开始创建 Namespace，请稍后刷新查看状态',
        });

        // 3秒后刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    } catch (error: any) {
        const message = error.response?.data?.message || '重试失败';
        toast.error('重试失败', {
            description: message,
        });
    } finally {
        retryingWorkspaces.value.delete(workspace.id);
    }
};

// 通过 workspaceStore 刷新
const workspaceStore = useWorkspaceStore();
workspaceStore.fetchWorkspaces();
</script>

<template>
    <Head title="工作空间" />

    <AppLayout>
        <div class="space-y-6 p-4">
            <!-- 页面标题和操作 -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">工作空间</h1>
                    <p class="text-muted-foreground">管理您的工作空间</p>
                </div>
                <Button as-child>
                    <Link :href="route('workspaces.create')">
                        <Plus class="mr-2 h-4 w-4" />
                        创建工作空间
                    </Link>
                </Button>
            </div>

            <!-- 工作空间列表 -->
            <div v-if="workspaces.length > 0" class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <Card v-for="workspace in workspaces" :key="workspace.id" class="relative">
                    <CardHeader class="pb-3">
                        <div class="flex items-center justify-between">
                            <CardTitle class="text-lg">{{ workspace.name }}</CardTitle>
                            <Badge :variant="getStatusVariant(workspace.status)">
                                {{ getStatusText(workspace.status) }}
                            </Badge>
                        </div>
                        <CardDescription class="flex items-center text-sm">
                            <Server class="mr-1 h-3 w-3" />
                            {{ workspace.cluster.name }}
                        </CardDescription>
                    </CardHeader>
                    <CardContent class="space-y-3">
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-muted-foreground">集群:</span>
                                <code class="rounded bg-muted px-2 py-1 text-xs">{{ workspace.cluster.name }}</code>
                            </div>

                            <div class="flex justify-between">
                                <span class="text-muted-foreground">创建时间:</span>
                                <span class="text-xs">{{ formatDate(workspace.created_at) }}</span>
                            </div>
                        </div>

                        <!-- 暂停信息 -->
                        <div v-if="workspace.status === 'suspended'" class="rounded-md bg-red-50 p-3 dark:bg-red-900/20">
                            <div class="flex items-center text-sm text-red-800 dark:text-red-200">
                                <AlertCircle class="mr-2 h-4 w-4" />
                                <span class="font-medium">工作空间已暂停</span>
                            </div>
                            <p v-if="workspace.suspension_reason" class="mt-1 text-xs text-red-700 dark:text-red-300">
                                原因: {{ workspace.suspension_reason }}
                            </p>
                            <p v-if="workspace.overdue_amount" class="text-xs text-red-700 dark:text-red-300">
                                欠费金额: ¥{{ workspace.overdue_amount }}
                            </p>
                        </div>

                        <!-- 失败信息 -->
                        <div
                            v-if="workspace.status === 'failed'"
                            class="mt-3 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20"
                        >
                            <div class="text-sm">
                                <div class="mb-1 font-medium text-red-800 dark:text-red-200">创建失败</div>
                                <div class="text-red-700 dark:text-red-300">{{ workspace.suspension_reason || '创建 Namespace 失败' }}</div>
                            </div>
                        </div>
                    </CardContent>
                    <CardFooter class="pt-3">
                        <div class="flex w-full gap-2">
                            <Button
                                v-if="workspace.status === 'active'"
                                variant="outline"
                                size="sm"
                                class="flex-1"
                                as="a"
                                @click="router.visit(route('workspaces.show', workspace.id))"
                            >
                                查看详情
                            </Button>
                            <Button
                                v-if="workspace.status === 'failed'"
                                variant="outline"
                                size="sm"
                                class="flex-1"
                                @click="retryCreateNamespace(workspace)"
                                :disabled="retryingWorkspaces.has(workspace.id)"
                            >
                                <RefreshCw v-if="retryingWorkspaces.has(workspace.id)" class="mr-2 h-4 w-4 animate-spin" />
                                <RotateCcw v-else class="mr-2 h-4 w-4" />
                                {{ retryingWorkspaces.has(workspace.id) ? '重试中...' : '重试' }}
                            </Button>
                            <Button
                                v-if="workspace.status === 'pending' || workspace.status === 'creating'"
                                variant="outline"
                                size="sm"
                                class="flex-1"
                                disabled
                            >
                                <Loader2 class="mr-2 h-4 w-4 animate-spin" />
                                {{ workspace.status === 'creating' ? '创建中...' : '等待创建...' }}
                            </Button>
                            <Button v-if="workspace.status === 'deleting'" variant="outline" size="sm" class="flex-1" disabled>
                                <Loader2 class="mr-2 h-4 w-4 animate-spin" />
                                删除中...
                            </Button>
                        </div>
                    </CardFooter>
                </Card>
            </div>

            <!-- 空状态 -->
            <div v-else class="py-12 text-center">
                <div class="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-muted">
                    <Server class="h-12 w-12 text-muted-foreground" />
                </div>
                <h3 class="mb-2 text-lg font-semibold">还没有工作空间</h3>
                <p class="mb-6 text-muted-foreground">创建您的第一个工作空间来开始部署应用</p>
                <Button as-child>
                    <Link :href="route('workspaces.create')">
                        <Plus class="mr-2 h-4 w-4" />
                        创建工作空间
                    </Link>
                </Button>
            </div>
        </div>
    </AppLayout>
</template>
