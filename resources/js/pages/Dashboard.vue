<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useResourceListener } from '@/composables/useResourceListener';
import AppLayout from '@/layouts/AppLayout.vue';
import eventBus from '@/lib/eventBus';
import { formatK8sStatus } from '@/lib/formatK8sStatus';
import { openWeb } from '@/lib/window';
import { useResourcesStore } from '@/stores/resourcesStore';
import { type BreadcrumbItem, type Workspace } from '@/types';
import { Head, usePage } from '@inertiajs/vue3';
import axios from 'axios';
import { PieChart } from 'echarts/charts';
import { LegendComponent, TitleComponent, TooltipComponent } from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { Activity, AlertCircle, CheckCircle2, Clock, Container, Database, MessageCircle, Route, Sparkles, Terminal } from 'lucide-vue-next';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import VChart from 'vue-echarts';

// 注册 ECharts 组件
use([CanvasRenderer, PieChart, TitleComponent, TooltipComponent, LegendComponent]);

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: '总览',
        href: '/dashboard',
    },
];

const page = usePage();
const appName = computed(() => page.props.display_name);

// 使用资源存储
const resourcesStore = useResourcesStore();

// 使用资源监听器
const resourceListener = useResourceListener({
    autoFetchOnSwitch: true,
    enableDebugLog: true, // 启用调试日志
    cleanupOnUnmount: false, // 离开页面时不自动停止监听
});

// 数据状态（从 store 获取，确保响应式）
const loading = computed(() => !resourcesStore.isLoaded && resourceListener.isFetching.value);

// 从 store 获取资源数据（响应式）
const pods = computed(() => resourcesStore.collections.pods || []);
const deployments = computed(() => resourcesStore.collections.deployments || []);
const statefulSets = computed(() => resourcesStore.collections.statefulsets || []);
const services = computed(() => resourcesStore.collections.services || []);
const storages = computed(() => resourcesStore.collections.storages || []);

// Metrics 依旧通过 API 获取；Events 直接来自资源 Store
const events = computed(() => resourcesStore.collections.events || []);
const metrics = ref(null);
const loadingMetrics = ref(false);

// 统计数据
const podStats = computed(() => {
    const stats = { running: 0, pending: 0, failed: 0, warning: 0, total: pods.value.length };
    pods.value.forEach((pod: any) => {
        const status = pod.status.toLowerCase();
        if (status === 'running') stats.running++;
        else if (status === 'pending') stats.pending++;
        else if (status.includes('failed')) stats.failed++;

        // 检查是否有警告事件
        if (pod.events?.some((event: any) => event.type === 'Warning')) {
            stats.warning++;
        }
    });
    return stats;
});

const deploymentStats = computed(() => {
    const statsMap = new Map();
    deployments.value.forEach((deployment: any) => {
        const status = deployment.status;
        if (!statsMap.has(status)) {
            statsMap.set(status, { count: 0, items: [] });
        }
        statsMap.get(status).count++;
        statsMap.get(status).items.push(deployment.name);
    });
    return statsMap;
});

const statefulSetStats = computed(() => {
    const statsMap = new Map();
    statefulSets.value.forEach((sts: any) => {
        const status = sts.status;
        if (!statsMap.has(status)) {
            statsMap.set(status, { count: 0, items: [] });
        }
        statsMap.get(status).count++;
        statsMap.get(status).items.push(sts.name);
    });
    return statsMap;
});

// 辅助计算属性
const deploymentTotalCount = computed(() => deployments.value.length);
const statefulSetTotalCount = computed(() => statefulSets.value.length);

const getDeploymentCountByStatus = (status: string) => {
    return deploymentStats.value.get(status)?.count || 0;
};

const getStatefulSetCountByStatus = (status: string) => {
    return statefulSetStats.value.get(status)?.count || 0;
};

// ECharts 配置
const podChartOption = computed(() => ({
    title: {
        text: 'Pod 状态分布',
        left: 'center',
        top: 20,
        textStyle: {
            fontSize: 14,
            fontWeight: 'bold',
        },
    },
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    series: [
        {
            name: 'Pod 状态',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '55%'],
            avoidLabelOverlap: false,
            label: {
                show: false,
                position: 'center',
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: '18',
                    fontWeight: 'bold',
                },
            },
            labelLine: {
                show: false,
            },
            data: [
                { value: podStats.value.running, name: '运行中', itemStyle: { color: '#10b981' } },
                { value: podStats.value.pending, name: '等待中', itemStyle: { color: '#3b82f6' } },
                { value: podStats.value.failed, name: '失败', itemStyle: { color: '#ef4444' } },
                { value: podStats.value.warning, name: '警告', itemStyle: { color: '#f59e0b' } },
            ].filter((item) => item.value > 0),
        },
    ],
}));

// 统一状态颜色规范
const getStatusColor = (status: string) => {
    const lowerStatus = status.toLowerCase();
    if (['running', 'available'].includes(lowerStatus)) return '#10b981'; // 绿色 - 运行中
    if (['warning', 'partial'].includes(lowerStatus)) return '#f59e0b'; // 黄色 - 警告
    if (['pending', 'updating', 'progressing'].includes(lowerStatus)) return '#6b7280'; // 灰色 - 等待中
    return '#ef4444'; // 红色 - 错误/失败
};

// Pod 容器概览圆环图
const podSummaryChartOption = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
        backgroundColor: 'var(--popover)',
        borderColor: 'var(--border)',
        textStyle: { color: 'var(--foreground)' },
    },
    series: [
        {
            name: 'Pod 状态',
            type: 'pie',
            radius: ['40%', '65%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: {
                show: false,
            },
            emphasis: {
                scale: false,
                scaleSize: 0,
                label: {
                    show: false,
                },
            },
            labelLine: {
                show: false,
            },
            data: [
                { value: podStats.value.running, name: '运行中', itemStyle: { color: getStatusColor('running') } },
                { value: podStats.value.warning, name: '警告', itemStyle: { color: getStatusColor('warning') } },
                { value: podStats.value.failed, name: '失败', itemStyle: { color: getStatusColor('failed') } },
                { value: podStats.value.pending, name: '等待中', itemStyle: { color: getStatusColor('pending') } },
            ].filter((item) => item.value > 0),
        },
    ],
}));

// Pod 容器图例数据
const podLegendData = computed(() =>
    [
        { name: '运行中', value: podStats.value.running, color: getStatusColor('running') },
        { name: '警告', value: podStats.value.warning, color: getStatusColor('warning') },
        { name: '失败', value: podStats.value.failed, color: getStatusColor('failed') },
        { name: '等待中', value: podStats.value.pending, color: getStatusColor('pending') },
    ].filter((item) => item.value > 0),
);

// 无状态集概览圆环图
const deploymentSummaryChartOption = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
            return `${params.name}: ${params.value}个 (${params.percent}%)`;
        },
        backgroundColor: 'var(--popover)',
        borderColor: 'var(--border)',
        textStyle: { color: 'var(--foreground)' },
    },
    series: [
        {
            name: 'Deployment 状态',
            type: 'pie',
            radius: ['40%', '65%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: {
                show: false,
            },
            emphasis: {
                scale: false,
                scaleSize: 0,
                label: {
                    show: false,
                },
            },
            labelLine: {
                show: false,
            },
            data: Array.from(deploymentStats.value.entries()).map(([status, data]: [string, any]) => ({
                value: data.count,
                name: formatK8sStatus(status),
                itemStyle: { color: getStatusColor(status) },
            })),
        },
    ],
}));

// 无状态集图例数据
const deploymentLegendData = computed(() =>
    Array.from(deploymentStats.value.entries()).map(([status, data]: [string, any]) => ({
        name: formatK8sStatus(status),
        value: data.count,
        color: getStatusColor(status),
    })),
);

// 有状态集概览圆环图
const statefulSetSummaryChartOption = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
            return `${params.name}: ${params.value}个 (${params.percent}%)`;
        },
        backgroundColor: 'var(--popover)',
        borderColor: 'var(--border)',
        textStyle: { color: 'var(--foreground)' },
    },
    series: [
        {
            name: 'StatefulSet 状态',
            type: 'pie',
            radius: ['40%', '65%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: {
                show: false,
            },
            emphasis: {
                scale: false,
                scaleSize: 0,
                label: {
                    show: false,
                },
            },
            labelLine: {
                show: false,
            },
            data: Array.from(statefulSetStats.value.entries()).map(([status, data]: [string, any]) => ({
                value: data.count,
                name: formatK8sStatus(status),
                itemStyle: { color: getStatusColor(status) },
            })),
        },
    ],
}));

// 有状态集图例数据
const statefulSetLegendData = computed(() =>
    Array.from(statefulSetStats.value.entries()).map(([status, data]: [string, any]) => ({
        name: formatK8sStatus(status),
        value: data.count,
        color: getStatusColor(status),
    })),
);

// 服务与存储概览圆环图
const serviceStorageChartOption = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
        backgroundColor: 'var(--popover)',
        borderColor: 'var(--border)',
        textStyle: { color: 'var(--foreground)' },
    },
    series: [
        {
            name: '服务与存储',
            type: 'pie',
            radius: ['40%', '65%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: {
                show: false,
            },
            emphasis: {
                scale: false,
                scaleSize: 0,
                label: {
                    show: false,
                },
            },
            labelLine: {
                show: false,
            },
            data: [
                { value: services.value.length, name: '服务', itemStyle: { color: getStatusColor('running') } },
                { value: storages.value.length, name: '存储', itemStyle: { color: getStatusColor('warning') } },
            ].filter((item) => item.value > 0),
        },
    ],
}));

// 服务与存储图例数据
const serviceStorageLegendData = computed(() =>
    [
        { name: '服务', value: services.value.length, color: getStatusColor('running') },
        { name: '存储', value: storages.value.length, color: getStatusColor('warning') },
    ].filter((item) => item.value > 0),
);

// 获取最新事件
const recentEvents = computed(() => {
    const eventsArray = Array.isArray(events.value) ? events.value : [];
    return eventsArray
        .sort((a: any, b: any) => {
            // 按时间倒序
            const at = new Date(a.timestamp || 0).getTime();
            const bt = new Date(b.timestamp || 0).getTime();
            return bt - at;
        })
        .slice(0, 5)
        .map((event: any) => ({
            ...event,
            timeAgo: event.timestamp ? new Date(event.timestamp).toLocaleString() : '未知时间',
        }));
});

// 仅获取 metrics 数据
const fetchMetrics = async () => {
    loadingMetrics.value = true;

    try {
        const metricsResponse = await axios.get('/api/workspaces/current/metrics').catch(() => ({ data: null }));
        metrics.value = metricsResponse.data;
    } catch (error) {
        console.error('获取指标数据失败:', error);
    } finally {
        loadingMetrics.value = false;
    }
};

// 初始化和工作区切换的核心逻辑
const handleWorkspaceChange = async (workspace: Workspace | null) => {
    if (workspace) {
        console.log(`[Dashboard] 开始处理工作区: ${workspace.name}`);
        // 启动资源监听器（会自动获取资源数据）
        await resourceListener.startListening(workspace);
        // 获取 metrics
        await fetchMetrics();
    } else {
        console.warn('[Dashboard] 未找到当前工作区，停止处理。');
        resourceListener.stopListening();
        resourcesStore.clearAllResources();
    }
};

// 监听工作区变化
watch(
    () => (page.props as any).auth?.user?.current_workspace as Workspace | null,
    (newWorkspace, oldWorkspace) => {
        if (newWorkspace && newWorkspace.id !== oldWorkspace?.id) {
            console.log(`[Dashboard] 检测到工作区切换: ${oldWorkspace?.name} -> ${newWorkspace.name}`);
            handleWorkspaceChange(newWorkspace);
        }
    },
    { deep: true }, // 使用 deep watch 以便检测对象内部变化
);

// 监听资源刷新事件
const handleResourceRefresh = async () => {
    const currentWorkspace = (page.props as any).auth?.user?.current_workspace as Workspace | null;
    if (currentWorkspace) {
        await resourceListener.refreshResources();
        await fetchMetrics();
    }
};

// 注册事件监听器
onMounted(() => {
    const currentWorkspace = (page.props as any).auth?.user?.current_workspace as Workspace | null;
    console.log('[Dashboard] 组件已挂载，当前工作区:', currentWorkspace?.name || '无');
    handleWorkspaceChange(currentWorkspace);

    eventBus.on('resource:refresh', handleResourceRefresh);
});

onUnmounted(() => {
    eventBus.off('resource:refresh', handleResourceRefresh);
});

const getEventIcon = (type: string) => {
    return type === 'Warning' ? AlertCircle : CheckCircle2;
};

const getEventColor = (type: string) => {
    return type === 'Warning' ? 'text-red-500' : 'text-green-500';
};

// 窗口管理器测试方法
const openTerminal = () => {
    eventBus.emit('window:open', {
        type: 'terminal',
        data: {
            // 不指定 podName，让用户在窗口中选择
            mode: 'shell',
            isConnected: false,
        },
    });
};

const openAIAssistant = () => {
    eventBus.emit('window:open', {
        type: 'ai-assistant',
        data: {
            isLoading: false,
        },
    });
};

const openCustomWindow = () => {
    eventBus.emit('window:open', {
        type: 'custom',
        data: {
            title: '自定义窗口',
            content: '这是一个自定义窗口的内容',
        },
        options: {
            title: '自定义窗口示例',
            size: { width: 600, height: 400 },
        },
    });
};

const openForumWindow = () => {
    openWeb('https://forum.leafaas.com/t/manifests', 'Leafaas 论坛');
};
</script>

<template>
    <Head title="总览" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="p-4">
            <!-- 页面标题 -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold tracking-tight">{{ appName }} 运行平台</h1>
                        <p class="text-muted-foreground">实时监控您的应用程序和资源状态</p>
                    </div>

                    <!-- 窗口管理器测试按钮 -->
                    <div class="flex items-center gap-2">
                        <Button variant="outline" size="sm" @click="openForumWindow" class="flex items-center gap-2">
                            <MessageCircle class="h-4 w-4" />
                            打开论坛
                        </Button>
                        <Button variant="outline" size="sm" @click="openTerminal" class="flex items-center gap-2">
                            <Terminal class="h-4 w-4" />
                            打开终端
                        </Button>
                        <Button variant="outline" size="sm" @click="openAIAssistant" class="flex items-center gap-2">
                            <Sparkles class="h-4 w-4" />
                            AI 助手
                        </Button>
                        <!-- <Button variant="outline" size="sm" @click="openCustomWindow" class="flex items-center gap-2"> 自定义窗口 </Button> -->
                    </div>
                </div>
            </div>

            <div v-if="loading" class="space-y-8">
                <!-- 加载状态 -->
                <div class="grid grid-cols-1 gap-6 md:grid-cols-4">
                    <Skeleton class="h-32" />
                    <Skeleton class="h-32" />
                    <Skeleton class="h-32" />
                    <Skeleton class="h-32" />
                </div>
                <Skeleton class="h-64" />
            </div>

            <div v-else class="space-y-8">
                <!-- 统计概览圆环图 -->
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <!-- Pod 容器状态圆环 -->
                    <div class="rounded-lg border bg-background p-3">
                        <div class="mb-2">
                            <h3 class="mb-2 text-center text-sm font-semibold">Pod 容器</h3>
                            <div v-if="podStats.total > 0" class="mb-2 flex flex-wrap justify-center gap-2">
                                <div v-for="item in podLegendData" :key="item.name" class="flex items-center gap-1 text-xs">
                                    <div class="h-2 w-2 rounded-full" :style="`background-color: ${item.color}`"></div>
                                    <span>{{ item.name }} {{ item.value }}</span>
                                </div>
                            </div>
                        </div>
                        <VChart v-if="podStats.total > 0" :option="podSummaryChartOption" style="height: 100px" autoresize />
                        <div v-else class="flex h-[100px] items-center justify-center text-muted-foreground">
                            <div class="text-center">
                                <Container class="mx-auto h-8 w-8 opacity-50" />
                                <p class="mt-2 text-sm">暂无 Pod 数据</p>
                            </div>
                        </div>
                    </div>

                    <!-- 无状态集状态圆环 -->
                    <div class="rounded-lg border bg-background p-3">
                        <div class="mb-2">
                            <h3 class="mb-2 text-center text-sm font-semibold">无状态集</h3>
                            <div v-if="deploymentTotalCount > 0" class="mb-2 flex flex-wrap justify-center gap-2">
                                <div v-for="item in deploymentLegendData" :key="item.name" class="flex items-center gap-1 text-xs">
                                    <div class="h-2 w-2 rounded-full" :style="`background-color: ${item.color}`"></div>
                                    <span>{{ item.name }} {{ item.value }}</span>
                                </div>
                            </div>
                        </div>
                        <VChart v-if="deploymentTotalCount > 0" :option="deploymentSummaryChartOption" style="height: 100px" autoresize />
                        <div v-else class="flex h-[100px] items-center justify-center text-muted-foreground">
                            <div class="text-center">
                                <Activity class="mx-auto h-8 w-8 opacity-50" />
                                <p class="mt-2 text-sm">暂无无状态集数据</p>
                            </div>
                        </div>
                    </div>

                    <!-- 有状态集状态圆环 -->
                    <div class="rounded-lg border bg-background p-3">
                        <div class="mb-2">
                            <h3 class="mb-2 text-center text-sm font-semibold">有状态集</h3>
                            <div v-if="statefulSetTotalCount > 0" class="mb-2 flex flex-wrap justify-center gap-2">
                                <div v-for="item in statefulSetLegendData" :key="item.name" class="flex items-center gap-1 text-xs">
                                    <div class="h-2 w-2 rounded-full" :style="`background-color: ${item.color}`"></div>
                                    <span>{{ item.name }} {{ item.value }}</span>
                                </div>
                            </div>
                        </div>
                        <VChart v-if="statefulSetTotalCount > 0" :option="statefulSetSummaryChartOption" style="height: 100px" autoresize />
                        <div v-else class="flex h-[100px] items-center justify-center text-muted-foreground">
                            <div class="text-center">
                                <Database class="mx-auto h-8 w-8 opacity-50" />
                                <p class="mt-2 text-sm">暂无有状态集数据</p>
                            </div>
                        </div>
                    </div>

                    <!-- 服务 & 存储状态圆环 -->
                    <div class="rounded-lg border bg-background p-3">
                        <div class="mb-2">
                            <h3 class="mb-2 text-center text-sm font-semibold">服务 & 存储</h3>
                            <div v-if="services.length + storages.length > 0" class="mb-2 flex flex-wrap justify-center gap-2">
                                <div v-for="item in serviceStorageLegendData" :key="item.name" class="flex items-center gap-1 text-xs">
                                    <div class="h-2 w-2 rounded-full" :style="`background-color: ${item.color}`"></div>
                                    <span>{{ item.name }} {{ item.value }}</span>
                                </div>
                            </div>
                        </div>
                        <VChart v-if="services.length + storages.length > 0" :option="serviceStorageChartOption" style="height: 100px" autoresize />
                        <div v-else class="flex h-[100px] items-center justify-center text-muted-foreground">
                            <div class="text-center">
                                <Route class="mx-auto h-8 w-8 opacity-50" />
                                <p class="mt-2 text-sm">暂无服务和存储数据</p>
                            </div>
                        </div>
                    </div>
                </div>

                <Separator />

                <!-- 最近事件 -->
                <div>
                    <h2 class="mb-4 text-xl font-semibold">最近事件</h2>
                    <div v-if="recentEvents.length > 0" class="rounded-lg border bg-background">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead class="w-16">类型</TableHead>
                                    <TableHead>原因</TableHead>
                                    <TableHead>消息</TableHead>
                                    <TableHead>对象</TableHead>
                                    <TableHead class="w-32">时间</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="event in recentEvents" :key="event.timestamp + event.reason">
                                    <TableCell>
                                        <component :is="getEventIcon(event.type)" :class="getEventColor(event.type)" class="h-4 w-4" />
                                    </TableCell>
                                    <TableCell>
                                        <Badge :variant="event.type === 'Warning' ? 'destructive' : 'secondary'">
                                            {{ event.reason }}
                                        </Badge>
                                    </TableCell>
                                    <TableCell class="max-w-md truncate">{{ event.message }}</TableCell>
                                    <TableCell>
                                        <span v-if="event.involved_object" class="text-sm text-muted-foreground">
                                            {{ event.involved_object.kind }}/{{ event.involved_object.name }}
                                        </span>
                                    </TableCell>
                                    <TableCell class="text-sm text-muted-foreground">
                                        {{ event.timeAgo }}
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                    <div v-else class="flex items-center justify-center rounded-lg border bg-background p-8">
                        <div class="text-center text-muted-foreground">
                            <Clock class="mx-auto h-12 w-12 opacity-50" />
                            <p class="mt-2">暂无最近事件</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- <ResourceStatus /> -->
    </AppLayout>
</template>
