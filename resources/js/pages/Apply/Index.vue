<script setup lang="ts">
import DockerImportDialog from '@/components/DockerImportDialog.vue';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import VariableHelper from '@/components/VariableHelper.vue';
import { useFileDropzone } from '@/composables/useFileDropzone';
import { useYamlEditor } from '@/composables/useYamlEditor';
import AppLayout from '@/layouts/AppLayout.vue';
import { convertContentToCustomYaml } from '@/lib/dockerConvert';
import { Head, usePage } from '@inertiajs/vue3';
import axios from 'axios';
import { FileText, Loader2, Send, Trash2, Upload } from 'lucide-vue-next';
import { onMounted, ref } from 'vue';
import { toast } from 'vue-sonner';

const page = usePage();

const {
    yamlText,
    isSubmittingState,
    hasWorkspaceState,
    currentProgressValue,
    totalProgressValue,
    progressValue,
    currentApplyingText,
    initializeDefaultYaml,
    applyYaml,
    deleteYaml,
} = useYamlEditor();

// 拖拽功能
const { isDragging, onDragEnter, onDragLeave, onDragOver, onDrop } = useFileDropzone({
    onFileRead: (content: string) => {
        yamlText.value = convertContentToCustomYaml(content);
    },
});

// Docker 导入对话框
const showDockerDialog = ref(false);
const handleDockerConverted = (converted: string) => {
    yamlText.value = converted;
};

// 从 URL 加载 Manifest
const loadingManifest = ref(false);
const manifestUrl = ref('');

// 初始化默认 YAML
onMounted(async () => {
    initializeDefaultYaml();
    const url = new URL(window.location.href).searchParams.get('url');
    if (url) {
        manifestUrl.value = url;
        loadingManifest.value = true;
        try {
            const res = await axios.post(route('apply.get-manifest'), { url });
            if (res.data && res.data.content) {
                yamlText.value = convertContentToCustomYaml(res.data.content);
                toast.success('清单已成功从 URL 加载。');
            }
        } catch (err) {
            let msg = '加载清单失败';
            const error = err as any;
            if (error.response && error.response.data && error.response.data.error) {
                msg = error.response.data.error;
            }
            toast.error(msg);
        } finally {
            loadingManifest.value = false;
        }
    }
});

// 应用/更新资源
const handleApply = async () => {
    await applyYaml();
};

// 删除资源
const handleDelete = async () => {
    await deleteYaml();
};
</script>

<template>
    <Head title="YAML 资源应用" />

    <AppLayout>
        <div class="container mx-auto p-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">应用更改</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        {{ hasWorkspaceState ? '输入 YAML 配置来创建或更新资源' : '请先选择工作空间' }}， 使用
                        <code>---</code> 分隔多个资源配置。每个配置必须包含 <code>kind</code> 和 <code>name</code> 字段。 支持使用
                        <code>${`{变量名}`}</code> 格式的变量。可以拖拽 YAML/YML 文件到编辑器中导入。
                    </p>
                </div>
                <div class="flex gap-2">
                    <Button variant="secondary" @click="showDockerDialog = true" :disabled="loadingManifest || isSubmittingState">
                        <Upload class="mr-2 h-4 w-4" />
                        导入 Docker
                    </Button>

                    <Button variant="destructive" @click="handleDelete" :disabled="isSubmittingState || !hasWorkspaceState || loadingManifest">
                        <Loader2 v-if="isSubmittingState" class="mr-2 h-4 w-4 animate-spin" />
                        <Trash2 v-else class="mr-2 h-4 w-4" />
                        {{ isSubmittingState ? '删除中...' : '删除资源' }}
                    </Button>

                    <Button @click="handleApply" :disabled="isSubmittingState || !hasWorkspaceState || loadingManifest">
                        <Loader2 v-if="isSubmittingState" class="mr-2 h-4 w-4 animate-spin" />
                        <Send v-else class="mr-2 h-4 w-4" />
                        {{ isSubmittingState ? '应用中...' : '应用/更新' }}
                    </Button>
                </div>
            </div>

            <!-- 进度显示 -->
            <div v-if="isSubmittingState" class="my-6 space-y-3 rounded-lg border bg-card p-4">
                <div class="flex items-center justify-between text-sm">
                    <span class="font-medium">操作进度</span>
                    <span class="text-muted-foreground">{{ currentProgressValue }} / {{ totalProgressValue }}</span>
                </div>
                <Progress :model-value="progressValue" class="h-2" />
                <div v-if="currentApplyingText" class="text-sm text-muted-foreground">
                    正在处理: <code class="rounded bg-muted px-1.5 py-0.5">{{ currentApplyingText }}</code>
                </div>
            </div>

            <!-- 无工作空间提示 -->
            <div v-if="!hasWorkspaceState" class="flex items-center justify-center py-12">
                <div class="text-center">
                    <FileText class="mx-auto h-12 w-12 text-muted-foreground" />
                    <p class="mt-4 text-sm text-muted-foreground">请先选择一个工作空间来应用资源</p>
                </div>
            </div>

            <!-- YAML 编辑器 -->
            <div v-else class="grid grid-cols-1 gap-4 lg:grid-cols-3">
                <!-- 左侧：YAML 编辑器 -->
                <div class="space-y-4 lg:col-span-2">
                    <div class="relative" @dragenter="onDragEnter" @dragleave="onDragLeave" @dragover="onDragOver" @drop="onDrop">
                        <!-- 拖拽覆盖层 -->
                        <div
                            v-if="isDragging"
                            class="absolute inset-0 z-10 flex items-center justify-center rounded-md border-2 border-dashed border-primary bg-primary/5"
                        >
                            <div class="text-center">
                                <Upload class="mx-auto h-8 w-8 text-primary" />
                                <p class="mt-2 text-sm font-medium text-primary">释放文件以导入 YAML</p>
                                <p class="text-xs text-muted-foreground">支持 .yaml 和 .yml 文件</p>
                            </div>
                        </div>
                        <!-- 从URL加载 -->
                        <div
                            v-if="loadingManifest"
                            class="absolute inset-0 z-10 flex flex-col items-center justify-center rounded-md border-2 border-dashed border-primary bg-background/80"
                        >
                            <Loader2 class="h-8 w-8 animate-spin text-primary" />
                            <p class="mt-4 text-sm font-medium text-primary">正在从 URL 加载清单...</p>
                            <p class="max-w-full truncate px-4 pt-1 text-xs text-muted-foreground" :title="manifestUrl">
                                {{ manifestUrl }}
                            </p>
                        </div>
                        <ScrollArea class="rounded-md border">
                            <textarea
                                id="yaml-content"
                                v-model="yamlText"
                                class="h-[600px] w-full resize-none border-0 bg-transparent p-4 font-mono text-sm focus:ring-0 focus:outline-none"
                                placeholder="输入 YAML 配置..."
                                spellcheck="false"
                                :disabled="isSubmittingState || loadingManifest"
                            />
                        </ScrollArea>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-end gap-2">
                        <Button variant="destructive" @click="handleDelete" :disabled="isSubmittingState || !hasWorkspaceState || loadingManifest">
                            <Loader2 v-if="isSubmittingState" class="mr-2 h-4 w-4 animate-spin" />
                            <Trash2 v-else class="mr-2 h-4 w-4" />
                            {{ isSubmittingState ? '删除中...' : '删除资源' }}
                        </Button>

                        <Button @click="handleApply" :disabled="isSubmittingState || !hasWorkspaceState || loadingManifest">
                            <Loader2 v-if="isSubmittingState" class="mr-2 h-4 w-4 animate-spin" />
                            <Send v-else class="mr-2 h-4 w-4" />
                            {{ isSubmittingState ? '应用中...' : '应用/更新' }}
                        </Button>
                    </div>
                </div>

                <!-- 右侧：变量帮助 -->
                <div class="space-y-4">
                    <VariableHelper />
                </div>
            </div>
        </div>
    </AppLayout>

    <!-- Docker 导入对话框 -->
    <DockerImportDialog v-model:open="showDockerDialog" @converted="handleDockerConverted" />
</template>
