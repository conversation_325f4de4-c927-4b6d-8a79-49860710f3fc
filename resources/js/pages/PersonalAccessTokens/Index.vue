<template>
    <AppLayout>
        <Head title="访问令牌" />

        <div class="space-y-6 p-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold">访问令牌</h1>
                    <p class="mt-1 text-sm">管理你的个人访问令牌和已授权的应用程序</p>
                </div>
                <Button @click="openCreateDialog">
                    <Plus class="mr-2 h-4 w-4" />
                    创建令牌
                </Button>
            </div>

            <!-- 新创建的令牌显示 -->
            <div v-if="newAccessToken" class="rounded-md border border-blue-200 bg-blue-50 p-4 dark:border-blue-500/30 dark:bg-blue-950/20">
                <div class="flex">
                    <Info class="h-5 w-5 text-blue-400" />
                    <div class="ml-3">
                        <p class="mb-2 text-sm font-medium text-blue-800 dark:text-blue-300">访问令牌已生成，请立即复制保存：</p>
                        <div class="relative">
                            <pre class="rounded border bg-blue-100 p-3 font-mono text-sm break-all dark:bg-blue-900/50">{{ newAccessToken }}</pre>
                            <Button size="sm" variant="outline" class="absolute top-2 right-2" @click="copyToken">
                                <Copy class="h-3 w-3" />
                            </Button>
                        </div>
                        <p class="mt-2 text-xs text-blue-600 dark:text-blue-400">⚠️ 此令牌只会显示一次，请务必保存好</p>
                    </div>
                </div>
            </div>

            <!-- 个人访问令牌列表 -->
            <div class="rounded-lg border">
                <div class="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">个人访问令牌</h3>
                </div>

                <div v-if="tokensList.length === 0" class="p-8 text-center">
                    <div class="text-gray-400 dark:text-gray-500">
                        <Key class="mx-auto mb-4 h-12 w-12" />
                        <p class="text-sm">暂无访问令牌</p>
                        <p class="mt-1 text-xs">点击上方按钮创建你的第一个令牌</p>
                    </div>
                </div>

                <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
                    <div v-for="token in tokensList" :key="token.id" class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="min-w-0 flex-1">
                                <div class="mb-2 flex items-center gap-3">
                                    <h4 class="truncate text-sm font-medium text-gray-900 dark:text-white">
                                        {{ token.name }}
                                    </h4>
                                    <Badge variant="outline">
                                        {{ token.client.name }}
                                    </Badge>
                                </div>
                                <div class="space-y-1">
                                    <p class="text-xs">创建时间：{{ token.created_at }}</p>
                                    <p v-if="token.last_used_at" class="text-xs">最后使用：{{ token.last_used_at }}</p>
                                    <p v-if="token.expires_at" class="text-xs">过期时间：{{ token.expires_at }}</p>
                                </div>
                            </div>

                            <Button
                                variant="outline"
                                size="sm"
                                @click="confirmDeleteToken(token)"
                                class="text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-red-950/20 dark:hover:text-red-300"
                            >
                                <Trash2 class="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已授权的应用程序 -->
            <div class="rounded-lg border">
                <div class="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">已授权的应用</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">管理已获得你账户授权的第三方应用程序</p>
                </div>

                <div v-if="authorizedAppsList.length === 0" class="p-8 text-center">
                    <div class="text-gray-400 dark:text-gray-500">
                        <Shield class="mx-auto mb-4 h-12 w-12" />
                        <p class="text-sm">暂无已授权应用</p>
                        <p class="mt-1 text-xs">当你授权第三方应用访问你的账户时，它们会出现在这里</p>
                    </div>
                </div>

                <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
                    <div
                        v-for="app in authorizedAppsList"
                        :key="app.client_id"
                        class="p-6 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/50"
                    >
                        <div class="flex items-start justify-between">
                            <div class="min-w-0 flex-1">
                                <h4 class="mb-2 truncate text-sm font-medium text-gray-900 dark:text-white">
                                    {{ app.client_name }}
                                </h4>
                                <div class="space-y-1">
                                    <p class="text-xs text-gray-400 dark:text-gray-500">授权令牌数：{{ app.tokens_count }}</p>
                                    <p class="text-xs text-gray-400 dark:text-gray-500">首次授权：{{ app.created_at }}</p>
                                    <p v-if="app.last_used_at" class="text-xs text-gray-400 dark:text-gray-500">最后使用：{{ app.last_used_at }}</p>
                                </div>
                            </div>

                            <Button
                                variant="outline"
                                size="sm"
                                @click="confirmRevokeApp(app)"
                                class="text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-red-950/20 dark:hover:text-red-300"
                            >
                                <UserX class="mr-2 h-4 w-4" />
                                撤销授权
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 创建令牌对话框 -->
            <Dialog v-model:open="showCreateDialog">
                <DialogContent class="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>创建访问令牌</DialogTitle>
                    </DialogHeader>

                    <form @submit.prevent="createToken">
                        <div class="space-y-4">
                            <div>
                                <Label for="token-name">令牌名称</Label>
                                <Input
                                    id="token-name"
                                    v-model="tokenForm.name"
                                    placeholder="我的应用令牌"
                                    :class="{ 'border-red-500': tokenForm.errors.name }"
                                />
                                <p v-if="tokenForm.errors.name" class="mt-1 text-sm text-red-600">
                                    {{ tokenForm.errors.name }}
                                </p>
                                <p class="mt-1 text-xs text-gray-500">用于标识这个令牌的用途</p>
                            </div>
                        </div>

                        <DialogFooter class="mt-6">
                            <Button type="button" variant="outline" @click="resetCreateDialog" :disabled="tokenForm.processing"> 取消 </Button>
                            <Button type="submit" :disabled="tokenForm.processing">
                                {{ tokenForm.processing ? '创建中...' : '创建' }}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>

            <!-- 删除令牌确认对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent class="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>确认删除</DialogTitle>
                        <DialogDescription>
                            确定要删除令牌 "{{ tokenToDelete?.name }}" 吗？ 此操作不可恢复，使用此令牌的应用将无法继续访问你的账户。
                        </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false" :disabled="deleteForm.processing"> 取消 </Button>
                        <Button variant="destructive" @click="deleteToken" :disabled="deleteForm.processing">
                            {{ deleteForm.processing ? '删除中...' : '删除' }}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 撤销应用授权确认对话框 -->
            <Dialog v-model:open="showRevokeDialog">
                <DialogContent class="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>撤销应用授权</DialogTitle>
                        <DialogDescription>
                            确定要撤销应用 "{{ appToRevoke?.client_name }}" 的所有授权吗？
                            此操作将删除该应用的所有访问令牌，应用将无法继续访问你的账户。
                        </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showRevokeDialog = false" :disabled="revokeForm.processing"> 取消 </Button>
                        <Button variant="destructive" @click="revokeApp" :disabled="revokeForm.processing">
                            {{ revokeForm.processing ? '撤销中...' : '撤销授权' }}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import type { AccessToken } from '@/types/passport';
import { Head } from '@inertiajs/vue3';
import axios from 'axios';
import { Copy, Info, Key, Plus, Shield, Trash2, UserX } from 'lucide-vue-next';
import { toast } from 'sonner';
import { reactive, ref } from 'vue';

interface AuthorizedApp {
    client_id: string;
    client_name: string;
    tokens_count: number;
    last_used_at?: string;
    created_at: string;
}

interface Props {
    tokens: { data: AccessToken[] };
    authorizedApps: { data: AuthorizedApp[] };
}

const props = defineProps<Props>();

// 响应式数据
const tokensList = ref([...props.tokens.data]);
const authorizedAppsList = ref([...props.authorizedApps.data]);
const newAccessToken = ref<string>('');

// 表单状态
const showCreateDialog = ref(false);
const showDeleteDialog = ref(false);
const showRevokeDialog = ref(false);
const tokenToDelete = ref<AccessToken | null>(null);
const appToRevoke = ref<AuthorizedApp | null>(null);

// 表单数据
const tokenForm = reactive({
    name: '',
    errors: {} as Record<string, string>,
    processing: false,
});

const deleteForm = reactive({
    processing: false,
});

const revokeForm = reactive({
    processing: false,
});

// 重置令牌表单
const resetTokenForm = () => {
    tokenForm.name = '';
    tokenForm.errors = {};
    tokenForm.processing = false;
};

// 打开创建对话框
const openCreateDialog = () => {
    resetTokenForm();
    showCreateDialog.value = true;
};

// 重置创建对话框
const resetCreateDialog = () => {
    showCreateDialog.value = false;
    resetTokenForm();
};

// 创建令牌
const createToken = async () => {
    tokenForm.processing = true;
    tokenForm.errors = {};

    try {
        const response = await axios.post(route('oauth.tokens.store'), {
            name: tokenForm.name,
        });

        // 添加到令牌列表
        tokensList.value.unshift(response.data.token);
        newAccessToken.value = response.data.access_token;

        resetCreateDialog();
        toast.success(response.data.message);
    } catch (error: any) {
        if (error.response?.status === 422) {
            tokenForm.errors = error.response.data.errors || {};
        } else {
            toast.error('创建失败，请重试');
        }
    } finally {
        tokenForm.processing = false;
    }
};

// 确认删除令牌
const confirmDeleteToken = (token: AccessToken) => {
    tokenToDelete.value = token;
    showDeleteDialog.value = true;
};

// 删除令牌
const deleteToken = async () => {
    if (!tokenToDelete.value) return;

    deleteForm.processing = true;

    try {
        const response = await axios.delete(route('oauth.tokens.destroy', tokenToDelete.value.id));

        // 从列表中移除
        const index = tokensList.value.findIndex((t) => t.id === tokenToDelete.value!.id);
        if (index !== -1) {
            tokensList.value.splice(index, 1);
        }

        showDeleteDialog.value = false;
        toast.success(response.data.message);
    } catch (error) {
        toast.error('删除失败，请重试');
    } finally {
        deleteForm.processing = false;
    }
};

// 确认撤销应用授权
const confirmRevokeApp = (app: AuthorizedApp) => {
    appToRevoke.value = app;
    showRevokeDialog.value = true;
};

// 撤销应用授权
const revokeApp = async () => {
    if (!appToRevoke.value) return;

    revokeForm.processing = true;

    try {
        const response = await axios.delete(route('oauth.clients.revoke', appToRevoke.value.client_id));

        // 从授权应用列表中移除
        const appIndex = authorizedAppsList.value.findIndex((a) => a.client_id === appToRevoke.value!.client_id);
        if (appIndex !== -1) {
            authorizedAppsList.value.splice(appIndex, 1);
        }

        // 从令牌列表中移除相关令牌
        tokensList.value = tokensList.value.filter((t) => t.client.id !== appToRevoke.value!.client_id);

        showRevokeDialog.value = false;
        toast.success(response.data.message);
    } catch (error) {
        toast.error('撤销失败，请重试');
    } finally {
        revokeForm.processing = false;
    }
};

// 复制令牌
const copyToken = async () => {
    if (newAccessToken.value) {
        try {
            await navigator.clipboard.writeText(newAccessToken.value);
            toast.success('令牌已复制到剪贴板');
            newAccessToken.value = ''; // 清除显示
        } catch (err) {
            toast.error('复制失败，请手动复制');
        }
    }
};
</script>
