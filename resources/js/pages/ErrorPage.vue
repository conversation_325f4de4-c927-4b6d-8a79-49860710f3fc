<script setup lang="ts">
import Logo from '@/assets/images/Logo.png';
import LiquidLogo from '@/components/LiquidLogo/LiquidLogo.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps({
    status: {
        type: Number,
        default: 500,
    },
});

const errorConfig = computed(() => {
    const config = {
        503: {
            title: '服务不可用',
            description: '抱歉，我们正在进行维护。请稍后再来。',
            color: 'text-orange-500',
        },
        500: {
            title: '服务器错误',
            description: '哎呀，我们的服务器出了点问题。',
            color: 'text-red-500',
        },
        404: {
            title: '页面未找到',
            description: '抱歉，您寻找的页面无法找到。',
            color: 'text-blue-500',
        },
        403: {
            title: '禁止访问',
            description: '抱歉，您无权访问此页面。',
            color: 'text-purple-500',
        },
    };
    // @ts-expect-error status
    return config[props.status] || config[500];
});
</script>

<template>
    <div class="flex min-h-screen items-center justify-center p-4">
        <Card class="w-full md:max-w-lg">
            <CardHeader class="space-y-4 text-center">
                <LiquidLogo :image-url="Logo"></LiquidLogo>
                <div>
                    <CardTitle class="mb-2 text-3xl"> {{ props.status }}: {{ errorConfig.title }} </CardTitle>
                    <CardDescription class="text-lg">
                        {{ errorConfig.description }}
                    </CardDescription>
                </div>
            </CardHeader>
            <CardContent class="text-center">
                <Button as-child variant="outline">
                    <Link href="/"> 返回主页 </Link>
                </Button>
            </CardContent>
        </Card>
    </div>
</template>
