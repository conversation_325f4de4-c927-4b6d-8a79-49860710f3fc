<template>
    <AppLayout>
        <div class="space-y-6 p-4">
            <Head title="密钥管理" />

            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">密钥管理</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">管理工作空间中的密钥配置</p>
                </div>
                <Button @click="router.visit(route('secrets.create'))">
                    <Plus class="mr-2 h-4 w-4" />
                    创建密钥
                </Button>
            </div>

            <!-- Secret 列表 -->
            <Card>
                <CardContent class="p-0">
                    <div v-if="!resourcesStore.isLoaded" class="p-6">
                        <div class="space-y-4">
                            <Skeleton class="h-4 w-full" />
                            <Skeleton class="h-4 w-3/4" />
                            <Skeleton class="h-4 w-1/2" />
                        </div>
                    </div>

                    <div v-else-if="secrets.length === 0" class="p-6 text-center">
                        <div class="text-gray-500 dark:text-gray-400">
                            <Key class="mx-auto mb-4 h-12 w-12 opacity-50" />
                            <p class="text-lg font-medium">暂无密钥</p>
                            <p class="text-sm">创建您的第一个密钥来存储敏感数据</p>
                        </div>
                    </div>

                    <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
                        <div v-for="secret in secrets" :key="secret.name" class="p-4 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/50">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <Key class="h-5 w-5 text-gray-400" />
                                        <div>
                                            <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ secret.name }}
                                            </h3>
                                            <div class="mt-1 flex items-center space-x-4">
                                                <Badge :variant="getSecretTypeVariant(secret.type)">
                                                    {{ getSecretTypeLabel(secret.type) }}
                                                </Badge>
                                                <span class="text-xs text-gray-500 dark:text-gray-400"> {{ secret.data_count }} 个键 </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    {{ formatDate(secret.created_at) }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="sm">
                                            <MoreHorizontal class="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem @click="viewSecret(secret)">
                                            <Eye class="mr-2 h-4 w-4" />
                                            查看详情
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="router.visit(route('secrets.edit', secret.name))">
                                            <Edit class="mr-2 h-4 w-4" />
                                            编辑
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem @click="confirmDeleteSecret(secret)" class="text-red-600 dark:text-red-400">
                                            <Trash2 class="mr-2 h-4 w-4" />
                                            删除
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- 查看 Secret 对话框 -->
            <Dialog v-model:open="showViewDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>密钥详情</DialogTitle>
                    </DialogHeader>

                    <div v-if="selectedSecret" class="space-y-4">
                        <div>
                            <Label>名称</Label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ selectedSecret.name }}</p>
                        </div>
                        <div>
                            <Label>类型</Label>
                            <Badge :variant="getSecretTypeVariant(selectedSecret.type)">
                                {{ getSecretTypeLabel(selectedSecret.type) }}
                            </Badge>
                        </div>
                        <div>
                            <Label>数据键</Label>
                            <div class="space-y-1">
                                <div
                                    v-for="key in selectedSecret.data_keys"
                                    :key="key"
                                    class="mr-2 inline-block rounded bg-gray-100 px-2 py-1 text-sm text-gray-900 dark:bg-gray-800 dark:text-white"
                                >
                                    {{ key }}
                                </div>
                            </div>
                        </div>
                        <div>
                            <Label>创建时间</Label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedSecret.created_at) }}</p>
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" @click="showViewDialog = false"> 关闭 </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 删除 Secret 对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>确认删除</DialogTitle>
                        <DialogDescription> 确定要删除密钥 "{{ secretToDelete?.name }}" 吗？此操作不可恢复。 </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false"> 取消 </Button>
                        <Button @click="deleteSecret" :disabled="deleting">
                            {{ deleting ? '删除中...' : '删除' }}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { Secret } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { Edit, Eye, Key, MoreHorizontal, Plus, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, ref } from 'vue';
import { route } from 'ziggy-js';

const resourcesStore = useResourcesStore();
const secrets = computed(() => resourcesStore.collections.secrets);

const showViewDialog = ref(false);
const selectedSecret = ref<Secret | null>(null);
const showDeleteDialog = ref(false);
const secretToDelete = ref<Secret | null>(null);
const deleting = ref(false);

const getSecretTypeLabel = (type: string): string => {
    const types: Record<string, string> = {
        Opaque: '通用',
        'kubernetes.io/dockerconfigjson': 'Docker Registry',
        'kubernetes.io/tls': 'TLS 证书',
        'kubernetes.io/basic-auth': '基础认证',
        'kubernetes.io/ssh-auth': 'SSH 认证',
    };
    return types[type] || type;
};

const getSecretTypeVariant = (type: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
    const variants: Record<string, 'default' | 'destructive' | 'outline' | 'secondary'> = {
        Opaque: 'secondary',
        'kubernetes.io/dockerconfigjson': 'default',
        'kubernetes.io/tls': 'secondary',
        'kubernetes.io/basic-auth': 'secondary',
        'kubernetes.io/ssh-auth': 'secondary',
    };
    return variants[type] || 'secondary';
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};

const viewSecret = (secret: Secret) => {
    selectedSecret.value = secret;
    showViewDialog.value = true;
};

const confirmDeleteSecret = (secret: Secret) => {
    secretToDelete.value = secret;
    showDeleteDialog.value = true;
};

const deleteSecret = async () => {
    if (!secretToDelete.value) {
        return;
    }

    try {
        deleting.value = true;
        await axios.delete(route('api.secrets.destroy', { secret: secretToDelete.value.name }));
        toast.success('密钥删除成功');
        showDeleteDialog.value = false;
    } catch (error) {
        console.error('删除密钥失败:', error);
        toast.error('删除密钥失败');
    } finally {
        deleting.value = false;
    }
};
</script>
