<script setup lang="ts">
import { Head } from '@inertiajs/vue3';

import AppearanceTabs from '@/components/AppearanceTabs.vue';
import { Button } from '@/components/ui/button';
import axios from '@/lib/axios';
import { usePage } from '@inertiajs/vue3';
import { Image as ImageIcon, Trash2, Upload } from 'lucide-vue-next';
import { ref } from 'vue';

import HeadingSmall from '@/components/HeadingSmall.vue';
import { type BreadcrumbItem } from '@/types';

import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import eventBus from '@/lib/eventBus';

// 获取当前壁纸
const page = usePage();
const initialWallpaper = (page.props as any).auth?.user?.settings?.desktop_wallpaper_url ?? null;
const wallpaper = ref<string | null>(initialWallpaper);

const fileInput = ref<HTMLInputElement | null>(null);

const uploadWallpaper = async (e: Event) => {
    const target = e.target as HTMLInputElement;
    if (!target.files || target.files.length === 0) return;
    const file = target.files[0];
    const formData = new FormData();
    formData.append('wallpaper', file);
    try {
        const { data } = await axios.post('/desktop/wallpaper', formData, {
            headers: { 'Content-Type': 'multipart/form-data' },
        });
        wallpaper.value = data.wallpaper_url;
        (eventBus as any).emit('desktop:wallpaper-updated', { url: wallpaper.value });
    } catch {}
};

const removeWallpaper = async () => {
    try {
        await axios.delete('/desktop/wallpaper');
        wallpaper.value = null;
        (eventBus as any).emit('desktop:wallpaper-updated', { url: null });
    } catch {}
};

const breadcrumbItems: BreadcrumbItem[] = [
    {
        title: '外观设置',
        href: '/settings/appearance',
    },
];
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbItems">
        <Head title="外观设置" />

        <SettingsLayout>
            <div class="space-y-6">
                <HeadingSmall title="外观设置" description="更新您账户的外观设置" />
                <AppearanceTabs />

                <!-- 桌面壁纸 -->
                <div class="mt-8 space-y-4">
                    <h2 class="text-lg font-medium">桌面壁纸</h2>
                    <div class="flex items-center gap-6">
                        <div class="flex h-40 w-64 items-center justify-center overflow-hidden rounded-lg border border-border bg-muted">
                            <img v-if="wallpaper" :src="wallpaper" class="h-full w-full object-cover" />
                            <ImageIcon v-else class="h-10 w-10 text-muted-foreground" />
                        </div>

                        <div class="space-y-2">
                            <input ref="fileInput" type="file" accept="image/*" class="hidden" @change="uploadWallpaper" />
                            <Button size="sm" variant="outline" @click="fileInput?.click()"> <Upload class="mr-2 h-4 w-4" /> 上传壁纸 </Button>
                            <Button v-if="wallpaper" size="sm" variant="destructive" @click="removeWallpaper">
                                <Trash2 class="mr-2 h-4 w-4" /> 移除壁纸
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
