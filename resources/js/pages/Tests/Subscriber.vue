<template>
    <div class="space-y-4 p-6">
        <h1 class="text-xl font-semibold">SDK 测试 - Subscriber</h1>
        <p class="text-sm text-muted-foreground">收到的事件：</p>
        <div class="max-h-96 space-y-2 overflow-auto">
            <div v-for="(evt, index) in events" :key="index" class="rounded border bg-muted/50 p-2 text-sm">
                <pre class="break-all whitespace-pre-wrap">{{ evt }}</pre>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { deskProvide } from '@/lib/deskRpc';
import eventBus from '@/lib/eventBus';
import { onMounted, ref } from 'vue';

const events = ref<string[]>([]);

function record(type: string, payload: any) {
    events.value.unshift(`${type}: ${JSON.stringify(payload)}`);
}

onMounted(() => {
    eventBus.on('sdk:ping' as any, (p: any) => record('sdk:ping', p));
    eventBus.on('sdk:custom' as any, (p: any) => record('sdk:custom', p));
    deskProvide('getTime', () => new Date().toString());
});
</script>
