<template>
    <div class="space-y-4 p-6">
        <h1 class="text-xl font-semibold">SDK 测试 - Publisher</h1>
        <Button @click="sendPing">发送 PING 事件</Button>
        <Button variant="outline" @click="sendCustom">发送 CUSTOM 事件</Button>
        <Button variant="secondary" @click="reqTime">请求时间 (Promise)</Button>
        <p class="text-sm text-muted-foreground">事件将跨 iframe 广播，并在 Subscriber 中显示。</p>
        <div id="log" class="text-sm text-muted-foreground"></div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { deskRequest } from '@/lib/deskRpc';
import eventBus from '@/lib/eventBus';

function sendPing() {
    eventBus.emit('sdk:ping', { from: 'publisher', time: Date.now() });
}

function sendCustom() {
    eventBus.emit('sdk:custom', { msg: 'Hello from Publisher', date: new Date().toLocaleString() });
}

async function reqTime() {
    const log = (txt: string) => {
        const el = document.getElementById('log');
        if (el) el.textContent += txt + '\n';
    };
    try {
        log('请求时间...');
        const resp = await deskRequest('getTime');
        log('收到时间: ' + resp);
    } catch (e: any) {
        log('RPC失败: ' + e.message);
    }
}
</script>
