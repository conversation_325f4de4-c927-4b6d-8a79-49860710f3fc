<script setup lang="ts">
import PodTerminal from '@/components/PodTerminal.vue';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import Base from '@/layouts/Base.vue';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { Pod, Workspace } from '@/types';
import { Head } from '@inertiajs/vue3';
import { Play, Square, Terminal } from 'lucide-vue-next';
import { computed, ref, watch } from 'vue';
import { toast } from 'vue-sonner';

interface Props {
    podName: string;
    workspace: Workspace;
    containerName?: string;
}

const props = defineProps<Props>();

const resourcesStore = useResourcesStore();
const isLoading = computed(() => resourcesStore.isLoading && !resourcesStore.isLoaded);
const pod = computed(() => resourcesStore.getResourceByName('pods', props.podName) as Pod | undefined);

const terminalRef = ref<InstanceType<typeof PodTerminal>>();
const selectedContainer = ref<string>('');
const selectedMode = ref<'shell' | 'attach'>('shell');
const isConnected = ref(false);
const connecting = ref(false);

watch(
    pod,
    (newPod) => {
        if (newPod?.containers.length) {
            if (props.containerName && newPod.containers.some((c) => c.name === props.containerName)) selectedContainer.value = props.containerName;
            else selectedContainer.value = newPod.containers[0].name;
        }
    },
    { immediate: true },
);

const connectTerminal = async () => {
    if (!terminalRef.value || !selectedContainer.value) return;

    try {
        connecting.value = true;
        await terminalRef.value.connect();
    } catch (error) {
        console.error('Failed to connect terminal:', error);
        toast.error('连接失败', {
            description: '无法连接到终端。',
        });
    } finally {
        connecting.value = false;
    }
};

const disconnectTerminal = () => {
    if (terminalRef.value) {
        terminalRef.value.disconnect();
    }
};

const onConnectionStatusChanged = (status: string) => {
    isConnected.value = status === 'connected';
    connecting.value = status === 'connecting';
};
</script>

<template>
    <Head :title="`终端 - ${podName}`" />
    <Base>
        <div class="flex h-screen w-screen flex-col bg-black">
            <!-- Connection Bar -->
            <div v-if="!isConnected" class="absolute inset-x-0 top-0 z-20 flex h-screen items-center justify-center bg-black/80 backdrop-blur-sm">
                <div class="w-full max-w-md rounded-lg border bg-background p-8 text-center shadow-lg">
                    <Terminal class="mx-auto mb-4 h-12 w-12" />
                    <h2 class="text-2xl font-bold">连接到 Pod 终端</h2>
                    <p class="mb-6 text-muted-foreground">{{ podName }}</p>

                    <div v-if="isLoading" class="space-y-4">
                        <Skeleton class="h-10 w-full" />
                        <Skeleton class="h-10 w-full" />
                        <Skeleton class="h-10 w-full" />
                    </div>

                    <div v-if="!isLoading && pod" class="space-y-4">
                        <div class="space-y-2 text-left">
                            <label class="text-sm font-medium">容器</label>
                            <Select v-model="selectedContainer" :disabled="isConnected || pod.containers.length === 0">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择容器" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="container in pod.containers" :key="container.name" :value="container.name">
                                        {{ container.name }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div class="space-y-2 text-left">
                            <label class="text-sm font-medium">模式</label>
                            <Select v-model="selectedMode" :disabled="isConnected">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择模式" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="shell">进入 Shell</SelectItem>
                                    <SelectItem value="attach">连接到容器</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <Button class="w-full" size="lg" @click="connectTerminal" :disabled="!selectedContainer || connecting">
                            <Play class="mr-2 h-5 w-5" />
                            {{ connecting ? '连接中...' : '连接' }}
                        </Button>
                    </div>
                    <div v-if="!isLoading && !pod" class="text-destructive">无法加载 Pod 详情，请返回重试。</div>
                </div>
            </div>

            <div v-if="isConnected" class="flex flex-none items-center justify-between border-b border-gray-800 bg-black px-4 py-2 text-white">
                <div class="flex items-center gap-3">
                    <Terminal class="h-5 w-5 text-green-400" />
                    <div class="flex items-baseline gap-2">
                        <span class="font-medium">{{ podName }}</span>
                        <span class="text-sm text-muted-foreground">{{ selectedContainer }}</span>
                    </div>
                </div>
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2">
                        <div class="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
                        <span class="text-sm font-medium text-green-400">已连接</span>
                    </div>
                    <Button variant="ghost" size="sm" @click="disconnectTerminal" class="hover:bg-gray-800 hover:text-white">
                        <Square class="mr-2 h-4 w-4" />
                        断开连接
                    </Button>
                </div>
            </div>

            <!-- Terminal -->
            <div class="min-h-0 flex-1">
                <PodTerminal
                    ref="terminalRef"
                    :pod-name="podName"
                    :container-name="selectedContainer"
                    :mode="selectedMode"
                    :auto-connect="false"
                    :show-status-bar="false"
                    class="h-full w-full"
                    @connection-status-changed="onConnectionStatusChanged"
                />
            </div>
        </div>
    </Base>
</template>
