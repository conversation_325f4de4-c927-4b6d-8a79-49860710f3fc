<script setup lang="ts">
import ContainerStatusGrid from '@/components/ContainerStatusGrid.vue';
import PodCommandExecutor from '@/components/PodCommandExecutor.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import eventBus from '@/lib/eventBus';
import { formatPodStatus } from '@/lib/formatK8sStatus';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { EnrichedPod, Pod, PodWarningStatus, Workspace } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import {
    AlertCircle,
    AlertTriangle,
    CheckCircle2,
    Clock,
    FileText,
    MoreHorizontal,
    RefreshCw,
    Settings,
    Terminal,
    Trash2,
    XCircle,
    Zap,
} from 'lucide-vue-next';
import { computed, onUnmounted, ref } from 'vue';
import { toast } from 'vue-sonner';
import { route } from 'ziggy-js';

defineProps<{
    workspace: Workspace;
}>();

const loading = ref(false);
const selectedPod = ref<EnrichedPod | null>(null);
const showDeleteDialog = ref(false);
const showCommandDialog = ref(false);
const commandDialogPod = ref<EnrichedPod | null>(null);

const resourcesStore = useResourcesStore();

const podWarnings = ref<Record<string, PodWarningStatus>>({});

const eventTimeRange = ref<number | null>(30); // 默认30分钟
const eventTimeOptions = [
    { label: '最近30分钟', value: 30 },
    { label: '最近1小时', value: 60 },
    { label: '最近6小时', value: 360 },
    { label: '最近24小时', value: 1440 },
    { label: '所有事件', value: null },
];

const enrichedPods = computed<EnrichedPod[]>(() => {
    if (!resourcesStore.collections.pods || resourcesStore.collections.pods.length === 0) return [];

    const parseCpuToMillicore = (cpuStr: string): number => {
        if (!cpuStr) return 0;
        cpuStr = cpuStr.trim();
        if (cpuStr.endsWith('n')) {
            const n = parseFloat(cpuStr.replace('n', ''));
            return n / 1_000_000; // 1m = 1,000,000n
        }
        if (cpuStr.endsWith('m')) {
            return parseFloat(cpuStr.replace('m', ''));
        }
        // 默认为核心数
        return parseFloat(cpuStr) * 1000;
    };

    const parseMemoryToMi = (memStr: string): number => {
        if (!memStr) return 0;
        memStr = memStr.trim();
        if (memStr.endsWith('Ki')) {
            return parseFloat(memStr.replace('Ki', '')) / 1024;
        }
        if (memStr.endsWith('Mi')) {
            return parseFloat(memStr.replace('Mi', ''));
        }
        if (memStr.endsWith('Gi')) {
            return parseFloat(memStr.replace('Gi', '')) * 1024;
        }
        if (memStr.endsWith('K')) {
            return parseFloat(memStr.replace('K', '')) / 1024;
        }
        if (memStr.endsWith('M')) {
            return parseFloat(memStr.replace('M', ''));
        }
        if (memStr.endsWith('G')) {
            return parseFloat(memStr.replace('G', '')) * 1024;
        }
        // bytes
        return parseFloat(memStr) / (1024 * 1024);
    };

    return resourcesStore.collections.pods.map((pod: Pod): EnrichedPod => {
        const podEvents = resourcesStore.collections.events.filter(
            (event) => event.involved_object && event.involved_object.kind === 'Pod' && event.involved_object.name === pod.name,
        );

        const podMetrics = resourcesStore.metrics.pods[pod.name];

        let cpuUsage = '0m';
        let memoryUsage = '0M';

        if (podMetrics && Array.isArray(podMetrics.containers)) {
            const totalCpuMillicore = podMetrics.containers.reduce((sum: number, c: any) => sum + parseCpuToMillicore(c.usage.cpu), 0);
            cpuUsage = `${Math.round(totalCpuMillicore)}m`;

            const totalMemMi = podMetrics.containers.reduce((sum: number, c: any) => sum + parseMemoryToMi(c.usage.memory), 0);
            memoryUsage = totalMemMi >= 1024 ? `${(totalMemMi / 1024).toFixed(1)}Gi` : `${Math.round(totalMemMi)}M`;
        }

        const warningEvents = podEvents.filter((event) => event.type === 'Warning');
        const warningStatus = {
            has_warnings: warningEvents.length > 0,
            warning_events: warningEvents,
            warning_count: warningEvents.length,
        };

        podWarnings.value[pod.name] = warningStatus;

        return {
            name: pod.name,
            namespace: pod.namespace,
            status: pod.status,
            phase: pod.phase,
            pod_ip: pod.pod_ip || null,
            containers: pod.containers,
            conditions: pod.conditions,
            start_time: pod.start_time || null,
            restart_count: pod.restart_count,
            created_at: pod.created_at || null,
            events: podEvents,
            metrics: { cpu: cpuUsage, memory: memoryUsage },
            warningStatus,
            age: formatAge(pod.created_at),
        };
    });
});

const statusSummary = computed(() => {
    const summary = {
        running: 0,
        pending: 0,
        failed: 0,
        succeeded: 0,
        unknown: 0,
        warnings: 0,
        total: enrichedPods.value.length,
    };

    enrichedPods.value.forEach((pod) => {
        const status = pod.status.toLowerCase();
        if (status === 'running') summary.running++;
        else if (status === 'pending') summary.pending++;
        else if (status === 'failed') summary.failed++;
        else if (status === 'succeeded') summary.succeeded++;
        else summary.unknown++;

        if (pod.warningStatus.has_warnings) summary.warnings++;
    });

    return summary;
});

const handleEventTimeRangeChange = async (value: any) => {
    let newRange: number | null;
    if (value === 'null' || value === null) {
        newRange = null;
    } else {
        newRange = parseInt(String(value));
    }

    eventTimeRange.value = newRange;

    toast.promise(resourcesStore.fetchEvents(newRange), {
        loading: '正在加载事件...',
        success: '事件加载成功',
        error: '事件加载失败',
    });
};

const formatAge = (createdAt?: string): string => {
    if (!createdAt) return 'Unknown';

    const now = new Date();
    const created = new Date(createdAt);
    const diffMs = now.getTime() - created.getTime();

    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays > 0) return `${diffDays}d`;
    if (diffHours > 0) return `${diffHours}h`;
    if (diffMinutes > 0) return `${diffMinutes}m`;
    return '<1m';
};

const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'default';
        case 'pending':
            return 'secondary';
        case 'succeeded':
            return 'default';
        case 'failed':
            return 'destructive';
        case 'unknown':
            return 'outline';
        default:
            return 'secondary';
    }
};

const getPhaseIcon = (phase: string) => {
    switch (phase.toLowerCase()) {
        case 'running':
            return CheckCircle2;
        case 'pending':
            return Clock;
        case 'succeeded':
            return CheckCircle2;
        case 'failed':
            return XCircle;
        case 'unknown':
            return AlertCircle;
        default:
            return AlertCircle;
    }
};

const getPhaseClass = (phase: string) => {
    switch (phase.toLowerCase()) {
        case 'running':
            return 'text-green-500';
        case 'pending':
            return 'text-yellow-500 animate-pulse';
        case 'succeeded':
            return 'text-blue-500';
        case 'failed':
            return 'text-red-500';
        case 'unknown':
            return 'text-gray-500';
        default:
            return 'text-gray-500';
    }
};

const getWarningIcon = (pod: EnrichedPod) => {
    if (!pod.warningStatus.has_warnings) return null;
    return AlertTriangle;
};

const confirmDeletePod = (pod: EnrichedPod) => {
    selectedPod.value = pod;
    showDeleteDialog.value = true;
};

const deletePod = async () => {
    if (!selectedPod.value) return;
    loading.value = true;
    try {
        await axios.delete(`/api/pods/${selectedPod.value.name}`);
        toast.success('成功', { description: `Pod "${selectedPod.value.name}" 已被删除。` });
    } catch (error) {
        console.error('删除 Pod 失败:', error);
        toast.error('删除 Pod 失败');
    } finally {
        loading.value = false;
        showDeleteDialog.value = false;
    }
};

const restartPod = async (podName: string) => {
    loading.value = true;
    try {
        await axios.post(`/api/pods/${podName}/restart`);
        toast.success('成功', { description: `Pod "${podName}" 正在重启。` });
    } catch (error) {
        console.error('重启 Pod 失败:', error);
        toast.error('重启 Pod 失败');
    } finally {
        loading.value = false;
    }
};

const openPersistentTerminal = (pod: EnrichedPod) => {
    const defaultContainer = pod.containers.length > 0 ? pod.containers[0].name : undefined;
    if (!defaultContainer) {
        toast.error('无法打开终端', { description: `Pod "${pod.name}" 中没有可用的容器。` });
        return;
    }
    eventBus.emit('terminal:open', {
        podName: pod.name,
        containerName: defaultContainer,
        mode: 'shell',
    });
};

const openCommandDialog = (pod: EnrichedPod) => {
    commandDialogPod.value = pod;
    showCommandDialog.value = true;
};

const refreshAllData = () => {
    toast.promise(resourcesStore.fetchAllResources(), {
        loading: '正在刷新数据...',
        success: '数据刷新成功',
        error: '数据刷新失败',
    });
};

const onResourceRefreshComplete = () => {
    //
};

const onResourceRefreshError = (error: any) => {
    toast.error('资源刷新失败', {
        description: error.message,
    });
};

eventBus.on('resource:refresh:complete', onResourceRefreshComplete);
eventBus.on('resource:refresh:error', onResourceRefreshError);

onUnmounted(() => {
    eventBus.off('resource:refresh:complete', onResourceRefreshComplete);
    eventBus.off('resource:refresh:error', onResourceRefreshError);
});
</script>
<template>
    <AppLayout>
        <Head title="Pod 管理" />

        <div class="p-4">
            <!-- Header -->
            <div class="flex items-start justify-between">
                <div>
                    <h1 class="text-2xl font-bold">Pod 管理</h1>
                    <p class="mt-1 text-sm text-gray-500">
                        查看和管理工作空间中所有的 Pods。
                        <Link :href="route('dashboard')" class="text-blue-500 hover:underline">返回仪表盘</Link>
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <Select :model-value="eventTimeRange?.toString() || 'null'" @update:model-value="handleEventTimeRangeChange">
                        <SelectTrigger class="w-[180px]">
                            <SelectValue placeholder="选择时间范围" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem v-for="option in eventTimeOptions" :key="option.value ?? 'all'" :value="option.value?.toString() ?? 'null'">
                                {{ option.label }}
                            </SelectItem>
                        </SelectContent>
                    </Select>
                    <Button variant="outline" :disabled="resourcesStore.isLoading" @click="refreshAllData">
                        <RefreshCw :class="['mr-2 h-4 w-4', { 'animate-spin': resourcesStore.isLoading }]" />
                        刷新
                    </Button>
                </div>
            </div>

            <!-- 状态统计 -->
            <div class="mt-4 grid grid-cols-2 gap-4 md:grid-cols-6">
                <div class="rounded-lg border bg-card p-4 text-card-foreground">
                    <div class="text-sm font-medium text-muted-foreground">总计</div>
                    <div class="text-2xl font-bold">{{ statusSummary.total }}</div>
                </div>
                <div class="rounded-lg border bg-card p-4 text-card-foreground">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-muted-foreground">运行中</span>
                        <CheckCircle2 class="h-4 w-4 text-green-500" />
                    </div>
                    <div class="text-2xl font-bold">{{ statusSummary.running }}</div>
                </div>
                <div class="rounded-lg border bg-card p-4 text-card-foreground">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-muted-foreground">等待中</span>
                        <Clock class="h-4 w-4 text-yellow-500" />
                    </div>
                    <div class="text-2xl font-bold">{{ statusSummary.pending }}</div>
                </div>
                <div class="rounded-lg border bg-card p-4 text-card-foreground">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-muted-foreground">已失败</span>
                        <XCircle class="h-4 w-4 text-red-500" />
                    </div>
                    <div class="text-2xl font-bold">{{ statusSummary.failed }}</div>
                </div>
                <div class="rounded-lg border bg-card p-4 text-card-foreground">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-muted-foreground">已成功</span>
                        <CheckCircle2 class="h-4 w-4 text-blue-500" />
                    </div>
                    <div class="text-2xl font-bold">{{ statusSummary.succeeded }}</div>
                </div>
                <div class="rounded-lg border bg-card p-4 text-card-foreground">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-muted-foreground">警告</span>
                        <AlertTriangle class="h-4 w-4 text-orange-500" />
                    </div>
                    <div class="text-2xl font-bold">{{ statusSummary.warnings }}</div>
                </div>
            </div>

            <!-- Pods List -->
            <div class="mt-6">
                <div v-if="resourcesStore.isLoading && !resourcesStore.isLoaded" class="text-center">
                    <p class="py-12 text-lg text-gray-500">正在加载 Pods...</p>
                </div>
                <div v-else-if="enrichedPods.length === 0" class="text-center">
                    <p class="py-12 text-lg text-gray-500">此工作空间中没有 Pods。</p>
                </div>
                <div v-else class="space-y-4">
                    <div
                        v-for="pod in enrichedPods"
                        :key="pod.name"
                        class="overflow-hidden rounded-lg border bg-card text-card-foreground transition-shadow hover:shadow-md"
                    >
                        <div class="flex items-center justify-between p-4">
                            <div class="flex min-w-0 items-center space-x-4">
                                <component :is="getPhaseIcon(pod.phase)" :class="['h-6 w-6 shrink-0', getPhaseClass(pod.phase)]" />
                                <div class="min-w-0">
                                    <div class="flex items-center space-x-2">
                                        <Link :href="route('pods.show', pod.name)" class="truncate font-semibold text-primary hover:underline">
                                            {{ pod.name }}
                                        </Link>
                                        <Badge :variant="getStatusBadgeVariant(pod.status)">
                                            {{ formatPodStatus(pod.status) }}
                                        </Badge>
                                        <TooltipProvider v-if="getWarningIcon(pod)">
                                            <Tooltip>
                                                <TooltipTrigger>
                                                    <AlertTriangle class="h-5 w-5 text-orange-500" />
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>{{ pod.warningStatus.warning_count }} 条警告</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>
                                    <p class="mt-1 text-sm text-muted-foreground">{{ pod.namespace }} / {{ pod.pod_ip }} / {{ pod.age }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="icon">
                                            <MoreHorizontal class="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem @click="router.visit(route('pods.show', pod.name))">
                                            <Settings class="mr-2 h-4 w-4" />
                                            <span>查看详情</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="router.visit(route('pods.logs', pod.name))">
                                            <FileText class="mr-2 h-4 w-4" />
                                            <span>查看日志</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem @click="openCommandDialog(pod)">
                                            <Zap class="mr-2 h-4 w-4" />
                                            <span>执行命令</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="openPersistentTerminal(pod)">
                                            <Terminal class="mr-2 h-4 w-4" />
                                            <span>持久化终端</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem @click="restartPod(pod.name)">
                                            <RefreshCw class="mr-2 h-4 w-4" />
                                            <span>重启</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="confirmDeletePod(pod)" class="text-red-500 focus:text-red-500">
                                            <Trash2 class="mr-2 h-4 w-4" />
                                            <span>删除</span>
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </div>
                        <Separator />
                        <div class="grid grid-cols-1 gap-4 p-4 md:grid-cols-3">
                            <div>
                                <h4 class="mb-2 text-sm font-semibold">容器状态</h4>
                                <ContainerStatusGrid :containers="pod.containers" />
                            </div>
                            <div>
                                <h4 class="mb-2 text-sm font-semibold">资源使用</h4>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center space-x-2">
                                        <Zap class="h-4 w-4 text-blue-500" />
                                        <span class="text-sm">
                                            <span v-if="resourcesStore.isLoading" class="text-muted-foreground">...</span>
                                            <span v-else>{{ pod.metrics.cpu }}</span>
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <Settings class="h-4 w-4 text-purple-500" />
                                        <span class="text-sm">
                                            <span v-if="resourcesStore.isLoading" class="text-muted-foreground">...</span>
                                            <span v-else>{{ pod.metrics.memory }}</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-2 text-sm font-semibold">最近事件</h4>
                                <div v-if="pod.events.length > 0" class="max-h-24 space-y-1 overflow-y-auto">
                                    <div v-for="(event, index) in pod.events" :key="index" class="flex items-center text-xs">
                                        <AlertTriangle v-if="event.type === 'Warning'" class="mr-1 h-3 w-3 shrink-0 text-orange-500" />
                                        <span class="mr-2 text-muted-foreground" v-if="event.last_timestamp">{{
                                            formatAge(event.last_timestamp)
                                        }}</span>
                                        <span class="truncate">{{ event.message }}</span>
                                    </div>
                                </div>
                                <p v-else class="text-xs text-muted-foreground">没有事件</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dialogs -->
        <Dialog :open="showDeleteDialog" @update:open="showDeleteDialog = $event">
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>确认删除</DialogTitle>
                    <DialogDescription>
                        您确定要删除 Pod
                        <strong>{{ selectedPod?.name }}</strong
                        >吗？此操作无法撤销。
                    </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <Button variant="outline" @click="showDeleteDialog = false">取消</Button>
                    <Button variant="destructive" @click="deletePod" :disabled="loading">
                        <RefreshCw v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
                        删除
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>

        <Dialog :open="showCommandDialog" @update:open="showCommandDialog = $event">
            <DialogContent class="max-w-4xl">
                <DialogHeader>
                    <DialogTitle>在 {{ commandDialogPod?.name }} 中执行命令</DialogTitle>
                </DialogHeader>
                <div v-if="commandDialogPod">
                    <PodCommandExecutor
                        :pod-name="commandDialogPod.name"
                        :containers="commandDialogPod.containers"
                        v-model:modelValue="showCommandDialog"
                    />
                </div>
            </DialogContent>
        </Dialog>
    </AppLayout>
</template>
