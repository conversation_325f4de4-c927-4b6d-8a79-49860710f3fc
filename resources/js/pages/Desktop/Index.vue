<template>
    <DesktopToolbar @toggle-launchpad="showLaunchpad = !showLaunchpad" />

    <!-- 启动台 -->
    <div v-if="showLaunchpad" class="fixed inset-0 z-20 mt-10 overflow-y-auto bg-background/90 p-10 backdrop-blur">
        <div class="grid gap-8 md:grid-cols-8 lg:grid-cols-10">
            <div
                v-for="app in apps"
                :key="app.id + '-launch'"
                class="flex w-24 cursor-pointer flex-col items-center gap-3"
                @dblclick="handleIconDblClick(app)"
            >
                <component :is="getIcon(app.icon)" class="h-10 w-10 text-primary" />
                <span class="text-center text-xs">{{ app.name }}</span>
            </div>
        </div>
    </div>

    <!-- 桌面背景 -->
    <div class="relative h-screen w-screen overflow-hidden pt-10" :style="wallpaperStyle">
        <!-- 桌面图标（PC） -->
        <div v-if="!isMobile" class="grid h-full w-full content-start justify-start gap-6 p-8 md:grid-cols-8 lg:grid-cols-10">
            <div
                v-for="app in apps"
                :key="app.id"
                :class="[
                    'flex w-20 cursor-pointer flex-col items-center gap-2 select-none',
                    selectedAppId === app.id ? 'rounded bg-muted/50 p-2' : '',
                ]"
                draggable="true"
                @dragstart="handleDragStart($event, app)"
                @dragover="handleDragOver"
                @drop="handleDrop($event, app)"
                @click="handleIconClick(app)"
                @dblclick="handleIconDblClick(app)"
                @contextmenu="showContextMenu($event, app)"
            >
                <component :is="getIcon(app.icon)" class="h-8 w-8 text-primary" />
                <span class="truncate text-center text-xs text-foreground">{{ app.name }}</span>
            </div>
        </div>

        <!-- 应用列表（移动端） -->
        <div v-else class="h-full space-y-2 overflow-y-auto p-4">
            <div
                v-for="app in apps"
                :key="app.id + '-mobile'"
                class="flex cursor-pointer items-center gap-3 rounded-lg p-3 hover:bg-muted"
                @click="handleIconDblClick(app)"
            >
                <component :is="getIcon(app.icon)" class="h-5 w-5 text-primary" />
                <span class="text-sm">{{ app.name }}</span>
            </div>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div
        v-if="contextMenu.visible && contextMenu.app"
        :style="{ top: contextMenu.y + 'px', left: contextMenu.x + 'px' }"
        class="fixed z-40 min-w-[120px] rounded-md border bg-popover p-1 text-sm shadow-md"
    >
        <div class="px-3 py-2 text-muted-foreground">{{ contextMenu.app.name }}</div>
        <UIButton
            variant="ghost"
            size="sm"
            class="w-full justify-start px-3"
            @click="
                openApp(contextMenu.app);
                hideContextMenu();
            "
        >
            <Info class="mr-2 h-4 w-4" /> 打开
        </UIButton>
        <UIButton variant="ghost" size="sm" class="w-full justify-start px-3" @click="hideContextMenu()">
            <Trash2 class="mr-2 h-4 w-4" /> 移除
        </UIButton>
    </div>
</template>

<script setup lang="ts">
import DesktopToolbar from '@/components/DesktopToolbar.vue';
import { Button as UIButton } from '@/components/ui/button';
import axios from '@/lib/axios';
import eventBus from '@/lib/eventBus';
import { useDesktopStore, type DesktopApp } from '@/stores/desktopStore';
import { usePage } from '@inertiajs/vue3';
import * as LucideIcons from 'lucide-vue-next';
import { Image as ImageIcon, Info, Trash2 } from 'lucide-vue-next';
import { computed, onMounted, ref } from 'vue';

defineOptions({ inheritAttrs: false });

interface PageProps {
    apps: DesktopApp[];
    wallpaper: string | null;
}

// 获取 Inertia props
const page = usePage<PageProps>();
const desktopProps = computed(() => page.props as unknown as PageProps);

const desktopStore = useDesktopStore();

// 初始化 store
onMounted(() => {
    desktopStore.setApps(desktopProps.value.apps);
    desktopStore.setWallpaper(desktopProps.value.wallpaper || null);
});

const apps = computed(() => desktopStore.apps);
const wallpaper = computed(() => desktopStore.wallpaper);

const wallpaperStyle = computed(() => {
    return wallpaper.value
        ? {
              backgroundImage: `url(${wallpaper.value})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
          }
        : {};
});

// 移动端检测
const isMobile = ref(false);

const checkMobile = () => {
    isMobile.value = window.innerWidth < 768;
};

onMounted(() => {
    checkMobile();
    window.addEventListener('resize', checkMobile);
    eventBus.on('desktop:wallpaper-updated' as any, (payload: any) => {
        desktopStore.setWallpaper(payload.url);
    });
});

// state for selection & context
const selectedAppId = ref<string | null>(null);
const showLaunchpad = ref(false);

// click handlers
const openApp = (app: DesktopApp) => {
    eventBus.emit('window:open', {
        type: app.type,
        data: {
            url: app.url,
            title: app.name,
        },
        options: {
            icon: app.icon,
        },
    });
};

const handleIconClick = (app: DesktopApp) => {
    selectedAppId.value = app.id;
};

const handleIconDblClick = (app: DesktopApp) => {
    openApp(app);
    selectedAppId.value = null;
};

// context menu position & state
const contextMenu = ref<{ visible: boolean; x: number; y: number; app: DesktopApp | null }>({
    visible: false,
    x: 0,
    y: 0,
    app: null,
});

const showContextMenu = (event: MouseEvent, app: DesktopApp) => {
    event.preventDefault();
    contextMenu.value = { visible: true, x: event.clientX, y: event.clientY, app };
};

const hideContextMenu = () => (contextMenu.value.visible = false);

// Drag & drop order
const handleDragStart = (event: DragEvent, app: DesktopApp) => {
    event.dataTransfer?.setData('text/plain', app.id);
};

const handleDrop = (event: DragEvent, targetApp: DesktopApp) => {
    event.preventDefault();
    const draggedId = event.dataTransfer?.getData('text/plain');
    if (!draggedId || draggedId === targetApp.id) return;

    const draggedIndex = apps.value.findIndex((a) => a.id === draggedId);
    const targetIndex = apps.value.findIndex((a) => a.id === targetApp.id);
    if (draggedIndex === -1 || targetIndex === -1) return;

    // reorder array
    const updated = [...apps.value];
    const [draggedItem] = updated.splice(draggedIndex, 1);
    updated.splice(targetIndex, 0, draggedItem);
    desktopStore.setApps(updated);

    // save order to server
    const order = updated.map((a) => a.id);
    axios.post('/desktop/icon-order', { order }).catch(() => {});
};

const handleDragOver = (e: DragEvent) => e.preventDefault();

// 动态获取图标组件（Lucide 图标）
const getIcon = (iconName: string): any => {
    return (LucideIcons as any)[iconName] || ImageIcon;
};

// watch for page unload to hide context menu
onMounted(() => {
    window.addEventListener('click', hideContextMenu);
});

// template adjustments to add toolbar, selection class, events, launchpad overlay
</script>

<style scoped>
/* 隐藏滚动条，保持桌面整洁 */
::-webkit-scrollbar {
    display: none;
}
</style>
