<script setup lang="ts">
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { Workspace } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { Minus, MoreHorizontal, Plus, TrendingDown, Zap } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    workspace: Workspace;
}

defineProps<Props>();

const resourcesStore = useResourcesStore();
const hpas = computed(() => resourcesStore.collections.horizontalpodautoscalers);
const loading = computed(() => resourcesStore.isLoading && !resourcesStore.isLoaded);
const deleting = ref<string | null>(null);
const showDeleteDialog = ref(false);
const hpaToDelete = ref<string | null>(null);

const fetchHPAs = async () => {
    try {
        await resourcesStore.fetchAllResources();
    } catch (error) {
        console.error('获取 HPA 列表失败:', error);
        toast.error('获取 HPA 列表失败');
    }
};

const openDeleteDialog = (name: string) => {
    hpaToDelete.value = name;
    showDeleteDialog.value = true;
};

const deleteHPA = async () => {
    if (!hpaToDelete.value) return;

    try {
        deleting.value = hpaToDelete.value;
        await axios.delete(`/api/hpas/${hpaToDelete.value}`);
        toast.success('HPA 删除成功');
        resourcesStore.removeResource('horizontalpodautoscalers', hpaToDelete.value!);
    } catch (error) {
        console.error('删除 HPA 失败:', error);
        toast.error('删除 HPA 失败');
    } finally {
        deleting.value = null;
        showDeleteDialog.value = false;
        hpaToDelete.value = null;
    }
};

const getStatusBadgeVariant = (status: string) => {
    switch (status) {
        case 'Active':
            return 'default';
        case 'Limited':
            return 'secondary';
        case 'Inactive':
            return 'outline';
        case 'Unable to Scale':
            return 'destructive';
        default:
            return 'outline';
    }
};

const getStatusIcon = (status: string) => {
    switch (status) {
        case 'Active':
            return Zap;
        case 'Limited':
            return Minus;
        case 'Inactive':
            return TrendingDown;
        case 'Unable to Scale':
            return TrendingDown;
        default:
            return Minus;
    }
};

const formatLastScaleTime = (time: string | null | undefined) => {
    if (!time) return '-';
    return new Date(time).toLocaleString('zh-CN');
};

onMounted(async () => {
    if (!resourcesStore.isLoaded) {
        await fetchHPAs();
    }
});
</script>

<template>
    <Head title="弹性伸缩" />

    <AppLayout>
        <div class="space-y-6 p-4">
            <!-- 页面标题和操作 -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">弹性伸缩</h1>
                    <p class="text-muted-foreground">根据负载自动扩缩容工作负载</p>
                </div>
                <Link :href="route('hpas.create')">
                    <Button>
                        <Plus class="mr-2 h-4 w-4" />
                        创建
                    </Button>
                </Link>
            </div>

            <!-- HPA 列表 -->
            <Card>
                <CardHeader>
                    <CardTitle>弹性伸缩列表</CardTitle>
                </CardHeader>
                <CardContent>
                    <div v-if="loading" class="space-y-3">
                        <div v-for="i in 3" :key="i" class="flex items-center space-x-4">
                            <Skeleton class="h-4 w-[250px]" />
                            <Skeleton class="h-4 w-[200px]" />
                            <Skeleton class="h-4 w-[150px]" />
                            <Skeleton class="h-4 w-[100px]" />
                        </div>
                    </div>

                    <div v-else-if="hpas.length === 0" class="py-8 text-center">
                        <div class="text-muted-foreground">
                            <Zap class="mx-auto mb-4 h-12 w-12 opacity-50" />
                            <h3 class="mb-2 text-lg font-semibold">暂无弹性伸缩</h3>
                            <p class="mb-4">您还没有创建任何弹性伸缩</p>
                            <Link :href="route('hpas.create')">
                                <Button>
                                    <Plus class="mr-2 h-4 w-4" />
                                    创建第一个弹性伸缩
                                </Button>
                            </Link>
                        </div>
                    </div>

                    <div v-else>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>名称</TableHead>
                                    <TableHead>目标</TableHead>
                                    <TableHead>副本数</TableHead>
                                    <TableHead>状态</TableHead>
                                    <TableHead>CPU 使用率</TableHead>
                                    <TableHead>内存使用率</TableHead>
                                    <TableHead>最后扩缩容时间</TableHead>
                                    <TableHead>操作</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="hpa in hpas" :key="hpa.name">
                                    <TableCell class="font-medium">
                                        <Link :href="route('hpas.show', hpa.name)" class="hover:underline">
                                            {{ hpa.name }}
                                        </Link>
                                    </TableCell>
                                    <TableCell>
                                        <div class="space-y-1">
                                            <div class="font-medium">{{ hpa.scale_target_ref.kind }}/{{ hpa.scale_target_ref.name }}</div>
                                            <div class="text-sm text-muted-foreground">{{ hpa.min_replicas }}-{{ hpa.max_replicas }} 副本</div>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div class="space-y-1">
                                            <div>当前: {{ hpa.current_replicas || '-' }}</div>
                                            <div class="text-sm text-muted-foreground">期望: {{ hpa.desired_replicas || '-' }}</div>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <Badge :variant="getStatusBadgeVariant(hpa.status)">
                                            <component :is="getStatusIcon(hpa.status)" class="mr-1 h-3 w-3" />
                                            {{ hpa.status }}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>
                                        <div v-if="hpa.current_cpu_utilization !== null" class="space-y-1">
                                            <div>{{ hpa.current_cpu_utilization }}%</div>
                                            <div v-if="hpa.target_cpu_utilization" class="text-sm text-muted-foreground">
                                                目标: {{ hpa.target_cpu_utilization }}%
                                            </div>
                                        </div>
                                        <div v-else class="text-muted-foreground">-</div>
                                    </TableCell>
                                    <TableCell>
                                        <div v-if="hpa.current_memory_utilization !== null" class="space-y-1">
                                            <div>{{ hpa.current_memory_utilization }}%</div>
                                            <div v-if="hpa.target_memory_utilization" class="text-sm text-muted-foreground">
                                                目标: {{ hpa.target_memory_utilization }}%
                                            </div>
                                        </div>
                                        <div v-else class="text-muted-foreground">-</div>
                                    </TableCell>
                                    <TableCell>
                                        <div class="text-sm">
                                            {{ formatLastScaleTime(hpa.last_scale_time) }}
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger as-child>
                                                <Button variant="ghost" class="h-8 w-8 p-0">
                                                    <MoreHorizontal class="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem as-child>
                                                    <Link :href="route('hpas.show', hpa.name)"> 查看详情 </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem as-child>
                                                    <Link :href="route('hpas.edit', hpa.name)"> 编辑 </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem class="text-destructive focus:text-destructive" @click="openDeleteDialog(hpa.name)">
                                                    删除
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </CardContent>
            </Card>

            <!-- 删除确认对话框 -->
            <AlertDialog v-model:open="showDeleteDialog">
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>确认删除</AlertDialogTitle>
                        <AlertDialogDescription> 您确定要删除 HPA "{{ hpaToDelete }}" 吗？此操作无法撤销。 </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction
                            @click="deleteHPA"
                            :disabled="deleting === hpaToDelete"
                            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {{ deleting === hpaToDelete ? '删除中...' : '删除' }}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    </AppLayout>
</template>
