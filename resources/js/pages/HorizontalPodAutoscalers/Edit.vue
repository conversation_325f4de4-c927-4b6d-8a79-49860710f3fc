<template>
    <AppLayout>
        <Head title="编辑弹性伸缩" />

        <div class="space-y-6 p-4">
            <div class="flex items-center space-x-4">
                <Button variant="ghost" @click="router.visit(route('hpas.show', { hpa: hpaName }))">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回详情
                </Button>
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">编辑弹性伸缩</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">修改 {{ hpaName }} 的配置</p>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="space-y-6">
                <Card>
                    <CardContent class="p-6">
                        <div class="space-y-4">
                            <Skeleton class="h-4 w-1/4" />
                            <Skeleton class="h-4 w-1/2" />
                            <Skeleton class="h-4 w-3/4" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Error State -->
            <Card v-else-if="error" class="border-red-200 dark:border-red-800">
                <CardContent class="p-6 text-center">
                    <div class="text-red-600 dark:text-red-400">
                        <AlertCircle class="mx-auto mb-4 h-12 w-12" />
                        <p class="text-lg font-medium">加载失败</p>
                        <p class="text-sm">{{ error }}</p>
                        <Button class="mt-4" @click="loadHPA">重试</Button>
                    </div>
                </CardContent>
            </Card>

            <!-- Edit Form -->
            <div v-else-if="isFormReady" class="grid grid-cols-1 gap-6 lg:grid-cols-3">
                <!-- 主要配置区域 -->
                <div class="space-y-6 lg:col-span-2">
                    <!-- 基本配置 -->
                    <Card>
                        <CardHeader>
                            <CardTitle>基本配置</CardTitle>
                            <CardDescription>修改弹性伸缩的基本信息和目标工作负载</CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-6">
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div>
                                    <Label for="name">弹性伸缩名称 *</Label>
                                    <Input id="name" v-model="form.name" disabled class="mt-1 bg-gray-50 dark:bg-gray-800" />
                                    <p class="mt-1 text-xs text-gray-500">弹性伸缩名称创建后不可修改</p>
                                </div>
                                <div>
                                    <Label for="target-workload">目标工作负载 *</Label>
                                    <WorkloadSelector v-model="selectedWorkload" @update:model-value="onWorkloadChange" />
                                </div>
                            </div>

                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div>
                                    <Label for="min-replicas">最小副本数 *</Label>
                                    <Input id="min-replicas" v-model.number="form.min_replicas" type="number" min="1" max="100" class="mt-1" />
                                </div>
                                <div>
                                    <Label for="max-replicas">最大副本数 *</Label>
                                    <Input id="max-replicas" v-model.number="form.max_replicas" type="number" min="1" max="1000" class="mt-1" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- 度量指标配置 -->
                    <Card>
                        <CardHeader>
                            <CardTitle>度量指标</CardTitle>
                            <CardDescription>配置触发扩缩容的度量指标</CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-6">
                            <div v-for="(metric, index) in form.metrics" :key="index" class="space-y-4 rounded-lg border p-4">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium">度量指标 {{ index + 1 }}</h4>
                                    <Button v-if="form.metrics.length > 1" variant="outline" size="sm" @click="removeMetric(index)">
                                        <X class="h-4 w-4" />
                                    </Button>
                                </div>

                                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label>度量类型 *</Label>
                                        <Select v-model="metric.type" @update:model-value="onMetricTypeChange(index)">
                                            <SelectTrigger class="mt-1">
                                                <SelectValue placeholder="选择度量类型" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="Resource">资源 (CPU/内存)</SelectItem>
                                                <!-- <SelectItem value="Pods">Pod 度量</SelectItem> -->
                                                <!-- <SelectItem value="Object">对象度量</SelectItem> -->
                                                <!-- <SelectItem value="External">外部度量</SelectItem> -->
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <!-- 资源类型配置 -->
                                    <div v-if="metric.type === 'Resource'">
                                        <Label>资源名称 *</Label>
                                        <Select v-model="metric.resource_name">
                                            <SelectTrigger class="mt-1">
                                                <SelectValue placeholder="选择资源" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="cpu">CPU</SelectItem>
                                                <SelectItem value="memory">内存</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <!-- Pod/Object/External 度量名称 -->
                                    <div v-else>
                                        <Label>度量名称 *</Label>
                                        <Input v-model="metric.metric_name" placeholder="例如：requests_per_second" class="mt-1" />
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <!-- 资源目标类型 -->
                                    <div v-if="metric.type === 'Resource'">
                                        <Label>目标类型 *</Label>
                                        <Select v-model="metric.target_type">
                                            <SelectTrigger class="mt-1">
                                                <SelectValue placeholder="选择目标类型" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="Utilization">使用率 (%)</SelectItem>
                                                <SelectItem value="AverageValue">平均值</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <!-- 目标值 -->
                                    <div>
                                        <Label>目标值 *</Label>
                                        <div class="mt-1 flex items-center space-x-2">
                                            <Input
                                                v-model="metric.target_value"
                                                :placeholder="getTargetValuePlaceholder(metric)"
                                                :type="metric.type === 'Resource' && metric.target_type === 'AverageValue' ? 'number' : 'text'"
                                                :min="
                                                    metric.type === 'Resource' &&
                                                    metric.target_type === 'AverageValue' &&
                                                    metric.resource_name === 'cpu'
                                                        ? 500
                                                        : metric.type === 'Resource' &&
                                                            metric.target_type === 'AverageValue' &&
                                                            metric.resource_name === 'memory'
                                                          ? 512
                                                          : undefined
                                                "
                                                :step="
                                                    metric.type === 'Resource' &&
                                                    metric.target_type === 'AverageValue' &&
                                                    metric.resource_name === 'cpu'
                                                        ? 500
                                                        : metric.type === 'Resource' &&
                                                            metric.target_type === 'AverageValue' &&
                                                            metric.resource_name === 'memory'
                                                          ? 512
                                                          : undefined
                                                "
                                            />
                                            <span class="text-sm text-gray-500">
                                                {{ getTargetValueUnit(metric) }}
                                            </span>
                                        </div>
                                        <p class="mt-1 text-xs text-gray-500">
                                            {{ getTargetValueDescription(metric) }}
                                        </p>
                                    </div>
                                </div>

                                <!-- Object 类型特殊配置 -->
                                <div v-if="metric.type === 'Object'" class="grid grid-cols-1 gap-4 md:grid-cols-3">
                                    <div>
                                        <Label>对象 API 版本 *</Label>
                                        <Input v-model="metric.object_api_version" placeholder="例如：v1" class="mt-1" />
                                    </div>
                                    <div>
                                        <Label>对象类型 *</Label>
                                        <Input v-model="metric.object_kind" placeholder="例如：Service" class="mt-1" />
                                    </div>
                                    <div>
                                        <Label>对象名称 *</Label>
                                        <Input v-model="metric.object_name" placeholder="例如：my-service" class="mt-1" />
                                    </div>
                                </div>
                            </div>

                            <Button variant="outline" @click="addMetric">
                                <Plus class="mr-2 h-4 w-4" />
                                添加度量指标
                            </Button>
                        </CardContent>
                    </Card>

                    <!-- 行为配置 (高级) -->
                    <Card>
                        <CardHeader>
                            <CardTitle>行为配置 (可选)</CardTitle>
                            <CardDescription>配置扩缩容的行为策略</CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-6">
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <!-- 扩容行为 -->
                                <div>
                                    <Label>扩容稳定窗口 (秒)</Label>
                                    <Input
                                        v-model.number="form.behavior!.scale_up.stabilization_window_seconds"
                                        type="number"
                                        min="0"
                                        max="3600"
                                        placeholder="0"
                                        class="mt-1"
                                    />
                                    <p class="mt-1 text-xs text-gray-500">扩容后等待指标稳定的时间</p>
                                </div>

                                <!-- 缩容行为 -->
                                <div>
                                    <Label>缩容稳定窗口 (秒)</Label>
                                    <Input
                                        v-model.number="form.behavior!.scale_down.stabilization_window_seconds"
                                        type="number"
                                        min="0"
                                        max="3600"
                                        placeholder="300"
                                        class="mt-1"
                                    />
                                    <p class="mt-1 text-xs text-gray-500">缩容后等待指标稳定的时间</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <!-- 侧边栏预览 -->
                <div class="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>配置预览</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div>
                                <Label class="text-sm font-medium text-gray-500">弹性伸缩名称</Label>
                                <p class="text-sm">{{ form.name || '未设置' }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500">目标工作负载</Label>
                                <p class="text-sm">{{ selectedWorkload ? `${selectedWorkload.type}/${selectedWorkload.name}` : '未选择' }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500">副本数范围</Label>
                                <p class="text-sm">{{ form.min_replicas }} - {{ form.max_replicas }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500">度量指标数量</Label>
                                <p class="text-sm">{{ form.metrics.length }} 个</p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>
                                <AlertTriangle class="mr-2 inline h-4 w-4 text-yellow-500" />
                                修改提示
                            </CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-3 text-sm">
                            <div class="flex items-start space-x-2">
                                <Info class="mt-0.5 h-4 w-4 flex-shrink-0 text-blue-500" />
                                <p>修改弹性伸缩配置可能会影响当前的扩缩容行为。</p>
                            </div>
                            <div class="flex items-start space-x-2">
                                <AlertTriangle class="mt-0.5 h-4 w-4 flex-shrink-0 text-yellow-500" />
                                <p>修改度量指标时，建议先了解当前工作负载的资源使用情况。</p>
                            </div>
                            <div class="flex items-start space-x-2">
                                <TrendingUp class="mt-0.5 h-4 w-4 flex-shrink-0 text-green-500" />
                                <p>建议在低峰期进行重要配置修改，以减少对业务的影响。</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div v-if="isFormReady" class="flex justify-end space-x-4">
                <Button variant="outline" @click="router.visit(route('hpas.show', { hpa: hpaName }))"> 取消 </Button>
                <Button @click="updateHPA" :disabled="updating || !isFormValid">
                    <Loader2 v-if="updating" class="mr-2 h-4 w-4 animate-spin" />
                    保存修改
                </Button>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import WorkloadSelector from '@/components/selectors/WorkloadSelector.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { CreateHPAData, HorizontalPodAutoscaler, HPAMetric, Workspace } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { AlertCircle, AlertTriangle, ArrowLeft, Info, Loader2, Plus, TrendingUp, X } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, onMounted, reactive, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    hpaName: string;
    workspace: Workspace;
}

const props = defineProps<Props>();

const resourcesStore = useResourcesStore();
const loading = ref(true);
const updating = ref(false);
const error = ref<string | null>(null);
const isFormReady = ref(false);
const selectedWorkload = ref<{ type: string; name: string } | null>(null);

const form = reactive<CreateHPAData>({
    name: '',
    scale_target_ref: {
        kind: '',
        name: '',
        api_version: 'apps/v1',
    },
    target_type: 'Deployment',
    target_name: '',
    min_replicas: 1,
    max_replicas: 10,
    metrics: [],
    behavior: {
        scale_up: {
            stabilization_window_seconds: 0,
        },
        scale_down: {
            stabilization_window_seconds: 300,
        },
    },
});

const isFormValid = computed(() => {
    return (
        form.name &&
        form.target_name &&
        form.metrics.length > 0 &&
        form.metrics.every((metric) => {
            if (metric.type === 'Resource') {
                return metric.resource_name && metric.target_type && metric.target_value;
            } else if (metric.type === 'Object') {
                return metric.metric_name && metric.object_api_version && metric.object_kind && metric.object_name && metric.target_value;
            } else {
                return metric.metric_name && metric.target_value;
            }
        })
    );
});

const loadHPA = async () => {
    try {
        loading.value = true;
        error.value = null;

        if (!resourcesStore.isLoaded) {
            await resourcesStore.fetchAllResources();
        }

        const hpaData = resourcesStore.getResourceByName('horizontalpodautoscalers', props.hpaName) as HorizontalPodAutoscaler | null;

        if (!hpaData) {
            throw new Error('未找到 HPA');
        }

        // 填充表单数据
        form.name = hpaData.name;
        form.target_type = hpaData.scale_target_ref.kind;
        form.target_name = hpaData.scale_target_ref.name;
        form.min_replicas = hpaData.min_replicas;
        form.max_replicas = hpaData.max_replicas;
        form.metrics = transformMetricsForForm(hpaData.metrics);

        // 设置行为配置
        if (hpaData.behavior) {
            if (hpaData.behavior.scaleUp?.stabilizationWindowSeconds !== undefined) {
                form.behavior!.scale_up.stabilization_window_seconds = hpaData.behavior.scaleUp.stabilizationWindowSeconds;
            }
            if (hpaData.behavior.scaleDown?.stabilizationWindowSeconds !== undefined) {
                form.behavior!.scale_down.stabilization_window_seconds = hpaData.behavior.scaleDown.stabilizationWindowSeconds;
            }
        }

        // 设置选中的工作负载
        selectedWorkload.value = {
            type: form.target_type,
            name: form.target_name,
        };

        isFormReady.value = true;
    } catch (err: any) {
        console.error('加载 HPA 详情失败:', err);
        error.value = err.response?.data?.message || '加载 HPA 详情失败';
    } finally {
        loading.value = false;
    }
};

const transformMetricsForForm = (metrics: any[]): HPAMetric[] => {
    return metrics.map((metric) => {
        const formMetric: HPAMetric = {
            type: metric.type,
        };

        if (metric.type === 'Resource') {
            formMetric.resource_name = metric.resource?.name;
            formMetric.target_type = metric.resource?.target?.type;
            if (metric.resource?.target?.averageUtilization) {
                formMetric.target_value = metric.resource.target.averageUtilization.toString();
            } else if (metric.resource?.target?.averageValue) {
                formMetric.target_value = metric.resource.target.averageValue;
            }
        } else if (metric.type === 'Object') {
            formMetric.metric_name = metric.object?.metric?.name;
            formMetric.object_api_version = metric.object?.describedObject?.apiVersion;
            formMetric.object_kind = metric.object?.describedObject?.kind;
            formMetric.object_name = metric.object?.describedObject?.name;
            formMetric.target_value = metric.object?.target?.value;
        } else {
            formMetric.metric_name = metric.metric?.name || metric.pods?.metric?.name || metric.external?.metric?.name;
            formMetric.target_value =
                metric.target?.value || metric.pods?.target?.averageValue || metric.external?.target?.value || metric.external?.target?.averageValue;
        }

        return formMetric;
    });
};

const onWorkloadChange = (workload: { type: string; name: string } | null) => {
    if (workload) {
        form.target_type = workload.type;
        form.target_name = workload.name;
        selectedWorkload.value = workload;
    }
};

const addMetric = () => {
    form.metrics.push({
        type: 'Resource',
        resource_name: 'cpu',
        target_type: 'Utilization',
        target_value: '70',
    });
};

const removeMetric = (index: number) => {
    form.metrics.splice(index, 1);
};

const onMetricTypeChange = (index: number) => {
    const metric = form.metrics[index];
    // 重置度量配置
    if (metric.type === 'Resource') {
        metric.resource_name = 'cpu';
        metric.target_type = 'Utilization';
        metric.target_value = '70';
        delete metric.metric_name;
        delete metric.object_api_version;
        delete metric.object_kind;
        delete metric.object_name;
    } else if (metric.type === 'Object') {
        metric.metric_name = '';
        metric.object_api_version = 'v1';
        metric.object_kind = 'Service';
        metric.object_name = '';
        metric.target_value = '100';
        delete metric.resource_name;
        delete metric.target_type;
    } else {
        metric.metric_name = '';
        metric.target_value = '100';
        delete metric.resource_name;
        delete metric.target_type;
        delete metric.object_api_version;
        delete metric.object_kind;
        delete metric.object_name;
    }
};

const getTargetValuePlaceholder = (metric: HPAMetric) => {
    if (metric.type === 'Resource' && metric.target_type === 'Utilization') {
        return '70';
    } else if (metric.type === 'Resource' && metric.target_type === 'AverageValue') {
        return metric.resource_name === 'memory' ? '512' : '500';
    }
    return '100';
};

const getTargetValueUnit = (metric: HPAMetric) => {
    if (metric.type === 'Resource' && metric.target_type === 'Utilization') {
        return '%';
    } else if (metric.type === 'Resource' && metric.target_type === 'AverageValue') {
        return '单位 MB';
    }
    return '';
};

const getTargetValueDescription = (metric: HPAMetric) => {
    if (metric.type === 'Resource' && metric.target_type === 'Utilization') {
        return `当平均 ${metric.resource_name === 'cpu' ? 'CPU' : '内存'} 使用率超过此值时触发扩容`;
    } else if (metric.type === 'Resource' && metric.target_type === 'AverageValue') {
        const resourceName = metric.resource_name === 'cpu' ? 'CPU' : '内存';
        const requirement = metric.resource_name === 'cpu' ? 'CPU 必须是 500 的倍数，最低 500' : '内存必须是 512 的倍数，最低 512';
        return `当平均 ${resourceName} 使用量超过此值时触发扩容。${requirement}`;
    }
    return '当度量值超过此值时触发扩容';
};

const updateHPA = async () => {
    if (!isFormValid.value) {
        toast.error('请完整填写表单');
        return;
    }

    try {
        updating.value = true;

        // 转换表单数据为 API 格式
        const payload = {
            name: form.name,
            target_type: form.target_type,
            target_name: form.target_name,
            min_replicas: form.min_replicas,
            max_replicas: form.max_replicas,
            metrics: form.metrics,
            behavior: form.behavior,
        };

        await axios.put(`/api/hpas/${props.hpaName}`, payload);
        toast.success('HPA 更新成功');
        router.visit(route('hpas.show', { hpa: props.hpaName }));
    } catch (error: any) {
        console.error('更新 HPA 失败:', error);
        const message = error.response?.data?.message || '更新 HPA 失败';
        toast.error(message);
    } finally {
        updating.value = false;
    }
};

onMounted(() => {
    loadHPA();
});
</script>
