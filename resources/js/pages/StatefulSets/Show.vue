<template>
    <AppLayout>
        <Head :title="`StatefulSet: ${statefulSetName}`" />

        <div class="space-y-6 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <Button variant="ghost" @click="router.visit(route('applications.index'))">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        返回列表
                    </Button>
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ statefulSetName }}
                        </h1>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">有状态应用详情</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <Button variant="outline" @click="showScaleDialog = true">
                        <Expand class="mr-2 h-4 w-4" />
                        扩容
                    </Button>
                    <Button variant="outline" @click="editStatefulSet">
                        <Edit class="mr-2 h-4 w-4" />
                        编辑
                    </Button>
                    <Button variant="destructive" @click="showDeleteDialog = true">
                        <Trash2 class="mr-2 h-4 w-4" />
                        删除
                    </Button>
                </div>
            </div>

            <!-- 使用新的 WorkloadDetails 组件 -->
            <WorkloadDetails :workload-name="statefulSetName" :workload-type="'statefulset'" />

            <!-- 扩容对话框 -->
            <Dialog v-model:open="showScaleDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>扩容有状态应用</DialogTitle>
                        <DialogDescription> 调整 {{ statefulSetName }} 的副本数 </DialogDescription>
                    </DialogHeader>

                    <div class="space-y-4">
                        <div>
                            <Label for="replicas">副本数</Label>
                            <Input id="replicas" v-model.number="scaleForm.replicas" type="number" min="0" max="100" class="mt-1" />
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" @click="showScaleDialog = false">取消</Button>
                        <Button @click="scaleStatefulSet" :disabled="scaling">
                            <Loader2 v-if="scaling" class="mr-2 h-4 w-4 animate-spin" />
                            确认扩容
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 删除确认对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>删除有状态应用</DialogTitle>
                        <DialogDescription> 确定要删除有状态应用 "{{ statefulSetName }}" 吗？此操作不可撤销。 </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false">取消</Button>
                        <Button variant="destructive" @click="deleteStatefulSet" :disabled="deleting">
                            <Loader2 v-if="deleting" class="mr-2 h-4 w-4 animate-spin" />
                            删除
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import WorkloadDetails from '@/components/workloads/WorkloadDetails.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { Workspace } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { ArrowLeft, Edit, Expand, Loader2, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    statefulSetName: string;
    workspace: Workspace;
}

const props = defineProps<Props>();
const workspace = computed(() => props.workspace);

const resourcesStore = useResourcesStore();
const showScaleDialog = ref(false);
const showDeleteDialog = ref(false);
const scaling = ref(false);
const deleting = ref(false);

const scaleForm = ref({
    replicas: 1,
});

// 获取 statefulset 数据用于扩容
const statefulSetData = computed(() => {
    return resourcesStore.collections.statefulsets.find((s) => s.name === props.statefulSetName);
});

// 确保资源已加载
onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }

    // 设置扩容表单的初始值
    if (statefulSetData.value) {
        scaleForm.value.replicas = statefulSetData.value.replicas;
    }
});

const editStatefulSet = () => {
    router.visit(route('statefulsets.edit', { statefulset: props.statefulSetName }));
};

const scaleStatefulSet = async () => {
    if (!statefulSetData.value) return;

    try {
        scaling.value = true;
        await axios.patch(`/api/statefulsets/${props.statefulSetName}/scale`, {
            replicas: scaleForm.value.replicas,
        });

        toast.success('扩容成功');
        showScaleDialog.value = false;
        // 数据将通过 WebSocket 自动更新
    } catch (error) {
        console.error('扩容失败:', error);
        toast.error('扩容失败');
    } finally {
        scaling.value = false;
    }
};

const deleteStatefulSet = async () => {
    if (!statefulSetData.value) return;

    try {
        deleting.value = true;
        await axios.delete(`/api/statefulsets/${props.statefulSetName}`);

        toast.success('删除成功');
        router.visit(route('applications.index'));
    } catch (error) {
        console.error('删除失败:', error);
        toast.error('删除失败');
    } finally {
        deleting.value = false;
        showDeleteDialog.value = false;
    }
};
</script>
