<template>
    <Head :title="`服务详情 - ${serviceName}`" />

    <AppLayout>
        <div class="space-y-6 p-4 md:p-6">
            <!-- 加载状态 -->
            <div v-if="loading" class="py-12 text-center">
                <div class="inline-flex items-center">
                    <Loader2 class="mr-2 h-6 w-6 animate-spin" />
                    加载中...
                </div>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="error" class="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
                <div class="flex items-center">
                    <AlertCircle class="mr-2 h-5 w-5 text-red-500" />
                    <span class="text-red-700 dark:text-red-300">{{ error }}</span>
                </div>
            </div>

            <!-- 服务详情 -->
            <div v-else-if="service">
                <div class="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
                    <div>
                        <div class="flex items-center gap-3">
                            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                                {{ service.name }}
                            </h1>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger>
                                        <Badge :variant="getServiceTypeVariant(service.service_type)">
                                            {{ friendlyServiceType(service.service_type).name }}
                                        </Badge>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>{{ friendlyServiceType(service.service_type).description }}</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>

                            <Badge :variant="getStatusVariant(service.status)">
                                {{ formatServiceStatus(service.status) }}
                            </Badge>
                        </div>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">服务详细信息，创建于 {{ formatDate(service.created_at) }}</p>
                    </div>
                    <div class="flex w-full items-center gap-2 md:w-auto">
                        <Button variant="outline" @click="$inertia.visit(route('services.index'))">
                            <ArrowLeft class="mr-2 h-4 w-4" />
                            返回列表
                        </Button>
                        <Button @click="$inertia.visit(route('services.edit', service.name))">
                            <Edit class="mr-2 h-4 w-4" />
                            编辑
                        </Button>
                        <Button variant="destructive" @click="confirmDelete">
                            <Trash2 class="mr-2 h-4 w-4" />
                            删除
                        </Button>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-3">
                    <!-- 主要信息 -->
                    <div class="space-y-6 lg:col-span-2">
                        <!-- 目标应用 -->
                        <div
                            v-if="service.workload_type && service.workload_name"
                            class="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800"
                        >
                            <h2 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">目标应用</h2>
                            <div class="flex items-center space-x-3">
                                <Badge variant="secondary">{{ formatWorkloadType(service.workload_type) }}</Badge>
                                <span class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ service.workload_name }}
                                </span>
                            </div>
                        </div>

                        <!-- 端口配置 -->
                        <div class="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
                            <h2 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">端口配置</h2>
                            <div v-if="!service.ports || service.ports.length === 0" class="py-8 text-center text-gray-500">暂无端口配置</div>
                            <Table v-else>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>名称</TableHead>
                                        <TableHead>协议</TableHead>
                                        <TableHead class="text-right">服务端口</TableHead>
                                        <TableHead class="text-right">目标端口</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <TableRow v-for="(port, index) in service.ports" :key="index">
                                        <TableCell class="font-medium">{{ port.name || '-' }}</TableCell>
                                        <TableCell>{{ port.protocol }}</TableCell>
                                        <TableCell class="text-right">{{ port.port }}</TableCell>
                                        <TableCell class="text-right">{{ port.target_port || port.port }}</TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    <!-- 侧边栏信息 -->
                    <div class="space-y-6">
                        <!-- 网络信息 -->
                        <div class="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
                            <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">网络信息</h3>
                            <div class="space-y-4">
                                <div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">内部 DNS 地址</div>
                                    <div class="mt-1 flex items-center justify-between rounded bg-gray-100 p-2 font-mono text-sm dark:bg-gray-700">
                                        <span>{{ service.name }}.{{ workspace.namespace }}.svc.cluster.local</span>
                                        <Button variant="ghost" size="icon" @click="copyToClipboard(`${service.name}.${workspace.namespace}`)">
                                            <Copy class="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>

                                <div v-if="service.cluster_ip">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">内部 IP</div>
                                    <div class="mt-1 flex items-center justify-between rounded bg-gray-100 p-2 font-mono text-sm dark:bg-gray-700">
                                        <span>{{ service.cluster_ip }}</span>
                                        <Button variant="ghost" size="icon" @click="copyToClipboard(service.cluster_ip)">
                                            <Copy class="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>

                                <div v-if="service.service_type === 'LoadBalancer'">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">外部 IP</div>
                                    <div
                                        v-if="service.external_addresses && service.external_addresses.length > 0"
                                        class="mt-1 flex items-center justify-between rounded bg-gray-100 p-2 font-mono text-sm dark:bg-gray-700"
                                    >
                                        <span>{{ service.external_addresses.join(', ') }}</span>
                                        <Button variant="ghost" size="icon" @click="copyToClipboard(service.external_addresses.join(', '))">
                                            <Copy class="h-4 w-4" />
                                        </Button>
                                    </div>
                                    <div v-else class="mt-1 text-sm text-yellow-600 dark:text-yellow-400">分配中...</div>
                                </div>

                                <div v-if="service.service_type === 'NodePort' && clusterNodes.length > 0">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">节点端口外部访问</div>
                                    <div class="mt-1 space-y-2">
                                        <div v-for="node in clusterNodes" :key="node.name" class="rounded bg-gray-100 p-2 dark:bg-gray-700">
                                            <div class="text-xs font-semibold text-gray-600 dark:text-gray-300">节点: {{ node.name }}</div>
                                            <div class="mt-1 flex flex-wrap gap-x-2 gap-y-1">
                                                <span
                                                    v-for="port in service.ports.filter((p) => p.node_port)"
                                                    :key="port.port"
                                                    class="inline-flex items-center font-mono text-sm"
                                                >
                                                    {{ node.external_ip }}:{{ port.node_port }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="service.session_affinity">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">会话保持</div>
                                    <div class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">
                                        {{ service.session_affinity === 'None' ? '无' : '基于客户端 IP' }}
                                    </div>
                                </div>

                                <div v-if="service.external_traffic_policy">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">外部流量策略</div>
                                    <div class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">
                                        {{ service.external_traffic_policy }}
                                    </div>
                                </div>

                                <div v-if="service.load_balancer_class">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">负载均衡器类</div>
                                    <div class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">
                                        {{ service.load_balancer_class }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 访问地址 -->
                        <div
                            v-if="getAccessUrls().length > 0"
                            class="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800"
                        >
                            <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">可访问地址</h3>
                            <div class="space-y-2">
                                <div
                                    v-for="url in getAccessUrls()"
                                    :key="url"
                                    class="flex items-center justify-between rounded bg-gray-50 p-2 dark:bg-gray-700"
                                >
                                    <a :href="url" target="_blank" class="font-mono text-sm text-blue-600 hover:underline dark:text-blue-400">
                                        {{ url }}
                                    </a>
                                    <Button variant="ghost" size="icon" @click="copyToClipboard(url)">
                                        <Copy class="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 删除确认对话框 -->
            <AlertDialog v-model:open="deleteDialog.open">
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>确认删除服务</AlertDialogTitle>
                        <AlertDialogDescription> 您确定要删除服务 "{{ service?.name }}" 吗？此操作无法撤销。 </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction @click="deleteService" class="bg-red-600 hover:bg-red-700"> 删除 </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { formatWorkloadType } from '@/lib/formatK8sStatus';
import type { Service, Workspace } from '@/types';
import { Head } from '@inertiajs/vue3';
import { AlertCircle, ArrowLeft, Copy, Edit, Loader2, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    serviceName: string;
    workspace: Workspace;
}

const props = defineProps<Props>();

const service = ref<Service | null>(null);
const loading = ref(true);
const error = ref<string | undefined>();
const clusterNodes = ref<any[]>([]);

const deleteDialog = ref({
    open: false,
});

// 加载服务数据
const loadService = async () => {
    loading.value = true;
    try {
        const response = await axios.get(`/api/services/${props.serviceName}`);
        service.value = response.data;
        error.value = undefined;
    } catch (err: any) {
        error.value = err.response?.data?.message || '加载服务详情失败';
        console.error('加载服务失败:', err);
    } finally {
        loading.value = false;
    }
};

const confirmDelete = () => {
    deleteDialog.value.open = true;
};

const deleteService = async () => {
    try {
        await axios.delete(`/api/services/${props.serviceName}`);

        toast.success('服务删除成功');
        // 跳转到列表页
        window.location.href = route('services.index');
    } catch (err: any) {
        toast.error(err.response?.data?.message || '删除服务失败');
        console.error('删除服务失败:', err);
    } finally {
        deleteDialog.value.open = false;
    }
};

const getServiceTypeVariant = (type: string) => {
    switch (type) {
        case 'LoadBalancer':
            return 'default';
        case 'ClusterIP':
            return 'secondary';
        default:
            return 'outline';
    }
};

const getStatusVariant = (status: string) => {
    switch (status) {
        case 'Active':
            return 'default';
        case 'Pending':
            return 'secondary';
        default:
            return 'outline';
    }
};

const formatServiceStatus = (status: string) => {
    switch (status) {
        case 'Active':
            return '活跃';
        case 'Pending':
            return '等待中';
        default:
            return status;
    }
};

const formatDate = (dateString: string) => {
    if (!dateString) return '未知';
    return new Date(dateString).toLocaleString('zh-CN');
};

const friendlyServiceType = (type: string) => {
    switch (type) {
        case 'ClusterIP':
            return { name: '内部服务', description: '仅在集群内部暴露服务' };
        case 'NodePort':
            return { name: '节点端口', description: '通过每个节点的静态端口暴露服务' };
        case 'LoadBalancer':
            return { name: '负载均衡', description: '使用云服务商的负载均衡器向外部暴露服务' };
        default:
            return { name: type, description: '未知的服务类型' };
    }
};

const getAccessUrls = () => {
    const urls: string[] = [];
    if (!service.value) return urls;

    if (service.value.service_type === 'LoadBalancer' && service.value.external_addresses) {
        service.value.external_addresses.forEach((ip: string) => {
            service.value!.ports.forEach((port) => {
                urls.push(`http://${ip}:${port.port}`);
            });
        });
    }

    // if (service.value.service_type === 'NodePort' && resourcesStore.collections.nodes.length > 0) {
    //     resourcesStore.collections.nodes.forEach((node) => {
    //         if (node.external_ip) {
    //             service.value!.ports.forEach((port) => {
    //                 if (port.nodePort) {
    //                     urls.push(`http://${node.external_ip}:${port.nodePort}`);
    //                 }
    //             });
    //         }
    //     });
    // }

    return urls;
};

const copyToClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text);
        toast.success('已复制到剪贴板');
    } catch (err) {
        toast.error('复制失败');
    }
};

onMounted(() => {
    loadService();
});
</script>
