<template>
    <Head title="服务管理" />
    <AppLayout>
        <div class="space-y-6 p-4 md:p-6">
            <div class="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">服务管理</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">管理工作空间中的服务，为应用提供外部访问。</p>
                </div>
                <Button @click="$inertia.visit(route('services.create'))">
                    <Plus class="mr-2 h-4 w-4" />
                    创建服务
                </Button>
            </div>

            <div v-if="resourcesStore.error" class="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
                <div class="flex items-center">
                    <AlertCircle class="mr-2 h-5 w-5 text-red-500" />
                    <span class="text-red-700 dark:text-red-300">{{ resourcesStore.error }}</span>
                </div>
            </div>

            <div v-if="!resourcesStore.isLoaded" class="py-12 text-center">
                <div class="inline-flex items-center">
                    <Loader2 class="mr-2 h-6 w-6 animate-spin" />
                    加载中...
                </div>
            </div>

            <div v-else-if="services.length === 0" class="py-12 text-center">
                <div class="text-gray-400 dark:text-gray-600">
                    <Server class="mx-auto mb-4 h-12 w-12" />
                    <h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">暂无服务</h3>
                    <p class="mb-4 text-gray-600 dark:text-gray-400">还没有创建任何服务，开始创建第一个服务吧。</p>
                    <Button @click="$inertia.visit(route('services.create'))">
                        <Plus class="mr-2 h-4 w-4" />
                        创建服务
                    </Button>
                </div>
            </div>

            <div v-else class="space-y-4">
                <div v-for="service in services" :key="service.name" class="rounded-lg border p-4 transition-shadow hover:shadow-lg md:p-6">
                    <div class="flex flex-col items-start gap-4 md:flex-row md:items-center md:justify-between">
                        <div class="flex-1">
                            <div class="flex items-center gap-3">
                                <Link
                                    :href="route('services.show', service.name)"
                                    class="text-lg font-semibold text-gray-900 hover:text-blue-600 dark:text-white dark:hover:text-blue-400"
                                >
                                    {{ service.name }}
                                </Link>
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger>
                                            <Badge :variant="getServiceTypeVariant(service.service_type)">
                                                {{ friendlyServiceType(service.service_type).name }}
                                            </Badge>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>{{ friendlyServiceType(service.service_type).description }}</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                                <Badge :variant="getStatusVariant(service.status)">
                                    {{ formatServiceStatus(service.status) }}
                                </Badge>
                            </div>
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">创建于 {{ formatDate(service.created_at) }}</div>
                        </div>

                        <div class="flex w-full flex-wrap items-center gap-2 md:w-auto md:flex-nowrap">
                            <Button variant="outline" size="sm" @click="$inertia.visit(route('services.show', service.name))">
                                <Eye class="mr-1 h-4 w-4" />
                                查看
                            </Button>
                            <Button variant="outline" size="sm" @click="$inertia.visit(route('services.edit', service.name))">
                                <Edit class="mr-1 h-4 w-4" />
                                编辑
                            </Button>
                            <Button variant="destructive" size="sm" @click="confirmDelete(service)">
                                <Trash2 class="mr-1 h-4 w-4" />
                                删除
                            </Button>
                        </div>
                    </div>

                    <div
                        class="mt-4 grid grid-cols-1 gap-x-6 gap-y-4 border-t border-gray-800 pt-4 sm:grid-cols-2 lg:grid-cols-3 dark:border-gray-700"
                    >
                        <!-- 工作负载信息 -->
                        <div v-if="service.workload_type && service.workload_name">
                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">目标应用</div>
                            <div class="mt-1 flex items-center space-x-2">
                                <Badge variant="secondary">{{ formatWorkloadType(service.workload_type) }}</Badge>
                                <span class="font-semibold">{{ service.workload_name }}</span>
                            </div>
                        </div>

                        <!-- 访问地址 -->
                        <div>
                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">访问地址</div>
                            <div class="mt-1 space-y-1">
                                <div class="flex items-center gap-2">
                                    <span class="font-mono text-sm text-gray-800 dark:text-gray-200" title="内部访问地址">
                                        {{ service.name }}.{{ workspace.namespace }}.svc.cluster.local:{{ service.ports[0]?.port }}
                                    </span>
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <Badge variant="outline">内部</Badge>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>此地址仅限集群内部访问</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <template v-if="getAccessUrls(service).length > 0">
                                    <div v-for="url in getAccessUrls(service)" :key="url" class="flex items-center gap-2">
                                        <span class="font-mono text-sm text-gray-800 dark:text-gray-200">
                                            {{ url }}
                                        </span>
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger>
                                                    <Badge variant="secondary">外部</Badge>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>此地址可从外部访问</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>
                                </template>
                                <div
                                    v-if="
                                        service.service_type === 'LoadBalancer' &&
                                        (!service.external_addresses || service.external_addresses.length === 0)
                                    "
                                >
                                    <div class="text-sm text-yellow-600 dark:text-yellow-400">正在分配外部 IP...</div>
                                </div>
                            </div>
                        </div>

                        <!-- 端口信息 -->
                        <div>
                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">端口映射</div>
                            <div class="mt-1 flex flex-wrap gap-1">
                                <TooltipProvider v-for="port in service.ports" :key="port.port">
                                    <Tooltip>
                                        <TooltipTrigger>
                                            <span
                                                class="inline-flex items-center rounded bg-gray-100 px-2 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                                            >
                                                {{ port.port }} <ArrowRight class="mx-1 h-3 w-3" />
                                                {{ port.target_port || port.port }}
                                                <span class="ml-1 text-gray-500">/{{ port.protocol }}</span>
                                            </span>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>服务端口 {{ port.port }} 转发至目标端口 {{ port.target_port || port.port }}</p>
                                            <p v-if="port.name">端口名称: {{ port.name }}</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 删除确认对话框 -->
            <AlertDialog v-model:open="deleteDialog.open">
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>确认删除服务</AlertDialogTitle>
                        <AlertDialogDescription> 您确定要删除服务 "{{ deleteDialog.service?.name }}" 吗？此操作无法撤销。 </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction @click="deleteService" class="bg-red-600 hover:bg-red-700"> 删除 </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { formatWorkloadType } from '@/lib/formatK8sStatus';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { Service, Workspace } from '@/types';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { AlertCircle, ArrowRight, Edit, Eye, Loader2, Plus, Server, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { route } from 'ziggy-js';

const { t } = useI18n();
const page = usePage();
const workspace = computed(() => (page.props as any).workspace as Workspace);

const resourcesStore = useResourcesStore();
const services = computed(() => resourcesStore.collections.services);

const deleteDialog = ref({
    open: false,
    service: null as Service | null,
});

const formatServiceStatus = (status: string) => {
    return status; // 可以在这里添加更友好的状态格式化
};

const getServiceTypeVariant = (type: string): 'default' | 'secondary' => {
    switch (type) {
        case 'ClusterIP':
            return 'secondary';
        case 'NodePort':
            return 'default';
        case 'LoadBalancer':
            return 'default';
        default:
            return 'secondary';
    }
};

const getStatusVariant = (status: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
    if (status?.toLowerCase() === 'active') {
        return 'secondary';
    }
    return 'outline';
};

const friendlyServiceType = (type: string) => {
    const types: Record<string, { name: string; description: string }> = {
        ClusterIP: { name: '集群内', description: '仅在集群内部暴露服务' },
        NodePort: { name: '节点端口', description: '通过每个节点的 IP 和静态端口暴露服务' },
        LoadBalancer: { name: '负载均衡', description: '使用云提供商的负载均衡器向外部暴露服务' },
        ExternalName: { name: '外部名称', description: '将服务映射到 CNAME 记录' },
    };
    return types[type] || { name: type, description: '未知服务类型' };
};

const formatDate = (dateString: string) => {
    if (!dateString) return '未知';
    return new Date(dateString).toLocaleString('zh-CN');
};

const getAccessUrls = (service: Service) => {
    const urls: string[] = [];
    if (!service) return urls;

    if (service.service_type === 'LoadBalancer' && service.external_addresses) {
        service.external_addresses.forEach((ip: string) => {
            service.ports.forEach((port) => {
                urls.push(`${ip}:${port.port}`);
            });
        });
    }

    // if (service.service_type === 'NodePort' && resourcesStore.collections.nodes.length > 0) {
    //     resourcesStore.collections.nodes.forEach((node) => {
    //         if (node.external_ip) {
    //             service.ports.forEach((port) => {
    //                 if (port.nodePort) {
    //                     urls.push(`${node.external_ip}:${port.nodePort}`);
    //                 }
    //             });
    //         }
    //     });
    // }

    return urls;
};

const confirmDelete = (service: Service) => {
    deleteDialog.value.service = service;
    deleteDialog.value.open = true;
};

const deleteService = async () => {
    const serviceToDelete = deleteDialog.value.service;
    if (!serviceToDelete) return;

    try {
        await axios.delete(route('api.services.destroy', serviceToDelete.name));
        toast.success(t('message.delete_service_successfully', { name: serviceToDelete.name }));
        // 数据会通过 WebSocket 自动更新
    } catch (e) {
        if (axios.isAxiosError(e) && e.response) {
            toast.error(e.response.data.message ?? t('message.failed_to_delete_service'));
        } else {
            toast.error(t('message.failed_to_delete_service'));
        }
    } finally {
        deleteDialog.value.open = false;
        deleteDialog.value.service = null;
    }
};
</script>
