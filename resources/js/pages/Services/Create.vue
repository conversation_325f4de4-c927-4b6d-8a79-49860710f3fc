<template>
    <Head title="创建服务" />
    <AppLayout>
        <div class="space-y-6 p-4 md:p-6">
            <div class="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">创建服务</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">为您的应用创建一个新的网络服务，使其能够被访问。</p>
                </div>
                <Button variant="outline" @click="$inertia.visit(route('services.index'))">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回列表
                </Button>
            </div>

            <form @submit.prevent="createService" class="space-y-8 p-6">
                <!-- 基本信息 -->
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">基本信息</h2>
                    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div>
                            <Label for="name">服务名称 *</Label>
                            <Input id="name" v-model="form.name" placeholder="例如 my-app-service" :class="{ 'border-red-500': errors.name }" />
                            <p v-if="errors.name" class="mt-1 text-sm text-red-500">
                                {{ errors.name }}
                            </p>
                            <p class="mt-1 text-sm text-gray-500">只能包含小写字母、数字和连字符，且必须以字母数字开头和结尾。</p>
                            <p v-if="form.name" class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                内部访问地址: {{ form.name }}.{{ workspace.namespace }}.svc.cluster.local
                            </p>
                        </div>

                        <div>
                            <Label for="type">服务类型 *</Label>
                            <Select v-model="form.type">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择服务类型" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="ClusterIP">内部服务 (ClusterIP)</SelectItem>
                                    <!-- <SelectItem value="NodePort">节点端口 (NodePort)</SelectItem> -->
                                    <SelectItem value="LoadBalancer">负载均衡 (LoadBalancer)</SelectItem>
                                </SelectContent>
                            </Select>
                            <p v-if="errors.type" class="mt-1 text-sm text-red-500">
                                {{ errors.type }}
                            </p>
                            <p class="mt-1 text-sm text-gray-500">
                                {{ getServiceTypeDescription(form.type) }}
                            </p>
                        </div>
                    </div>
                </div>

                <hr class="border-gray-200 dark:border-gray-700" />

                <!-- 目标工作负载 -->
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">目标应用</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400">选择此服务要将网络流量转发到的应用。</p>
                    <WorkloadSelector v-model="workloadSelection" :show-ports="true" @update:modelValue="onWorkloadChange" />
                    <p v-if="errors.target_workload_type || errors.target_workload_name" class="mt-2 text-sm text-red-500">
                        {{ errors.target_workload_type || errors.target_workload_name || '请选择一个目标应用' }}
                    </p>
                </div>

                <hr class="border-gray-200 dark:border-gray-700" />

                <!-- 端口配置 -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">端口配置</h2>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">要暴露容器的哪些端口？</p>
                        </div>
                        <Button type="button" variant="outline" size="sm" @click="addPort" :disabled="form.type === 'LoadBalancer'">
                            <Plus class="mr-2 h-4 w-4" />
                            添加端口
                        </Button>
                    </div>

                    <div v-if="form.type === 'LoadBalancer'" class="rounded-lg border border-gray-200 p-4 dark:border-gray-700">
                        <div class="flex items-start">
                            <Info class="mt-0.5 mr-2 h-5 w-5 flex-shrink-0 text-blue-500" />
                            <div class="text-sm text-blue-700 dark:text-blue-300">
                                <p class="font-medium">负载均衡模式说明</p>
                                <p class="mt-1">我们将从服务商获取一个外部 IP 地址，并将服务端口映射到您选择的目标应用端口。您无需手动配置端口。</p>
                            </div>
                        </div>
                    </div>

                    <div v-if="form.ports.length === 0" class="py-8 text-center text-gray-500">
                        <p>暂无端口配置</p>
                        <p class="mt-1 text-sm">
                            {{ form.type === 'LoadBalancer' ? '选择目标应用后将自动配置端口' : '点击"添加端口"按钮进行配置' }}
                        </p>
                    </div>

                    <div v-else class="space-y-4">
                        <div v-for="(port, index) in form.ports" :key="index" class="rounded-lg border border-gray-200 p-4 dark:border-gray-600">
                            <div class="mb-4 flex items-center justify-between">
                                <h3 class="font-medium text-gray-900 dark:text-white">端口 {{ index + 1 }}</h3>
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    @click="removePort(index)"
                                    :disabled="form.type === 'LoadBalancer'"
                                    class="text-red-600 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/50"
                                >
                                    <Trash2 class="h-4 w-4" />
                                </Button>
                            </div>

                            <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
                                <div>
                                    <Label>端口名称</Label>
                                    <Input v-model="port.name" placeholder="例如 http" :disabled="form.type === 'LoadBalancer'" />
                                    <p class="mt-1 text-xs text-gray-500">可选，用于标识端口。</p>
                                </div>
                                <div>
                                    <Label>服务端口 *</Label>
                                    <Input v-model.number="port.port" type="number" min="1" max="65535" :disabled="form.type === 'LoadBalancer'" />
                                    <p v-if="errors[`ports.${index}.port`]" class="mt-1 text-sm text-red-500">
                                        {{ errors[`ports.${index}.port`] }}
                                    </p>
                                    <p v-else class="mt-1 text-xs text-gray-500">
                                        服务暴露的端口（如果不是内部服务，则并不是外部连接端口，外部连接后的端口创建后分配）。
                                    </p>
                                </div>
                                <div>
                                    <Label>目标端口（容器暴露的端口）</Label>
                                    <Input
                                        v-model.number="port.target_port"
                                        type="number"
                                        min="1"
                                        max="65535"
                                        placeholder="默认与服务端口相同"
                                        :disabled="form.type === 'LoadBalancer'"
                                    />
                                    <p class="mt-1 text-xs text-gray-500">流量转发到应用容器的端口。</p>
                                </div>
                                <div>
                                    <Label>协议</Label>
                                    <Select v-model="port.protocol" :disabled="form.type === 'LoadBalancer'">
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="TCP">TCP</SelectItem>
                                            <SelectItem value="UDP">UDP</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <p v-if="errors.ports" class="mt-2 text-sm text-red-500">
                        {{ errors.ports }}
                    </p>
                </div>

                <hr class="border-gray-200 dark:border-gray-700" />

                <!-- 高级选项 -->
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">高级选项</h2>
                    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <!-- IP 池选择（仅 LoadBalancer 显示） -->
                        <div v-if="form.type === 'LoadBalancer'" class="space-y-2">
                            <Label for="ip_pool_id">IP 池</Label>
                            <Select v-model="form.ip_pool_id">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择 IP 池" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="pool in ipPools" :key="pool.id" :value="pool.id.toString()">
                                        {{ pool.name }} ({{ pool.ip_version === 'ipv4' ? 'IPv4' : 'IPv6' }},
                                        {{ pool.sharing_strategy === 'shared' ? '共享' : '独享' }})
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                            <p class="mt-1 text-sm text-gray-500">选择用于分配IP地址的池，共享模式支持端口复用，独享模式每个服务独占IP</p>

                            <!-- IP 池价格显示 -->
                            <div
                                v-if="form.ip_pool_id && selectedIpPoolPricing"
                                class="mt-3 rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-900/20"
                            >
                                <div class="flex items-center gap-2 text-sm font-medium text-blue-800 dark:text-blue-200">
                                    <DollarSign class="h-4 w-4" />
                                    IP 池价格
                                </div>
                                <div class="mt-2 space-y-1">
                                    <div
                                        v-for="(pricing, unitType) in selectedIpPoolPricing.pricing"
                                        :key="unitType"
                                        class="flex items-center justify-between text-sm"
                                    >
                                        <span class="text-gray-600 dark:text-gray-400">{{ pricing.name }}</span>
                                        <div class="flex items-center gap-2">
                                            <PriceDisplay :value="pricing.price_per_hour" :decimals="4" unit="/小时" />
                                            <span class="text-gray-400">|</span>
                                            <PriceDisplay :value="pricing.price_per_minute" :decimals="6" unit="/分钟" />
                                        </div>
                                    </div>
                                </div>
                                <p class="mt-2 text-xs text-blue-600 dark:text-blue-400">注意：LoadBalancer 服务会根据实际分配的 IP 地址数量计费</p>
                            </div>

                            <!-- 价格加载状态 -->
                            <div
                                v-else-if="form.ip_pool_id && isLoadingIpPoolPricing"
                                class="mt-3 rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800"
                            >
                                <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                                    <Loader2 class="h-4 w-4 animate-spin" />
                                    正在加载价格信息...
                                </div>
                            </div>
                        </div>

                        <div>
                            <Label for="session_affinity">会话保持</Label>
                            <Select v-model="form.session_affinity">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择会话保持策略" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="None">无</SelectItem>
                                    <SelectItem value="ClientIP">基于客户端 IP</SelectItem>
                                </SelectContent>
                            </Select>
                            <p class="mt-1 text-sm text-gray-500">将来自同一客户端的请求路由到同一个应用实例。</p>
                            <p class="mt-1 text-sm text-gray-500">
                                注意，无论哪种服务类型，一旦创建服务成功后，都可以被集群内部/外部访问，比如别的用户和你在同一个集群，他也可以访问你的服务，同样，你也可以访问它的服务。
                            </p>
                        </div>

                        <div v-if="form.type === 'LoadBalancer' || form.type === 'NodePort'">
                            <Label for="external_traffic_policy">外部流量策略</Label>
                            <Select v-model="form.external_traffic_policy">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择流量策略" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Cluster">集群 (Cluster)</SelectItem>
                                    <SelectItem value="Local">本地 (Local)</SelectItem>
                                </SelectContent>
                            </Select>
                            <p class="mt-1 text-sm text-gray-500">
                                "本地" 策略会保留客户端源 IP，但可能导致流量分发不均。负载均衡模式只能为 "集群" 策略。
                            </p>
                        </div>

                        <div>
                            <LabelSelector v-model="form.labels" />
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center justify-end space-x-4 border-t border-gray-200 pt-6 dark:border-gray-700">
                    <Button type="button" variant="outline" @click="$inertia.visit(route('services.index'))"> 取消 </Button>
                    <Button type="submit" :disabled="loading || !isFormValid">
                        <Loader2 v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
                        创建服务
                    </Button>
                </div>
            </form>
        </div>
        <div class="p-4">
            <DataPreview api="/api/services" method="POST" :data-ref="form" />
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import DataPreview from '@/components/DataPreview.vue';
import PriceDisplay from '@/components/PriceDisplay.vue';
import LabelSelector from '@/components/selectors/LabelSelector.vue';
import WorkloadSelector from '@/components/selectors/WorkloadSelector.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { IpPoolInfo } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { ArrowLeft, DollarSign, Info, Loader2, Plus, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, ref, watch } from 'vue';
import { route } from 'ziggy-js';

interface ServicePort {
    name?: string;
    port: number;
    target_port?: number;
    protocol: string;
}

interface WorkloadSelection {
    type: string;
    name: string;
}

interface Props {
    workspace: {
        id: number;
        name: string;
        namespace: string;
    };
}

const props = defineProps<Props>();

const loading = ref(false);
const errors = ref<Record<string, string>>({});

const form = ref({
    name: '',
    type: 'ClusterIP',
    target_workload_type: '',
    target_workload_name: '',
    ports: [] as ServicePort[],
    session_affinity: 'None',
    external_traffic_policy: 'Cluster',
    allow_shared_ip: true, // LoadBalancer 模式下固定为 true
    ip_pool_id: '', // 新增 IP 池选择
    labels: {} as Record<string, string>,
});

const workloadSelection = ref<WorkloadSelection | null>(null);

const isFormValid = computed(() => {
    let portsAreValid = true;
    if (form.value.ports.length > 0) {
        portsAreValid = form.value.ports.every((p) => {
            const port = Number(p.port);
            return !isNaN(port) && port > 0 && port <= 65535;
        });
    }

    return form.value.name && form.value.type && form.value.target_workload_type && form.value.target_workload_name && portsAreValid;
});

const getServiceTypeDescription = (type: string) => {
    switch (type) {
        case 'ClusterIP':
            return '创建一个仅限内部访问的服务，适用于数据库等内部组件。';
        case 'NodePort':
            return '在每个节点上暴露一个静态端口，可以通过 `节点IP:节点端口` 从外部访问。';
        case 'LoadBalancer':
            return '使用负载均衡器向公网暴露服务，自动分配外部 IP。';
        default:
            return '请选择一个服务类型。';
    }
};

const onWorkloadChange = (selection: WorkloadSelection) => {
    form.value.target_workload_type = selection.type;
    form.value.target_workload_name = selection.name;

    // LoadBalancer 模式下自动获取工作负载端口
    if (form.value.type === 'LoadBalancer') {
        loadWorkloadPorts(selection);
    }
};

const loadWorkloadPorts = async (selection: WorkloadSelection) => {
    try {
        const response = await axios.get(`/api/workspaces/current/all`);

        // 根据工作负载类型从相应的集合中查找
        let workload = null;
        if (selection.type === 'Deployment' && response.data.deployments) {
            workload = response.data.deployments.find((w: any) => w.name === selection.name);
        } else if (selection.type === 'StatefulSet' && response.data.statefulsets) {
            workload = response.data.statefulsets.find((w: any) => w.name === selection.name);
        }

        if (workload && workload.ports) {
            form.value.ports = workload.ports.map((port: any) => ({
                name: port.port_name || `port-${port.port}`,
                port: port.port,
                target_port: port.port,
                protocol: port.protocol || 'TCP',
            }));
        }
    } catch (error) {
        console.error('加载工作负载端口失败:', error);
    }
};

const addPort = () => {
    form.value.ports.push({
        name: '',
        port: 80,
        target_port: 80,
        protocol: 'TCP',
    });
};

const removePort = (index: number) => {
    form.value.ports.splice(index, 1);
};

const createService = async () => {
    if (!isFormValid.value) return;

    loading.value = true;
    errors.value = {};

    try {
        const payload = {
            ...form.value,
            // LoadBalancer 模式下的特殊处理
            ...(form.value.type === 'LoadBalancer' && {
                allow_shared_ip: true,
                ip_pool_id: form.value.ip_pool_id, // 传递 IP 池 ID
            }),
        };

        await axios.post('/api/services', payload);

        toast.success('服务创建成功');

        router.visit(route('services.index'));
    } catch (error: any) {
        if (error.response?.status === 422) {
            errors.value = error.response.data.errors || {};
            toast.error('表单验证失败，请检查输入');
        } else {
            toast.error(error.response?.data?.message || '创建服务失败');
        }
        console.error('创建服务失败:', error);
    } finally {
        loading.value = false;
    }
};

// 监听服务类型变化
watch(
    () => form.value.type,
    (newType) => {
        if (newType === 'LoadBalancer') {
            // LoadBalancer 模式下重新加载端口
            if (workloadSelection.value) {
                loadWorkloadPorts(workloadSelection.value);
            }
        } else {
            // ClusterIP 和 NodePort 模式下清空端口，让用户自定义
            form.value.ports = [];
        }
    },
);

// 获取 IP 池列表
const ipPools = ref<IpPoolInfo[]>([]);
const loadIpPools = async () => {
    try {
        const response = await axios.get('/api/ip-pools');
        ipPools.value = response.data;
    } catch (error) {
        console.error('加载 IP 池失败:', error);
    }
};

// IP 池价格相关状态
const selectedIpPoolPricing = ref<any>(null);
const isLoadingIpPoolPricing = ref(false);

// 加载选中IP池的价格信息
const loadIpPoolPricing = async (ipPoolId: string) => {
    if (!ipPoolId) {
        selectedIpPoolPricing.value = null;
        return;
    }

    isLoadingIpPoolPricing.value = true;
    try {
        const response = await axios.get(`/api/pricing/ip/${ipPoolId}`);
        selectedIpPoolPricing.value = response.data;
    } catch (error) {
        console.error('加载 IP 池价格失败:', error);
        selectedIpPoolPricing.value = null;
    } finally {
        isLoadingIpPoolPricing.value = false;
    }
};

// 监听IP池选择变化
watch(
    () => form.value.ip_pool_id,
    (newIpPoolId) => {
        if (newIpPoolId && form.value.type === 'LoadBalancer') {
            loadIpPoolPricing(newIpPoolId);
        } else {
            selectedIpPoolPricing.value = null;
        }
    },
);

// 组件挂载时加载 IP 池
loadIpPools();
</script>
