<template>
    <Head title="编辑服务" />
    <AppLayout>
        <div class="space-y-6 p-4 md:p-6">
            <div class="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">编辑服务</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        修改服务 <span class="font-semibold">{{ serviceName }}</span> 的配置。
                    </p>
                </div>
                <Button variant="outline" @click="$inertia.visit(route('services.index'))">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回列表
                </Button>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="py-12 text-center">
                <div class="inline-flex items-center">
                    <Loader2 class="mr-2 h-6 w-6 animate-spin" />
                    加载中...
                </div>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="error" class="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
                <div class="flex items-center">
                    <AlertCircle class="mr-2 h-5 w-5 text-red-500" />
                    <span class="text-red-700 dark:text-red-300">{{ error }}</span>
                </div>
            </div>

            <!-- 编辑表单 -->
            <form
                v-else-if="service"
                @submit.prevent="updateService"
                class="space-y-8 rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800"
            >
                <!-- 基本信息 -->
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">基本信息</h2>
                    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div>
                            <Label>服务名称</Label>
                            <p class="mt-1 font-semibold text-gray-800 dark:text-gray-200">{{ service?.name }}</p>
                            <p class="mt-1 text-sm text-gray-500">服务名称不可修改。</p>
                            <p v-if="service?.name" class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                内部访问地址: {{ service.name }}.{{ workspace.namespace }}.svc.cluster.local
                            </p>
                        </div>
                        <div>
                            <Label>服务类型</Label>
                            <p class="mt-1 font-semibold text-gray-800 dark:text-gray-200">{{ friendlyServiceType(service.type).name }}</p>
                            <p class="mt-1 text-sm text-gray-500">服务类型不可修改。</p>
                        </div>
                    </div>
                </div>

                <hr class="border-gray-200 dark:border-gray-700" />

                <!-- 目标工作负载 -->
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">目标应用</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400">选择此服务要将网络流量转发到的应用。</p>
                    <WorkloadSelector v-model="workloadSelection" :show-ports="true" @update:modelValue="onWorkloadChange" />
                    <div v-if="errors.target_workload_name" class="text-sm text-red-600">
                        {{ errors.target_workload_name }}
                    </div>
                </div>

                <hr class="border-gray-200 dark:border-gray-700" />

                <!-- 端口配置 -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">端口配置</h2>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">定义服务如何暴露应用的端口。</p>
                        </div>
                        <Button v-if="service.type !== 'LoadBalancer'" type="button" variant="outline" size="sm" @click="addPort">
                            <Plus class="mr-2 h-4 w-4" />
                            添加端口
                        </Button>
                    </div>

                    <div
                        v-if="service.type === 'LoadBalancer'"
                        class="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-900/20"
                    >
                        <div class="flex items-start">
                            <Info class="mt-0.5 mr-2 h-5 w-5 flex-shrink-0 text-blue-500" />
                            <div class="text-sm text-blue-700 dark:text-blue-300">
                                <p class="font-medium">负载均衡模式说明</p>
                                <p class="mt-1">负载均衡模式下，端口配置将自动从目标应用获取，不支持手动编辑。</p>
                            </div>
                        </div>
                    </div>

                    <div v-if="form.ports.length === 0" class="py-8 text-center text-gray-500">
                        <p>暂无端口配置</p>
                        <p class="mt-1 text-sm">
                            {{ service.type === 'LoadBalancer' ? '请先选择目标应用' : '点击"添加端口"按钮进行配置' }}
                        </p>
                    </div>

                    <div v-else class="space-y-4">
                        <div v-for="(port, index) in form.ports" :key="index" class="rounded-lg border border-gray-200 p-4 dark:border-gray-600">
                            <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
                                <div>
                                    <Label :for="`port-name-${index}`">端口名称</Label>
                                    <Input
                                        :id="`port-name-${index}`"
                                        v-model="port.name"
                                        placeholder="例如 http"
                                        :disabled="service.type === 'LoadBalancer'"
                                        class="mt-1"
                                    />
                                </div>
                                <div>
                                    <Label :for="`port-${index}`">服务端口 *</Label>
                                    <Input
                                        :id="`port-${index}`"
                                        v-model.number="port.port"
                                        type="number"
                                        min="1"
                                        max="65535"
                                        :disabled="service.type === 'LoadBalancer'"
                                        class="mt-1"
                                        required
                                    />
                                </div>
                                <div>
                                    <Label :for="`target-port-${index}`">目标端口 *</Label>
                                    <Input
                                        :id="`target-port-${index}`"
                                        v-model.number="port.target_port"
                                        type="number"
                                        min="1"
                                        max="65535"
                                        :disabled="service.type === 'LoadBalancer'"
                                        class="mt-1"
                                        required
                                    />
                                </div>
                                <div class="flex items-end">
                                    <div class="flex-1">
                                        <Label :for="`protocol-${index}`">协议</Label>
                                        <Select v-model="port.protocol" :disabled="service.type === 'LoadBalancer'">
                                            <SelectTrigger :id="`protocol-${index}`" class="mt-1">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="TCP">TCP</SelectItem>
                                                <SelectItem value="UDP">UDP</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <Button
                                        v-if="service.type !== 'LoadBalancer'"
                                        type="button"
                                        variant="ghost"
                                        size="icon"
                                        @click="removePort(index)"
                                        class="ml-2 text-red-600 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/50"
                                    >
                                        <Trash2 class="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="border-gray-200 dark:border-gray-700" />

                <!-- 高级选项 -->
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">高级选项</h2>

                    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div>
                            <Label for="session-affinity">会话保持</Label>
                            <Select v-model="form.session_affinity">
                                <SelectTrigger id="session-affinity" class="mt-1">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="None">无</SelectItem>
                                    <SelectItem value="ClientIP">基于客户端 IP</SelectItem>
                                </SelectContent>
                            </Select>
                            <p class="mt-1 text-sm text-gray-500">将来自同一客户端的请求路由到同一个应用实例。</p>
                        </div>

                        <div v-if="service.type !== 'ClusterIP'">
                            <Label for="external-traffic-policy">外部流量策略</Label>
                            <Select v-model="form.external_traffic_policy">
                                <SelectTrigger id="external-traffic-policy" class="mt-1">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Cluster">集群 (Cluster)</SelectItem>
                                    <SelectItem value="Local">本地 (Local)</SelectItem>
                                </SelectContent>
                            </Select>
                            <p class="mt-1 text-sm text-gray-500">"本地" 策略会保留客户端源 IP，但可能导致流量分发不均。</p>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="flex justify-end space-x-3 border-t border-gray-200 pt-6 dark:border-gray-700">
                    <Button type="button" variant="outline" @click="$inertia.visit(route('services.show', serviceName))"> 取消 </Button>
                    <Button type="submit" :disabled="submitting || !isFormValid">
                        <Loader2 v-if="submitting" class="mr-2 h-4 w-4 animate-spin" />
                        保存更改
                    </Button>
                </div>
            </form>
        </div>

        <div v-if="service" class="p-4">
            <DataPreview api="/api/services" method="PUT" :data-ref="form" />
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import DataPreview from '@/components/DataPreview.vue';
import WorkloadSelector from '@/components/selectors/WorkloadSelector.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { Service, ServicePort } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { AlertCircle, ArrowLeft, Info, Loader2, Plus, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, nextTick, onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface WorkloadSelection {
    type: string;
    name: string;
}

interface Props {
    serviceName: string;
    workspace: {
        id: number;
        name: string;
        namespace: string;
    };
}

const props = defineProps<Props>();

const service = ref<Service | null>(null);
const loading = ref(true);
const submitting = ref(false);
const errors = ref<Record<string, string>>({});
const error = ref<string | undefined>();

const form = ref({
    target_workload_type: '',
    target_workload_name: '',
    ports: [] as ServicePort[],
    session_affinity: 'None',
    external_traffic_policy: 'Cluster',
});

const workloadSelection = ref<WorkloadSelection | null>(null);

const isFormValid = computed(() => {
    let portsAreValid = true;
    if (form.value.ports.length > 0) {
        portsAreValid = form.value.ports.every((p) => {
            const port = Number(p.port);
            const targetPort = Number(p.target_port);
            return !isNaN(port) && port > 0 && port <= 65535 && !isNaN(targetPort) && targetPort > 0 && targetPort <= 65535;
        });
    }

    return form.value.target_workload_type && form.value.target_workload_name && portsAreValid;
});

const friendlyServiceType = (type: string) => {
    switch (type) {
        case 'ClusterIP':
            return { name: '内部服务', description: '仅在集群内部暴露服务' };
        case 'NodePort':
            return { name: '节点端口', description: '通过每个节点的静态端口暴露服务' };
        case 'LoadBalancer':
            return { name: '负载均衡', description: '使用云服务商的负载均衡器向外部暴露服务' };
        default:
            return { name: type, description: '未知的服务类型' };
    }
};

const loadService = async () => {
    loading.value = true;
    try {
        const response = await axios.get(`/api/services/${props.serviceName}`);
        service.value = response.data;

        if (service.value) {
            // 填充表单数据
            form.value.target_workload_type = service.value.workload_type || '';
            form.value.target_workload_name = service.value.workload_name || '';
            form.value.ports = service.value.ports.map((port) => ({
                name: port.name || '',
                port: port.port,
                target_port: port.target_port || port.port,
                protocol: port.protocol || 'TCP',
            }));
            form.value.session_affinity = service.value.session_affinity || 'None';
            form.value.external_traffic_policy = service.value.external_traffic_policy || 'Cluster';

            // 设置工作负载选择
            if (service.value.workload_type && service.value.workload_name) {
                workloadSelection.value = {
                    type: service.value.workload_type,
                    name: service.value.workload_name,
                };
            }

            // 使用 nextTick 确保 DOM 更新
            await nextTick();
        }

        error.value = undefined;
    } catch (err: any) {
        error.value = err.response?.data?.message || '加载服务详情失败';
        console.error('加载服务失败:', err);
    } finally {
        loading.value = false;
    }
};

const onWorkloadChange = (selection: WorkloadSelection) => {
    if (selection) {
        form.value.target_workload_type = selection.type;
        form.value.target_workload_name = selection.name;

        // LoadBalancer 模式下自动获取工作负载端口
        if (service.value?.type === 'LoadBalancer') {
            loadWorkloadPorts(selection);
        }
    }
};

const loadWorkloadPorts = async (selection: WorkloadSelection) => {
    try {
        const response = await axios.get(`/api/workspaces/current/all`);

        // 根据工作负载类型从相应的集合中查找
        let workload = null;
        if (selection.type === 'Deployment' && response.data.deployments) {
            workload = response.data.deployments.find((w: any) => w.name === selection.name);
        } else if (selection.type === 'StatefulSet' && response.data.statefulsets) {
            workload = response.data.statefulsets.find((w: any) => w.name === selection.name);
        }

        if (workload && workload.ports) {
            form.value.ports = workload.ports.map((port: any) => ({
                name: port.port_name || `port-${port.port}`,
                port: port.port,
                target_port: port.port,
                protocol: port.protocol || 'TCP',
            }));
        }
    } catch (error) {
        console.error('加载工作负载端口失败:', error);
    }
};

const addPort = () => {
    form.value.ports.push({
        name: '',
        port: 80,
        target_port: 80,
        protocol: 'TCP',
    });
};

const removePort = (index: number) => {
    form.value.ports.splice(index, 1);
};

const updateService = async () => {
    if (!isFormValid.value) return;

    submitting.value = true;
    errors.value = {};

    try {
        const payload = {
            ...form.value,
        };

        await axios.put(`/api/services/${props.serviceName}`, payload);

        toast.success('服务更新成功');
        // 跳转到详情页
        router.visit(route('services.show', { service: props.serviceName }));
    } catch (error: any) {
        if (error.response?.status === 422) {
            errors.value = error.response.data.errors || {};
            toast.error('表单验证失败，请检查输入');
        } else {
            toast.error(error.response?.data?.message || '更新服务失败');
        }
        console.error('更新服务失败:', error);
    } finally {
        submitting.value = false;
    }
};

onMounted(() => {
    loadService();
});
</script>
