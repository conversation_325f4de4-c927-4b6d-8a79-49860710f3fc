<template>
    <AppLayout>
        <Head :title="`部署: ${deploymentName}`" />

        <div class="space-y-6 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <Button variant="ghost" @click="router.visit(route('applications.index'))">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        返回列表
                    </Button>
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ deploymentName }}
                        </h1>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">无状态应用详情</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <Button variant="outline" @click="showScaleDialog = true">
                        <Expand class="mr-2 h-4 w-4" />
                        扩容
                    </Button>
                    <Button variant="outline" @click="editDeployment">
                        <Edit class="mr-2 h-4 w-4" />
                        编辑
                    </Button>
                    <Button variant="destructive" @click="showDeleteDialog = true">
                        <Trash2 class="mr-2 h-4 w-4" />
                        删除
                    </Button>
                </div>
            </div>

            <!-- 使用新的 WorkloadDetails 组件 -->
            <WorkloadDetails :workload-name="deploymentName" :workload-type="'deployment'" />

            <!-- 扩容对话框 -->
            <Dialog v-model:open="showScaleDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>扩容工作负载</DialogTitle>
                        <DialogDescription> 调整 {{ deploymentName }} 的副本数 </DialogDescription>
                    </DialogHeader>

                    <div class="space-y-4">
                        <div>
                            <Label for="replicas">副本数</Label>
                            <Input id="replicas" v-model.number="scaleForm.replicas" type="number" min="0" max="100" class="mt-1" />
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" @click="showScaleDialog = false">取消</Button>
                        <Button @click="scaleDeployment" :disabled="scaling">
                            <Loader2 v-if="scaling" class="mr-2 h-4 w-4 animate-spin" />
                            确认扩容
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 删除确认对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>删除工作负载</DialogTitle>
                        <DialogDescription> 确定要删除工作负载 "{{ deploymentName }}" 吗？此操作不可撤销。 </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false">取消</Button>
                        <Button variant="destructive" @click="deleteDeployment" :disabled="deleting">
                            <Loader2 v-if="deleting" class="mr-2 h-4 w-4 animate-spin" />
                            删除
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import WorkloadDetails from '@/components/workloads/WorkloadDetails.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { Workspace } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { ArrowLeft, Edit, Expand, Loader2, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    deploymentName: string;
    workspace: Workspace;
}

const props = defineProps<Props>();
const workspace = computed(() => props.workspace);

const resourcesStore = useResourcesStore();
const showScaleDialog = ref(false);
const showDeleteDialog = ref(false);
const scaling = ref(false);
const deleting = ref(false);

const scaleForm = ref({
    replicas: 1,
});

// 获取 deployment 数据用于扩容
const deploymentData = computed(() => {
    return resourcesStore.collections.deployments.find((d) => d.name === props.deploymentName);
});

// 确保资源已加载
onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }

    // 设置扩容表单的初始值
    if (deploymentData.value) {
        scaleForm.value.replicas = deploymentData.value.replicas;
    }
});

const editDeployment = () => {
    router.visit(route('deployments.edit', { deployment: props.deploymentName }));
};

const scaleDeployment = async () => {
    if (!deploymentData.value) return;

    try {
        scaling.value = true;
        await axios.patch(`/api/deployments/${props.deploymentName}/scale`, {
            replicas: scaleForm.value.replicas,
        });

        toast.success('工作负载扩容成功');
        showScaleDialog.value = false;
        // 数据将通过 WebSocket 自动更新
    } catch (error) {
        console.error('扩容工作负载失败:', error);
        toast.error('扩容工作负载失败');
    } finally {
        scaling.value = false;
    }
};

const deleteDeployment = async () => {
    if (!deploymentData.value) return;

    try {
        deleting.value = true;
        await axios.delete(`/api/deployments/${props.deploymentName}`);

        toast.success('工作负载删除成功');
        router.visit(route('applications.index'));
    } catch (error) {
        console.error('删除工作负载失败:', error);
        toast.error('删除工作负载失败');
    } finally {
        deleting.value = false;
        showDeleteDialog.value = false;
    }
};
</script>
