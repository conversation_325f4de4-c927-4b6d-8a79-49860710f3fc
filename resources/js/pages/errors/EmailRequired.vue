<script setup lang="ts">
import Logo from '@/assets/images/Logo.png';
import LiquidLogo from '@/components/LiquidLogo/LiquidLogo.vue';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
</script>

<template>
    <div class="flex min-h-screen items-center justify-center p-4">
        <Card class="w-full md:max-w-lg">
            <CardHeader class="space-y-4 text-center">
                <LiquidLogo :image-url="Logo"></LiquidLogo>
                <div>
                    <CardTitle class="mb-2 text-3xl"> 抱歉，我们的系统依赖邮箱才能工作 </CardTitle>
                    <CardDescription class="text-lg"> 给您带来的不便，请前往下面的地址来绑定邮箱，绑定后请再次登录。 </CardDescription>
                </div>
            </CardHeader>
            <CardContent class="text-center">
                <Button as-child variant="outline">
                    <a target="_blank" href="https://auth.leaflow.cn/email/change"> 绑定邮箱 </a>
                </Button>
            </CardContent>
        </Card>
    </div>
</template>
