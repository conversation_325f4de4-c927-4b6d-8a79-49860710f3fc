<template>
    <Head title="应用管理" />

    <AppLayout>
        <div class="p-4 md:p-8">
            <div class="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
                <div>
                    <h1 class="text-2xl font-bold">应用管理</h1>
                    <p class="mt-1 text-sm text-gray-500">管理您的无状态和有状态应用。</p>
                </div>
                <div class="flex w-full items-center gap-2 md:w-auto">
                    <div class="relative w-full md:w-64">
                        <Search class="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input v-model="searchTerm" placeholder="搜索应用..." class="pl-9" />
                    </div>
                    <Button as-child>
                        <Link :href="route('applications.create')"> <Plus class="mr-2 h-4 w-4" /> 新建应用 </Link>
                    </Button>
                </div>
            </div>

            <div v-if="isLoading" class="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                <Card v-for="i in 8" :key="i" class="animate-pulse">
                    <CardHeader>
                        <div class="h-6 w-3/4 rounded bg-muted"></div>
                    </CardHeader>
                    <CardContent>
                        <div class="mb-4 h-4 w-1/2 rounded bg-muted"></div>
                        <div class="h-4 w-1/4 rounded bg-muted"></div>
                    </CardContent>
                </Card>
            </div>

            <div v-else-if="filteredApplications.length > 0" class="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                <Card
                    v-for="app in filteredApplications"
                    :key="`${app.type}-${app.name}`"
                    class="flex cursor-pointer flex-col justify-between transition-all hover:shadow-lg"
                    @click="viewApplication(app)"
                >
                    <CardHeader>
                        <div class="flex items-start justify-between">
                            <CardTitle class="flex items-center gap-2">
                                <Package class="h-5 w-5" />
                                <span class="truncate">{{ app.name }}</span>
                            </CardTitle>
                            <Badge :variant="app.type === 'Deployment' ? 'default' : 'secondary'" class="shrink-0">
                                {{ app.type === 'Deployment' ? '无状态' : '有状态' }}
                            </Badge>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div class="flex items-center gap-2 text-sm text-muted-foreground">
                            <div class="h-2.5 w-2.5 rounded-full" :class="getStatusColor(app.status)"></div>
                            <span>{{ formatK8sStatus(app.status) }}</span>
                        </div>
                        <div class="mt-2 text-sm font-semibold">{{ app.ready_replicas ?? 0 }} / {{ app.replicas ?? 0 }} Ready</div>
                        <p class="mt-4 text-xs text-muted-foreground">创建于 {{ new Date(app.created_at!).toLocaleString() }}</p>
                    </CardContent>
                </Card>
            </div>

            <div v-else class="mt-16 flex flex-col items-center justify-center text-center">
                <PackagePlus class="h-16 w-16 text-muted-foreground" />
                <h3 class="mt-4 text-lg font-semibold">没有找到应用</h3>
                <p class="mt-2 text-sm text-muted-foreground">看起来这里还没有任何应用，从创建一个新应用开始吧。</p>
                <Button as-child class="mt-6">
                    <Link :href="route('applications.create')"> <Plus class="mr-2 h-4 w-4" /> 新建应用 </Link>
                </Button>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/AppLayout.vue';
import { formatK8sStatus } from '@/lib/formatK8sStatus';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { Deployment, StatefulSet } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { Package, PackagePlus, Plus, Search } from 'lucide-vue-next';
import { computed, onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

type Application = (Deployment | StatefulSet) & { type: 'Deployment' | 'StatefulSet' };

const resourcesStore = useResourcesStore();

const searchTerm = ref('');

// 页面加载时如未加载资源则拉取
onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }
});

const isLoading = computed(() => resourcesStore.isLoading);

const applications = computed(() => {
    // 合并 deployments 和 statefulsets，确保 type 字段为字面量类型
    const deployments = resourcesStore.collections.deployments.map((d) => ({ ...d, type: 'Deployment' as const }));
    const statefulsets = resourcesStore.collections.statefulsets.map((s) => ({ ...s, type: 'StatefulSet' as const }));
    return [...deployments, ...statefulsets].sort((a, b) => new Date(b.created_at!).getTime() - new Date(a.created_at!).getTime());
});

const filteredApplications = computed(() => {
    if (!searchTerm.value) {
        return applications.value;
    }
    return applications.value.filter((app) => app.name.toLowerCase().includes(searchTerm.value.toLowerCase()));
});

const getStatusColor = (status: string) => {
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('running') || lowerStatus.includes('available')) {
        return 'bg-green-500';
    }
    if (lowerStatus.includes('pending') || lowerStatus.includes('updating')) {
        return 'bg-yellow-500 animate-pulse';
    }
    if (lowerStatus.includes('unavailable') || lowerStatus.includes('failed')) {
        return 'bg-red-500';
    }
    return 'bg-gray-400';
};

const viewApplication = (app: Application) => {
    if (app.type === 'Deployment') {
        router.visit(route('deployments.show', { deployment: app.name }));
    } else {
        router.visit(route('statefulsets.show', { statefulset: app.name }));
    }
};
</script>

<style scoped>
/* You can add any additional styles here */
</style>
