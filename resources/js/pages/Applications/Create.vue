<template>
    <AppLayout>
        <Head title="创建应用" />
        <div class="p-4 md:p-6 lg:p-8">
            <div class="mx-auto max-w-4xl">
                <div class="mb-8">
                    <h1 class="text-2xl font-bold tracking-tight">创建应用</h1>
                    <p class="mt-2 text-muted-foreground">选择工作负载类型并配置您的新应用程序。</p>
                </div>

                <div class="mb-8 space-y-4">
                    <Label>工作负载类型</Label>
                    <div class="flex flex-col gap-4 sm:flex-row">
                        <div
                            class="flex-1 cursor-pointer rounded-lg border p-4 transition-all"
                            :class="{
                                'border-primary bg-primary/5': workloadType === 'deployment',
                                'hover:border-primary/50': workloadType !== 'deployment',
                            }"
                            @click="workloadType = 'deployment'"
                        >
                            <h3 class="font-semibold">无状态</h3>
                            <p class="mt-1 text-sm text-muted-foreground">适用于不需要持久化存储的 Web 服务器等应用。</p>
                        </div>
                        <div
                            class="flex-1 cursor-pointer rounded-lg border p-4 transition-all"
                            :class="{
                                'border-primary bg-primary/5': workloadType === 'statefulset',
                                'hover:border-primary/50': workloadType !== 'statefulset',
                            }"
                            @click="workloadType = 'statefulset'"
                        >
                            <h3 class="font-semibold">有状态</h3>
                            <p class="mt-1 text-sm text-muted-foreground">适用于需要稳定网络标识和持久化存储的数据库等应用。</p>
                        </div>
                    </div>
                </div>

                <WorkloadForm v-model="formData" :workload-type="workloadType" />

                <div class="mt-8 flex justify-end">
                    <Button @click="submit" :disabled="isSubmitting">
                        {{ isSubmitting ? '创建中...' : '创建' }}
                    </Button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import WorkloadForm from '@/components/workloads/WorkloadForm.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { CreateDeploymentData, CreateStatefulSetData } from '@/types';
import { Head, router, usePage } from '@inertiajs/vue3';
import { ref, toRaw, watch } from 'vue';
import { toast } from 'vue-sonner';

const workloadType = ref<'deployment' | 'statefulset'>('deployment');

const createInitialFormData = (): CreateDeploymentData | CreateStatefulSetData => ({
    name: '',
    replicas: 1,
    image_pull_secrets: [],
    labels: {},
    containers: [
        {
            name: 'main',
            image: '',
            working_dir: '',
            command: [],
            args: [],
            ports: [],
            env: [],
            env_from_configmap: [],
            env_from_secret: [],
            volume_mounts: [],
            configmap_mounts: [],
            secret_mounts: [],
            resources: {
                memory: 512,
                cpu: 500,
            },
            liveness_probe: undefined,
            readiness_probe: undefined,
            startup_probe: undefined,
        },
    ],
});

const formData = ref(createInitialFormData());
const isSubmitting = ref(false);
const page = usePage();

watch(workloadType, () => {
    formData.value = createInitialFormData();
});

const submit = async () => {
    isSubmitting.value = true;
    if (!page.props.auth.workspace) {
        return;
    }
    const url = workloadType.value === 'deployment' ? route('api.deployments.store') : route('api.statefulsets.store');

    try {
        await axios.post(url, toRaw(formData.value));
        toast.success('创建成功', {
            description: `工作负载 ${formData.value.name} 已成功创建。`,
        });
        const redirectUrl = workloadType.value === 'deployment' ? route('applications.index') : route('applications.index');
        router.visit(redirectUrl);
    } catch (error: any) {
        toast.error('创建失败', {
            description: error.response?.data?.message || '发生未知错误',
        });
    } finally {
        isSubmitting.value = false;
    }
};
</script>
