/// <reference types="../types/globals" />
import axios from '@/lib/axios';
import eventBus from '@/lib/eventBus';
import { defineStore } from 'pinia';
import type { Workspace } from '../types';

interface WorkspaceState {
    workspaces: Workspace[];
    isLoaded: boolean;
    currentWorkspace: Workspace | null;
}

export const useWorkspaceStore = defineStore('workspace', {
    state: (): WorkspaceState => ({
        workspaces: [],
        isLoaded: false,
        currentWorkspace: null,
    }),

    actions: {
        async fetchWorkspaces() {
            try {
                const response = await axios.get('/api/workspaces');

                if (response.data) {
                    this.workspaces = response.data.data;
                    this.currentWorkspace = response.data.currentWorkspace;
                }

                this.isLoaded = true;
            } catch (error) {
                console.error('获取工作空间数据失败:', error);
            }
        },

        switchWorkspace(workspaceId: number) {
            return new Promise((resolve, reject) => {
                axios
                    .post(`/api/workspaces/${workspaceId}/set-current`)
                    .then((response) => {
                        // 寻找 id 然后赋值给 currentWorkspace
                        this.currentWorkspace = this.workspaces.find((workspace) => workspace.id === workspaceId) || null;

                        // 发出工作区切换事件
                        eventBus.emit('workspace:switched', workspaceId);

                        // 切换成功，让 Inertia 处理页面状态更新
                        resolve(true);
                    })
                    .catch((error) => {
                        console.error('切换工作空间失败:', error);
                        reject(new Error('切换工作空间失败'));
                    });
            });
        },
    },
});
