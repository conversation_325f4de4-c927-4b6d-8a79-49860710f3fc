import { getPageSnapshotWithDiff } from '@/lib/webContent';
import { executeWebTool, webToolDefinitions } from '@/lib/webTools';
import axios from 'axios';
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

export interface FunctionCall {
    name: string;
    arguments?: any;
    result?: any;
    success: boolean;
    error?: string;
}

export interface Message {
    id: string;
    role: 'user' | 'assistant' | 'system' | 'tool';
    content: string | null;
    created_at: string;
    image_url?: string | null;
    function_calls?: FunctionCall[];
    tool_call_id?: string; // 用于 tool 角色消息
    tool_calls?: any[]; // 用于存储 OpenAI 的 tool_calls 信息
}

export const useAIAssistantStore = defineStore('aiAssistant', () => {
    // 状态 - 只保存当前对话
    const currentMessages = ref<Message[]>([]);
    const isLoading = ref(false);

    // 生成唯一 ID
    const generateId = () => {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    };

    // 获取当前网页内容
    const getCurrentWebContent = () => {
        const { snapshot } = getPageSnapshotWithDiff();
        return `当前网页信息：
页面标题：${snapshot.title}
页面URL：${snapshot.url}
视口大小：${snapshot.viewport.width}x${snapshot.viewport.height}

页面结构：
${JSON.stringify(snapshot.domTree, null, 2)}`;
    };

    // 优化工具调用内容以减少API上下文长度
    const optimizeToolCallsForAPI = (messages: Message[]) => {
        // 首先清理除最后一条用户消息外的所有用户消息中的 current_web_content 标签
        const cleanedMessages = cleanWebContentFromOldUserMessages(messages);
        return cleanedMessages.map(formatMessageForAPI);
    };

    // 格式化单个消息为API格式
    const formatMessageForAPI = (msg: Message) => {
        const openaiMessage: any = {
            role: msg.role,
            content: msg.content,
        };

        // 如果是 tool 角色，需要添加 tool_call_id
        if (msg.role === 'tool' && msg.tool_call_id) {
            openaiMessage.tool_call_id = msg.tool_call_id;
        }

        // 如果是 assistant 角色且有 tool_calls，添加 tool_calls 信息
        if (msg.role === 'assistant' && msg.tool_calls) {
            openaiMessage.tool_calls = msg.tool_calls;
        }

        // 如果消息包含图片，添加图片内容
        if (msg.image_url && msg.role === 'user' && msg.content) {
            openaiMessage.content = [
                {
                    type: 'text',
                    text: msg.content,
                },
                {
                    type: 'image_url',
                    image_url: {
                        url: msg.image_url,
                    },
                },
            ];
        }

        return openaiMessage;
    };

    // 创建新对话（清空当前对话）
    const createConversation = async () => {
        try {
            isLoading.value = true;
            currentMessages.value = [];
            saveToLocalStorage();
        } catch (error) {
            console.error('创建对话失败:', error);
            throw error;
        } finally {
            isLoading.value = false;
        }
    };

    // 添加消息到当前对话
    const addMessage = (message: Omit<Message, 'id' | 'created_at'>) => {
        const newMessage: Message = {
            ...message,
            id: generateId(),
            created_at: new Date().toISOString(),
        };

        currentMessages.value.push(newMessage);
        saveToLocalStorage();
    };

    // 发送消息到 OpenAI API（支持工具调用）
    const sendMessage = async (content: string, imageData?: { url: string; mimeType: string }) => {
        // 添加用户消息（不包含网页内容）
        const userMessage: Omit<Message, 'id' | 'created_at'> = {
            role: 'user',
            content: content,
            image_url: imageData?.url || null,
        };
        addMessage(userMessage);

        // 在最后一条用户消息中附加网页内容
        attachWebContentToLastUserMessage();

        try {
            isLoading.value = true;
            await processConversationWithTools();
        } catch (error) {
            console.error('发送消息失败:', error);
            throw error;
        } finally {
            isLoading.value = false;
        }
    };

    // 在最后一条用户消息中附加网页内容
    const attachWebContentToLastUserMessage = () => {
        if (currentMessages.value.length === 0) return;

        // 找到最后一个用户消息
        for (let i = currentMessages.value.length - 1; i >= 0; i--) {
            const message = currentMessages.value[i];
            if (message.role === 'user') {
                const webContent = getCurrentWebContent();

                // 检查消息是否已经包含网页内容标签
                if (message.content && message.content.includes('<current_web_content>')) {
                    // 更新现有的网页内容
                    message.content = message.content.replace(
                        /<current_web_content>.*?<\/current_web_content>/s,
                        `<current_web_content>\n${webContent}\n</current_web_content>`,
                    );
                } else {
                    // 添加网页内容标签
                    message.content = (message.content || '') + `\n\n<current_web_content>\n${webContent}\n</current_web_content>`;
                }

                saveToLocalStorage();
                break;
            }
        }
    };

    // 处理包含工具调用的对话
    const processConversationWithTools = async () => {
        // // 准备发送到 OpenAI API 的消息，添加系统提示
        // const systemMessage = {
        //     role: 'system',
        //     content: getSystemPrompt(),
        // };
        const optimizedMessages = optimizeToolCallsForAPI(currentMessages.value);
        const messages = [...optimizedMessages];

        // 调用 OpenAI API（带工具调用支持）
        const response = await axios.post('/api/chat/completions', {
            messages: messages,
            max_tokens: 4000,
            temperature: 0.7,
            stream: false,
            tools: webToolDefinitions.map((tool) => ({
                type: 'function',
                function: tool,
            })),
            tool_choice: 'auto',
            mode: 'agent',
        });

        const choice = response.data.choices[0];
        const assistantContent = choice.message.content || '';

        // 处理工具调用
        if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
            // 添加助手消息（包含工具调用请求）
            const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
                role: 'assistant',
                content: assistantContent,
                tool_calls: choice.message.tool_calls,
            };
            addMessage(assistantMessage);

            // 执行每个工具调用并添加 tool 角色消息
            for (const toolCall of choice.message.tool_calls) {
                if (toolCall.type === 'function') {
                    const functionName = toolCall.function.name;
                    let functionArgs: any = {};

                    try {
                        functionArgs = JSON.parse(toolCall.function.arguments || '{}');
                    } catch (e) {
                        console.error('解析工具参数失败:', e);
                    }

                    let toolResult: any;
                    try {
                        // 执行工具调用
                        toolResult = await executeWebTool(functionName, functionArgs);

                        // 添加 tool 角色消息
                        const toolMessage: Omit<Message, 'id' | 'created_at'> = {
                            role: 'tool',
                            content: JSON.stringify(toolResult),
                            tool_call_id: toolCall.id,
                        };
                        addMessage(toolMessage);
                    } catch (error) {
                        // 添加错误的 tool 角色消息
                        const errorMessage: Omit<Message, 'id' | 'created_at'> = {
                            role: 'tool',
                            content: JSON.stringify({
                                success: false,
                                error: error instanceof Error ? error.message : '未知错误',
                            }),
                            tool_call_id: toolCall.id,
                        };
                        addMessage(errorMessage);
                    }
                }
            }

            // 工具调用完成后，更新最后一个用户消息的网页内容
            attachWebContentToLastUserMessage();

            // 递归调用以获取基于工具结果的最终回复
            await processConversationWithTools();
        } else {
            // 没有工具调用，直接添加助手回复
            const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
                role: 'assistant',
                content: assistantContent,
            };
            addMessage(assistantMessage);
        }
    };

    // 发送消息并包含页面内容（现在页面内容会自动附加到用户消息）
    const sendMessageWithPageContent = async (content: string, imageData?: { url: string; mimeType: string }) => {
        // 直接发送消息，页面内容会自动附加到用户消息
        return await sendMessage(content, imageData);
    };

    // 保存到本地存储（只保存当前对话）
    const saveToLocalStorage = () => {
        // 清理除最后一条用户消息外的所有用户消息中的 current_web_content 标签
        const cleanedMessages = cleanWebContentFromOldUserMessages(currentMessages.value);
        localStorage.setItem('ai-current-messages', JSON.stringify(cleanedMessages));
    };

    // 清理除最后一条用户消息外的所有用户消息中的 current_web_content 标签
    const cleanWebContentFromOldUserMessages = (messages: Message[]): Message[] => {
        if (messages.length === 0) return messages;

        // 找到最后一条用户消息的索引
        let lastUserMessageIndex = -1;
        for (let i = messages.length - 1; i >= 0; i--) {
            if (messages[i].role === 'user') {
                lastUserMessageIndex = i;
                break;
            }
        }

        // 如果没有找到用户消息，直接返回原数组
        if (lastUserMessageIndex === -1) return messages;

        // 创建新的消息数组，清理除最后一条用户消息外的所有用户消息中的 current_web_content
        return messages.map((message, index) => {
            if (message.role === 'user' && index !== lastUserMessageIndex && message.content) {
                // 移除 current_web_content 标签
                const cleanedContent = message.content.replace(/<current_web_content>.*?<\/current_web_content>/gs, '').trim();
                return {
                    ...message,
                    content: cleanedContent,
                };
            }
            return message;
        });
    };

    // 从本地存储加载（只加载当前对话）
    const loadFromLocalStorage = () => {
        try {
            const savedMessages = localStorage.getItem('ai-current-messages');
            if (savedMessages) {
                currentMessages.value = JSON.parse(savedMessages);
            }
        } catch (error) {
            console.error('从本地存储加载失败:', error);
        }
    };

    // 重置状态
    const reset = () => {
        currentMessages.value = [];
        localStorage.removeItem('ai-current-messages');
    };

    // 初始化时加载数据
    loadFromLocalStorage();

    return {
        // 状态
        currentMessages,
        isLoading,

        // 计算属性
        currentConversationId: computed(() => 'current'), // 固定返回当前对话 ID

        // 方法
        getCurrentWebContent,
        createConversation,
        addMessage,
        sendMessage,
        sendMessageWithPageContent,
        saveToLocalStorage,
        loadFromLocalStorage,
        reset,
    };
});
