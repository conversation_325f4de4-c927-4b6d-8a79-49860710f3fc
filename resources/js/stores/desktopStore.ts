import { defineStore } from 'pinia';

export interface DesktopApp {
    id: string;
    name: string;
    icon: string; // Lucide 图标名称或 URL
    url: string;
    type: 'browser';
}

export interface DesktopState {
    apps: DesktopApp[];
    wallpaper: string | null;
}

export const useDesktopStore = defineStore('desktop', {
    state: (): DesktopState => ({
        apps: [],
        wallpaper: null,
    }),

    getters: {
        getAppById: (state) => (id: string) => state.apps.find((a) => a.id === id),
    },

    actions: {
        setApps(apps: DesktopApp[]) {
            this.apps = apps;
        },
        setWallpaper(url: string | null) {
            this.wallpaper = url;
        },
        addApp(app: DesktopApp) {
            if (!this.apps.find((a) => a.id === app.id)) {
                this.apps.push(app);
            }
        },
    },
    // 如需持久化，可启用以下配置
    // persist: {
    //     key: 'desktopStore',
    //     paths: ['wallpaper'],
    // },
});
