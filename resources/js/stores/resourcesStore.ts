import axios from '@/lib/axios';
import eventBus from '@/lib/eventBus';
import type {
    ConfigMap,
    Deployment,
    HorizontalPodAutoscaler,
    Ingress,
    K8sEvent,
    NamespaceMetrics,
    Pod,
    Secret,
    Service,
    StatefulSet,
    Storage,
} from '@/types';
import { defineStore } from 'pinia';

// 资源变更事件类型
export interface ResourceChangeEvent {
    namespace: string;
    cluster: {
        id: number;
        name: string;
    };
    resource_type: string;
    changes: {
        created?: (
            | Deployment
            | StatefulSet
            | Service
            | Pod
            | Ingress
            | Storage
            | Secret
            | ConfigMap
            | HorizontalPodAutoscaler
            | K8sEvent
            | NamespaceMetrics
        )[];
        updated?: (
            | Deployment
            | StatefulSet
            | Service
            | Pod
            | Ingress
            | Storage
            | Secret
            | ConfigMap
            | (HorizontalPodAutoscaler & { previousResourceVersion?: string })
            | (K8sEvent & { previousResourceVersion?: string })
            | NamespaceMetrics
        )[];
        deleted?: (
            | Deployment
            | StatefulSet
            | Service
            | Pod
            | Ingress
            | Storage
            | Secret
            | ConfigMap
            | HorizontalPodAutoscaler
            | K8sEvent
            | NamespaceMetrics
        )[];
    };
    summary: {
        created_count: number;
        updated_count: number;
        deleted_count: number;
        total_changes: number;
    };
    timestamp: string;
}

// 资源集合类型
export interface ResourceCollections {
    deployments: Deployment[];
    statefulsets: StatefulSet[];
    services: Service[];
    pods: Pod[];
    ingresses: Ingress[];
    storages: Storage[];
    secrets: Secret[];
    configmaps: ConfigMap[];
    horizontalpodautoscalers: HorizontalPodAutoscaler[];
    events: K8sEvent[];
}

export interface ResourceMetrics {
    pods: NamespaceMetrics;
    // 未来可以添加其他资源的指标
}

export const useResourcesStore = defineStore('resources', {
    state: () => ({
        // 资源集合
        collections: {
            deployments: [],
            statefulsets: [],
            services: [],
            pods: [],
            ingresses: [],
            storages: [],
            secrets: [],
            configmaps: [],
            horizontalpodautoscalers: [],
            events: [],
        } as ResourceCollections,

        metrics: {
            pods: {},
        } as ResourceMetrics,

        // 监听状态
        isListening: false,
        currentNamespace: null as string | null,

        // 加载状态
        isLoaded: false,
        isLoading: false,
        lastUpdate: null as Date | null,
        error: null as string | null,
    }),

    getters: {
        // 按类型获取资源数量
        getResourceCount: (state) => (resource_type: keyof ResourceCollections) => {
            return state.collections[resource_type]?.length || 0;
        },

        // 获取总资源数量
        getTotalResourceCount: (state) => {
            return Object.values(state.collections).reduce((total, collection) => total + collection.length, 0);
        },

        // 按类型获取资源列表
        getResourcesByType: (state) => (resource_type: keyof ResourceCollections) => {
            return state.collections[resource_type] || [];
        },

        // 获取所有资源数量统计
        getResourceCounts: (state) => {
            const counts: Record<string, number> = {};
            Object.keys(state.collections).forEach((key) => {
                counts[key] = state.collections[key as keyof ResourceCollections].length;
            });
            return counts;
        },

        // 检查是否有某个资源
        hasResource: (state) => (resource_type: keyof ResourceCollections, name: string) => {
            return state.collections[resource_type]?.some((resource: any) => resource.name === name) || false;
        },

        // 按名称获取资源
        getResourceByName: (state) => (resource_type: keyof ResourceCollections, name: string) => {
            return state.collections[resource_type]?.find((resource: any) => resource.name === name);
        },
    },

    actions: {
        // 清空所有资源
        clearAllResources() {
            this.collections = {
                deployments: [],
                statefulsets: [],
                services: [],
                pods: [],
                ingresses: [],
                storages: [],
                secrets: [],
                configmaps: [],
                horizontalpodautoscalers: [],
                events: [],
            };
            this.metrics = {
                pods: {},
            };
            this.isLoaded = false;
            this.lastUpdate = null;
            this.error = null;

            // 发送清空事件
            eventBus.emit('resource:store:cleared');
        },

        // 批量拉取所有资源（初次加载/切换工作区时调用）
        async fetchAllResources() {
            console.log('fetchAllResources');
            this.clearAllResources();
            this.isLoading = true;
            this.error = null;

            try {
                // 调用统一的 API 获取所有资源
                const response = await axios.get('/api/workspaces/current/all');
                const data = response.data;

                // 更新各类资源集合
                this.collections = {
                    deployments: data.deployments || [],
                    statefulsets: data.statefulsets || [],
                    services: data.services || [],
                    pods: data.pods || [],
                    ingresses: data.ingresses || [],
                    storages: data.storages || [],
                    secrets: data.secrets || [],
                    configmaps: data.configmaps || [],
                    horizontalpodautoscalers: data.horizontalpodautoscalers || [],
                    events: data.events || [],
                };
                // 处理 Pod 指标
                let podMetrics: Record<string, any> = {};

                if (Array.isArray(data.pod_metrics)) {
                    data.pod_metrics.forEach((m: any) => {
                        if (m && m.name) {
                            podMetrics[m.name] = m;
                        }
                    });
                }

                this.metrics = {
                    pods: podMetrics,
                };

                console.log(this.collections);

                this.isLoaded = true;
                this.lastUpdate = new Date();

                // 发送加载完成事件
                eventBus.emit('resource:store:loaded');
                eventBus.emit('resource:refresh:complete');

                console.log('资源数据已加载:', {
                    deployments: this.collections.deployments.length,
                    statefulsets: this.collections.statefulsets.length,
                    services: this.collections.services.length,
                    pods: this.collections.pods.length,
                    ingresses: this.collections.ingresses.length,
                    storages: this.collections.storages.length,
                    secrets: this.collections.secrets.length,
                    configmaps: this.collections.configmaps.length,
                    horizontalpodautoscalers: this.collections.horizontalpodautoscalers.length,
                    events: this.collections.events.length,
                });
            } catch (error: any) {
                this.error = error.message || '获取资源失败';
                this.isLoaded = false;

                // 发送错误事件
                eventBus.emit('resource:refresh:error', error);

                console.error('获取资源数据失败:', error);
                throw error;
            } finally {
                this.isLoading = false;
            }
        },

        // 处理资源变更事件（从 WebSocket 事件中调用）
        handleResourceChange(event: ResourceChangeEvent) {
            const { resource_type, changes } = event;

            if (!changes || !resource_type) {
                console.warn('无效的资源变更事件:', event);
                return;
            }

            // 获取对应的资源集合
            const collection = this.collections[resource_type as keyof ResourceCollections] as any[];
            if (!collection) {
                console.warn(`未知的资源类型: ${resource_type}`);
                return;
            }

            // 处理创建的资源
            if (changes.created && changes.created.length > 0) {
                for (const newResource of changes.created) {
                    // 直接使用事件中的完整资源数据
                    const existingIndex = collection.findIndex((item: any) => item.name === (newResource as any).name);
                    if (existingIndex === -1) {
                        // 添加新资源
                        collection.push(newResource);
                        console.log(`资源已创建: ${resource_type}/${(newResource as any).name}`);
                    } else {
                        // 如果已存在，可能是网络延迟导致的重复事件，替换为最新数据
                        collection.splice(existingIndex, 1, newResource);
                        console.log(`资源已创建(替换现有): ${resource_type}/${(newResource as any).name}`);
                    }
                }
            }

            // 处理更新的资源
            if (changes.updated && changes.updated.length > 0) {
                for (const updatedResource of changes.updated) {
                    // 检查 resourceVersion（防止处理过期事件）
                    const existingResourceIndex = collection.findIndex((item: any) => item.name === updatedResource.name);

                    if (existingResourceIndex >= 0) {
                        const existingResource = collection[existingResourceIndex];
                        const existingVersion = (existingResource as any).resource_version || (existingResource as any).resourceVersion;
                        const newVersion = (updatedResource as any).resource_version || (updatedResource as any).resourceVersion;

                        // 如果新的 resourceVersion 小于或等于现有的，则忽略此事件
                        if (existingVersion && newVersion && parseInt(newVersion) <= parseInt(existingVersion)) {
                            console.log(
                                `忽略过期的资源事件: ${resource_type}/${(updatedResource as any).name}, 版本: ${newVersion} <= ${existingVersion}`,
                            );
                            continue;
                        }

                        // 直接使用事件中的完整资源数据替换现有资源
                        collection.splice(existingResourceIndex, 1, updatedResource);
                        console.log(`资源已更新: ${resource_type}/${(updatedResource as any).name}`);
                    } else {
                        // 如果本地没有该资源，可能是新创建的，直接添加
                        collection.push(updatedResource);
                        console.log(`资源已更新(新增): ${resource_type}/${(updatedResource as any).name}`);
                    }
                }
            }

            // 处理删除的资源
            if (changes.deleted && changes.deleted.length > 0) {
                for (const deletedResource of changes.deleted) {
                    const existingIndex = collection.findIndex((item: any) => item.name === (deletedResource as any).name);
                    if (existingIndex >= 0) {
                        collection.splice(existingIndex, 1);
                        console.log(`资源已删除: ${resource_type}/${(deletedResource as any).name}`);
                    }
                }
            }

            // 更新最后更新时间
            this.lastUpdate = new Date();

            console.log(
                `资源变更处理完成: ${resource_type}, 创建: ${changes.created?.length || 0}, 更新: ${changes.updated?.length || 0}, 删除: ${changes.deleted?.length || 0}`,
            );
        },

        // 专门用于获取事件数据
        async fetchEvents(sinceMinutes: number | null = 30) {
            try {
                const params = sinceMinutes ? { since_minutes: sinceMinutes } : {};
                const response = await axios.get('/api/workspaces/current/events', { params });
                this.collections.events = response.data;
                console.log(`事件已刷新 (范围: ${sinceMinutes || '所有'})`);
            } catch (error: any) {
                console.error('获取事件数据失败:', error);
                this.error = error.message || '获取事件失败';
            }
        },

        // 指标已在 fetchAllResources 中处理，此占位函数已弃用
        // async fetchMetrics() {},

        // 设置监听状态
        setListeningState(isListening: boolean, namespace?: string) {
            this.isListening = isListening;
            this.currentNamespace = namespace || null;

            // 发送监听状态变化事件
            eventBus.emit('listener:status:changed', {
                isListening: this.isListening,
                namespace: this.currentNamespace,
                error: this.error,
            });
        },

        // 手动刷新单个资源类型（可选功能）
        async refreshResourceType(resource_type: keyof ResourceCollections) {
            try {
                const response = await axios.get(`/api/${resource_type}`);
                this.collections[resource_type] = response.data;
                this.lastUpdate = new Date();
                console.log(`${resource_type} 资源已刷新`);
            } catch (error: any) {
                console.error(`刷新 ${resource_type} 失败:`, error);
                throw error;
            }
        },

        // 添加单个资源到集合（用于实时添加，无需等待事件）
        addResource(resource_type: keyof ResourceCollections, resource: any) {
            const collection = this.collections[resource_type] as any[];
            const existingIndex = collection.findIndex((item: any) => item.name === resource.name);

            if (existingIndex >= 0) {
                collection.splice(existingIndex, 1, resource);
            } else {
                collection.push(resource);
            }

            this.lastUpdate = new Date();
        },

        // 从集合中移除单个资源（用于实时删除，无需等待事件）
        removeResource(resource_type: keyof ResourceCollections, resourceName: string) {
            const collection = this.collections[resource_type] as any[];
            const existingIndex = collection.findIndex((item: any) => item.name === resourceName);

            if (existingIndex >= 0) {
                collection.splice(existingIndex, 1);
                this.lastUpdate = new Date();
                return true;
            }

            return false;
        },
    },
});
