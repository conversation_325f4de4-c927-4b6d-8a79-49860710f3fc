import '../css/app.css';

import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';

import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

import { createApp, h } from 'vue';
import { ZiggyVue } from 'ziggy-js';
import { initializeTheme } from './composables/useAppearance';
import './echo';
import i18n from './i18n';
import Persistent from './layouts/app/Persistent.vue';
import './lib/axios'; // 初始化 axios 配置
import { initIframeBridge } from './lib/iframeBridge';

const pinia = createPinia();

pinia.use(piniaPluginPersistedstate);

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

// --- Event Bus <-> postMessage Bridge ---
initIframeBridge();
// --- End Bridge ---

createInertiaApp({
    title: (title) => (title ? `${title} - ${appName}` : appName),
    async resolve(name) {
        const page: any = await resolvePageComponent(`./pages/${name}.vue`, import.meta.glob('./pages/**/*.vue'));

        let useDefaultLayout = true;

        if (name.includes('.Clean') || name.includes('.Public') || window.location.pathname.toLowerCase().startsWith('/public')) {
            useDefaultLayout = false;
        }

        if (useDefaultLayout) {
            page.default.layout = page.default.layout || Persistent;
        }

        return page;
    },
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(pinia)
            .use(i18n)
            .use(ZiggyVue);

        app.mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});

// This will set light / dark mode on page load...
initializeTheme();
