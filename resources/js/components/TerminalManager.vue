<template>
    <div class="terminal-manager">
        <!-- Terminal Windows -->
        <PersistentTerminal
            v-for="terminal in terminals"
            :key="terminal.id"
            :terminal-id="terminal.id"
            :pod-name="terminal.podName"
            :container-name="terminal.containerName"
            :mode="terminal.mode"
            :initial-position="terminal.position"
            :initial-size="terminal.size"
            :z-index="terminal.zIndex"
            @close="closeTerminal"
            @minimize="minimizeTerminal"
            @maximize="maximizeTerminal"
            @focus="focusTerminal"
            @update-instance="updateTerminalInstance"
        />

        <!-- Terminal Taskbar -->
        <div
            v-if="terminals.length > 0"
            class="fixed bottom-4 left-1/2 z-[9999] -translate-x-1/2 transform rounded-lg border border-border bg-background px-4 py-2 shadow-lg"
        >
            <div class="flex items-center gap-2">
                <Terminal class="h-4 w-4 text-muted-foreground" />
                <span class="text-sm font-medium text-muted-foreground">终端</span>
                <Separator orientation="vertical" class="h-4" />
                <div class="flex items-center gap-1">
                    <Button
                        v-for="terminal in terminals"
                        :key="terminal.id"
                        variant="ghost"
                        size="sm"
                        :class="['h-8 px-2 text-xs', terminal.isMinimized ? 'opacity-60' : '', !terminal.isConnected ? 'text-muted-foreground' : '']"
                        @click="toggleTerminal(terminal)"
                    >
                        <div class="flex items-center gap-1">
                            <div :class="['h-2 w-2 rounded-full', terminal.isConnected ? 'bg-green-500' : 'bg-red-500']" />
                            <span class="max-w-[120px] truncate">
                                {{ terminal.title }}
                            </span>
                        </div>
                    </Button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import PersistentTerminal from '@/components/PersistentTerminal.vue';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import eventBus, { type TerminalInstance } from '@/lib/eventBus';
import { Terminal } from 'lucide-vue-next';
import { onMounted, onUnmounted, ref } from 'vue';

interface TerminalData extends TerminalInstance {
    position: { x: number; y: number };
    size: { width: number; height: number };
}

const terminals = ref<TerminalData[]>([]);
const nextZIndex = ref(1000);

// Terminal management
const openTerminal = (payload: { podName: string; containerName?: string; mode?: 'shell' | 'attach' }) => {
    const { podName, containerName, mode = 'shell' } = payload;

    // Check if terminal already exists
    const existingTerminal = terminals.value.find(
        (t) => t.podName === podName && t.containerName === (containerName || getDefaultContainer(podName)),
    );

    if (existingTerminal) {
        // Focus existing terminal
        focusTerminal(existingTerminal.id);
        return;
    }

    // Create new terminal
    const terminalId = `terminal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const finalContainerName = containerName || getDefaultContainer(podName);

    const newTerminal: TerminalData = {
        id: terminalId,
        podName,
        containerName: finalContainerName,
        mode,
        title: `${podName} (${finalContainerName})`,
        isConnected: false,
        isMinimized: false,
        zIndex: nextZIndex.value++,
        position: calculatePosition(),
        size: { width: 800, height: 600 },
    };

    terminals.value.push(newTerminal);
};

const closeTerminal = (terminalId: string) => {
    const index = terminals.value.findIndex((t) => t.id === terminalId);
    if (index !== -1) {
        terminals.value.splice(index, 1);
    }
};

const minimizeTerminal = (terminalId: string) => {
    const terminal = terminals.value.find((t) => t.id === terminalId);
    if (terminal) {
        terminal.isMinimized = true;
    }
};

const maximizeTerminal = (terminalId: string) => {
    const terminal = terminals.value.find((t) => t.id === terminalId);
    if (terminal) {
        terminal.isMinimized = false;
    }
};

const focusTerminal = (terminalId: string) => {
    const terminal = terminals.value.find((t) => t.id === terminalId);
    if (terminal) {
        terminal.zIndex = nextZIndex.value++;
        if (terminal.isMinimized) {
            terminal.isMinimized = false;
        }
    }
};

const toggleTerminal = (terminal: TerminalData) => {
    if (terminal.isMinimized) {
        maximizeTerminal(terminal.id);
        focusTerminal(terminal.id);
    } else {
        minimizeTerminal(terminal.id);
    }
};

const updateTerminalInstance = (instance: TerminalInstance) => {
    const terminal = terminals.value.find((t) => t.id === instance.id);
    if (terminal) {
        Object.assign(terminal, instance);
    }
};

// Helper functions
const calculatePosition = (): { x: number; y: number } => {
    const offset = terminals.value.length * 30;
    return {
        x: 100 + offset,
        y: 100 + offset,
    };
};

const getDefaultContainer = (podName: string): string => {
    // This is a placeholder - in a real implementation, you'd fetch this from the API
    return 'main';
};

// Event listeners
const handleTerminalOpen = (payload: { podName: string; containerName?: string; mode?: 'shell' | 'attach' }) => {
    openTerminal(payload);
};

const handleTerminalClose = (terminalId: string) => {
    closeTerminal(terminalId);
};

const handleTerminalMinimize = (terminalId: string) => {
    minimizeTerminal(terminalId);
};

const handleTerminalMaximize = (terminalId: string) => {
    maximizeTerminal(terminalId);
};

const handleTerminalFocus = (terminalId: string) => {
    focusTerminal(terminalId);
};

// Lifecycle
onMounted(() => {
    eventBus.on('terminal:open', handleTerminalOpen);
    eventBus.on('terminal:close', handleTerminalClose);
    eventBus.on('terminal:minimize', handleTerminalMinimize);
    eventBus.on('terminal:maximize', handleTerminalMaximize);
    eventBus.on('terminal:focus', handleTerminalFocus);
});

onUnmounted(() => {
    eventBus.off('terminal:open', handleTerminalOpen);
    eventBus.off('terminal:close', handleTerminalClose);
    eventBus.off('terminal:minimize', handleTerminalMinimize);
    eventBus.off('terminal:maximize', handleTerminalMaximize);
    eventBus.off('terminal:focus', handleTerminalFocus);
});

// Expose methods
defineExpose({
    openTerminal,
    closeTerminal,
    terminals,
});
</script>

<style scoped>
.terminal-manager {
    position: relative;
    pointer-events: none;
}

.terminal-manager > * {
    pointer-events: auto;
}
</style>
