<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from '@/components/ui/scroll-area';
import { createVariableResolver } from '@/lib/variableResolver';
import { ChevronDown, ChevronRight, Copy, Info } from 'lucide-vue-next';
import { computed, ref } from 'vue';
import { toast } from 'vue-sonner';

const variableResolver = createVariableResolver();
const isOpen = ref(false);

const availableVariables = computed(() => variableResolver.getAvailableVariables());
const variableDescriptions = computed(() => {
    if ('getVariableDescriptions' in variableResolver) {
        return (variableResolver as any).getVariableDescriptions();
    }
    return {};
});

// 复制变量到剪贴板
const copyVariable = async (variableName: string) => {
    const variableText = `\${${variableName}}`;
    try {
        await navigator.clipboard.writeText(variableText);
        toast.success(`已复制: ${variableText}`);
    } catch (error) {
        toast.error('复制失败');
    }
};

// 变量分组
const variableGroups = computed(() => {
    const groups: Record<string, string[]> = {
        workspace: [],
        user: [],
        time: [],
        other: [],
    };

    availableVariables.value.forEach((variable) => {
        if (variable.startsWith('workspace_') || variable === 'namespace' || variable.startsWith('cluster_')) {
            groups.workspace.push(variable);
        } else if (variable.startsWith('user_')) {
            groups.user.push(variable);
        } else if (variable === 'timestamp' || variable === 'date' || variable === 'datetime') {
            groups.time.push(variable);
        } else {
            groups.other.push(variable);
        }
    });

    return groups;
});

const groupNames = {
    workspace: '工作空间',
    user: '用户',
    time: '时间',
    other: '其他',
};
</script>

<template>
    <Card>
        <Collapsible v-model:open="isOpen">
            <CollapsibleTrigger as-child>
                <CardHeader class="cursor-pointer transition-colors hover:bg-muted/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <Info class="size-4" />
                            <CardTitle class="text-sm">可用变量</CardTitle>
                            <Badge variant="secondary" class="text-xs"> {{ availableVariables.length }} 个 </Badge>
                        </div>
                        <ChevronRight v-if="!isOpen" class="size-4 transition-transform" />
                        <ChevronDown v-else class="size-4 transition-transform" />
                    </div>
                    <CardDescription class="text-xs"> 点击查看可在 YAML 中使用的变量列表 </CardDescription>
                </CardHeader>
            </CollapsibleTrigger>

            <CollapsibleContent>
                <CardContent class="pt-0">
                    <ScrollArea class="h-64">
                        <div class="space-y-4">
                            <template v-for="(variables, groupKey) in variableGroups" :key="groupKey">
                                <div v-if="variables.length > 0" class="space-y-2">
                                    <h4 class="text-sm font-medium text-muted-foreground">
                                        {{ groupNames[groupKey as keyof typeof groupNames] }}
                                    </h4>
                                    <div class="space-y-1">
                                        <div
                                            v-for="variable in variables"
                                            :key="variable"
                                            class="flex items-center justify-between rounded-md border p-2 hover:bg-muted/50"
                                        >
                                            <div class="min-w-0 flex-1">
                                                <div class="flex items-center gap-2">
                                                    <code class="rounded bg-muted px-1.5 py-0.5 font-mono text-xs">
                                                        ${{ '{' + variable + '}' }}
                                                    </code>
                                                </div>
                                                <p v-if="variableDescriptions[variable]" class="mt-1 text-xs text-muted-foreground">
                                                    {{ variableDescriptions[variable] }}
                                                </p>
                                            </div>
                                            <Button variant="ghost" size="icon" class="size-6 shrink-0" @click="copyVariable(variable)">
                                                <Copy class="size-3" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </ScrollArea>

                    <div class="mt-4 rounded-md bg-muted/50 p-3">
                        <h4 class="mb-2 text-sm font-medium">使用说明</h4>
                        <ul class="space-y-1 text-xs text-muted-foreground">
                            <li>• 在 YAML 中使用 <code>${'{变量名}'}</code> 格式引用变量</li>
                            <li>
                                • 例如：<code>mysql8-headless.${{ '{namespace}' }}.svc.cluster.local</code>
                            </li>
                            <li>• 变量会在提交时自动解析为实际值</li>
                            <li>• 点击变量右侧的复制按钮可快速复制变量格式</li>
                        </ul>
                    </div>
                </CardContent>
            </CollapsibleContent>
        </Collapsible>
    </Card>
</template>
