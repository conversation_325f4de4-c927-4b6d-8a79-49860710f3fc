<template>
    <div class="space-y-4">
        <div>
            <Label>{{ label }}</Label>
            <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                {{ description }}
            </p>
        </div>

        <div v-if="loading" class="py-4 text-center">
            <p>加载中...</p>
        </div>

        <div v-else-if="workloads.length === 0" class="py-8 text-center text-gray-500">
            <p>暂无可用的工作负载</p>
        </div>

        <div v-else class="space-y-3">
            <div
                v-for="workload in workloads"
                :key="`${workload.type}-${workload.name}`"
                class="cursor-pointer rounded-lg border p-4 transition-colors"
                :class="{
                    'border-blue-500 bg-blue-50 dark:bg-blue-900/20': workloadModel.type === workload.type && workloadModel.name === workload.name,
                    'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600': !(
                        workloadModel.type === workload.type && workloadModel.name === workload.name
                    ),
                }"
                :title="`选择 ${workload.type} 类型的工作负载: ${workload.name}，包含 ${workload.ports.length} 个端口`"
                role="button"
                :aria-selected="workloadModel.type === workload.type && workloadModel.name === workload.name"
                tabindex="0"
                @click="selectWorkload(workload)"
                @keydown.enter="selectWorkload(workload)"
                @keydown.space.prevent="selectWorkload(workload)"
            >
                <div class="flex items-center justify-between">
                    <div>
                        <div class="flex items-center space-x-2">
                            <Badge :variant="workload.type === 'Deployment' ? 'default' : 'secondary'">
                                <!-- 根据 workload.type 显示不同的图标 -->
                                <template v-if="workload.type === 'Deployment'"> <Boxes class="h-4 w-4" /> 无状态 </template>
                                <template v-else> <Database class="h-4 w-4" /> 有状态 </template>
                            </Badge>
                            <span class="font-medium">{{ workload.name }}</span>
                        </div>
                        <div class="mt-1 text-sm text-gray-500">{{ workload.ports.length }} 个端口可用</div>
                    </div>
                    <div v-if="workloadModel.type === workload.type && workloadModel.name === workload.name" class="text-blue-500">
                        <Check class="h-5 w-5" />
                    </div>
                </div>

                <!-- 端口列表 -->
                <div v-if="showPorts && workload.ports.length > 0" class="mt-3 border-t border-gray-200 pt-3 dark:border-gray-600">
                    <div class="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">可用端口:</div>
                    <div class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
                        <div
                            v-for="port in workload.ports"
                            :key="`${port.container_name}-${port.port}`"
                            class="rounded bg-gray-100 px-2 py-1 text-xs dark:bg-gray-700"
                            :title="`容器 ${port.container_name} 的端口 ${port.port}，协议 ${port.protocol}`"
                        >
                            <div class="font-mono">{{ port.port }}/{{ port.protocol }}</div>
                            <div class="text-gray-500">{{ port.container_name }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { useResourcesStore } from '@/stores/resourcesStore';
import { Boxes, Check, Database } from 'lucide-vue-next';
import { computed, onMounted } from 'vue';

interface WorkloadPort {
    container_name: string;
    port: number;
    protocol: string;
}

interface Workload {
    type: 'Deployment' | 'StatefulSet';
    name: string;
    ports: WorkloadPort[];
}

interface SelectedWorkload {
    type: string;
    name: string;
}

interface Props {
    modelValue: SelectedWorkload | null | undefined;
    label?: string;
    description?: string;
    showPorts?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    label: '目标工作负载',
    description: '选择要为其创建弹性伸缩的工作负载',
    showPorts: true,
});

const emit = defineEmits<{
    'update:modelValue': [value: SelectedWorkload];
}>();

const resourcesStore = useResourcesStore();

const workloads = computed(() => {
    const deployments = resourcesStore.collections.deployments.map((deployment: any) => ({
        type: 'Deployment' as const,
        name: deployment.name,
        ports: (deployment.containers || []).flatMap((container: any) =>
            (container.ports || []).map((port: any) => ({
                container_name: container.name,
                port: port.container_port,
                protocol: port.protocol || 'TCP',
            })),
        ),
    }));
    const statefulsets = resourcesStore.collections.statefulsets.map((statefulset: any) => ({
        type: 'StatefulSet' as const,
        name: statefulset.name,
        ports: (statefulset.containers || []).flatMap((container: any) =>
            (container.ports || []).map((port: any) => ({
                container_name: container.name,
                port: port.container_port,
                protocol: port.protocol || 'TCP',
            })),
        ),
    }));
    return [...deployments, ...statefulsets];
});

const loading = computed(() => !resourcesStore.isLoaded);

const workloadModel = computed({
    get: () => props.modelValue ?? { type: '', name: '' },
    set: (value) => {
        emit('update:modelValue', value);
    },
});

const selectWorkload = (workload: Workload) => {
    workloadModel.value = {
        type: workload.type,
        name: workload.name,
    };
};

onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }
});
</script>
