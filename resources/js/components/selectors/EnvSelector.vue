<template>
    <div class="space-y-4 rounded-lg border p-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <Label>{{ label }}</Label>
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger as-child>
                            <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                            <p class="max-w-xs">
                                环境变量是注入到应用容器中的动态值。它们常用于配置数据库连接、API 密钥等敏感信息，而无需将这些信息硬编码到代码中。
                            </p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
            <Button type="button" variant="outline" size="sm" title="添加新的环境变量" @click="addEnv">
                <Plus class="mr-2 h-4 w-4" />
                添加变量
            </Button>
        </div>

        <div
            v-if="!internalEnvs.length"
            class="rounded-lg border-2 border-dashed border-gray-300 py-6 text-center text-gray-500 dark:border-gray-600"
        >
            <p>暂未配置环境变量</p>
        </div>

        <div v-else class="space-y-3">
            <div v-for="(env, index) in internalEnvs" :key="env.id" class="grid grid-cols-[1fr_1fr_auto] items-center gap-2">
                <Input
                    v-model="env.name"
                    @update:model-value="emitUpdate"
                    placeholder="变量名 (例如：DATABASE_URL)"
                    aria-label="环境变量名称"
                    :title="`输入第 ${index + 1} 个环境变量的名称`"
                    :class="{ 'border-red-500': !env.name && showValidation }"
                />
                <Input
                    v-model="env.value"
                    @update:model-value="emitUpdate"
                    placeholder="变量值"
                    aria-label="环境变量值"
                    :title="`输入第 ${index + 1} 个环境变量的值`"
                    :class="{ 'border-red-500': !env.value && showValidation }"
                />
                <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    :title="`删除第 ${index + 1} 个环境变量`"
                    @click="removeEnv(index)"
                    class="text-destructive hover:text-destructive/80"
                >
                    <Trash2 class="h-4 w-4" />
                </Button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Info, Plus, Trash2 } from 'lucide-vue-next';
import { ref, watch } from 'vue';

interface InputEnvVar {
    name: string;
    value: string;
}

interface EnvVarWithId extends InputEnvVar {
    id: number;
}

interface Props {
    modelValue: InputEnvVar[] | null | undefined;
    label?: string;
    description?: string;
    showValidation?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    label: '环境变量',
    description: '',
    showValidation: false,
});

const emit = defineEmits<{
    'update:modelValue': [value: InputEnvVar[]];
}>();

let idCounter = 0;
const internalEnvs = ref<EnvVarWithId[]>([]);

watch(
    () => props.modelValue,
    (newVal) => {
        const newCleanVal = newVal ? JSON.stringify(newVal) : '[]';
        const internalCleanVal = JSON.stringify(internalEnvs.value.map(({ id, ...rest }) => rest));

        if (newCleanVal !== internalCleanVal) {
            // Smart update to prevent focus loss
            // Remove extra items from the end
            if (newVal && newVal.length < internalEnvs.value.length) {
                internalEnvs.value.splice(newVal.length);
            }

            // Update existing items or add new ones
            (newVal || []).forEach((env, index) => {
                if (internalEnvs.value[index]) {
                    // Update existing item
                    internalEnvs.value[index].name = env.name;
                    internalEnvs.value[index].value = env.value;
                } else {
                    // Add new item
                    internalEnvs.value.push({ ...env, id: idCounter++ });
                }
            });
        }
    },
    { deep: true, immediate: true },
);

function emitUpdate() {
    const output = internalEnvs.value.map(({ id, ...rest }) => rest);
    emit('update:modelValue', output);
}

const addEnv = () => {
    internalEnvs.value.push({ id: idCounter++, name: '', value: '' });
    emitUpdate();
};

const removeEnv = (index: number) => {
    internalEnvs.value.splice(index, 1);
    emitUpdate();
};
</script>
