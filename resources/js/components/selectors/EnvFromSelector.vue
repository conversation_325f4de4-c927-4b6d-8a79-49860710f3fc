<template>
    <div class="space-y-6 rounded-lg border p-4">
        <!-- ConfigMap 环境变量 -->
        <div>
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <Label>从配置映射引用</Label>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p class="max-w-xs">将“配置映射”中的数据作为环境变量注入到您的应用中。这对于管理非敏感的配置信息非常有用。</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
                <Button type="button" variant="outline" size="sm" title="添加新的配置映射环境变量引用" @click="addConfigMapEnv">
                    <Plus class="mr-2 h-4 w-4" />
                    添加
                </Button>
            </div>

            <div
                v-if="!internalConfigMapEnvs.length"
                class="mt-2 rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
            >
                <p class="text-sm">暂未从配置映射引用环境变量</p>
            </div>

            <div v-else class="mt-3 space-y-3">
                <div
                    v-for="(envItem, index) in internalConfigMapEnvs"
                    :key="envItem.id"
                    class="grid grid-cols-1 items-start gap-3 rounded-lg border p-3 md:grid-cols-[1fr_1fr_1fr_auto]"
                >
                    <div class="w-full">
                        <Label>配置映射</Label>
                        <Select
                            v-model="envItem.configmap_name"
                            :title="`选择第 ${index + 1} 个配置映射环境变量的来源`"
                            @update:model-value="emitUpdate"
                        >
                            <SelectTrigger class="mt-1 w-full">
                                <SelectValue placeholder="选择配置映射" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="configMap in availableConfigMaps" :key="configMap.name" :value="configMap.name">
                                    {{ configMap.name }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div class="w-full">
                        <div class="flex items-center">
                            <Label>键名</Label>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger as-child>
                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p class="max-w-xs">
                                            指定要引用的配置映射中的特定键。如果留空，则配置映射中的所有键值对都将被注入为环境变量。
                                        </p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                        <Input
                            v-model="envItem.key"
                            :title="`输入第 ${index + 1} 个配置映射环境变量的键名`"
                            @update:model-value="emitUpdate"
                            placeholder="(可选) 指定键"
                            class="mt-1"
                        />
                    </div>

                    <div class="w-full">
                        <div class="flex items-center">
                            <Label>变量名前缀</Label>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger as-child>
                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p class="max-w-xs">
                                            为从配置映射中注入的所有环境变量添加一个前缀。仅在引用整个配置映射（即“键名”字段留空）时生效。
                                        </p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                        <Input
                            v-model="envItem.env_name"
                            :title="`输入第 ${index + 1} 个配置映射环境变量的变量名`"
                            @update:model-value="emitUpdate"
                            placeholder="(可选) 添加前缀"
                            class="mt-1"
                        />
                    </div>

                    <div class="flex h-full items-end justify-end">
                        <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            :title="`删除第 ${index + 1} 个配置映射环境变量`"
                            @click="removeConfigMapEnv(index)"
                            class="text-destructive hover:text-destructive/80"
                        >
                            <Trash2 class="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secret 环境变量 -->
        <div>
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <Label>从密钥引用</Label>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p class="max-w-xs">将“密钥”中的数据作为环境变量注入。这是管理密码、API 令牌等敏感信息的推荐方式，以确保安全。</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
                <Button type="button" variant="outline" size="sm" title="添加新的密钥环境变量引用" @click="addSecretEnv">
                    <Plus class="mr-2 h-4 w-4" />
                    添加
                </Button>
            </div>

            <div
                v-if="!internalSecretEnvs.length"
                class="mt-2 rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
            >
                <p class="text-sm">暂未从密钥引用环境变量</p>
            </div>

            <div v-else class="mt-3 space-y-3">
                <div
                    v-for="(envItem, index) in internalSecretEnvs"
                    :key="envItem.id"
                    class="grid grid-cols-1 items-start gap-3 rounded-lg border p-3 md:grid-cols-[1fr_1fr_1fr_auto]"
                >
                    <div class="w-full">
                        <Label>密钥</Label>
                        <Select v-model="envItem.secret_name" :title="`选择第 ${index + 1} 个密钥环境变量的来源`" @update:model-value="emitUpdate">
                            <SelectTrigger class="mt-1 w-full">
                                <SelectValue placeholder="选择密钥" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="secret in availableSecrets" :key="secret.name" :value="secret.name">
                                    {{ secret.name }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div class="w-full">
                        <div class="flex items-center">
                            <Label>键名</Label>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger as-child>
                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p class="max-w-xs">指定要引用的密钥中的特定键。如果留空，则该密钥中的所有键值对都将被注入为环境变量。</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                        <Input
                            v-model="envItem.key"
                            :title="`输入第 ${index + 1} 个密钥环境变量的键名`"
                            @update:model-value="emitUpdate"
                            placeholder="(可选) 指定键"
                            class="mt-1"
                        />
                    </div>

                    <div class="w-full">
                        <div class="flex items-center">
                            <Label>变量名前缀</Label>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger as-child>
                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p class="max-w-xs">为从密钥中注入的所有环境变量添加一个前缀。仅在引用整个密钥（即“键名”字段留空）时生效。</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                        <Input
                            v-model="envItem.env_name"
                            :title="`输入第 ${index + 1} 个密钥环境变量的变量名`"
                            @update:model-value="emitUpdate"
                            placeholder="(可选) 添加前缀"
                            class="mt-1"
                        />
                    </div>

                    <div class="flex h-full items-end justify-end">
                        <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            :title="`删除第 ${index + 1} 个密钥环境变量`"
                            @click="removeSecretEnv(index)"
                            class="text-destructive hover:text-destructive/80"
                        >
                            <Trash2 class="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { EnvFromConfigMap, EnvFromSecret } from '@/types';
import { Info, Plus, Trash2 } from 'lucide-vue-next';
import { computed, onMounted, ref, watch } from 'vue';

interface EnvFromModel {
    configmap?: EnvFromConfigMap[];
    secret?: EnvFromSecret[];
}

interface Props {
    modelValue: EnvFromModel | null | undefined;
}

const props = defineProps<Props>();

const emit = defineEmits<{
    'update:modelValue': [value: EnvFromModel];
}>();

interface EnvFromConfigMapWithId extends EnvFromConfigMap {
    id: number;
}

interface EnvFromSecretWithId extends EnvFromSecret {
    id: number;
}

let idCounter = 0;
const internalConfigMapEnvs = ref<EnvFromConfigMapWithId[]>([]);
const internalSecretEnvs = ref<EnvFromSecretWithId[]>([]);

const resourcesStore = useResourcesStore();
const availableConfigMaps = computed(() => resourcesStore.collections.configmaps);
const availableSecrets = computed(() => resourcesStore.collections.secrets);

watch(
    () => props.modelValue,
    (newVal) => {
        const internalVal = {
            configmap: internalConfigMapEnvs.value.map(({ id, ...rest }) => rest),
            secret: internalSecretEnvs.value.map(({ id, ...rest }) => rest),
        };

        if (JSON.stringify(newVal) === JSON.stringify(internalVal)) {
            return;
        }

        if (newVal) {
            internalConfigMapEnvs.value = (newVal.configmap || []).map((env) => ({ ...env, id: idCounter++ }));
            internalSecretEnvs.value = (newVal.secret || []).map((env) => ({ ...env, id: idCounter++ }));
        }
    },
    { deep: true, immediate: true },
);

function emitUpdate() {
    const output: EnvFromModel = {
        configmap: internalConfigMapEnvs.value.map(({ id, ...rest }) => rest),
        secret: internalSecretEnvs.value.map(({ id, ...rest }) => rest),
    };
    emit('update:modelValue', output);
}

const addConfigMapEnv = () => {
    internalConfigMapEnvs.value.push({
        id: idCounter++,
        configmap_name: '',
        key: '',
        env_name: '',
    });
};

const removeConfigMapEnv = (index: number) => {
    internalConfigMapEnvs.value.splice(index, 1);
    emitUpdate();
};

const addSecretEnv = () => {
    internalSecretEnvs.value.push({
        id: idCounter++,
        secret_name: '',
        key: '',
        env_name: '',
    });
};

const removeSecretEnv = (index: number) => {
    internalSecretEnvs.value.splice(index, 1);
    emitUpdate();
};

onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }
});
</script>
