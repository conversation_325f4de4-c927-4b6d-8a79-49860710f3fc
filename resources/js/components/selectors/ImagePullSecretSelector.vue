<template>
    <div class="space-y-2">
        <div v-for="(secret, index) in internalSecrets" :key="index" class="flex items-center space-x-2">
            <Select
                :model-value="secret || undefined"
                :title="`选择第 ${index + 1} 个镜像拉取密钥`"
                @update:model-value="(value) => updateSecretValue(index, value)"
            >
                <SelectTrigger class="flex-1">
                    <SelectValue :placeholder="placeholder" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem v-for="s in availableSecrets" :key="s.name" :value="s.name">
                        {{ s.name }}
                    </SelectItem>
                    <div v-if="availableSecrets.length === 0" class="py-6 text-center text-sm text-gray-500">暂无 Docker Registry 的可用密钥。</div>
                </SelectContent>
            </Select>
            <Button
                type="button"
                variant="ghost"
                size="icon"
                :title="`删除第 ${index + 1} 个镜像拉取密钥`"
                @click="removeSecret(index)"
                class="text-destructive hover:text-destructive/80"
            >
                <Trash2 class="h-4 w-4" />
            </Button>
        </div>
        <Button type="button" variant="outline" title="添加新的镜像拉取密钥" @click="addSecret" class="w-full">
            <Plus class="mr-2 h-4 w-4" />
            {{ addButtonText }}
        </Button>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useResourcesStore } from '@/stores/resourcesStore';
import { Plus, Trash2 } from 'lucide-vue-next';
import { computed, onMounted, ref, watch } from 'vue';

interface Props {
    modelValue: string[] | null | undefined;
    placeholder?: string;
    addButtonText?: string;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '选择镜像拉取密钥',
    addButtonText: '添加密钥',
});

const emit = defineEmits<{
    'update:modelValue': [value: string[]];
}>();

const resourcesStore = useResourcesStore();
const internalSecrets = ref<string[]>([]);

const availableSecrets = computed(() => {
    return resourcesStore.collections.secrets.filter((s) => s.type === 'DockerConfig');
});

watch(
    () => props.modelValue,
    (newValue) => {
        internalSecrets.value = newValue ? [...newValue] : [];
    },
    { immediate: true, deep: true },
);

const addSecret = () => {
    const newValue = [...internalSecrets.value, ''];
    emit('update:modelValue', newValue);
};

const removeSecret = (index: number) => {
    const newValue = internalSecrets.value.filter((_, i) => i !== index);
    emit('update:modelValue', newValue);
};

const updateSecretValue = (index: number, value: any) => {
    const newValue = [...internalSecrets.value];
    newValue[index] = value || '';
    emit('update:modelValue', newValue);
};

onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }
});
</script>
