<template>
    <div class="space-y-4 rounded-lg border p-4">
        <div class="space-y-4">
            <!-- Command 配置 -->
            <div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <Label>启动命令 (Command)</Label>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger as-child>
                                    <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p class="max-w-xs">
                                        覆盖容器镜像的默认入口点 (ENTRYPOINT)。这决定了容器启动时执行的程序。例如，您可以指定 `['/bin/sh', '-c']`
                                        来使用 shell。
                                    </p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <Button type="button" variant="outline" size="sm" title="添加新的命令项" @click="addCommand">
                        <Plus class="mr-2 h-4 w-4" />
                        添加
                    </Button>
                </div>

                <div
                    v-if="!internalCommand.length"
                    class="mt-2 rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
                >
                    <p class="text-sm">使用镜像默认的启动命令</p>
                </div>

                <div v-else class="mt-2 space-y-2">
                    <div v-for="(cmd, index) in internalCommand" :key="cmd.id" class="flex gap-2">
                        <Input
                            v-model="cmd.value"
                            @update:model-value="emitUpdate"
                            :placeholder="`命令 ${index + 1}`"
                            :title="`输入第 ${index + 1} 个命令`"
                            class="flex-1"
                        />
                        <Button type="button" variant="outline" size="sm" :title="`删除第 ${index + 1} 个命令`" @click="removeCommand(index)">
                            <X class="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>

            <!-- Args 配置 -->
            <div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <Label>命令参数 (Args)</Label>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger as-child>
                                    <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p class="max-w-xs">
                                        覆盖容器镜像的默认命令 (CMD)。这些参数会传递给“启动命令”。例如，您可以添加 `['echo', 'hello world']`
                                        来打印消息。
                                    </p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <Button type="button" variant="outline" size="sm" title="添加新的参数项" @click="addArg">
                        <Plus class="mr-2 h-4 w-4" />
                        添加
                    </Button>
                </div>

                <div
                    v-if="!internalArgs.length"
                    class="mt-2 rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
                >
                    <p class="text-sm">使用镜像默认的命令参数</p>
                </div>

                <div v-else class="mt-2 space-y-2">
                    <div v-for="(arg, index) in internalArgs" :key="arg.id" class="flex gap-2">
                        <Input
                            v-model="arg.value"
                            @update:model-value="emitUpdate"
                            :placeholder="`参数 ${index + 1}`"
                            :title="`输入第 ${index + 1} 个参数`"
                            class="flex-1"
                        />
                        <Button type="button" variant="outline" size="sm" :title="`删除第 ${index + 1} 个参数`" @click="removeArg(index)">
                            <X class="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Info, Plus, X } from 'lucide-vue-next';
import { ref, watch } from 'vue';

interface CommandModel {
    command: string[];
    args: string[];
}

interface Props {
    modelValue: CommandModel | null | undefined;
    description?: string;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({ command: [], args: [] }),
});

const emit = defineEmits<{
    'update:modelValue': [value: CommandModel];
}>();

interface CommandItem {
    id: number;
    value: string;
}

let idCounter = 0;
const internalCommand = ref<CommandItem[]>([]);
const internalArgs = ref<CommandItem[]>([]);

watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) {
            const newCommands = newVal.command || [];
            if (newCommands.length !== internalCommand.value.length || newCommands.some((cmd, i) => cmd !== internalCommand.value[i].value)) {
                internalCommand.value = newCommands.map((cmd) => ({ id: idCounter++, value: cmd }));
            }

            const newArgs = newVal.args || [];
            if (newArgs.length !== internalArgs.value.length || newArgs.some((arg, i) => arg !== internalArgs.value[i].value)) {
                internalArgs.value = newArgs.map((arg) => ({ id: idCounter++, value: arg }));
            }
        } else {
            internalCommand.value = [];
            internalArgs.value = [];
        }
    },
    { deep: true, immediate: true },
);

function emitUpdate() {
    const output: CommandModel = {
        command: internalCommand.value.map((item) => item.value),
        args: internalArgs.value.map((item) => item.value),
    };
    emit('update:modelValue', output);
}

const addCommand = () => {
    internalCommand.value.push({ id: idCounter++, value: '' });
    emitUpdate();
};

const removeCommand = (index: number) => {
    internalCommand.value.splice(index, 1);
    emitUpdate();
};

const addArg = () => {
    internalArgs.value.push({ id: idCounter++, value: '' });
    emitUpdate();
};

const removeArg = (index: number) => {
    internalArgs.value.splice(index, 1);
    emitUpdate();
};
</script>
