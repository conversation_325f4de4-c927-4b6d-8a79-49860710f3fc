<template>
    <div class="space-y-6 rounded-lg border p-4">
        <!-- ConfigMap 文件挂载 -->
        <div>
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <Label>从配置映射挂载文件</Label>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p class="max-w-xs">将“配置映射”中的键值对作为文件挂载到您的容器中。这对于提供配置文件非常有用。</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
                <Button type="button" variant="outline" size="sm" title="添加新的配置映射文件挂载" @click="addConfigMapMount">
                    <Plus class="mr-2 h-4 w-4" />
                    添加
                </Button>
            </div>

            <div
                v-if="!internalConfigMapMounts.length"
                class="mt-2 rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
            >
                <p class="text-sm">暂未从配置映射挂载文件</p>
            </div>

            <div v-else class="mt-3 space-y-4">
                <div v-for="(mountItem, index) in internalConfigMapMounts" :key="mountItem.id" class="rounded-lg border p-4">
                    <div class="mb-4 flex items-center justify-between">
                        <h4 class="font-medium text-muted-foreground">挂载项 {{ index + 1 }}</h4>
                        <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            :title="`删除第 ${index + 1} 个配置映射挂载`"
                            @click="removeConfigMapMount(index)"
                            class="text-destructive hover:text-destructive/80"
                        >
                            <Trash2 class="h-4 w-4" />
                        </Button>
                    </div>

                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <Label>配置映射源</Label>
                            <Select
                                v-model="mountItem.configmap_name"
                                :title="`选择第 ${index + 1} 个配置映射挂载的数据源`"
                                @update:model-value="emitUpdate"
                            >
                                <SelectTrigger class="mt-1">
                                    <SelectValue placeholder="选择配置映射" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="configMap in availableConfigMaps" :key="configMap.name" :value="configMap.name">
                                        {{ configMap.name }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <div class="flex items-center">
                                <Label>挂载路径</Label>
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger as-child>
                                            <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p class="max-w-xs">文件将被挂载到容器内的这个绝对路径下。例如：`/app/config`</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                            <Input
                                v-model="mountItem.mount_path"
                                :title="`输入第 ${index + 1} 个配置映射挂载的路径`"
                                @update:model-value="emitUpdate"
                                placeholder="/etc/config"
                                class="mt-1"
                            />
                        </div>
                    </div>
                    <div class="mt-4">
                        <Label>挂载项</Label>
                        <div class="mt-2 space-y-2">
                            <div v-for="(item, itemIndex) in mountItem.items" :key="item.id" class="grid grid-cols-[1fr_1fr_auto] items-center gap-2">
                                <div class="flex items-center">
                                    <Input v-model="item.key" placeholder="源键名" @update:model-value="emitUpdate" title="配置映射中的键" />
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger as-child>
                                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p class="max-w-xs">要挂载的配置映射中的数据项的键。</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <div class="flex items-center">
                                    <Input v-model="item.path" placeholder="目标文件名" @update:model-value="emitUpdate" title="挂载后的文件名" />
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger as-child>
                                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p class="max-w-xs">键对应的值将以此文件名保存在挂载路径下。这是一个相对路径。</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    title="删除此项"
                                    @click="removeConfigMapItem(index, itemIndex)"
                                    class="text-destructive hover:text-destructive/80"
                                >
                                    <X class="h-4 w-4" />
                                </Button>
                            </div>
                            <Button type="button" variant="outline" size="sm" @click="addConfigMapItem(index)" class="w-full">
                                <Plus class="mr-2 h-4 w-4" />
                                添加挂载项
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secret 文件挂载 -->
        <div>
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <Label>从密钥挂载文件</Label>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p class="max-w-xs">将“密钥”中的数据作为文件安全地挂载到容器中。这是处理 SSL 证书、私钥等敏感文件的最佳实践。</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
                <Button type="button" variant="outline" size="sm" title="添加新的密钥文件挂载" @click="addSecretMount">
                    <Plus class="mr-2 h-4 w-4" />
                    添加
                </Button>
            </div>

            <div
                v-if="!internalSecretMounts.length"
                class="mt-2 rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
            >
                <p class="text-sm">暂未从密钥挂载文件</p>
            </div>

            <div v-else class="mt-3 space-y-4">
                <div v-for="(mountItem, index) in internalSecretMounts" :key="mountItem.id" class="rounded-lg border p-4">
                    <div class="mb-4 flex items-center justify-between">
                        <h4 class="font-medium text-muted-foreground">挂载项 {{ index + 1 }}</h4>
                        <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            :title="`删除第 ${index + 1} 个密钥挂载`"
                            @click="removeSecretMount(index)"
                            class="text-destructive hover:text-destructive/80"
                        >
                            <Trash2 class="h-4 w-4" />
                        </Button>
                    </div>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <Label>密钥源</Label>
                            <Select
                                v-model="mountItem.secret_name"
                                :title="`选择第 ${index + 1} 个密钥挂载的数据源`"
                                @update:model-value="emitUpdate"
                            >
                                <SelectTrigger class="mt-1">
                                    <SelectValue placeholder="选择密钥" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="secret in availableSecrets" :key="secret.name" :value="secret.name">
                                        {{ secret.name }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <div class="flex items-center">
                                <Label>挂载路径</Label>
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger as-child>
                                            <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p class="max-w-xs">文件将被挂载到容器内的这个绝对路径下。例如：`/etc/ssl/private`</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                            <Input
                                v-model="mountItem.mount_path"
                                :title="`输入第 ${index + 1} 个密钥挂载的路径`"
                                @update:model-value="emitUpdate"
                                placeholder="/etc/ssl/certs"
                                class="mt-1"
                            />
                        </div>
                    </div>
                    <div class="mt-4">
                        <Label>挂载项</Label>
                        <div class="mt-2 space-y-2">
                            <div v-for="(item, itemIndex) in mountItem.items" :key="item.id" class="grid grid-cols-[1fr_1fr_auto] items-center gap-2">
                                <div class="flex items-center">
                                    <Input v-model="item.key" placeholder="源键名" @update:model-value="emitUpdate" title="密钥中的键" />
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger as-child>
                                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p class="max-w-xs">要挂载的密钥中的数据项的键。</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <div class="flex items-center">
                                    <Input v-model="item.path" placeholder="目标文件名" @update:model-value="emitUpdate" title="挂载后的文件名" />
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger as-child>
                                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p class="max-w-xs">键对应的值将以此文件名保存在挂载路径下。这是一个相对路径。</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    title="删除此项"
                                    @click="removeSecretItem(index, itemIndex)"
                                    class="text-destructive hover:text-destructive/80"
                                >
                                    <X class="h-4 w-4" />
                                </Button>
                            </div>
                            <Button type="button" variant="outline" size="sm" @click="addSecretItem(index)" class="w-full">
                                <Plus class="mr-2 h-4 w-4" />
                                添加挂载项
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { ConfigMapMount, SecretMount } from '@/types';
import { Info, Plus, Trash2, X } from 'lucide-vue-next';
import { computed, onMounted, ref, watch } from 'vue';

// Models
interface FileMountModel {
    configmap_mounts?: ConfigMapMount[];
    secret_mounts?: SecretMount[];
}
interface ConfigMapMountWithId extends Omit<ConfigMapMount, 'items'> {
    id: number;
    items: (ConfigMapMount['items'][0] & { id: number })[];
}
interface SecretMountWithId extends Omit<SecretMount, 'items'> {
    id: number;
    items: (SecretMount['items'][0] & { id: number })[];
}

// Props & Emits
interface Props {
    modelValue: FileMountModel | null | undefined;
}
const props = defineProps<Props>();
const emit = defineEmits<{
    'update:modelValue': [value: FileMountModel];
}>();

// State
let idCounter = 0;
const internalConfigMapMounts = ref<ConfigMapMountWithId[]>([]);
const internalSecretMounts = ref<SecretMountWithId[]>([]);
const resourcesStore = useResourcesStore();
const availableConfigMaps = computed(() => resourcesStore.collections.configmaps);
const availableSecrets = computed(() => resourcesStore.collections.secrets);

// Watchers
watch(
    () => props.modelValue,
    (newVal) => {
        const internalVal = {
            configmap_mounts: internalConfigMapMounts.value.map(({ id, ...rest }) => ({
                ...rest,
                items: rest.items.map(({ id: itemId, ...itemRest }) => itemRest),
            })),
            secret_mounts: internalSecretMounts.value.map(({ id, ...rest }) => ({
                ...rest,
                items: rest.items.map(({ id: itemId, ...itemRest }) => itemRest),
            })),
        };

        if (JSON.stringify(newVal) === JSON.stringify(internalVal)) {
            return;
        }

        if (newVal) {
            internalConfigMapMounts.value = (newVal.configmap_mounts || []).map((mount) => ({
                ...mount,
                id: idCounter++,
                items: (mount.items || []).map((item) => ({ ...item, id: idCounter++ })),
            }));
            internalSecretMounts.value = (newVal.secret_mounts || []).map((mount) => ({
                ...mount,
                id: idCounter++,
                items: (mount.items || []).map((item) => ({ ...item, id: idCounter++ })),
            }));
        }
    },
    { deep: true, immediate: true },
);

// Methods
function emitUpdate() {
    const cleanConfigMapMounts = internalConfigMapMounts.value.map(({ id, items, ...rest }) => ({
        ...rest,
        items: items.map(({ id: itemId, ...itemRest }: { id: number; [key: string]: any }) => itemRest),
    }));
    const cleanSecretMounts = internalSecretMounts.value.map(({ id, items, ...rest }) => ({
        ...rest,
        items: items.map(({ id: itemId, ...itemRest }: { id: number; [key: string]: any }) => itemRest),
    }));

    emit('update:modelValue', {
        configmap_mounts: cleanConfigMapMounts,
        secret_mounts: cleanSecretMounts,
    });
}

// ConfigMap Methods
const addConfigMapMount = () => {
    internalConfigMapMounts.value.push({
        id: idCounter++,
        configmap_name: '',
        mount_path: '',
        items: [],
    });
};
const removeConfigMapMount = (index: number) => {
    internalConfigMapMounts.value.splice(index, 1);
    emitUpdate();
};
const addConfigMapItem = (mountIndex: number) => {
    if (!internalConfigMapMounts.value[mountIndex].items) {
        internalConfigMapMounts.value[mountIndex].items = [];
    }
    internalConfigMapMounts.value[mountIndex].items.push({ id: idCounter++, key: '', path: '' });
    emitUpdate();
};
const removeConfigMapItem = (mountIndex: number, itemIndex: number) => {
    if (internalConfigMapMounts.value[mountIndex]?.items) {
        internalConfigMapMounts.value[mountIndex].items.splice(itemIndex, 1);
        emitUpdate();
    }
};

// Secret Methods
const addSecretMount = () => {
    internalSecretMounts.value.push({
        id: idCounter++,
        secret_name: '',
        mount_path: '',
        items: [],
    });
};
const removeSecretMount = (index: number) => {
    internalSecretMounts.value.splice(index, 1);
    emitUpdate();
};
const addSecretItem = (mountIndex: number) => {
    if (!internalSecretMounts.value[mountIndex].items) {
        internalSecretMounts.value[mountIndex].items = [];
    }
    internalSecretMounts.value[mountIndex].items.push({ id: idCounter++, key: '', path: '' });
    emitUpdate();
};
const removeSecretItem = (mountIndex: number, itemIndex: number) => {
    if (internalSecretMounts.value[mountIndex]?.items) {
        internalSecretMounts.value[mountIndex].items.splice(itemIndex, 1);
        emitUpdate();
    }
};

// Lifecycle
onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }
});
</script>
