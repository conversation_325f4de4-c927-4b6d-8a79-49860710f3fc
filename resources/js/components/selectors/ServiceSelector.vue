<template>
    <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <div class="space-y-2">
            <Label :for="`service-name-${id}`" :class="{ required: required }">服务名称</Label>
            <Select v-model="selectedServiceName" title="选择要连接的服务" @update:model-value="onServiceChange">
                <SelectTrigger>
                    <SelectValue placeholder="选择服务" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem v-for="service in servicesWithPorts" :key="service.name" :value="service.name">
                        {{ service.name }}
                    </SelectItem>
                    <div v-if="!loading && servicesWithPorts.length === 0" class="py-6 text-center text-sm text-gray-500">暂无可用服务</div>
                </SelectContent>
            </Select>
        </div>
        <div class="space-y-2">
            <Label :for="`service-port-${id}`" :class="{ required: required }">服务端口</Label>
            <Select v-model="selectedPortNumber" :disabled="!selectedService" title="选择服务的端口" @update:model-value="onPortChange">
                <SelectTrigger>
                    <SelectValue placeholder="选择端口" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem v-for="port in selectedService?.ports" :key="port.port" :value="port.port">
                        {{ port.port }} ({{ port.protocol }})
                        <span v-if="port.name">- {{ port.name }}</span>
                    </SelectItem>
                </SelectContent>
            </Select>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useResourcesStore } from '@/stores/resourcesStore';
import { computed, onMounted, ref, watch } from 'vue';

interface ServiceBackend {
    name: string;
    port: {
        number: number | undefined;
    };
}

interface Props {
    modelValue: ServiceBackend;
    required?: boolean;
    id?: string;
}

const props = withDefaults(defineProps<Props>(), {
    required: false,
    id: 'default',
});

const emit = defineEmits<{
    'update:modelValue': [value: ServiceBackend];
}>();

const resourcesStore = useResourcesStore();
const services = computed(() => resourcesStore.collections.services);
const loading = computed(() => resourcesStore.isLoading);

const servicesWithPorts = computed(() => {
    return services.value.filter((service) => service.ports && service.ports.length > 0);
});

const selectedServiceName = ref<string | undefined>(props.modelValue.name);
const selectedPortNumber = ref<number | undefined>(props.modelValue.port.number);

const selectedService = computed(() => {
    return services.value.find((s) => s.name === selectedServiceName.value);
});

const onServiceChange = (serviceName: any) => {
    selectedServiceName.value = serviceName;
    const service = services.value.find((s) => s.name === serviceName);
    if (service && service.ports.length > 0) {
        selectedPortNumber.value = service.ports[0].port;
    } else {
        selectedPortNumber.value = undefined;
    }
    emitUpdate();
};

const onPortChange = (portNumber: any) => {
    selectedPortNumber.value = portNumber;
    emitUpdate();
};

const emitUpdate = () => {
    if (selectedServiceName.value) {
        emit('update:modelValue', {
            name: selectedServiceName.value,
            port: {
                number: selectedPortNumber.value,
            },
        });
    }
};

watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) {
            selectedServiceName.value = newVal.name;
            selectedPortNumber.value = newVal.port.number;
        }
    },
    { deep: true, immediate: true },
);

onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }
});
</script>

<style scoped>
.required::after {
    content: ' *';
    color: rgb(239 68 68);
}
</style>
