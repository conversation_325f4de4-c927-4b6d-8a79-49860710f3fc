<template>
    <div>
        <Label v-if="label">{{ label }}</Label>
        <Select v-model="storageModel" title="选择要使用的存储卷">
            <SelectTrigger>
                <SelectValue :placeholder="placeholder" />
            </SelectTrigger>
            <SelectContent>
                <SelectItem v-for="storage in availableStorages" :key="storage.name" :value="storage.name">
                    {{ storage.name }} ({{ storage.formatted_size }})
                    <span v-if="!storage.is_bound" class="ml-2 text-orange-500"> 未绑定 </span>
                </SelectItem>
                <div v-if="availableStorages.length === 0" class="py-6 text-center text-sm text-gray-500">暂无可用存储</div>
            </SelectContent>
        </Select>
        <p v-if="description" class="mt-1 text-sm text-gray-500">
            {{ description }}
        </p>
    </div>
</template>

<script setup lang="ts">
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useResourcesStore } from '@/stores/resourcesStore';
import { computed, onMounted, watch } from 'vue';

interface Props {
    modelValue: string | null | undefined;
    label?: string;
    placeholder?: string;
    description?: string;
    onlyBound?: boolean; // 是否只显示已绑定的存储
    required?: boolean; // 是否必选
}

const props = withDefaults(defineProps<Props>(), {
    label: '',
    placeholder: '选择存储',
    description: '',
    onlyBound: false,
    required: false,
});

const emit = defineEmits<{
    'update:modelValue': [value: string | null];
}>();

const resourcesStore = useResourcesStore();
const storages = computed(() => resourcesStore.collections.storages);

const storageModel = computed({
    get: () => props.modelValue || undefined,
    set: (value) => {
        emit('update:modelValue', value || null);
    },
});

const availableStorages = computed(() => {
    if (props.onlyBound) {
        return storages.value.filter((storage) => storage.is_bound);
    }
    return storages.value;
});

watch(availableStorages, (newStorages) => {
    if (storageModel.value && newStorages.length > 0) {
        const isSelectedStillAvailable = newStorages.some((s) => s.name === storageModel.value);
        if (!isSelectedStillAvailable) {
            storageModel.value = undefined;
        }
    }
});

onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }
});
</script>
