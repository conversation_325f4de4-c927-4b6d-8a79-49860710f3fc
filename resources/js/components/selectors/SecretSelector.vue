<template>
    <div class="space-y-2">
        <Label v-if="label">{{ label }}</Label>
        <div class="space-y-2">
            <div v-for="(selectedSecret, index) in internalSecrets" :key="index" class="flex items-center space-x-2">
                <Select
                    :model-value="selectedSecret || undefined"
                    :title="`选择第 ${index + 1} 个密钥`"
                    @update:model-value="(value) => updateSecretValue(index, value)"
                >
                    <SelectTrigger class="flex-1">
                        <SelectValue :placeholder="placeholder" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem v-for="secret in filteredSecrets" :key="secret.name" :value="secret.name">
                            {{ secret.name }} ({{ secret.type }})
                        </SelectItem>
                        <div v-if="filteredSecrets.length === 0" class="py-6 text-center text-sm text-gray-500">暂无可用密钥</div>
                    </SelectContent>
                </Select>
                <Button type="button" variant="outline" size="icon" :title="`删除第 ${index + 1} 个密钥`" @click="removeSecret(index)">
                    <Trash2 class="h-4 w-4" />
                </Button>
            </div>
            <Button type="button" variant="outline" title="添加新的密钥选择项" @click="addSecret" class="w-full">
                <Plus class="mr-2 h-4 w-4" />
                {{ addButtonText }}
            </Button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useResourcesStore } from '@/stores/resourcesStore';
import { Plus, Trash2 } from 'lucide-vue-next';
import { computed, onMounted, watch } from 'vue';

interface Props {
    modelValue: string[] | null | undefined;
    label?: string;
    placeholder?: string;
    addButtonText?: string;
    secretType?: string; // 过滤特定类型的secret，如 'DockerConfig'
}

const props = withDefaults(defineProps<Props>(), {
    label: '',
    placeholder: '选择密钥',
    addButtonText: '添加密钥',
    secretType: '',
});

const emit = defineEmits<{
    'update:modelValue': [value: string[]];
}>();

const resourcesStore = useResourcesStore();
const secrets = computed(() => resourcesStore.collections.secrets);

const internalSecrets = computed(() => props.modelValue ?? []);

// 根据类型过滤secrets
const filteredSecrets = computed(() => {
    if (!props.secretType) {
        return secrets.value;
    }
    return secrets.value.filter((secret) => secret.type === props.secretType);
});

const addSecret = () => {
    const newValue = [...internalSecrets.value, ''];
    emit('update:modelValue', newValue);
};

const removeSecret = (index: number) => {
    const newValue = internalSecrets.value.filter((_, i) => i !== index);
    emit('update:modelValue', newValue);
};

const updateSecretValue = (index: number, value: any) => {
    const newValue = [...internalSecrets.value];
    newValue[index] = value || '';
    emit('update:modelValue', newValue);
};

// 当可用的密钥列表变化时，验证当前的选择
watch(filteredSecrets, (newAvailableSecrets) => {
    const currentSecrets = internalSecrets.value;
    if (currentSecrets.length === 0 || newAvailableSecrets.length === 0) {
        return;
    }

    const validSecrets = currentSecrets.filter((secretName) => !secretName || newAvailableSecrets.some((s) => s.name === secretName));

    if (validSecrets.length !== currentSecrets.length) {
        emit('update:modelValue', validSecrets);
    }
});

onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }
});
</script>
