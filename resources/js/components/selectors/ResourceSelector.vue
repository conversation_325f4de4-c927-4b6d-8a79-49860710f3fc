<template>
    <div class="space-y-4 rounded-lg border p-4">
        <div class="flex items-center">
            <Label>{{ label }}</Label>
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger as-child>
                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                        <p class="max-w-xs">
                            为您的应用分配计算资源。合理的资源配置能确保应用稳定运行，并有效控制成本。这些值同时定义了应用可使用的资源上限(Limit)与启动时请求的资源量(Request)。
                        </p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        </div>

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
                <div class="flex items-center">
                    <Label :for="`memory-${id}`">内存 (MiB) *</Label>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p class="max-w-xs">
                                    分配给每个应用实例的内存量，单位为 MiB (兆字节)。如果应用使用超过此限制的内存，它可能会被终止并重启。
                                </p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
                <Input
                    :id="`memory-${id}`"
                    v-model.number="memory"
                    type="number"
                    min="512"
                    step="512"
                    placeholder="512"
                    title="输入内存限制，最小 512M，必须是 512 的倍数"
                    :class="{ 'border-red-500': !isValidMemory && showValidation }"
                    required
                    class="mt-1"
                />
                <p v-if="!isValidMemory && showValidation" class="mt-1 text-xs text-red-600">内存值必须是 512 的倍数且不小于 512</p>
            </div>

            <div>
                <div class="flex items-center">
                    <Label :for="`cpu-${id}`">CPU (毫核) *</Label>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p class="max-w-xs">
                                    分配给每个应用实例的 CPU 算力，单位为“毫核”。1000 毫核等于 1 个完整的 CPU 核心。这是确保应用获得稳定性能的关键。
                                </p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
                <Input
                    :id="`cpu-${id}`"
                    v-model.number="cpu"
                    type="number"
                    min="500"
                    step="500"
                    placeholder="500"
                    title="输入 CPU 限制，最小 500 毫核，必须是 500 的倍数"
                    :class="{ 'border-red-500': !isValidCpu && showValidation }"
                    required
                    class="mt-1"
                />
                <p v-if="!isValidCpu && showValidation" class="mt-1 text-xs text-red-600">CPU 值必须是 500 的倍数且不小于 500</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Info } from 'lucide-vue-next';
import { computed } from 'vue';

interface ModelResources {
    memory: number | null;
    cpu: number | null;
}

interface Props {
    modelValue: ModelResources | null | undefined;
    label?: string;
    description?: string;
    showValidation?: boolean;
    id?: string;
}

const props = withDefaults(defineProps<Props>(), {
    label: '资源限制',
    description: '',
    showValidation: false,
    id: 'default',
});

const emit = defineEmits<{
    'update:modelValue': [value: ModelResources];
}>();

const memory = computed({
    get: () => props.modelValue?.memory ?? undefined,
    set: (value) => {
        const newValue = typeof value === 'number' ? value : null;
        emit('update:modelValue', {
            cpu: props.modelValue?.cpu ?? null,
            memory: newValue,
        });
    },
});

const cpu = computed({
    get: () => props.modelValue?.cpu ?? undefined,
    set: (value) => {
        const newValue = typeof value === 'number' ? value : null;
        emit('update:modelValue', {
            memory: props.modelValue?.memory ?? null,
            cpu: newValue,
        });
    },
});

const isValidMemory = computed(() => {
    const mem = memory.value;
    if (typeof mem !== 'number') return false;
    return mem >= 512 && mem % 512 === 0;
});

const isValidCpu = computed(() => {
    const c = cpu.value;
    if (typeof c !== 'number') return false;
    return c >= 500 && c % 500 === 0;
});

const cpuForDisplay = computed(() => {
    return cpu.value ?? 0;
});

defineExpose({
    isValid: computed(() => isValidMemory.value && isValidCpu.value),
});
</script>
