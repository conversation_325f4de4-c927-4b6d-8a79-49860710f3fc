<template>
    <div class="space-y-3 rounded-lg border p-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <Label>{{ label }}</Label>
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger as-child>
                            <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                            <p v-if="probeType === 'liveness'" class="max-w-xs">
                                存活探针：用于检测您的应用是否仍在运行。如果探针失败，平台将自动重启该应用实例。
                            </p>
                            <p v-if="probeType === 'readiness'" class="max-w-xs">
                                就绪探针：用于检测您的应用是否已准备好接收流量。如果探针失败，平台将暂时停止向该实例发送流量，直到探针成功。
                            </p>
                            <p v-if="probeType === 'startup'" class="max-w-xs">
                                启动探针：用于检测启动缓慢的应用是否已成功启动。在启动探针成功之前，其他探针将被禁用。
                            </p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
            <Button v-if="!modelValue" type="button" variant="outline" size="sm" title="添加健康检查探针" @click="addProbe">
                <Plus class="mr-2 h-4 w-4" />
                添加
            </Button>
            <Button v-else type="button" variant="ghost" size="icon" title="删除健康检查探针" @click="removeProbe">
                <X class="h-4 w-4 text-destructive" />
            </Button>
        </div>
        <div v-if="modelValue" class="space-y-4 pt-4">
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                    <Label>探针类型 *</Label>
                    <Select v-model="probe.type" title="选择健康检查探针的类型">
                        <SelectTrigger class="mt-1">
                            <SelectValue placeholder="选择探针类型" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="http">HTTP GET</SelectItem>
                            <SelectItem value="tcp">TCP Socket</SelectItem>
                            <SelectItem value="exec">Exec Command</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div>
                    <div class="flex items-center">
                        <Label>初始延迟 (秒)</Label>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger as-child>
                                    <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p class="max-w-xs">应用启动后，等待多少秒才开始第一次探针检查。这有助于避免在应用准备好之前就开始检查。</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <Input
                        v-model.number="probe.initial_delay_seconds"
                        type="number"
                        min="0"
                        max="3600"
                        placeholder="0"
                        title="设置探针开始检查前的等待时间"
                        class="mt-1"
                    />
                </div>
            </div>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div>
                    <div class="flex items-center">
                        <Label>检查间隔 (秒)</Label>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger as-child>
                                    <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p class="max-w-xs">每隔多少秒执行一次健康检查。</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <Input
                        v-model.number="probe.period_seconds"
                        type="number"
                        min="1"
                        max="3600"
                        placeholder="10"
                        title="设置探针检查的时间间隔"
                        class="mt-1"
                    />
                </div>
                <div>
                    <div class="flex items-center">
                        <Label>超时时间 (秒)</Label>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger as-child>
                                    <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p class="max-w-xs">单次探针检查必须在此时间内返回成功，否则视为失败。</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <Input
                        v-model.number="probe.timeout_seconds"
                        type="number"
                        min="1"
                        max="3600"
                        placeholder="1"
                        title="设置探针检查的超时时间"
                        class="mt-1"
                    />
                </div>
                <div>
                    <div class="flex items-center">
                        <Label>失败阈值</Label>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger as-child>
                                    <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p class="max-w-xs">探针需要连续失败多少次才被最终标记为失败状态。</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <Input
                        v-model.number="probe.failure_threshold"
                        type="number"
                        min="1"
                        :max="probeType === 'startup' ? 30 : 10"
                        placeholder="3"
                        title="设置探针连续失败多少次后认为检查失败"
                        class="mt-1"
                    />
                </div>
            </div>

            <!-- HTTP 探针配置 -->
            <div v-if="probe.type === 'http'" class="space-y-4 rounded-md border bg-muted/20 p-4">
                <Label>HTTP GET 配置</Label>
                <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div>
                        <div class="flex items-center">
                            <Label>路径 *</Label>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger as-child>
                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p class="max-w-xs">要请求的 HTTP 路径，例如 `/healthz`。</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                        <Input v-model="probe.http_path" placeholder="/" title="设置 HTTP 请求的路径" class="mt-1" />
                    </div>
                    <div>
                        <div class="flex items-center">
                            <Label>端口 *</Label>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger as-child>
                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p class="max-w-xs">要请求的容器端口，例如 `8080`。</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                        <Input
                            v-model.number="probe.http_port"
                            type="number"
                            min="1"
                            max="65535"
                            placeholder="80"
                            title="设置 HTTP 请求的端口"
                            class="mt-1"
                        />
                    </div>
                    <div>
                        <Label>协议</Label>
                        <Select v-model="probe.http_scheme" title="选择 HTTP 请求的协议">
                            <SelectTrigger class="mt-1">
                                <SelectValue placeholder="HTTP" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="HTTP">HTTP</SelectItem>
                                <SelectItem value="HTTPS">HTTPS</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div>
                    <Label>自定义头部 (可选)</Label>
                    <div class="mt-2 space-y-2">
                        <div
                            v-for="(header, headerIndex) in probe.http_headers"
                            :key="headerIndex"
                            class="grid grid-cols-[1fr_1fr_auto] items-center gap-2"
                        >
                            <Input v-model="header.name" placeholder="头部名称" :title="`输入第 ${headerIndex + 1} 个 HTTP 头部的名称`" />
                            <Input v-model="header.value" placeholder="头部值" :title="`输入第 ${headerIndex + 1} 个 HTTP 头部的值`" />
                            <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                :title="`删除第 ${headerIndex + 1} 个 HTTP 头部`"
                                @click="removeHeader(headerIndex)"
                                class="text-destructive hover:text-destructive/80"
                            >
                                <X class="h-4 w-4" />
                            </Button>
                        </div>
                        <Button type="button" variant="outline" size="sm" title="添加新的 HTTP 头部" @click="addHeader" class="w-full">
                            <Plus class="mr-2 h-4 w-4" />
                            添加头部
                        </Button>
                    </div>
                </div>
            </div>

            <!-- TCP 探针配置 -->
            <div v-if="probe.type === 'tcp'" class="space-y-4 rounded-md border bg-muted/20 p-4">
                <div class="flex items-center">
                    <Label>TCP Socket 配置</Label>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p class="max-w-xs">平台将尝试在指定端口上建立一个 TCP 连接。如果连接成功建立，则探针成功。</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
                <div>
                    <Label>端口 *</Label>
                    <Input
                        v-model.number="probe.tcp_port"
                        type="number"
                        min="1"
                        max="65535"
                        placeholder="80"
                        title="设置 TCP 连接的端口"
                        class="mt-1"
                    />
                </div>
            </div>

            <!-- Exec 探针配置 -->
            <div v-if="probe.type === 'exec'" class="space-y-4 rounded-md border bg-muted/20 p-4">
                <div class="flex items-center">
                    <Label>Exec Command 配置</Label>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p class="max-w-xs">在容器内执行一个命令。如果命令以退出码 0 结束，则探针成功。</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
                <div>
                    <Label>命令 *</Label>
                    <div class="mt-2 space-y-2">
                        <div
                            v-for="(command, commandIndex) in probe.exec_command"
                            :key="commandIndex"
                            class="grid grid-cols-[1fr_auto] items-center gap-2"
                        >
                            <Input
                                v-model="probe.exec_command![commandIndex]"
                                placeholder="命令或参数"
                                :title="`输入第 ${commandIndex + 1} 个命令参数`"
                            />
                            <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                :title="`删除第 ${commandIndex + 1} 个命令参数`"
                                @click="removeCommand(commandIndex)"
                                class="text-destructive hover:text-destructive/80"
                            >
                                <X class="h-4 w-4" />
                            </Button>
                        </div>
                        <Button type="button" variant="outline" size="sm" title="添加新的命令参数" @click="addCommand" class="w-full">
                            <Plus class="mr-2 h-4 w-4" />
                            添加命令参数
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import type { HealthProbe } from '@/types';
import { Info, Plus, X } from 'lucide-vue-next';
import { ref, watch } from 'vue';

interface Props {
    modelValue?: HealthProbe | null;
    label: string;
    probeType?: 'liveness' | 'readiness' | 'startup';
}

interface Emits {
    (e: 'update:modelValue', value: HealthProbe | null): void;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: null,
    probeType: 'liveness',
});

const emit = defineEmits<Emits>();

const probe = ref<HealthProbe>({
    type: 'http',
    initial_delay_seconds: 0,
    period_seconds: 10,
    timeout_seconds: 1,
    success_threshold: 1,
    failure_threshold: 3,
    http_path: '/',
    http_port: 80,
    http_scheme: 'HTTP',
    http_headers: [],
    exec_command: [],
});

// 监听 modelValue 变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue) {
            // Deep comparison to avoid unnecessary updates
            if (JSON.stringify(newValue) !== JSON.stringify(probe.value)) {
                probe.value = { ...probe.value, ...newValue };
                if (!probe.value.http_headers) {
                    probe.value.http_headers = [];
                }
                if (!probe.value.exec_command) {
                    probe.value.exec_command = [];
                }
            }
        }
    },
    { immediate: true, deep: true },
);

// 监听 probe 变化
watch(
    probe,
    (newValue) => {
        if (props.modelValue) {
            emit('update:modelValue', { ...newValue });
        }
    },
    { deep: true },
);

const addProbe = () => {
    emit('update:modelValue', { ...probe.value });
};

const removeProbe = () => {
    emit('update:modelValue', null);
};

const addHeader = () => {
    if (!probe.value.http_headers) {
        probe.value.http_headers = [];
    }
    probe.value.http_headers.push({ name: '', value: '' });
};

const removeHeader = (index: number) => {
    probe.value.http_headers?.splice(index, 1);
};

const addCommand = () => {
    if (!probe.value.exec_command) {
        probe.value.exec_command = [];
    }
    probe.value.exec_command.push('');
};

const removeCommand = (index: number) => {
    probe.value.exec_command?.splice(index, 1);
};
</script>
