<template>
    <div class="space-y-4 rounded-lg border p-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <Label>{{ label }}</Label>
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger as-child>
                            <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                            <p class="max-w-xs">
                                自定义标签用于为资源添加额外的标识信息。标签键必须以特定前缀开头，值支持字母、数字、连字符、下划线和点。
                            </p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
            <Button type="button" variant="outline" size="sm" title="添加新的标签" @click="addLabel">
                <Plus class="mr-2 h-4 w-4" />
                添加标签
            </Button>
        </div>

        <div
            v-if="!internalLabels.length"
            class="rounded-lg border-2 border-dashed border-gray-300 py-6 text-center text-gray-500 dark:border-gray-600"
        >
            <p>暂未配置自定义标签</p>
        </div>

        <div v-else class="space-y-3">
            <div v-for="(labelItem, index) in internalLabels" :key="labelItem.id" class="grid grid-cols-[1fr_1fr_auto] items-center gap-2">
                <div>
                    <Input
                        v-model="labelItem.key"
                        @update:model-value="emitUpdate"
                        placeholder="标签键 (例如：x-app-type)"
                        aria-label="标签键"
                        :title="`输入第 ${index + 1} 个标签的键名`"
                        :class="{ 'border-red-500': (!labelItem.key || hasKeyError(labelItem.key)) && showValidation }"
                    />
                    <p v-if="(!labelItem.key || hasKeyError(labelItem.key)) && showValidation" class="mt-1 text-xs text-red-600">
                        {{ getKeyErrorMessage(labelItem.key) }}
                    </p>
                </div>
                <div>
                    <Input
                        v-model="labelItem.value"
                        @update:model-value="emitUpdate"
                        placeholder="标签值"
                        aria-label="标签值"
                        :title="`输入第 ${index + 1} 个标签的值`"
                        :class="{ 'border-red-500': (!labelItem.value || hasValueError(labelItem.value)) && showValidation }"
                    />
                    <p v-if="(!labelItem.value || hasValueError(labelItem.value)) && showValidation" class="mt-1 text-xs text-red-600">
                        {{ getValueErrorMessage(labelItem.value) }}
                    </p>
                </div>
                <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    :title="`删除第 ${index + 1} 个标签`"
                    @click="removeLabel(index)"
                    class="text-destructive hover:text-destructive/80"
                >
                    <Trash2 class="h-4 w-4" />
                </Button>
            </div>
        </div>

        <div v-if="maxLabels && internalLabels.length >= maxLabels" class="text-sm text-yellow-600 dark:text-yellow-400">
            <p>最多只能添加 {{ maxLabels }} 个标签</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import axios from '@/lib/axios';
import { Info, Plus, Trash2 } from 'lucide-vue-next';
import { computed, onMounted, ref, watch } from 'vue';

interface InputLabel {
    [key: string]: string;
}

interface LabelWithId {
    id: number;
    key: string;
    value: string;
}

interface Props {
    modelValue: InputLabel | null | undefined;
    label?: string;
    description?: string;
    showValidation?: boolean;
    allowedPrefixes?: string[];
    maxLabels?: number;
}

const props = withDefaults(defineProps<Props>(), {
    label: '自定义标签',
    description: '',
    showValidation: false,
    allowedPrefixes: () => [],
    maxLabels: 0,
});

// 配置状态
const k8sConfig = ref<{ max_labels: number; allow_label_prefix: string[] }>({
    max_labels: 16,
    allow_label_prefix: ['x-'],
});

// 获取配置
const fetchConfig = async () => {
    try {
        const response = await axios.get('/api/config/labels');
        k8sConfig.value = response.data;
    } catch (error) {
        console.error('获取标签配置失败:', error);
    }
};

// 组件挂载时获取配置
onMounted(fetchConfig);

// 使用配置或 props 中的值
const allowedPrefixes = computed(() => {
    if (props.allowedPrefixes.length > 0) {
        return props.allowedPrefixes;
    }
    return k8sConfig.value.allow_label_prefix || ['x-'];
});

const maxLabels = computed(() => {
    if (props.maxLabels > 0) {
        return props.maxLabels;
    }
    return k8sConfig.value.max_labels || 16;
});

const emit = defineEmits<{
    'update:modelValue': [value: InputLabel];
}>();

let idCounter = 0;
const internalLabels = ref<LabelWithId[]>([]);

// 将对象转换为数组形式
const convertToArray = (labels: InputLabel | null | undefined): LabelWithId[] => {
    if (!labels || typeof labels !== 'object') return [];

    return Object.entries(labels).map(([key, value]) => ({
        id: idCounter++,
        key,
        value: String(value),
    }));
};

// 将数组转换为对象形式
const convertToObject = (labels: LabelWithId[]): InputLabel => {
    const result: InputLabel = {};
    labels.forEach((item) => {
        if (item.key) {
            result[item.key] = item.value;
        }
    });
    return result;
};

watch(
    () => props.modelValue,
    (newVal) => {
        const newArray = convertToArray(newVal);
        const newCleanVal = JSON.stringify(newArray.map(({ id, ...rest }) => rest));
        const internalCleanVal = JSON.stringify(internalLabels.value.map(({ id, ...rest }) => rest));

        if (newCleanVal !== internalCleanVal) {
            internalLabels.value = newArray;
        }
    },
    { deep: true, immediate: true },
);

function emitUpdate() {
    const output = convertToObject(internalLabels.value);
    emit('update:modelValue', output);
}

const addLabel = () => {
    console.log('maxLabels', maxLabels.value);
    console.log('internalLabels', internalLabels.value);
    console.log('canAddMore', canAddMore.value);
    if (maxLabels.value && internalLabels.value.length >= maxLabels.value) {
        return;
    }

    internalLabels.value.push({ id: idCounter++, key: '', value: '' });
};

const removeLabel = (index: number) => {
    internalLabels.value.splice(index, 1);
    emitUpdate();
};

// 验证标签键
const hasKeyError = (key: string): boolean => {
    if (!key) return true;

    // 检查前缀
    const hasValidPrefix = allowedPrefixes.value.some((prefix: string) => key.startsWith(prefix));
    if (!hasValidPrefix) return true;

    // 检查格式 (Kubernetes 标签格式)
    const labelRegex = /^[a-z0-9A-Z]([a-z0-9A-Z._-]*[a-z0-9A-Z])?$/;
    if (!labelRegex.test(key)) return true;

    // 检查长度
    if (key.length > 63) return true;

    return false;
};

// 验证标签值
const hasValueError = (value: string): boolean => {
    if (!value) return true;

    // Kubernetes 标签值格式
    const valueRegex = /^[a-z0-9A-Z]([a-z0-9A-Z._-]*[a-z0-9A-Z])?$/;
    if (!valueRegex.test(value)) return true;

    // 检查长度
    if (value.length > 63) return true;

    return false;
};

// 获取键错误信息
const getKeyErrorMessage = (key: string): string => {
    if (!key) return '标签键不能为空';

    const hasValidPrefix = allowedPrefixes.value.some((prefix: string) => key.startsWith(prefix));
    if (!hasValidPrefix) {
        return `标签键必须以以下前缀之一开头：${allowedPrefixes.value.join(', ')}`;
    }

    if (key.length > 63) return '标签键长度不能超过63个字符';

    const labelRegex = /^[a-z0-9A-Z]([a-z0-9A-Z._-]*[a-z0-9A-Z])?$/;
    if (!labelRegex.test(key)) {
        return '标签键格式无效，只能包含字母、数字、点、连字符和下划线';
    }

    return '标签键格式无效';
};

// 获取值错误信息
const getValueErrorMessage = (value: string): string => {
    if (!value) return '标签值不能为空';

    if (value.length > 63) return '标签值长度不能超过63个字符';

    const valueRegex = /^[a-z0-9A-Z]([a-z0-9A-Z._-]*[a-z0-9A-Z])?$/;
    if (!valueRegex.test(value)) {
        return '标签值格式无效，只能包含字母、数字、点、连字符和下划线';
    }

    return '标签值格式无效';
};

// 检查是否可以添加更多标签
const canAddMore = computed(() => {
    return !maxLabels.value || internalLabels.value.length < maxLabels.value;
});
</script>
