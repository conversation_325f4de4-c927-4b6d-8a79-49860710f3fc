<template>
    <div class="space-y-4 rounded-lg border p-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <Label>{{ label }}</Label>
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger as-child>
                            <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                            <p v-if="mode === 'container'" class="max-w-xs">
                                声明您的应用容器需要暴露的端口。这仅为元数据，要让端口可访问，您必须创建一个“服务”。
                            </p>
                            <p v-else class="max-w-xs">定义服务如何暴露您的应用端口。每个端口映射都将流量从服务端口转发到应用容器的目标端口。</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
            <Button type="button" variant="outline" size="sm" title="添加新的端口配置" @click="addPort">
                <Plus class="mr-2 h-4 w-4" />
                添加端口
            </Button>
        </div>

        <div
            v-if="!internalPorts.length"
            class="rounded-lg border-2 border-dashed border-gray-300 py-6 text-center text-gray-500 dark:border-gray-600"
        >
            <p>暂未配置端口</p>
        </div>

        <div v-else class="space-y-3">
            <div v-for="(port, index) in internalPorts" :key="port.id" class="rounded-lg border p-4 dark:border-gray-700">
                <div class="mb-4 flex items-center justify-between">
                    <span class="font-medium text-muted-foreground">端口 {{ index + 1 }}</span>
                    <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        :title="`删除第 ${index + 1} 个端口配置`"
                        @click="removePort(index)"
                        class="text-destructive hover:text-destructive/80"
                    >
                        <Trash2 class="h-4 w-4" />
                    </Button>
                </div>

                <div v-if="mode === 'service'" class="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <div>
                        <div class="flex items-center">
                            <Label :for="`port_name_${index}`">名称</Label>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger as-child>
                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p class="max-w-xs">为此端口映射指定一个描述性名称，例如 `http` 或 `https`。</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                        <Input
                            v-model="port.name"
                            @update:model-value="emitUpdate"
                            type="text"
                            placeholder="例如：http"
                            :title="`输入第 ${index + 1} 个端口的名称`"
                            class="mt-1"
                        />
                    </div>

                    <div v-if="serviceType !== 'LoadBalancer'">
                        <div class="flex items-center">
                            <Label :for="`port_${index}`">服务端口</Label>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger as-child>
                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p class="max-w-xs">服务将在此端口上监听流量。集群内的其他应用可以通过此端口访问您的服务。</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                        <Input
                            v-model.number="(port as ServicePortWithId).port"
                            @update:model-value="emitUpdate"
                            type="number"
                            min="1"
                            max="65535"
                            placeholder="80"
                            :title="`输入第 ${index + 1} 个端口的服务端口号`"
                            class="mt-1"
                        />
                    </div>

                    <div>
                        <div class="flex items-center">
                            <Label :for="`target_port_${index}`">目标端口</Label>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger as-child>
                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p class="max-w-xs">流量将转发到您容器的这个端口。这应该与您在应用中配置的端口相匹配。</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                        <Input
                            v-model.number="(port as ServicePortWithId).target_port"
                            @update:model-value="emitUpdate"
                            type="number"
                            min="1"
                            max="65535"
                            placeholder="8080"
                            :title="`输入第 ${index + 1} 个端口的目标端口号（容器内部端口）`"
                            class="mt-1"
                        />
                    </div>

                    <div>
                        <Label :for="`protocol_${index}`">协议</Label>
                        <Select v-model="port.protocol" @update:model-value="emitUpdate" :title="`选择第 ${index + 1} 个端口的协议`">
                            <SelectTrigger class="mt-1">
                                <SelectValue placeholder="选择协议" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="TCP">TCP</SelectItem>
                                <SelectItem value="UDP">UDP</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div v-else-if="mode === 'container'" class="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div>
                        <Label :for="`container_port_name_${index}`">名称</Label>
                        <Input
                            v-model="port.name"
                            @update:model-value="emitUpdate"
                            placeholder="例如：http"
                            :title="`输入第 ${index + 1} 个容器端口的名称`"
                            class="mt-1"
                        />
                    </div>
                    <div>
                        <Label :for="`container_port_${index}`">端口</Label>
                        <Input
                            v-model.number="(port as ContainerPortWithId).container_port"
                            @update:model-value="emitUpdate"
                            type="number"
                            placeholder="8080"
                            min="1"
                            max="65535"
                            :title="`输入第 ${index + 1} 个容器端口号`"
                            class="mt-1"
                        />
                    </div>
                    <div>
                        <Label :for="`container_protocol_${index}`">协议</Label>
                        <Select v-model="port.protocol" @update:model-value="emitUpdate" :title="`选择第 ${index + 1} 个容器端口的协议`">
                            <SelectTrigger class="mt-1">
                                <SelectValue placeholder="协议" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="TCP">TCP</SelectItem>
                                <SelectItem value="UDP">UDP</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Info, Plus, Trash2 } from 'lucide-vue-next';
import { ref, watch } from 'vue';

interface ContainerPort {
    name?: string;
    container_port?: number;
    protocol?: string;
}

interface ServicePort {
    name?: string;
    port?: number;
    target_port?: number;
    protocol?: string;
}

type InputPortType = ContainerPort | ServicePort;

interface ContainerPortWithId extends ContainerPort {
    id: number;
}
interface ServicePortWithId extends ServicePort {
    id: number;
}
type PortWithId = ContainerPortWithId | ServicePortWithId;

interface Props {
    modelValue: InputPortType[] | null | undefined;
    label?: string;
    mode?: 'container' | 'service';
    serviceType?: string;
}

const props = withDefaults(defineProps<Props>(), {
    label: '端口配置',
    mode: 'container',
    serviceType: 'ClusterIP',
});

const emit = defineEmits<{
    'update:modelValue': [value: InputPortType[]];
}>();

let idCounter = 0;
const internalPorts = ref<PortWithId[]>([]);

watch(
    () => props.modelValue,
    (newVal) => {
        const newCleanVal = newVal ? JSON.stringify(newVal) : '[]';
        const internalCleanVal = JSON.stringify(internalPorts.value.map(({ id, ...rest }) => rest));

        if (newCleanVal !== internalCleanVal) {
            internalPorts.value = (newVal || []).map((port) => ({ ...port, id: idCounter++ }) as PortWithId);
        }
    },
    { deep: true, immediate: true },
);

function emitUpdate() {
    const output = internalPorts.value.map(({ id, ...rest }) => rest);
    emit('update:modelValue', output);
}

const addPort = () => {
    let newPort: PortWithId;
    const id = idCounter++;

    const defaultPortName = 'port' + id;

    if (props.mode === 'container') {
        newPort = {
            id,
            name: defaultPortName,
            container_port: 80,
            protocol: 'TCP',
        };
    } else {
        if (props.serviceType === 'LoadBalancer') {
            newPort = {
                id,
                name: defaultPortName,
                port: undefined,
                target_port: 8080,
                protocol: 'TCP',
            };
        } else {
            newPort = {
                id,
                name: defaultPortName,
                port: 80,
                target_port: 8080,
                protocol: 'TCP',
            };
        }
    }
    internalPorts.value.push(newPort);
    emitUpdate();
};

const removePort = (index: number) => {
    internalPorts.value.splice(index, 1);
    emitUpdate();
};
</script>
