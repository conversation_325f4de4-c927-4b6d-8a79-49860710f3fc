<template>
    <Teleport to="body">
        <div v-if="shouldRender" class="pointer-events-none fixed top-0 right-0 left-0 z-[9999] px-4 pt-3">
            <div class="mx-auto max-w-sm">
                <div
                    ref="notificationElement"
                    :class="[
                        'pointer-events-auto relative overflow-hidden rounded-lg px-3 py-2.5 text-white shadow-lg backdrop-blur-sm hover:scale-[1.02] hover:shadow-xl',
                        getNotificationClasses(),
                        animationClass,
                    ]"
                    :style="animationStyle"
                >
                    <!-- 背景装饰动画 -->
                    <div class="animate-shimmer absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>
                    <div class="relative flex items-center space-x-2.5">
                        <!-- 动画图标 -->
                        <div class="flex-shrink-0">
                            <!-- 成功类型图标 -->
                            <div v-if="!notification?.type || notification.type === 'success'" class="relative">
                                <CheckCircle class="animate-celebration h-5 w-5 text-white" />
                                <div class="absolute inset-0 h-5 w-5 animate-ping rounded-full bg-white/20 opacity-60"></div>
                            </div>

                            <!-- 搜索类型图标 -->
                            <div v-else-if="notification.type === 'searching'" class="relative h-6 w-6">
                                <!-- 文档图标作为背景 -->
                                <FileText class="absolute top-0.5 left-0.5 h-5 w-5 text-white" />
                                <!-- 放大镜扫描动画 -->
                                <Search class="animate-document-scan absolute h-3 w-3 text-white" />
                                <!-- 扫描光效 -->
                                <div class="animate-scan-pulse absolute inset-0 h-6 w-6 rounded-full bg-blue-300/30"></div>
                            </div>
                        </div>

                        <!-- 通知内容 -->
                        <div class="min-w-0 flex-1">
                            <p class="text-sm leading-tight font-medium">{{ notification?.title }}</p>
                            <p v-if="notification?.message" :class="getMessageTextClass()" class="mt-0.5 text-xs leading-tight">
                                {{ notification.message }}
                            </p>
                        </div>

                        <!-- 简化的进度指示器 -->
                        <div class="flex-shrink-0">
                            <div class="animate-progress-pulse relative h-6 w-6">
                                <svg class="h-6 w-6 -rotate-90 transform" viewBox="0 0 24 24">
                                    <circle class="text-green-300/40" stroke="currentColor" stroke-width="2" fill="none" cx="12" cy="12" r="10" />
                                    <circle
                                        class="text-white transition-all duration-100 ease-linear"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        fill="none"
                                        stroke-linecap="round"
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        :stroke-dasharray="`${progress * 0.628}, 62.8`"
                                    />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Teleport>
</template>

<script setup lang="ts">
import type { LiveNotificationData } from '@/types/notification';
import { CheckCircle, FileText, Search } from 'lucide-vue-next';
import { computed, nextTick, onBeforeUnmount, ref, watch } from 'vue';

const props = withDefaults(
    defineProps<{
        notification: LiveNotificationData | null;
        visible: boolean;
    }>(),
    {
        notification: null,
        visible: false,
    },
);

const emit = defineEmits<{
    hide: [];
}>();

const progress = ref(100);
const notificationElement = ref<HTMLElement | null>(null);
const animationPhase = ref<'hidden' | 'entering' | 'visible' | 'exiting'>('hidden');

let progressInterval: ReturnType<typeof setInterval> | null = null;
let hideTimeout: ReturnType<typeof setTimeout> | null = null;
let phaseTimeout: ReturnType<typeof setTimeout> | null = null;

// 随机动画参数
const animationParams = ref({
    enterDistance: 100,
    bounceAmount: 20,
    rotation: 0,
    scale: 0.95,
});

// 控制是否渲染组件（在退出动画期间保持渲染）
const shouldRender = computed(() => {
    return props.visible || animationPhase.value === 'exiting';
});

// 根据通知类型获取样式类
const getNotificationClasses = () => {
    const type = props.notification?.type || 'success';

    switch (type) {
        case 'searching':
            return 'bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-blue-600 dark:to-indigo-600 border border-blue-400/20 dark:border-blue-500/20';
        case 'success':
        default:
            return 'bg-gradient-to-r from-green-500 to-emerald-500 dark:from-green-600 dark:to-emerald-600 border border-green-400/20 dark:border-green-500/20';
    }
};

// 根据通知类型获取消息文本样式
const getMessageTextClass = () => {
    const type = props.notification?.type || 'success';

    switch (type) {
        case 'searching':
            return 'text-blue-100 dark:text-blue-200';
        case 'success':
        default:
            return 'text-green-100 dark:text-green-200';
    }
};

// 动画类名
const animationClass = computed(() => {
    let className = '';
    switch (animationPhase.value) {
        case 'entering':
            className = 'notification-enter';
            break;
        case 'visible':
            className = 'notification-visible';
            break;
        case 'exiting':
            className = 'notification-exit';
            break;
        default:
            className = 'notification-hidden';
    }
    console.log(`🏷️ Animation class for phase "${animationPhase.value}": ${className}`);
    return className;
});

// 动画样式
const animationStyle = computed(() => {
    const params = animationParams.value;

    let style = {};

    switch (animationPhase.value) {
        case 'hidden':
            style = {
                transform: `translateY(-${params.enterDistance}px) scale(${params.scale}) rotate(${params.rotation}deg)`,
                opacity: '0',
                transition: 'none',
            };
            break;
        case 'entering':
            style = {
                transform: 'translateY(0) scale(1) rotate(0deg)',
                opacity: '1',
                transition: 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important',
            };
            break;
        case 'visible':
            style = {
                transform: 'translateY(0) scale(1) rotate(0deg)',
                opacity: '1',
                transition: 'transform 0.3s ease-out',
            };
            break;
        case 'exiting':
            // 退出动画完全由JS控制，不设置CSS样式避免冲突
            style = {};
            break;
        default:
            style = {};
    }

    console.log(`🎨 Animation style for phase "${animationPhase.value}":`, style);
    return style;
});

const startProgress = (duration: number) => {
    progress.value = 100;
    const interval = 50;
    const decrement = (100 * interval) / duration;

    progressInterval = setInterval(() => {
        progress.value -= decrement;
        if (progress.value <= 0) {
            progress.value = 0;
            clearInterval(progressInterval!);
        }
    }, interval);
};

const clearTimers = () => {
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }
    if (hideTimeout) {
        clearTimeout(hideTimeout);
        hideTimeout = null;
    }
    if (phaseTimeout) {
        clearTimeout(phaseTimeout);
        phaseTimeout = null;
    }
};

const startExitAnimation = () => {
    console.log('🚪 Starting exit animation (JS sequence)...');
    animationPhase.value = 'exiting';

    const el = notificationElement.value;
    if (!el) {
        console.log('❌ Element not found, emitting hide directly');
        emit('hide');
        return;
    }

    console.log('📍 Current element transform:', el.style.transform);
    console.log('📍 Current element transition:', el.style.transition);

    // 清除任何现有的transition，确保立即应用初始状态
    el.style.transition = 'none';
    el.style.transform = 'translateY(0) scale(1) rotate(0deg)';

    // 强制重排，确保样式生效
    el.offsetHeight;

    // 第一步：轻微下沉
    console.log('⬇️ Phase 1: Sinking down...');
    el.style.transition = 'transform 150ms ease-out';
    el.style.transform = 'translateY(10px) scale(0.98)';

    // 等待第一段过渡结束
    setTimeout(() => {
        console.log('⬆️ Phase 2: Sliding up and fading...');
        // 第二步：加速上滑并淡出
        el.style.transition = 'transform 500ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms ease-in';
        el.style.transform = 'translateY(-150%) scale(0.9)';
        el.style.opacity = '0';

        // 结束后销毁
        phaseTimeout = setTimeout(() => {
            console.log('🔚 Exit animation JS sequence complete');
            emit('hide');
        }, 520);
    }, 170); // 稍大于第一段过渡
};

watch(
    () => props.visible,
    (newVisible) => {
        console.log('🔄 visible changed:', newVisible);

        if (newVisible && props.notification) {
            console.log('📝 Starting notification...');
            clearTimers();

            // 生成新的随机参数
            animationParams.value = {
                enterDistance: 80 + Math.random() * 40, // 80-120px
                bounceAmount: 10 + Math.random() * 20, // 10-30px
                rotation: (Math.random() - 0.5) * 10, // -5 to 5 degrees
                scale: 0.9 + Math.random() * 0.1, // 0.9-1.0
            };

            console.log('🎲 Animation params:', animationParams.value);

            // 初始隐藏状态
            animationPhase.value = 'hidden';

            nextTick(() => {
                console.log('⏰ NextTick - DOM updated');

                // 确保元素已经渲染并应用了hidden状态
                setTimeout(() => {
                    console.log('🚀 Starting enter animation');
                    // 开始进入动画
                    animationPhase.value = 'entering';

                    // 进入动画完成后切换到可见状态
                    phaseTimeout = setTimeout(() => {
                        console.log('✅ Enter animation complete - switching to visible');
                        animationPhase.value = 'visible';

                        const duration = props.notification?.duration || 4000;
                        console.log('⏱️ Duration:', duration);

                        // 开始进度条
                        startProgress(duration);

                        // 设置自动隐藏
                        hideTimeout = setTimeout(() => {
                            console.log('🏁 Auto-hide triggered');
                            startExitAnimation();
                        }, duration);
                    }, 600); // 等待进入动画完成
                }, 50); // 给浏览器时间渲染hidden状态
            });
        } else if (!newVisible && animationPhase.value !== 'exiting') {
            console.log('❌ Hiding notification (external trigger)...');
            clearTimers();
            animationPhase.value = 'hidden';
            progress.value = 100;
        }
    },
);

onBeforeUnmount(() => {
    clearTimers();
});
</script>

<style scoped>
/* 图标的创意动画 */
@keyframes icon-celebration {
    0%,
    100% {
        transform: scale(1) rotate(0deg);
    }

    25% {
        transform: scale(1.2) rotate(-10deg);
    }

    50% {
        transform: scale(1.1) rotate(5deg);
    }

    75% {
        transform: scale(1.15) rotate(-5deg);
    }
}

.animate-celebration {
    animation: icon-celebration 0.6s ease-in-out;
}

/* 进度环的脉冲效果 */
@keyframes progress-pulse {
    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

.animate-progress-pulse {
    animation: progress-pulse 2s ease-in-out infinite;
}

/* 背景闪烁效果 */
@keyframes shimmer {
    0% {
        transform: translateX(-100%) skewX(-15deg);
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        transform: translateX(200%) skewX(-15deg);
        opacity: 0;
    }
}

.animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
    animation-delay: 0.5s;
}

/* 文档扫描动画 */
@keyframes document-scan {
    0% {
        top: 0px;
        left: 0px;
        transform: rotate(0deg);
    }

    25% {
        top: 2px;
        left: 8px;
        transform: rotate(15deg);
    }

    50% {
        top: 8px;
        left: 12px;
        transform: rotate(-10deg);
    }

    75% {
        top: 5px;
        left: 3px;
        transform: rotate(20deg);
    }

    100% {
        top: 0px;
        left: 0px;
        transform: rotate(0deg);
    }
}

.animate-document-scan {
    animation: document-scan 1.5s ease-in-out infinite;
}

/* 扫描脉冲效果 */
@keyframes scan-pulse {
    0%,
    100% {
        transform: scale(1);
        opacity: 0.3;
    }

    50% {
        transform: scale(1.2);
        opacity: 0.6;
    }
}

.animate-scan-pulse {
    animation: scan-pulse 1s ease-in-out infinite;
}

/* 可见状态下的轻微摆动 */
.notification-visible {
    animation: gentle-sway 4s ease-in-out infinite;
}

@keyframes gentle-sway {
    0%,
    100% {
        transform: translateX(0) rotate(0deg);
    }

    25% {
        transform: translateX(1px) rotate(0.3deg);
    }

    75% {
        transform: translateX(-1px) rotate(-0.3deg);
    }
}
</style>
