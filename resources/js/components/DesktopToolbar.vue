<template>
    <div class="fixed top-0 right-0 left-0 z-30 flex h-10 items-center justify-between border-b border-border bg-background/70 px-4 backdrop-blur">
        <!-- 左侧：Workspace 切换器 -->
        <div class="flex items-center gap-2">
            <DesktopWorkspaceSwitcher />
            <!-- 启动台按钮 -->
            <Button variant="ghost" size="sm" @click="$emit('toggle-launchpad')">
                <Grid2x2 class="h-4 w-4" />
            </Button>

            <!-- 返回到仪表盘模式 -->
            <Button variant="ghost" size="sm" @click="router.visit(route('dashboard'))">
                <LayoutDashboard class="h-4 w-4" />
            </Button>
        </div>

        <!-- 右侧：用户菜单 -->
        <DropdownMenu>
            <DropdownMenuTrigger as-child>
                <Button variant="ghost" size="sm" class="flex items-center gap-2">
                    <User class="h-4 w-4" />
                    <span class="hidden text-sm md:inline">{{ user?.name }}</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                <DropdownMenuItem @select="openProfile"> 个人资料 </DropdownMenuItem>
                <DropdownMenuItem @select="openAppearance"> 外观设置 </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem as-child>
                    <Link :href="route('logout')" method="post" class="text-red-600 dark:text-red-400"> 退出登录 </Link>
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    </div>
</template>

<script setup lang="ts">
import DesktopWorkspaceSwitcher from '@/components/DesktopWorkspaceSwitcher.vue';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import eventBus from '@/lib/eventBus';
import { Link, router, usePage } from '@inertiajs/vue3';
import { Grid2x2, LayoutDashboard, User } from 'lucide-vue-next';
import { computed } from 'vue';
import { route } from 'ziggy-js';

const page = usePage();
const user = computed(() => page.props.auth?.user ?? null);

const openProfile = () => {
    eventBus.emit('window:open', {
        type: 'browser',
        data: { url: route('profile.edit'), title: '个人资料' },
        options: { icon: 'User' },
    });
};

const openAppearance = () => {
    eventBus.emit('window:open', {
        type: 'browser',
        data: { url: route('appearance'), title: '外观设置' },
        options: { icon: 'Palette' },
    });
};
</script>
