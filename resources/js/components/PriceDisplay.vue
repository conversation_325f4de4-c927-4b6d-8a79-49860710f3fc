<template>
    <span :class="containerClass">
        <span v-if="showCurrency" class="currency">{{ currency }}</span>
        <span class="amount">{{ formattedAmount }}</span>
        <span v-if="showUnit && unit" class="unit">{{ unit }}</span>
    </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
    /** 价格数值，支持字符串或数字 */
    value: string | number | null | undefined;
    /** 货币符号 */
    currency?: string;
    /** 小数位数 */
    decimals?: number;
    /** 是否显示货币符号 */
    showCurrency?: boolean;
    /** 单位文本 */
    unit?: string;
    /** 是否显示单位 */
    showUnit?: boolean;
    /** 容器CSS类名 */
    containerClass?: string;
    /** 当价格为0时的显示文本 */
    zeroText?: string;
    /** 当价格为null/undefined时的显示文本 */
    nullText?: string;
}

const props = withDefaults(defineProps<Props>(), {
    currency: '¥',
    decimals: 2,
    showCurrency: true,
    showUnit: false,
    containerClass: 'inline-flex items-baseline gap-0.5',
    zeroText: '0.00',
    nullText: '--',
});

const formattedAmount = computed(() => {
    // 处理 null 或 undefined
    if (props.value === null || props.value === undefined) {
        return props.nullText;
    }

    // 转换为字符串处理
    const valueStr = String(props.value);

    // 处理空字符串
    if (valueStr.trim() === '') {
        return props.nullText;
    }

    // 转换为数字
    const numValue = Number(valueStr);

    // 处理 NaN
    if (isNaN(numValue)) {
        return props.nullText;
    }

    // 处理0值
    if (numValue === 0) {
        return props.zeroText;
    }

    // 格式化数字
    return formatNumber(numValue, props.decimals);
});

/**
 * 格式化数字，避免精度问题
 */
function formatNumber(value: number, decimals: number): string {
    // 使用 toFixed 处理小数位数
    const fixed = value.toFixed(decimals);

    // 移除尾部的0
    const trimmed = fixed.replace(/\.?0+$/, '');

    // 如果结果为空或只有小数点，返回 "0"
    if (trimmed === '' || trimmed === '.') {
        return '0';
    }

    // 添加千分位分隔符
    return addThousandsSeparator(trimmed);
}

/**
 * 添加千分位分隔符
 */
function addThousandsSeparator(value: string): string {
    const parts = value.split('.');
    const integerPart = parts[0];
    const decimalPart = parts[1];

    // 为整数部分添加千分位分隔符
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    // 重新组合
    return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
}
</script>

<style scoped>
.currency {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.amount {
    font-weight: 600;
    color: #111827;
}

.unit {
    font-size: 0.875rem;
    color: #6b7280;
}

@media (prefers-color-scheme: dark) {
    .currency {
        color: #9ca3af;
    }

    .amount {
        color: #ffffff;
    }

    .unit {
        color: #9ca3af;
    }
}
</style>
