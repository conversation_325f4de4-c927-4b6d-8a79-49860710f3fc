<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useFileDropzone } from '@/composables/useFileDropzone';
import { convertContentToCustomYaml } from '@/lib/dockerConvert';
import { Upload } from 'lucide-vue-next';
import { ref, watch } from 'vue';
import { toast } from 'vue-sonner';

interface Props {
    open: boolean;
}
interface Emits {
    (e: 'update:open', value: boolean): void;
    (e: 'converted', value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const rawContent = ref('');

// 拖拽上传
const { isDragging, onDragEnter, onDragLeave, onDragOver, onDrop } = useFileDropzone({
    allowedExtensions: ['.yaml', '.yml', '.txt', '.conf', '.compose'],
    onFileRead: (content: string) => {
        rawContent.value = content;
    },
});

// 转换操作
const handleConvert = () => {
    if (!rawContent.value.trim()) {
        toast.error('请输入或导入 Docker Run 命令或 Compose 文件内容');
        return;
    }

    const converted = convertContentToCustomYaml(rawContent.value);
    if (converted === rawContent.value) {
        toast.error('无法识别内容，请确认格式是否正确');
        return;
    }
    emit('converted', converted);
    emit('update:open', false);
};

watch(
    () => props.open,
    (val) => {
        if (!val) {
            rawContent.value = '';
        }
    },
);
</script>

<template>
    <Dialog :open="props.open" @update:open="(v) => emit('update:open', v)">
        <DialogContent class="max-w-3xl">
            <DialogHeader>
                <DialogTitle>导入 Docker Run / Compose</DialogTitle>
                <DialogDescription>
                    粘贴 <code>docker run</code> 命令或 <code>docker-compose.yml</code> 内容，或拖拽文件到下方区域，然后点击 “转换” 即可生成平台所需
                    YAML。
                </DialogDescription>
            </DialogHeader>

            <div class="flex flex-col gap-4">
                <div class="flex-1 space-y-2">
                    <Label for="docker-content">Docker 内容</Label>
                    <div class="relative" @dragenter="onDragEnter" @dragleave="onDragLeave" @dragover="onDragOver" @drop="onDrop">
                        <!-- 拖拽覆盖层 -->
                        <div
                            v-if="isDragging"
                            class="absolute inset-0 z-10 flex items-center justify-center rounded-md border-2 border-dashed border-primary bg-primary/5"
                        >
                            <div class="text-center">
                                <Upload class="mx-auto h-8 w-8 text-primary" />
                                <p class="mt-2 text-sm font-medium text-primary">释放文件以导入内容</p>
                                <p class="text-xs text-muted-foreground">支持 .yml .yaml .txt 等文件</p>
                            </div>
                        </div>
                        <ScrollArea class="h-80 rounded-md border">
                            <textarea
                                id="docker-content"
                                v-model="rawContent"
                                class="h-80 w-full resize-none border-0 bg-transparent p-4 font-mono text-sm focus:ring-0 focus:outline-none"
                                placeholder="粘贴 docker run 或 docker-compose 内容..."
                                spellcheck="false"
                            />
                        </ScrollArea>
                    </div>
                </div>

                <div class="flex justify-end">
                    <Button variant="secondary" class="mr-2" @click="emit('update:open', false)">取消</Button>
                    <Button @click="handleConvert">转换</Button>
                </div>
            </div>
        </DialogContent>
    </Dialog>
</template>
