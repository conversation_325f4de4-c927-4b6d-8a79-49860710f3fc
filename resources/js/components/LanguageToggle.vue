<template>
    <Button variant="ghost" size="sm" @click="toggleLanguage" :title="t('common.language')">
        {{ locale === 'zh-CN' ? 'EN' : '中文' }}
    </Button>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useI18n } from '@/composables/useI18n';

const { t, locale, setLocale } = useI18n();

const toggleLanguage = () => {
    const newLocale = locale.value === 'zh-CN' ? 'en-US' : 'zh-CN';
    setLocale(newLocale);
};
</script>
