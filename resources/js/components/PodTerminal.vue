<template>
    <div class="relative h-full w-full">
        <!-- Pod 选择界面 -->
        <div v-if="!selectedPod" class="flex h-full w-full flex-col">
            <!-- 头部 -->
            <div class="border-b bg-muted/50 px-4 py-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <TerminalIcon class="h-5 w-5 text-primary" />
                        <h3 class="text-lg font-semibold">选择 Pod</h3>
                    </div>
                    <Button variant="outline" size="sm" @click="refreshPods" :disabled="loadingPods">
                        <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': loadingPods }" />
                        刷新
                    </Button>
                </div>
                <p class="mt-1 text-sm text-muted-foreground">选择一个 Pod 来连接终端</p>
            </div>

            <!-- Pod 列表 -->
            <div class="flex-1 overflow-auto p-4">
                <!-- 加载状态 -->
                <div v-if="loadingPods" class="flex items-center justify-center py-12">
                    <div class="text-center">
                        <RefreshCw class="mx-auto h-8 w-8 animate-spin text-muted-foreground" />
                        <p class="mt-2 text-sm text-muted-foreground">加载 Pod 列表中...</p>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-else-if="pods.length === 0" class="flex items-center justify-center py-12">
                    <div class="text-center">
                        <AlertCircle class="mx-auto h-12 w-12 text-muted-foreground" />
                        <h3 class="mt-4 text-lg font-medium">暂无 Pod</h3>
                        <p class="mt-2 text-sm text-muted-foreground">当前工作空间中没有运行的 Pod</p>
                    </div>
                </div>

                <!-- Pod 卡片列表 -->
                <div v-else class="grid gap-4">
                    <div
                        v-for="pod in pods"
                        :key="pod.name"
                        class="cursor-pointer rounded-lg border bg-card p-4 transition-all hover:shadow-md"
                        :class="{
                            'border-primary ring-2 ring-primary/20': pod.status.toLowerCase() === 'running',
                            'border-destructive': pod.status.toLowerCase() === 'failed',
                            'border-yellow-500': pod.status.toLowerCase() === 'pending',
                            'opacity-60': pod.status.toLowerCase() !== 'running',
                        }"
                        @click="selectPod(pod)"
                    >
                        <div class="flex items-start justify-between">
                            <div class="min-w-0 flex-1">
                                <div class="flex items-center space-x-2">
                                    <div
                                        class="h-3 w-3 rounded-full"
                                        :class="{
                                            'bg-green-500': pod.status.toLowerCase() === 'running',
                                            'bg-red-500': pod.status.toLowerCase() === 'failed',
                                            'bg-yellow-500': pod.status.toLowerCase() === 'pending',
                                            'bg-gray-500': !['running', 'failed', 'pending'].includes(pod.status.toLowerCase()),
                                        }"
                                    />
                                    <h4 class="truncate font-medium">{{ pod.name }}</h4>
                                    <Badge :variant="getStatusBadgeVariant(pod.status)">
                                        {{ formatPodStatus(pod.status) }}
                                    </Badge>
                                </div>

                                <div class="mt-2 space-y-1">
                                    <div class="flex items-center space-x-4 text-sm text-muted-foreground">
                                        <span>容器: {{ pod.containers.length }}</span>
                                        <span>重启: {{ pod.restart_count || 0 }}</span>
                                        <span>运行时间: {{ formatAge(pod.created_at) }}</span>
                                    </div>

                                    <!-- 容器列表 -->
                                    <div class="mt-2 flex flex-wrap gap-1">
                                        <Badge
                                            v-for="container in pod.containers"
                                            :key="container.name"
                                            variant="outline"
                                            class="text-xs"
                                            :class="{
                                                'border-green-500 text-green-700': container.ready,
                                                'border-red-500 text-red-700': !container.ready,
                                            }"
                                        >
                                            {{ container.name }}
                                        </Badge>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center space-x-2">
                                <Button v-if="pod.status.toLowerCase() === 'running'" size="sm" @click.stop="selectPod(pod)">
                                    <TerminalIcon class="mr-2 h-4 w-4" />
                                    连接
                                </Button>
                                <Button v-else size="sm" variant="outline" disabled> 不可用 </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 终端界面 -->
        <div v-else class="h-full w-full">
            <!-- 连接状态栏 -->
            <div v-if="props.showStatusBar" class="border-b bg-muted/50 px-4 py-2">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <div
                            class="h-2 w-2 rounded-full"
                            :class="{
                                'bg-green-500': connectionStatus === 'connected',
                                'bg-yellow-500': connectionStatus === 'connecting',
                                'bg-red-500': connectionStatus === 'disconnected',
                                'bg-gray-500': connectionStatus === 'closed',
                            }"
                        />
                        <span class="text-sm font-medium">
                            {{ getStatusText() }}
                        </span>
                        <Badge v-if="mode" variant="outline" class="text-xs">
                            {{ mode === 'shell' ? 'Shell' : 'Attach' }}
                        </Badge>
                        <Badge v-if="selectedContainer" variant="secondary" class="text-xs">
                            {{ selectedContainer }}
                        </Badge>
                        <Badge variant="outline" class="text-xs">
                            {{ selectedPod.name }}
                        </Badge>
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- 容器选择器 -->
                        <Select v-if="selectedPod.containers.length > 1" v-model="selectedContainer" @update:model-value="handleContainerChange">
                            <SelectTrigger class="w-32">
                                <SelectValue placeholder="选择容器" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem
                                    v-for="container in selectedPod.containers"
                                    :key="container.name"
                                    :value="container.name"
                                    :disabled="!container.ready"
                                >
                                    {{ container.name }}
                                    <Badge v-if="!container.ready" variant="destructive" class="ml-2 text-xs">未就绪</Badge>
                                </SelectItem>
                            </SelectContent>
                        </Select>

                        <Button variant="outline" size="sm" @click="goBack">
                            <ArrowLeft class="mr-1 h-3 w-3" />
                            返回
                        </Button>

                        <Button v-if="connectionStatus === 'disconnected'" variant="outline" size="sm" @click="connect" :disabled="connecting">
                            <RotateCcw class="mr-1 h-3 w-3" />
                            重连
                        </Button>
                        <Button v-if="connectionStatus === 'connected'" variant="outline" size="sm" @click="disconnect">
                            <X class="mr-1 h-3 w-3" />
                            断开
                        </Button>
                    </div>
                </div>
            </div>

            <!-- 终端区域 -->
            <div ref="terminalContainer" class="h-full w-full bg-black" :class="{ 'opacity-50': connectionStatus !== 'connected' }" />

            <!-- 加载遮罩 -->
            <div v-if="connecting" class="absolute inset-0 flex items-center justify-center bg-black/50">
                <div class="text-white">
                    <div class="mb-2 text-center">
                        <div class="mx-auto h-6 w-6 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    </div>
                    <p class="text-sm">正在连接到 Pod 终端...</p>
                </div>
            </div>

            <!-- 错误提示 -->
            <div v-if="error" class="absolute inset-0 flex items-center justify-center bg-black/80">
                <div class="max-w-md rounded-lg bg-destructive/90 p-4 text-destructive-foreground">
                    <div class="mb-2 flex items-center">
                        <AlertCircle class="mr-2 h-4 w-4" />
                        <h3 class="font-medium">连接失败</h3>
                    </div>
                    <p class="text-sm">{{ error }}</p>
                    <div class="mt-3 flex space-x-2 text-black dark:text-white">
                        <Button variant="outline" size="sm" @click="clearError">关闭</Button>
                        <Button variant="outline" size="sm" @click="goBack">返回选择</Button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import axios from '@/lib/axios';
import { formatPodStatus } from '@/lib/formatK8sStatus';
import type { Pod } from '@/types';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { Terminal } from '@xterm/xterm';
import { AlertCircle, ArrowLeft, RefreshCw, RotateCcw, Terminal as TerminalIcon, X } from 'lucide-vue-next';
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { toast } from 'vue-sonner';

// 引入 xterm 样式
import '@xterm/xterm/css/xterm.css';

interface Props {
    podName?: string;
    containerName?: string;
    mode?: 'shell' | 'attach';
    autoConnect?: boolean;
    showStatusBar?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    mode: 'shell',
    autoConnect: true,
    showStatusBar: true,
});

const emit = defineEmits<{
    'connection-status-changed': [status: string];
    'pod-selected': [pod: Pod, container: string];
}>();

// 响应式状态
const terminalContainer = ref<HTMLDivElement>();
const connectionStatus = ref<'disconnected' | 'connecting' | 'connected' | 'closed'>('disconnected');
const connecting = ref(false);
const error = ref<string | null>(null);

// Pod 选择相关状态
const pods = ref<Pod[]>([]);
const loadingPods = ref(false);
const selectedPod = ref<Pod | null>(null);
const selectedContainer = ref<string>('');

// Terminal 和插件实例
let terminal: Terminal | null = null;
let fitAddon: FitAddon | null = null;
let websocket: WebSocket | null = null;
let reconnectTimer: number | null = null;
let heartbeatTimer: number | null = null;

// 新增: 统一释放终端资源的帮助函数
const disposeTerminal = () => {
    if (terminal) {
        try {
            terminal.dispose();
        } catch (e) {
            console.error('终端释放失败:', e);
        }
        terminal = null;
        // 清空容器内容，避免残留导致布局异常
        if (terminalContainer.value) {
            terminalContainer.value.innerHTML = '';
        }
    }
};

// 计算属性
const currentPodName = computed(() => selectedPod.value?.name || props.podName);
const currentContainerName = computed(() => selectedContainer.value || props.containerName);

// 获取状态文本
const getStatusText = () => {
    switch (connectionStatus.value) {
        case 'connecting':
            return '正在连接...';
        case 'connected':
            return '已连接';
        case 'disconnected':
            return '未连接';
        case 'closed':
            return '连接关闭';
        default:
            return '未知状态';
    }
};

// 格式化时间
const formatAge = (created_at?: string): string => {
    if (!created_at) return 'Unknown';

    const now = new Date();
    const created = new Date(created_at);
    const diffMs = now.getTime() - created.getTime();

    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays > 0) return `${diffDays}d`;
    if (diffHours > 0) return `${diffHours}h`;
    if (diffMinutes > 0) return `${diffMinutes}m`;
    return '<1m';
};

// 获取状态徽章变体
const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'default';
        case 'pending':
            return 'secondary';
        case 'succeeded':
            return 'default';
        case 'failed':
            return 'destructive';
        case 'unknown':
            return 'outline';
        default:
            return 'secondary';
    }
};

// 获取 Pod 列表
const fetchPods = async () => {
    try {
        loadingPods.value = true;
        const response = await axios.get('/api/pods');
        pods.value = response.data;
    } catch (error) {
        console.error('获取 Pod 列表失败:', error);
        toast.error('获取 Pod 列表失败');
    } finally {
        loadingPods.value = false;
    }
};

// 刷新 Pod 列表
const refreshPods = () => {
    fetchPods();
};

// 选择 Pod
const selectPod = (pod: Pod) => {
    if (pod.status.toLowerCase() !== 'running') {
        toast.error('只能连接到运行中的 Pod');
        return;
    }

    selectedPod.value = pod;

    // 选择第一个就绪的容器
    const readyContainer = pod.containers.find((c) => c.ready);
    const defaultContainer = readyContainer || pod.containers[0];

    if (defaultContainer) {
        selectedContainer.value = defaultContainer.name;
        emit('pod-selected', pod, defaultContainer.name);

        // 如果设置了自动连接，则开始连接
        if (props.autoConnect) {
            nextTick(() => {
                initTerminal().then(() => {
                    connect();
                });
            });
        }
    } else {
        toast.error('Pod 中没有可用的容器');
    }
};

// 返回 Pod 选择界面
const goBack = () => {
    disconnect();
    disposeTerminal(); // 释放旧终端实例
    selectedPod.value = null;
    selectedContainer.value = '';
    refreshPods();
};

// 处理容器切换
const handleContainerChange = (value: any) => {
    if (!value || typeof value !== 'string') return;

    if (connectionStatus.value === 'connected') {
        disconnect();
    }
    selectedContainer.value = value;

    if (props.autoConnect) {
        nextTick(() => {
            connect();
        });
    }
};

// 初始化终端
const initTerminal = async () => {
    if (!terminalContainer.value) return;

    // 如果已存在终端实例，先释放，避免重复创建导致按键重复和空白区域
    disposeTerminal();

    // 创建终端实例
    terminal = new Terminal({
        cursorBlink: true,
        theme: {
            background: '#000000',
            foreground: '#ffffff',
            cursor: '#ffffff',
            selectionBackground: '#3f3f3f',
        },
        fontSize: 14,
        fontFamily: 'Menlo, Monaco, "Courier New", monospace',
        rows: 24,
        cols: 80,
    });

    // 添加插件
    fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();

    terminal.loadAddon(fitAddon);
    terminal.loadAddon(webLinksAddon);

    // 打开终端
    terminal.open(terminalContainer.value);

    // 适应容器大小
    await nextTick();
    fitAddon.fit();

    // 监听窗口大小变化
    const resizeObserver = new ResizeObserver(() => {
        if (!fitAddon || !terminalContainer.value) {
            return;
        }

        // 如果容器不可见（宽高为 0），跳过 fit 与 resize 消息，避免最小化时发送 0 尺寸导致格式错乱
        const rect = terminalContainer.value.getBoundingClientRect();
        if (rect.width < 50 || rect.height < 30) {
            return;
        }

        fitAddon.fit();

        // 发送终端大小到服务器
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            const message = {
                type: 'resize',
                cols: terminal?.cols || 80,
                rows: terminal?.rows || 24,
            };
            websocket.send(JSON.stringify(message));
        }
    });

    resizeObserver.observe(terminalContainer.value);

    // 监听用户输入
    terminal.onData((data) => {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            const message = {
                type: 'input',
                data: data,
            };
            websocket.send(JSON.stringify(message));
        }
    });

    return () => {
        resizeObserver.disconnect();
    };
};

// 获取连接 token
const getToken = async () => {
    const response = await axios.post(`/api/pods/${currentPodName.value}/terminal/token`, {
        pod_name: currentPodName.value,
        container_name: currentContainerName.value,
        mode: props.mode,
    });
    return response.data;
};

// 连接到 WebSocket
const connect = async () => {
    if (connecting.value || connectionStatus.value === 'connected') return;
    if (!currentPodName.value || !currentContainerName.value) return;

    await initTerminal();

    try {
        connecting.value = true;
        connectionStatus.value = 'connecting';
        error.value = null;

        // 获取 token
        const tokenData = await getToken();

        // 建立 WebSocket 连接
        websocket = new WebSocket(tokenData.websocket_url);

        websocket.onopen = () => {
            console.log('WebSocket connected');

            // 发送认证信息
            const authMessage = {
                type: 'auth',
                token: tokenData.token,
            };
            websocket!.send(JSON.stringify(authMessage));
        };

        websocket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                handleWebSocketMessage(message);
            } catch (e) {
                console.error('Failed to parse WebSocket message:', e);
            }
        };

        websocket.onerror = (event) => {
            console.error('WebSocket error:', event);
            error.value = '连接错误';
            connectionStatus.value = 'disconnected';
        };

        websocket.onclose = (event) => {
            console.log('WebSocket closed:', event.code, event.reason);
            connectionStatus.value = 'closed';

            // 自动重连（如果不是手动关闭）
            if (event.code !== 1000) {
                scheduleReconnect();
            }
        };
    } catch (e) {
        console.error('Failed to connect:', e);
        error.value = `连接失败: ${e instanceof Error ? e.message : '未知错误'}`;
        connectionStatus.value = 'disconnected';
    } finally {
        connecting.value = false;
    }
};

// 处理 WebSocket 消息
const handleWebSocketMessage = (message: any) => {
    switch (message.type) {
        case 'auth_success':
            connectionStatus.value = 'connected';
            terminal?.write('\r\n\x1b[32m✓ 连接成功\x1b[0m\r\n');
            startHeartbeat();

            // 新增：认证成功后，立即发送一次终端尺寸，确保后端 pty 正确初始化
            if (terminal && websocket && websocket.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'resize',
                    cols: terminal.cols,
                    rows: terminal.rows,
                };
                websocket.send(JSON.stringify(message));
            }
            break;

        case 'connected':
            connectionStatus.value = 'connected';
            break;

        case 'output':
            if (message.data && message.data.data) {
                terminal?.write(message.data.data);
            }
            break;

        case 'error':
            error.value = message.data?.message || '服务器错误';
            terminal?.write(`\r\n\x1b[31m错误: ${error.value}\x1b[0m\r\n`);
            break;

        case 'pong':
            // Server heartbeat response, ignore.
            break;

        default:
            console.log('Unknown message type:', message.type);
    }
};

// 断开连接
const disconnect = () => {
    connectionStatus.value = 'disconnected';

    if (websocket) {
        websocket.close(1000, 'User disconnected');
        websocket = null;
    }

    if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
    }

    if (heartbeatTimer) {
        clearInterval(heartbeatTimer);
        heartbeatTimer = null;
    }

    terminal?.write('\r\n\x1b[33m连接已断开\x1b[0m\r\n');
};

// 计划重连
const scheduleReconnect = () => {
    if (reconnectTimer) return;

    reconnectTimer = setTimeout(() => {
        reconnectTimer = null;
        if (connectionStatus.value !== 'connected' && connectionStatus.value !== 'disconnected') {
            toast.info('尝试重新连接到终端...');
            connect();
        }
    }, 5000);
};

// 开始心跳
const startHeartbeat = () => {
    if (heartbeatTimer) return;

    heartbeatTimer = setInterval(() => {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify({ type: 'ping' }));
        }
    }, 30000);
};

// 清除错误
const clearError = () => {
    error.value = null;
};

// 监听连接状态变化
watch(connectionStatus, (newStatus) => {
    emit('connection-status-changed', newStatus);
});

// 监听 props 变化
watch([() => props.podName, () => props.containerName, () => props.mode], () => {
    // 如果没有提供 podName，直接返回
    if (!props.podName) {
        return;
    }

    // 如果当前已经选中同一个 Pod 且容器、模式均未变化，则不执行任何操作，防止重复绑定导致输入重复
    const isSamePod = selectedPod.value && selectedPod.value.name === props.podName;
    const isSameContainer = selectedContainer.value === props.containerName;
    const isSameMode = props.mode === undefined || props.mode === undefined || props.mode === props.mode; // mode 基本不变，保险起见保留判断

    if (isSamePod && isSameContainer && isSameMode) {
        return;
    }

    // 如果已连接，先断开
    if (connectionStatus.value === 'connected') {
        disconnect();
    }

    // 查找并选择新的 Pod
    const pod = pods.value.find((p) => p.name === props.podName);
    if (pod) {
        selectPod(pod);
        if (props.containerName) {
            selectedContainer.value = props.containerName;
        }
    }
});

// 组件挂载
onMounted(async () => {
    // 如果没有提供 podName，显示 Pod 选择界面
    if (!props.podName) {
        await fetchPods();
    } else {
        // 如果提供了 podName，尝试获取 Pod 列表并找到对应的 Pod
        await fetchPods();
        const pod = pods.value.find((p) => p.name === props.podName);
        if (pod) {
            selectPod(pod);
            if (props.containerName) {
                selectedContainer.value = props.containerName;
            }
        } else {
            // 如果找不到指定的 Pod，显示选择界面
            selectedPod.value = null;
        }
    }
});

// 组件卸载
onUnmounted(() => {
    disconnect();
    disposeTerminal();
});

// 暴露方法给父组件
defineExpose({
    connect,
    disconnect,
    connectionStatus,
    selectPod,
    goBack,
});
</script>
