<script setup lang="ts">
import AppLogo from '@/components/AppLogo.vue';
import AppLogoIcon from '@/components/AppLogoIcon.vue';
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { NavigationMenu, NavigationMenuItem, NavigationMenuList, navigationMenuTriggerStyle } from '@/components/ui/navigation-menu';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import UserMenuContent from '@/components/UserMenuContent.vue';
import WorkspaceSwitcher from '@/components/WorkspaceSwitcher.vue';
import { getInitials } from '@/composables/useInitials';
import type { Auth, BreadcrumbItem, NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/vue3';
import { Bell, Boxes, ChevronDown, Container, Database, FileText, Globe, HardDrive, Key, Menu, Search, Server, Settings, Zap } from 'lucide-vue-next';
import { computed } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    breadcrumbs?: BreadcrumbItem[];
}

const props = withDefaults(defineProps<Props>(), {
    breadcrumbs: () => [],
});

const page = usePage();
const auth = computed(() => page.props.auth as Auth);

const isCurrentRoute = computed(() => (url: string) => page.url === url);

const activeItemStyles = computed(
    () => (url: string) => (isCurrentRoute.value(url) ? 'text-neutral-900 dark:bg-neutral-800 dark:text-neutral-100' : ''),
);

// 主要导航项目
const primaryNavItems: NavItem[] = [
    // {
    //     title: '仪表盘',
    //     href: '/dashboard',
    //     icon: LayoutGrid,
    // },
];

// 工作负载下拉菜单
const applicationNavItems: NavItem[] = [
    {
        title: '无状态',
        href: route('deployments.index'),
        icon: Boxes,
    },
    {
        title: '有状态',
        href: route('statefulsets.index'),
        icon: Database,
    },
    {
        title: '容器管理',
        href: route('pods.index'),
        icon: Container,
    },
    {
        title: '弹性伸缩',
        href: route('hpas.index'),
        icon: Zap,
    },
];

// 配置与存储下拉菜单
const configStorageNavItems: NavItem[] = [
    {
        title: '配置映射',
        href: route('configmaps.index'),
        icon: FileText,
    },
    {
        title: '密钥管理',
        href: route('secrets.index'),
        icon: Key,
    },
    {
        title: '存储管理',
        href: route('storages.index'),
        icon: HardDrive,
    },
];

// 网络管理下拉菜单
const networkNavItems: NavItem[] = [
    {
        title: '服务管理',
        href: route('services.index'),
        icon: Server,
    },
    {
        title: '入口管理',
        href: route('ingresses.index'),
        icon: Globe,
    },
];

// 检查下拉菜单中是否有当前激活的路由
const isDropdownActive = (items: NavItem[]) => {
    return items.some((item) => isCurrentRoute.value(item.href));
};

// 获取下拉菜单的激活样式
const getDropdownActiveStyles = (items: NavItem[]) => {
    return isDropdownActive(items) ? 'text-neutral-900 dark:bg-neutral-800 dark:text-neutral-100' : '';
};

// 所有菜单项目（用于移动端）
const allMainNavItems: NavItem[] = [...primaryNavItems, ...applicationNavItems, ...configStorageNavItems, ...networkNavItems];

// 添加工作区管理项目到移动端菜单
const workspaceNavItems: NavItem[] = [
    {
        title: '管理工作区',
        href: '/workspaces',
        icon: Server,
    },
];

const rightNavItems: NavItem[] = [
    // {
    //     title: 'Repository',
    //     href: 'https://github.com/laravel/vue-starter-kit',
    //     icon: Folder,
    // },
    // {
    //     title: 'Documentation',
    //     href: 'https://laravel.com/docs/starter-kits#vue',
    //     icon: BookOpen,
    // },
];
</script>

<template>
    <div>
        <div class="border-b border-sidebar-border/80">
            <div class="mx-auto flex h-16 items-center px-4 md:max-w-7xl">
                <!-- Mobile Menu -->
                <div class="lg:hidden">
                    <Sheet>
                        <SheetTrigger :as-child="true">
                            <Button variant="ghost" size="icon" class="mr-2 h-9 w-9">
                                <Menu class="h-5 w-5" />
                            </Button>
                        </SheetTrigger>
                        <SheetContent side="left" class="w-[300px] p-6">
                            <SheetTitle class="sr-only">导航菜单</SheetTitle>
                            <SheetHeader class="flex justify-start text-left">
                                <AppLogoIcon class="size-6 fill-current text-black dark:text-white" />
                            </SheetHeader>
                            <div class="flex h-full flex-1 flex-col justify-between space-y-4 py-6">
                                <nav class="-mx-3 space-y-1">
                                    <!-- 工作区选择器 -->
                                    <div class="px-3 py-2">
                                        <WorkspaceSwitcher />
                                    </div>

                                    <!-- 工作区管理 -->
                                    <Link
                                        v-for="item in workspaceNavItems"
                                        :key="item.title"
                                        :href="item.href"
                                        class="flex items-center gap-x-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent"
                                        :class="activeItemStyles(item.href)"
                                    >
                                        <component v-if="item.icon" :is="item.icon" class="h-5 w-5" />
                                        {{ item.title }}
                                    </Link>

                                    <!-- 其他导航项目 -->
                                    <Link
                                        v-for="item in allMainNavItems"
                                        :key="item.title"
                                        :href="item.href"
                                        class="flex items-center gap-x-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent"
                                        :class="activeItemStyles(item.href)"
                                    >
                                        <component v-if="item.icon" :is="item.icon" class="h-5 w-5" />
                                        {{ item.title }}
                                    </Link>
                                </nav>
                                <div class="flex flex-col space-y-4">
                                    <a
                                        v-for="item in rightNavItems"
                                        :key="item.title"
                                        :href="item.href"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        class="flex items-center space-x-2 text-sm font-medium"
                                    >
                                        <component v-if="item.icon" :is="item.icon" class="h-5 w-5" />
                                        <span>{{ item.title }}</span>
                                    </a>
                                </div>
                            </div>
                        </SheetContent>
                    </Sheet>
                </div>

                <Link :href="route('dashboard')" class="flex items-center gap-x-2">
                    <AppLogo />
                </Link>

                <!-- Desktop Menu -->
                <div class="hidden h-full lg:flex lg:flex-1">
                    <NavigationMenu class="ml-10 flex h-full items-stretch">
                        <NavigationMenuList class="flex h-full items-stretch space-x-2">
                            <!-- 工作区选择器 -->
                            <NavigationMenuItem class="relative flex h-full items-center">
                                <WorkspaceSwitcher />
                            </NavigationMenuItem>

                            <!-- 主要导航项目 -->
                            <NavigationMenuItem v-for="(item, index) in primaryNavItems" :key="index" class="relative flex h-full items-center">
                                <Link
                                    :class="[navigationMenuTriggerStyle(), activeItemStyles(item.href), 'h-9 cursor-pointer px-3']"
                                    :href="item.href"
                                >
                                    <component v-if="item.icon" :is="item.icon" class="mr-2 h-4 w-4" />
                                    {{ item.title }}
                                </Link>
                                <div
                                    v-if="isCurrentRoute(item.href)"
                                    class="absolute bottom-0 left-0 h-0.5 w-full translate-y-px bg-black dark:bg-white"
                                ></div>
                            </NavigationMenuItem>

                            <!-- 应用管理下拉菜单 -->
                            <NavigationMenuItem class="relative flex h-full items-center">
                                <DropdownMenu>
                                    <DropdownMenuTrigger :as-child="true">
                                        <Button
                                            :class="[
                                                navigationMenuTriggerStyle(),
                                                getDropdownActiveStyles(applicationNavItems),
                                                'h-9 cursor-pointer px-3',
                                            ]"
                                            variant="ghost"
                                        >
                                            <Boxes class="mr-2 h-4 w-4" />
                                            负载
                                            <ChevronDown class="ml-1 h-3 w-3" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="start" class="w-48">
                                        <DropdownMenuLabel>应用与容器</DropdownMenuLabel>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem v-for="item in applicationNavItems" :key="item.title" :as-child="true">
                                            <Link :href="item.href" class="flex items-center">
                                                <component v-if="item.icon" :is="item.icon" class="mr-2 h-4 w-4" />
                                                {{ item.title }}
                                            </Link>
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                                <div
                                    v-if="isDropdownActive(applicationNavItems)"
                                    class="absolute bottom-0 left-0 h-0.5 w-full translate-y-px bg-black dark:bg-white"
                                ></div>
                            </NavigationMenuItem>

                            <!-- 配置与存储下拉菜单 -->
                            <NavigationMenuItem class="relative flex h-full items-center">
                                <DropdownMenu>
                                    <DropdownMenuTrigger :as-child="true">
                                        <Button
                                            :class="[
                                                navigationMenuTriggerStyle(),
                                                getDropdownActiveStyles(configStorageNavItems),
                                                'h-9 cursor-pointer px-3',
                                            ]"
                                            variant="ghost"
                                        >
                                            <Settings class="mr-2 h-4 w-4" />
                                            配置
                                            <ChevronDown class="ml-1 h-3 w-3" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="start" class="w-48">
                                        <DropdownMenuLabel>配置与存储</DropdownMenuLabel>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem v-for="item in configStorageNavItems" :key="item.title" :as-child="true">
                                            <Link :href="item.href" class="flex items-center">
                                                <component v-if="item.icon" :is="item.icon" class="mr-2 h-4 w-4" />
                                                {{ item.title }}
                                            </Link>
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                                <div
                                    v-if="isDropdownActive(configStorageNavItems)"
                                    class="absolute bottom-0 left-0 h-0.5 w-full translate-y-px bg-black dark:bg-white"
                                ></div>
                            </NavigationMenuItem>

                            <!-- 网络管理下拉菜单 -->
                            <NavigationMenuItem class="relative flex h-full items-center">
                                <DropdownMenu>
                                    <DropdownMenuTrigger :as-child="true">
                                        <Button
                                            :class="[
                                                navigationMenuTriggerStyle(),
                                                getDropdownActiveStyles(networkNavItems),
                                                'h-9 cursor-pointer px-3',
                                            ]"
                                            variant="ghost"
                                        >
                                            <Globe class="mr-2 h-4 w-4" />
                                            网络
                                            <ChevronDown class="ml-1 h-3 w-3" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="start" class="w-48">
                                        <DropdownMenuLabel>网络与入口</DropdownMenuLabel>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem v-for="item in networkNavItems" :key="item.title" :as-child="true">
                                            <Link :href="item.href" class="flex items-center">
                                                <component v-if="item.icon" :is="item.icon" class="mr-2 h-4 w-4" />
                                                {{ item.title }}
                                            </Link>
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                                <div
                                    v-if="isDropdownActive(networkNavItems)"
                                    class="absolute bottom-0 left-0 h-0.5 w-full translate-y-px bg-black dark:bg-white"
                                ></div>
                            </NavigationMenuItem>
                        </NavigationMenuList>
                    </NavigationMenu>
                </div>

                <div class="ml-auto flex items-center space-x-2">
                    <div class="relative flex items-center space-x-1">
                        <Button variant="ghost" size="icon" class="group h-9 w-9 cursor-pointer">
                            <Search class="size-5 opacity-80 group-hover:opacity-100" />
                        </Button>
                        <Button variant="ghost" size="icon" class="group h-9 w-9 cursor-pointer">
                            <Bell class="size-5 opacity-80 group-hover:opacity-100" />
                        </Button>

                        <div class="hidden space-x-1 lg:flex">
                            <template v-for="item in rightNavItems" :key="item.title">
                                <TooltipProvider :delay-duration="0">
                                    <Tooltip>
                                        <TooltipTrigger>
                                            <Button variant="ghost" size="icon" as-child class="group h-9 w-9 cursor-pointer">
                                                <a :href="item.href" target="_blank" rel="noopener noreferrer">
                                                    <span class="sr-only">{{ item.title }}</span>
                                                    <component :is="item.icon" class="size-5 opacity-80 group-hover:opacity-100" />
                                                </a>
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>{{ item.title }}</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </template>
                        </div>
                    </div>

                    <DropdownMenu>
                        <DropdownMenuTrigger :as-child="true">
                            <Button
                                variant="ghost"
                                size="icon"
                                class="relative size-10 w-auto rounded-full p-1 focus-within:ring-2 focus-within:ring-primary"
                            >
                                <Avatar class="size-8 overflow-hidden rounded-full">
                                    <AvatarImage v-if="auth.user.avatar" :src="auth.user.avatar" :alt="auth.user.name" />
                                    <AvatarFallback class="rounded-lg bg-neutral-200 font-semibold text-black dark:bg-neutral-700 dark:text-white">
                                        {{ getInitials(auth.user?.name) }}
                                    </AvatarFallback>
                                </Avatar>
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" class="w-56">
                            <UserMenuContent :user="auth.user" />
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
        </div>

        <div v-if="props.breadcrumbs.length > 1" class="flex w-full border-b border-sidebar-border/70">
            <div class="mx-auto flex h-12 w-full items-center justify-start px-4 text-neutral-500 md:max-w-7xl">
                <Breadcrumbs :breadcrumbs="breadcrumbs" />
            </div>
        </div>
    </div>
</template>
