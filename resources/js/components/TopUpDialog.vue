<template>
    <Dialog>
        <DialogTrigger as-child>
            <Button>
                <Plus class="mr-2 h-4 w-4" />
                充值
            </Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-[425px]">
            <DialogHeader>
                <DialogTitle>账户充值</DialogTitle>
                <DialogDescription>选择支付方式并输入充值金额</DialogDescription>
            </DialogHeader>
            <form @submit.prevent="submit">
                <div class="grid gap-4 py-4">
                    <div class="grid grid-cols-4 items-center gap-4">
                        <Label for="payment_method" class="text-right">支付方式</Label>
                        <Select v-model="form.payment_method">
                            <SelectTrigger class="col-span-3">
                                <SelectValue placeholder="选择支付方式" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="method in paymentMethods" :key="method.id" :value="method.id">
                                    {{ method.name }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div v-if="form.errors.payment_method" class="col-span-4 text-sm text-red-500">
                        {{ form.errors.payment_method }}
                    </div>

                    <div class="grid grid-cols-4 items-center gap-4">
                        <Label for="amount" class="text-right">金额</Label>
                        <Input id="amount" v-model="form.amount" type="number" step="0.01" class="col-span-3" />
                    </div>
                    <div v-if="form.errors.amount" class="col-span-4 text-sm text-red-500">
                        {{ form.errors.amount }}
                    </div>

                    <p>目前正在测试阶段，不开放支付，完全免费，加Q群 439747955 获取兑换码。</p>
                </div>
                <DialogFooter>
                    <Button type="submit" :disabled="form.processing">
                        <Loader2 v-if="form.processing" class="mr-2 h-4 w-4 animate-spin" />
                        确认充值
                    </Button>
                </DialogFooter>
            </form>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { PaymentMethod } from '@/types';
import { useForm, usePage } from '@inertiajs/vue3';
import { Loader2, Plus } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed } from 'vue';

const page = usePage();

const paymentMethods = computed((): PaymentMethod[] => {
    return (page.props.paymentMethods as PaymentMethod[]) || [];
});

const form = useForm({
    amount: 0,
    payment_method: '',
});

const submit = () => {
    form.post(route('balance.top-up'), {
        preserveScroll: true,
        onSuccess: () => {
            toast.success('充值请求已提交');
            form.reset();
            // This will close the dialog if you add v-model:open
        },
        onError: () => {
            toast.error('充值失败，请检查输入');
        },
    });
};
</script>
