<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useSidebar } from '@/components/ui/sidebar';
import { useWorkspaceStore } from '@/stores/workspaceStore';
import { Link, router, usePage } from '@inertiajs/vue3';
import { Building2, Check, ChevronsUpDown, Plus } from 'lucide-vue-next';
import { computed, onMounted } from 'vue';

const page = usePage();
const workspaceStore = useWorkspaceStore();
const { state } = useSidebar();

// 判断 sidebar 是否折叠
const isCollapsed = computed(() => state.value === 'collapsed');

// 在组件挂载时获取工作空间列表（仅用于下拉菜单）
onMounted(() => {
    if (workspaceStore.currentWorkspace) {
        return;
    }

    // 只有在登录状态下才获取工作空间列表
    if (page.props.auth?.user) {
        workspaceStore.fetchWorkspaces();
    }
});

// 从 Inertia props 中获取当前工作空间
const currentWorkspace = computed(() => page.props.auth?.workspace || null);

// 从 store 中获取工作空间列表（用于下拉菜单）
const workspaces = computed(() => workspaceStore.workspaces);

// 获取首字符（英文则大写）
const getFirstChar = (str: string) => {
    if (!str) return '';
    const firstChar = str.charAt(0);
    // 判断是否为英文字符
    if (/[a-zA-Z]/.test(firstChar)) {
        return firstChar.toUpperCase();
    }
    return firstChar;
};

// 切换工作空间
const switchWorkspace = async (workspaceId: number) => {
    try {
        await workspaceStore.switchWorkspace(workspaceId);
        // 刷新页面以获取最新的工作空间状态
        router.visit(window.location.href);
    } catch (error) {
        console.error('切换工作空间失败:', error);
    }
};
</script>

<template>
    <DropdownMenu>
        <DropdownMenuTrigger as-child>
            <Button
                variant="ghost"
                class="relative left-[-7.5px] flex w-full items-center gap-2 data-[state=open]:bg-accent data-[state=open]:text-accent-foreground"
                :class="isCollapsed ? 'min-w-[48px] justify-center px-2 py-2' : 'justify-between px-3 py-6'"
            >
                <!-- 折叠状态：只显示图标 -->
                <template v-if="isCollapsed">
                    <template v-if="currentWorkspace">
                        <div class="flex h-6 w-6 items-center justify-center rounded-md text-sm font-medium">
                            {{ getFirstChar(currentWorkspace.name || '工作区') }}
                        </div>
                    </template>
                    <template v-else>
                        <Building2 class="size-5 text-muted-foreground" />
                    </template>
                </template>

                <!-- 展开状态：显示完整内容 -->
                <template v-else>
                    <div class="flex w-full items-center gap-2">
                        <template v-if="currentWorkspace">
                            <div class="flex h-7 w-7 items-center justify-center rounded-md text-sm font-medium">
                                {{ getFirstChar(currentWorkspace.name || '工作区') }}
                            </div>
                        </template>
                        <template v-else>
                            <Building2 class="size-5 text-muted-foreground" />
                        </template>
                        <div class="flex flex-col gap-0 leading-tight">
                            <span class="text-left text-sm font-medium" :class="!currentWorkspace ? 'text-muted-foreground' : ''">
                                {{ currentWorkspace?.name || '没有工作空间' }}
                            </span>
                            <span class="text-left text-xs text-muted-foreground">
                                {{ currentWorkspace?.namespace || '请创建工作空间' }}
                            </span>
                        </div>
                    </div>
                    <ChevronsUpDown class="ml-auto size-4" />
                </template>
            </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent class="min-w-56" align="start">
            <!-- 工作空间切换部分 -->
            <div class="p-2">
                <div class="mb-2 text-xs font-medium text-muted-foreground">
                    {{ workspaces.length > 0 ? '当前工作区' : '工作空间' }}
                </div>
                <div v-if="workspaces.length > 0" class="space-y-1">
                    <DropdownMenuItem v-for="workspace in workspaces" :key="workspace.id" @select="switchWorkspace(workspace.id)">
                        <div class="flex items-center">
                            <Building2 class="mr-2 size-4" />
                            <span>{{ workspace.name }}</span>
                            <span v-if="workspace.status !== 'active'" class="ml-2 text-xs text-muted-foreground">({{ workspace.status }})</span>
                        </div>
                        <Check v-if="currentWorkspace && currentWorkspace.id === workspace.id" class="ml-auto" />
                    </DropdownMenuItem>
                </div>
                <div v-else class="mb-2 px-2 py-3 text-center">
                    <Building2 class="mx-auto mb-2 size-8 text-muted-foreground/50" />
                    <div class="text-sm text-muted-foreground">没有可用的工作空间</div>
                    <div class="text-xs text-muted-foreground">请创建一个工作空间开始使用</div>
                </div>
                <DropdownMenuItem as-child>
                    <Link :href="route('workspaces.create')" class="w-full">
                        <div class="flex items-center">
                            <Plus class="mr-2 size-4" />
                            <span class="text-sm">{{ workspaces.length > 0 ? '创建新工作区' : '创建工作空间' }}</span>
                        </div>
                    </Link>
                </DropdownMenuItem>
            </div>
        </DropdownMenuContent>
    </DropdownMenu>
</template>
