<template>
    <section class="bg-muted/30 py-24 sm:py-32">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mx-auto max-w-4xl text-center">
                <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">{{ t('welcome.pricing.title') }}</h2>
                <p class="mt-4 text-lg text-muted-foreground">{{ t('welcome.pricing.subtitle') }}</p>

                <!-- 分页切换 -->
                <div class="mx-auto mt-8 flex w-fit rounded-lg border bg-background p-1 shadow-sm">
                    <button
                        :class="[
                            'rounded-md px-4 py-2 text-sm font-medium transition-all duration-200',
                            currentTab === 'docker' ? 'bg-primary text-primary-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground',
                        ]"
                        @click="currentTab = 'docker'"
                    >
                        Heroku Dyno 对比
                    </button>
                    <button
                        :class="[
                            'rounded-md px-4 py-2 text-sm font-medium transition-all duration-200',
                            currentTab === 'cloud' ? 'bg-primary text-primary-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground',
                        ]"
                        @click="currentTab = 'cloud'"
                    >
                        阿里云 ECS 对比
                    </button>
                </div>
            </div>

            <!-- 主要内容：价格计算器 + 对比 -->
            <div class="mx-auto mt-12 max-w-7xl">
                <div class="grid gap-8 lg:grid-cols-2">
                    <!-- 左侧：资源配置 -->
                    <div class="space-y-6">
                        <Card>
                            <CardContent class="p-6">
                                <h3 class="mb-6 text-lg font-semibold">{{ t('welcome.calculator.resourceConfig') }}</h3>

                                <!-- 集群选择 -->
                                <div class="mb-6">
                                    <label class="mb-2 block text-sm font-medium">{{ t('welcome.calculator.selectCluster') }}</label>
                                    <Select v-model="selectedClusterId" @update:model-value="onClusterChange">
                                        <SelectTrigger>
                                            <SelectValue placeholder="选择集群" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="cluster in availableClusters" :key="cluster.id" :value="cluster.id.toString()">
                                                <div class="flex items-center space-x-2">
                                                    <Server class="h-4 w-4" />
                                                    <span>{{ cluster.name }}</span>
                                                    <span class="text-xs text-muted-foreground">- {{ cluster.description || '高性能集群' }}</span>
                                                </div>
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div v-if="!selectedCluster" class="py-8 text-center text-sm text-muted-foreground">请先选择一个集群</div>

                                <!-- 内存配置 -->
                                <div v-if="selectedCluster">
                                    <div class="mb-6">
                                        <label class="mb-2 block text-sm font-medium">{{ t('welcome.calculator.memory') }}</label>
                                        <Input
                                            v-model.number="resources.memory"
                                            type="number"
                                            :min="512"
                                            :max="1048576"
                                            :step="512"
                                            placeholder="512"
                                            class="w-full"
                                        />
                                        <p class="mt-1 text-xs text-muted-foreground">{{ t('welcome.calculator.memoryNote') }}</p>
                                    </div>

                                    <!-- CPU配置 -->
                                    <div class="mb-6">
                                        <label class="mb-2 block text-sm font-medium">{{ t('welcome.calculator.cpu') }}</label>
                                        <Input
                                            v-model.number="resources.cpu"
                                            type="number"
                                            :min="500"
                                            :max="128000"
                                            :step="500"
                                            placeholder="500"
                                            class="w-full"
                                        />
                                        <p class="mt-1 text-xs text-muted-foreground">{{ t('welcome.calculator.cpuNote') }}</p>
                                    </div>

                                    <!-- 存储配置 -->
                                    <div class="mb-6">
                                        <label class="mb-2 block text-sm font-medium">{{ t('welcome.calculator.storage') }}</label>
                                        <Input
                                            v-model.number="resources.storage"
                                            type="number"
                                            :min="0"
                                            :max="10240"
                                            placeholder="0"
                                            class="w-full"
                                        />
                                        <p class="mt-1 text-xs text-muted-foreground">{{ t('welcome.calculator.storageNote') }}</p>
                                    </div>

                                    <!-- 负载均衡器配置 -->
                                    <div class="mb-6">
                                        <label class="mb-2 block text-sm font-medium">{{ t('welcome.calculator.loadBalancer') }}</label>
                                        <Input
                                            v-model.number="resources.loadbalancer"
                                            type="number"
                                            :min="0"
                                            :max="10"
                                            placeholder="0"
                                            class="w-full"
                                        />
                                        <p class="mt-1 text-xs text-muted-foreground">{{ t('welcome.calculator.loadBalancerNote') }}</p>
                                    </div>

                                    <!-- 预设配置 -->
                                    <div>
                                        <label class="mb-3 block text-sm font-medium">{{ t('welcome.calculator.quickConfig') }}</label>
                                        <div class="grid gap-2 sm:grid-cols-3">
                                            <Button variant="outline" size="sm" @click="applyPreset('light')" class="text-xs">
                                                {{ t('welcome.calculator.lightApp') }}
                                            </Button>
                                            <Button variant="outline" size="sm" @click="applyPreset('standard')" class="text-xs">
                                                {{ t('welcome.calculator.standardApp') }}
                                            </Button>
                                            <Button variant="outline" size="sm" @click="applyPreset('performance')" class="text-xs">
                                                {{ t('welcome.calculator.performanceApp') }}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    <!-- 右侧：费用预览和对比 -->
                    <div class="space-y-6">
                        <!-- 我们的价格预览 -->
                        <Card class="border-primary/20 bg-gradient-to-br from-primary/5 to-blue-500/5">
                            <CardContent class="p-6">
                                <div class="mb-4 text-center">
                                    <h3 class="mb-2 text-lg font-bold text-primary">{{ selectedCluster?.name || '智能云原生平台' }}</h3>
                                    <p class="text-sm text-muted-foreground">灵活选择配置</p>
                                </div>

                                <div v-if="isCalculating" class="flex items-center justify-center py-8">
                                    <LoaderCircle class="h-6 w-6 animate-spin text-primary" />
                                    <span class="ml-2 text-sm text-muted-foreground">{{ t('common.loading') }}</span>
                                </div>

                                <div v-else-if="priceResult && ourPlatformPrices" class="space-y-3">
                                    <!-- CPU 规格 -->
                                    <div class="flex items-center justify-between text-sm">
                                        <span>CPU 资源</span>
                                        <div class="text-right">
                                            <div class="font-semibold text-primary">{{ Math.round(resources.cpu / 1000) }}核心</div>
                                            <div class="text-xs text-primary">HK${{ ourPlatformPrices.cpu.minute.toFixed(4) }}/分钟</div>
                                        </div>
                                    </div>

                                    <!-- 内存规格 -->
                                    <div class="flex items-center justify-between text-sm">
                                        <span>内存资源</span>
                                        <div class="text-right">
                                            <div class="font-semibold text-primary">{{ resources.memory }}MB</div>
                                            <div class="text-xs text-primary">HK${{ ourPlatformPrices.memory.minute.toFixed(4) }}/分钟</div>
                                        </div>
                                    </div>

                                    <!-- 存储规格 -->
                                    <div v-if="resources.storage > 0" class="flex items-center justify-between text-sm">
                                        <span>存储空间</span>
                                        <div class="text-right">
                                            <div class="font-semibold text-primary">{{ resources.storage }}GB</div>
                                            <div class="text-xs text-primary">HK${{ ourPlatformPrices.storage.minute.toFixed(4) }}/分钟</div>
                                        </div>
                                    </div>

                                    <!-- 流量费用 -->
                                    <div class="flex items-center justify-between text-sm">
                                        <span>网络流量</span>
                                        <div class="text-right">
                                            <div class="font-semibold text-green-600">不限制流量</div>
                                            <div class="text-xs text-muted-foreground">公平共享使用</div>
                                        </div>
                                    </div>

                                    <hr class="border-primary/20" />

                                    <!-- 包月总计 -->
                                    <div class="flex items-center justify-between font-bold">
                                        <span>包月总计</span>
                                        <span class="text-lg text-primary">HK${{ priceResult.formatted.per_month.replace(/[^0-9.]/g, '') }}/月</span>
                                    </div>

                                    <!-- 按时总计 -->
                                    <div class="flex items-center justify-between font-bold">
                                        <span>按时总计</span>
                                        <span class="text-lg text-primary">HK${{ priceResult.formatted.per_hour.replace(/[^0-9.]/g, '') }}/小时</span>
                                    </div>

                                    <!-- 按分钟总计 -->
                                    <div class="flex items-center justify-between font-bold">
                                        <span>按分钟</span>
                                        <span class="text-lg text-primary">HK${{ ourPlatformPrices.total.minute.toFixed(4) }}/分钟</span>
                                    </div>

                                    <div class="mt-3 text-center text-xs text-muted-foreground">
                                        * 按分钟精确计费，流量不限制，公平共享使用，支持自动扩缩容
                                    </div>
                                </div>

                                <div v-else-if="calculationError" class="rounded-lg bg-destructive/10 p-4 text-center">
                                    <p class="text-sm text-destructive">{{ calculationError }}</p>
                                </div>

                                <div v-else class="py-8 text-center text-sm text-muted-foreground">{{ t('welcome.calculator.configFirst') }}</div>
                            </CardContent>
                        </Card>
                        <!-- Heroku Dyno 对比 -->
                        <div v-if="currentTab === 'docker'">
                            <!-- Heroku 竞品 -->
                            <Card class="mb-6 border-[#6567a5]/20 bg-gradient-to-br from-[#6567a5]/5 to-[#7f3ace]/5">
                                <CardContent class="p-6">
                                    <div class="mb-4 text-center">
                                        <h3 class="mb-2 text-lg font-bold text-[#6567a5]">Heroku Platform</h3>
                                        <p class="text-sm text-muted-foreground">基于您的配置自动匹配 Dyno 规格</p>
                                    </div>
                                    <div class="space-y-3">
                                        <div class="flex items-center justify-between text-sm">
                                            <div class="flex flex-col">
                                                <span>Dyno 实例</span>
                                                <span class="text-xs text-muted-foreground">
                                                    🎯 智能匹配：{{ herokuTotalPrice.spec.displayName }} - {{ herokuTotalPrice.spec.family }}
                                                </span>
                                            </div>
                                            <div class="text-right">
                                                <div class="text-xs text-muted-foreground">包月：HK${{ herokuTotalPrice.breakdown.dyno }}/月</div>
                                                <div class="text-xs text-[#6567a5]">按时：HK${{ herokuTotalPrice.breakdown.dynoHourly }}/小时</div>
                                            </div>
                                        </div>

                                        <!-- 负载均衡器（当用户选择了负载均衡器时显示） -->
                                        <div v-if="resources.loadbalancer > 0" class="flex items-center justify-between text-sm">
                                            <span>HTTP 负载均衡器 ({{ resources.loadbalancer }}个)</span>
                                            <span class="text-xs font-semibold text-green-600">免费</span>
                                        </div>

                                        <!-- 存储费用（当用户选择了存储时显示） -->
                                        <div v-if="resources.storage > 0" class="flex items-center justify-between text-sm">
                                            <div class="flex flex-col">
                                                <span>AWS S3 存储 ({{ resources.storage }}GB)</span>
                                                <span class="text-xs text-muted-foreground"> 每GB HK${{ (0.023 * USD_TO_HKD).toFixed(3) }} </span>
                                            </div>
                                            <div class="text-right">
                                                <div class="text-xs font-semibold">
                                                    包月：HK${{ Math.round(resources.storage * 0.023 * USD_TO_HKD) }}/月
                                                </div>
                                                <div class="text-xs font-semibold text-[#6567a5]">
                                                    按时：HK${{ ((resources.storage * 0.023 * USD_TO_HKD) / 30 / 24).toFixed(4) }}/小时
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 入站流量 -->
                                        <div class="flex items-center justify-between text-sm">
                                            <span>入站流量（上传）</span>
                                            <span class="text-xs font-semibold text-green-600">完全免费</span>
                                        </div>

                                        <!-- 出站流量 -->
                                        <div class="flex items-center justify-between text-sm">
                                            <div class="flex flex-col">
                                                <span>出站流量（下载）</span>
                                                <span class="text-xs text-muted-foreground">
                                                    包含2TB/月，超出HK${{ (0.05 * USD_TO_HKD).toFixed(2) }}/GB
                                                </span>
                                            </div>
                                            <div class="text-right">
                                                <div class="text-xs font-semibold text-green-600">2TB免费</div>
                                                <div class="text-xs font-semibold text-[#6567a5]">超出按量</div>
                                            </div>
                                        </div>

                                        <hr class="border-[#6567a5]/20" />

                                        <!-- 包月总计 -->
                                        <div class="flex items-center justify-between font-bold">
                                            <span>包月总计</span>
                                            <span class="text-[#6567a5]">{{ herokuTotalPrice.totalFormatted }}</span>
                                        </div>

                                        <!-- 按时总计 -->
                                        <div class="flex items-center justify-between font-bold">
                                            <span>按时总计</span>
                                            <span class="text-[#6567a5]">{{ herokuTotalPrice.hourlyFormatted }}</span>
                                        </div>

                                        <div class="text-center text-xs text-muted-foreground">
                                            * 基于 Heroku 官方定价的预估价，实际费用可能因使用量而异
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        <!-- 传统云计算对比 -->
                        <div v-if="currentTab === 'cloud'">
                            <!-- 阿里云服务器 -->
                            <Card class="mb-6 border-[#ff6a00]/20 bg-gradient-to-br from-[#ff6a00]/5 to-[#ff8c00]/5">
                                <CardContent class="p-6">
                                    <div class="mb-4 text-center">
                                        <h3 class="mb-2 text-lg font-bold text-[#ff6a00]">阿里云 ECS</h3>
                                        <p class="text-sm text-muted-foreground">基于您的配置自动匹配实例规格</p>
                                    </div>
                                    <div class="space-y-3">
                                        <div class="flex items-center justify-between text-sm">
                                            <span>ECS 实例规格</span>
                                            <div class="text-right">
                                                <button
                                                    @click="showSpecDetails = !showSpecDetails"
                                                    class="cursor-pointer border-b border-dashed border-[#ff6a00]/50 font-semibold text-[#ff6a00] transition-colors hover:border-[#ff6a00] hover:text-[#ff8c00]"
                                                >
                                                    {{ aliyunTotalPrice.spec.displayName }}
                                                </button>
                                                <div class="text-xs text-muted-foreground">包月：HK${{ aliyunTotalPrice.breakdown.server }}/月</div>
                                                <div class="text-xs text-[#ff6a00]">按时：HK${{ aliyunTotalPrice.breakdown.serverHourly }}/小时</div>
                                            </div>
                                        </div>

                                        <div class="flex items-center justify-between text-sm">
                                            <span>ESSD 云盘存储 ({{ resources.storage }}GB)</span>
                                            <div class="text-right">
                                                <div class="text-xs font-semibold">
                                                    {{ resources.storage > 0 ? `HK$${aliyunTotalPrice.breakdown.storage}/月` : '未使用' }}
                                                </div>
                                                <div v-if="resources.storage > 0" class="text-xs font-semibold text-[#ff6a00]">
                                                    HK${{ aliyunTotalPrice.breakdown.storageHourly }}/小时
                                                </div>
                                            </div>
                                        </div>

                                        <hr class="border-[#ff6a00]/20" />

                                        <!-- 包月总计 -->
                                        <div class="flex items-center justify-between font-bold">
                                            <span>包月总计</span>
                                            <span class="text-[#ff6a00]">{{ cloudTotalPrice }}</span>
                                        </div>

                                        <!-- 按时总计 -->
                                        <div class="flex items-center justify-between font-bold">
                                            <span>按时总计</span>
                                            <span class="text-[#ff6a00]">{{ cloudHourlyPrice }}</span>
                                        </div>

                                        <!-- 按量付费项目 -->
                                        <div class="mt-3 border-t border-[#ff6a00]/10 pt-3">
                                            <div class="mb-2 text-xs text-muted-foreground">按量付费项目</div>
                                            <div class="flex items-center justify-between text-sm text-muted-foreground">
                                                <span>公网带宽费用</span>
                                                <span>按实际流量计费</span>
                                            </div>
                                        </div>

                                        <div class="text-center text-xs text-muted-foreground">
                                            * 基于阿里云官方定价的预估价，实际费用可能因使用量而异
                                        </div>

                                        <!-- 智能规格匹配详情 -->
                                        <div v-if="showSpecDetails" class="mt-4 border-t border-destructive/20 pt-4">
                                            <div class="rounded-lg bg-orange-50 p-4 dark:bg-orange-950/20">
                                                <h4 class="mb-3 flex items-center text-sm font-bold text-orange-700 dark:text-orange-300">
                                                    🎯 智能规格匹配分析
                                                    <span class="ml-2 rounded bg-orange-200 px-2 py-0.5 text-xs dark:bg-orange-800"
                                                        >为什么传统云更贵？</span
                                                    >
                                                </h4>
                                                <div class="space-y-2 text-sm">
                                                    <div class="flex justify-between">
                                                        <span class="text-orange-600 dark:text-orange-400">您的实际需求</span>
                                                        <span class="font-medium"
                                                            >{{ Math.round(resources.cpu / 1000) }}核 {{ resources.memory }}MB</span
                                                        >
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span class="text-orange-600 dark:text-orange-400">阿里云强制规格</span>
                                                        <span class="font-medium"
                                                            >{{ aliyunTotalPrice.spec.cpu }}核
                                                            {{ Math.round(aliyunTotalPrice.spec.memory / 1024) }}GB</span
                                                        >
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span class="text-orange-600 dark:text-orange-400">资源浪费率</span>
                                                        <span class="font-semibold text-orange-700 dark:text-orange-300">
                                                            {{ calculateWastePercentage() }}%
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="mt-3 rounded border-l-4 border-red-400 bg-red-100 p-3 dark:bg-red-950/30">
                                                    <p class="text-xs text-red-700 dark:text-red-300">
                                                        💡 <strong>传统云的痛点：</strong>您只需要 {{ Math.round(resources.cpu / 1000) }}核{{
                                                            resources.memory
                                                        }}MB， 但阿里云强制您购买 {{ aliyunTotalPrice.spec.cpu }}核{{
                                                            Math.round(aliyunTotalPrice.spec.memory / 1024)
                                                        }}GB， 多花钱买用不上的资源！我们的平台按需分配，杜绝资源浪费。
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useI18n } from '@/composables/useI18n';
import axios from '@/lib/axios';
import type { PriceCalculationResponse } from '@/types';
import { LoaderCircle, Server } from 'lucide-vue-next';
import { computed, onMounted, ref, watch } from 'vue';

const { t } = useI18n();

// 当前选中的标签页
const currentTab = ref<'docker' | 'cloud'>('docker');

// 价格计算器相关状态
const availableClusters = ref<Array<{ id: number; name: string; description?: string }>>([]);
const selectedCluster = ref<{ id: number; name: string; description?: string } | null>(null);
const selectedClusterId = ref<string>('');
const resources = ref({
    memory: 1024,
    cpu: 1000,
    storage: 10,
    loadbalancer: 0,
});

const isCalculating = ref(false);
const calculationError = ref('');
const priceResult = ref<PriceCalculationResponse | null>(null);
const showSpecDetails = ref(false);

// Heroku Dyno 规格配置（美元价格，将转换为港币）
const herokuSpecs = {
    // Classic 系列
    'dyno-1c-0.5gb': {
        family: 'Classic',
        cpu: 1,
        memory: 512, // 0.5GB = 512MB
        pricePerHour: 0.035,
        maxPricePerMonth: 25,
        displayName: 'Classic (1核0.5G)',
    },
    'dyno-2c-1gb': {
        family: 'Classic',
        cpu: 2,
        memory: 1024, // 1GB = 1024MB
        pricePerHour: 0.07,
        maxPricePerMonth: 50,
        displayName: 'Classic (2核1G)',
    },

    // General Purpose 系列
    'dyno-1c-4gb': {
        family: 'General Purpose',
        cpu: 1,
        memory: 4096, // 4GB = 4096MB
        pricePerHour: 0.11,
        maxPricePerMonth: 80,
        displayName: 'General Purpose (1核4G)',
    },
    'dyno-2c-8gb': {
        family: 'General Purpose',
        cpu: 2,
        memory: 8192, // 8GB = 8192MB
        pricePerHour: 0.22,
        maxPricePerMonth: 160,
        displayName: 'General Purpose (2核8G)',
    },
    'dyno-4c-16gb': {
        family: 'General Purpose',
        cpu: 4,
        memory: 16384, // 16GB = 16384MB
        pricePerHour: 0.44,
        maxPricePerMonth: 320,
        displayName: 'General Purpose (4核16G)',
    },
    'dyno-8c-32gb': {
        family: 'General Purpose',
        cpu: 8,
        memory: 32768, // 32GB = 32768MB
        pricePerHour: 0.88,
        maxPricePerMonth: 640,
        displayName: 'General Purpose (8核32G)',
    },
    'dyno-16c-64gb': {
        family: 'General Purpose',
        cpu: 16,
        memory: 65536, // 64GB = 65536MB
        pricePerHour: 1.39,
        maxPricePerMonth: 1000,
        displayName: 'General Purpose (16核64G)',
    },

    // Compute 系列
    'dyno-2c-4gb': {
        family: 'Compute',
        cpu: 2,
        memory: 4096, // 4GB = 4096MB
        pricePerHour: 0.21,
        maxPricePerMonth: 150,
        displayName: 'Compute (2核4G)',
    },
    'dyno-4c-8gb': {
        family: 'Compute',
        cpu: 4,
        memory: 8192, // 8GB = 8192MB
        pricePerHour: 0.42,
        maxPricePerMonth: 300,
        displayName: 'Compute (4核8G)',
    },
    'dyno-8c-16gb': {
        family: 'Compute',
        cpu: 8,
        memory: 16384, // 16GB = 16384MB
        pricePerHour: 0.83,
        maxPricePerMonth: 600,
        displayName: 'Compute (8核16G)',
    },
    'dyno-16c-32gb': {
        family: 'Compute',
        cpu: 16,
        memory: 32768, // 32GB = 32768MB
        pricePerHour: 1.67,
        maxPricePerMonth: 1200,
        displayName: 'Compute (16核32G)',
    },
    'dyno-32c-64gb': {
        family: 'Compute',
        cpu: 32,
        memory: 65536, // 64GB = 65536MB
        pricePerHour: 3.33,
        maxPricePerMonth: 2400,
        displayName: 'Compute (32核64G)',
    },

    // Memory 系列
    'dyno-1c-8gb': {
        family: 'Memory',
        cpu: 1,
        memory: 8192, // 8GB = 8192MB
        pricePerHour: 0.14,
        maxPricePerMonth: 100,
        displayName: 'Memory (1核8G)',
    },
    'dyno-2c-16gb': {
        family: 'Memory',
        cpu: 2,
        memory: 16384, // 16GB = 16384MB
        pricePerHour: 0.35,
        maxPricePerMonth: 250,
        displayName: 'Memory (2核16G)',
    },
    'dyno-4c-32gb': {
        family: 'Memory',
        cpu: 4,
        memory: 32768, // 32GB = 32768MB
        pricePerHour: 0.69,
        maxPricePerMonth: 500,
        displayName: 'Memory (4核32G)',
    },
    'dyno-8c-64gb': {
        family: 'Memory',
        cpu: 8,
        memory: 65536, // 64GB = 65536MB
        pricePerHour: 1.04,
        maxPricePerMonth: 750,
        displayName: 'Memory (8核64G)',
    },
    'dyno-16c-128gb': {
        family: 'Memory',
        cpu: 16,
        memory: 131072, // 128GB = 131072MB
        pricePerHour: 2.08,
        maxPricePerMonth: 1500,
        displayName: 'Memory (16核128G)',
    },
};

// Heroku 其他服务价格（美元）
const herokuServicePrices = {
    // 暂无其他服务
};

// 美元转港币汇率
const USD_TO_HKD = 7.8;

// 竞品价格（港币/月）- 用于兼容旧代码
const competitorPrices = {
    // Heroku 价格（兼容旧代码）
    herokuDyno: 195, // Standard-1X Dyno
    herokuMonitoring: 390, // Metrics Monitoring
};

// 阿里云实例规格配置（突发性能型 T5 + 经济型 e）
const aliyunSpecs = {
    // 突发性能型 T5 系列
    'ecs.t5-lc2m1.nano': { cpu: 1, memory: 512, pricePerMonth: Math.round(14.4 * 1.0955) }, // HK$16
    'ecs.t5-lc1m1.small': { cpu: 1, memory: 1024, pricePerMonth: Math.round(21.6 * 1.0955) }, // HK$24
    'ecs.t5-lc1m2.small': { cpu: 1, memory: 2048, pricePerMonth: Math.round(28.8 * 1.0955) }, // HK$32
    'ecs.t5-lc1m2.large': { cpu: 2, memory: 4096, pricePerMonth: Math.round(57.6 * 1.0955) }, // HK$63
    'ecs.t5-c1m1.large': { cpu: 2, memory: 2048, pricePerMonth: Math.round(72.0 * 1.0955) }, // HK$79
    'ecs.t5-c1m2.large': { cpu: 2, memory: 4096, pricePerMonth: Math.round(108.0 * 1.0955) }, // HK$118
    'ecs.t5-c1m4.large': { cpu: 2, memory: 8192, pricePerMonth: Math.round(180.0 * 1.0955) }, // HK$197
    'ecs.t5-c1m1.xlarge': { cpu: 4, memory: 4096, pricePerMonth: Math.round(216.0 * 1.0955) }, // HK$237
    'ecs.t5-c1m2.xlarge': { cpu: 4, memory: 8192, pricePerMonth: Math.round(324.0 * 1.0955) }, // HK$355
    'ecs.t5-c1m4.xlarge': { cpu: 4, memory: 16384, pricePerMonth: 591 }, // HK$591

    // 经济型 e 系列
    'ecs.e-c4m1.large': { cpu: 2, memory: 512, pricePerMonth: Math.round(15 * 1.0955) }, // HK$16
    'ecs.e-c2m1.large': { cpu: 2, memory: 1024, pricePerMonth: Math.round(20 * 1.0955) }, // HK$22
    'ecs.e-c1m1.large': { cpu: 2, memory: 2048, pricePerMonth: Math.round(30 * 1.0955) }, // HK$33
    'ecs.e-c1m2.large': { cpu: 2, memory: 4096, pricePerMonth: Math.round(60 * 1.0955) }, // HK$66
    'ecs.e-c1m4.large': { cpu: 2, memory: 8192, pricePerMonth: Math.round(100 * 1.0955) }, // HK$110
    'ecs.e-c1m2.xlarge': { cpu: 4, memory: 8192, pricePerMonth: Math.round(180 * 1.0955) }, // HK$197
    'ecs.e-c1m4.xlarge': { cpu: 4, memory: 16384, pricePerMonth: Math.round(300 * 1.0955) }, // HK$329
    'ecs.e-c1m2.2xlarge': { cpu: 8, memory: 16384, pricePerMonth: Math.round(500 * 1.0955) }, // HK$548
    'ecs.e-c1m4.2xlarge': { cpu: 8, memory: 32768, pricePerMonth: Math.round(800 * 1.0955) }, // HK$876

    // 通用型 G8i 系列
    'ecs.g8i.large': { cpu: 2, memory: 8192, pricePerMonth: Math.round(72 * 1.0955) }, // HK$79
    'ecs.g8i.xlarge': { cpu: 4, memory: 16384, pricePerMonth: Math.round(144 * 1.0955) }, // HK$158
    'ecs.g8i.2xlarge': { cpu: 8, memory: 32768, pricePerMonth: Math.round(288 * 1.0955) }, // HK$315
    'ecs.g8i.3xlarge': { cpu: 12, memory: 49152, pricePerMonth: Math.round(432 * 1.0955) }, // HK$473
    'ecs.g8i.4xlarge': { cpu: 16, memory: 65536, pricePerMonth: Math.round(576 * 1.0955) }, // HK$631
    'ecs.g8i.6xlarge': { cpu: 24, memory: 98304, pricePerMonth: Math.round(864 * 1.0955) }, // HK$946
    'ecs.g8i.8xlarge': { cpu: 32, memory: 131072, pricePerMonth: Math.round(1152 * 1.0955) }, // HK$1262
    'ecs.g8i.12xlarge': { cpu: 48, memory: 196608, pricePerMonth: Math.round(1728 * 1.0955) }, // HK$1893
    'ecs.g8i.16xlarge': { cpu: 64, memory: 262144, pricePerMonth: Math.round(2304 * 1.0955) }, // HK$2524
    'ecs.g8i.24xlarge': { cpu: 96, memory: 393216, pricePerMonth: Math.round(3456 * 1.0955) }, // HK$3786
    'ecs.g8i.48xlarge': { cpu: 192, memory: 1048576, pricePerMonth: Math.round(6912 * 1.0955) }, // HK$7572
};

// 阿里云其他服务价格
// 阿里云SLB规格数据
const aliyunSlbSpecs = {
    'slb.s1.small': {
        name: '简约型I',
        maxConnections: 5000,
        cps: 3000,
        qps: 1000,
        pricePerHour: 0.12,
        pricePerMonth: Math.round(0.12 * 24 * 30 * 1.0955), // 换算成港币/月
    },
    'slb.s2.small': {
        name: '标准型I',
        maxConnections: 50000,
        cps: 5000,
        qps: 5000,
        pricePerHour: 0.38,
        pricePerMonth: Math.round(0.38 * 24 * 30 * 1.0955), // 换算成港币/月
    },
    'slb.s2.medium': {
        name: '标准型II',
        maxConnections: 100000,
        cps: 10000,
        qps: 10000,
        pricePerHour: 0.76,
        pricePerMonth: Math.round(0.76 * 24 * 30 * 1.0955), // 换算成港币/月
    },
    'slb.s3.small': {
        name: '高阶型I',
        maxConnections: 200000,
        cps: 20000,
        qps: 20000,
        pricePerHour: 1.52,
        pricePerMonth: Math.round(1.52 * 24 * 30 * 1.0955), // 换算成港币/月
    },
    'slb.s3.medium': {
        name: '高阶型II',
        maxConnections: 500000,
        cps: 50000,
        qps: 30000,
        pricePerHour: 2.0, // 估算价格，因为原数据未提供
        pricePerMonth: Math.round(2.0 * 24 * 30 * 1.0955), // 换算成港币/月
    },
    'slb.s3.large': {
        name: '超强型I',
        maxConnections: 1000000,
        cps: 100000,
        qps: 50000,
        pricePerHour: 3.0, // 估算价格，因为原数据未提供
        pricePerMonth: Math.round(3.0 * 24 * 30 * 1.0955), // 换算成港币/月
    },
};

const aliyunServicePrices = {
    storagePerGB: 1.32, // 云盘存储每GB (¥1.2038 换算成港币 = HK$1.318)
    bandwidth: 2.5, // 带宽每Mbps (按流量计费约等价)
};

// 自动匹配Heroku实例规格
const matchHerokuSpec = (memory: number, cpu: number) => {
    // 将内存从Mi转换为MB，CPU从m转换为核心数
    const memoryMB = memory;
    const cpuCores = cpu / 1000;

    // 找到最接近的实例规格
    let bestMatch = null;
    let bestScore = Infinity;

    for (const [specName, spec] of Object.entries(herokuSpecs)) {
        // 只考虑满足最低要求的规格
        if (spec.cpu >= cpuCores && spec.memory >= memoryMB) {
            // 计算匹配得分 (资源越接近用户需求，得分越低)
            const cpuScore = Math.abs(spec.cpu - cpuCores) / cpuCores;
            const memoryScore = Math.abs(spec.memory - memoryMB) / memoryMB;
            const totalScore = cpuScore + memoryScore;

            if (totalScore < bestScore) {
                bestScore = totalScore;
                bestMatch = {
                    name: specName,
                    ...spec,
                };
            }
        }
    }

    // 如果没有找到匹配的规格，返回最高配置
    if (!bestMatch) {
        const highestSpec = Object.entries(herokuSpecs).sort((a, b) => b[1].maxPricePerMonth - a[1].maxPricePerMonth)[0];
        bestMatch = {
            name: highestSpec[0],
            ...highestSpec[1],
        };
    }

    return bestMatch;
};

// 自动匹配阿里云实例规格
const matchAliyunSpec = (memory: number, cpu: number) => {
    // 将内存从Mi转换为MB，CPU从m转换为核心数
    const memoryMB = memory;
    const cpuCores = cpu / 1000;

    // 找到最接近的实例规格
    let bestMatch = null;
    let bestScore = Infinity;

    for (const [specName, spec] of Object.entries(aliyunSpecs)) {
        // 只考虑满足最低要求的规格
        if (spec.cpu >= cpuCores && spec.memory >= memoryMB) {
            // 计算匹配得分 (资源越接近用户需求，得分越低)
            const cpuScore = Math.abs(spec.cpu - cpuCores) / cpuCores;
            const memoryScore = Math.abs(spec.memory - memoryMB) / memoryMB;
            const totalScore = cpuScore + memoryScore;

            if (totalScore < bestScore) {
                bestScore = totalScore;
                bestMatch = {
                    name: specName,
                    ...spec,
                    displayName: getSpecDisplayName(specName),
                };
            }
        }
    }

    // 如果没有找到匹配的规格，返回最高配置
    if (!bestMatch) {
        const highestSpec = Object.entries(aliyunSpecs).sort((a, b) => b[1].pricePerMonth - a[1].pricePerMonth)[0];
        bestMatch = {
            name: highestSpec[0],
            ...highestSpec[1],
            displayName: getSpecDisplayName(highestSpec[0]),
        };
    }

    return bestMatch;
};

// 获取规格的显示名称
const getSpecDisplayName = (specName: string) => {
    const specMap: Record<string, string> = {
        // 突发性能型 T5 系列
        'ecs.t5-lc2m1.nano': '突发型 t5 (1核0.5G)',
        'ecs.t5-lc1m1.small': '突发型 t5 (1核1G)',
        'ecs.t5-lc1m2.small': '突发型 t5 (1核2G)',
        'ecs.t5-lc1m2.large': '突发型 t5 (2核4G)',
        'ecs.t5-c1m1.large': '突发型 t5 (2核2G)',
        'ecs.t5-c1m2.large': '突发型 t5 (2核4G)',
        'ecs.t5-c1m4.large': '突发型 t5 (2核8G)',
        'ecs.t5-c1m1.xlarge': '突发型 t5 (4核4G)',
        'ecs.t5-c1m2.xlarge': '突发型 t5 (4核8G)',
        'ecs.t5-c1m4.xlarge': '突发型 t5 (4核16G)',

        // 经济型 e 系列
        'ecs.e-c4m1.large': '经济型 e (2核0.5G)',
        'ecs.e-c2m1.large': '经济型 e (2核1G)',
        'ecs.e-c1m1.large': '经济型 e (2核2G)',
        'ecs.e-c1m2.large': '经济型 e (2核4G)',
        'ecs.e-c1m4.large': '经济型 e (2核8G)',
        'ecs.e-c1m2.xlarge': '经济型 e (4核8G)',
        'ecs.e-c1m4.xlarge': '经济型 e (4核16G)',
        'ecs.e-c1m2.2xlarge': '经济型 e (8核16G)',
        'ecs.e-c1m4.2xlarge': '经济型 e (8核32G)',

        // 通用型 G8i 系列
        'ecs.g8i.large': '通用型 g8i (2核8G)',
        'ecs.g8i.xlarge': '通用型 g8i (4核16G)',
        'ecs.g8i.2xlarge': '通用型 g8i (8核32G)',
        'ecs.g8i.3xlarge': '通用型 g8i (12核48G)',
        'ecs.g8i.4xlarge': '通用型 g8i (16核64G)',
        'ecs.g8i.6xlarge': '通用型 g8i (24核96G)',
        'ecs.g8i.8xlarge': '通用型 g8i (32核128G)',
        'ecs.g8i.12xlarge': '通用型 g8i (48核192G)',
        'ecs.g8i.16xlarge': '通用型 g8i (64核256G)',
        'ecs.g8i.24xlarge': '通用型 g8i (96核384G)',
        'ecs.g8i.48xlarge': '通用型 g8i (192核1024G)',
    };
    return specMap[specName] || specName;
};

// 智能匹配阿里云SLB规格
const matchAliyunSlb = (ecsSpec: any) => {
    const allSlbSpecs = Object.entries(aliyunSlbSpecs);

    // 根据ECS规格的CPU核心数估算所需的连接数
    const estimatedConnections = ecsSpec.cpu * 1000; // 每核心估算1000连接
    const estimatedQps = ecsSpec.cpu * 500; // 每核心估算500 QPS

    // 过滤出满足需求的SLB规格
    const suitableSlbs = allSlbSpecs.filter(([_, slb]) => slb.maxConnections >= estimatedConnections && slb.qps >= estimatedQps);

    if (suitableSlbs.length === 0) {
        // 如果没有合适的规格，返回最强的规格（最大连接数的）
        return allSlbSpecs.reduce((max, current) => (current[1].maxConnections > max[1].maxConnections ? current : max))[1];
    }

    // 在合适的规格中选择最便宜的
    return suitableSlbs.reduce((min, current) => (current[1].pricePerMonth < min[1].pricePerMonth ? current : min))[1];
};

// 计算阿里云总价格
const aliyunTotalPrice = computed(() => {
    const matchedSpec = matchAliyunSpec(resources.value.memory, resources.value.cpu);

    let total = matchedSpec.pricePerMonth;
    let slbSpec = null;

    // 只有当用户选择了负载均衡器时才添加SLB费用
    if (resources.value.loadbalancer > 0) {
        slbSpec = matchAliyunSlb(matchedSpec);
        total += slbSpec.pricePerMonth * resources.value.loadbalancer;
    }

    // 添加存储费用
    if (resources.value.storage > 0) {
        total += resources.value.storage * aliyunServicePrices.storagePerGB;
    }

    // 计算按小时费用（包月价格 * 1.5 / 30天 / 24小时）
    const monthlyToHourlyRate = 1.5 / (30 * 24); // 按小时比包月贵50%
    const serverHourly = Number((matchedSpec.pricePerMonth * monthlyToHourlyRate).toFixed(2));
    const storageHourly =
        resources.value.storage > 0 ? Number((resources.value.storage * aliyunServicePrices.storagePerGB * monthlyToHourlyRate).toFixed(2)) : 0;

    return {
        total: Math.round(total),
        spec: matchedSpec,
        slb: slbSpec,
        breakdown: {
            server: matchedSpec.pricePerMonth,
            storage: resources.value.storage > 0 ? Math.round(resources.value.storage * aliyunServicePrices.storagePerGB) : 0,
            serverHourly: serverHourly,
            storageHourly: storageHourly,
        },
    };
});

// 获取集群数据
const fetchClusters = async () => {
    try {
        const response = await axios.get('/api/clusters');
        const clusters = response.data.filter((cluster: any) => cluster.billing_enabled);
        availableClusters.value = clusters;
        if (clusters.length > 0) {
            selectedCluster.value = clusters[0];
            selectedClusterId.value = clusters[0].id.toString();
        }
    } catch (error) {
        console.error('Failed to fetch clusters:', error);
    }
};

// 选择集群
const selectCluster = (cluster: { id: number; name: string; description?: string }) => {
    selectedCluster.value = cluster;
    selectedClusterId.value = cluster.id.toString();
    calculatePrice();
};

// 处理下拉菜单变化
const onClusterChange = (value: any) => {
    if (!value) return;
    const clusterId = value.toString();
    const cluster = availableClusters.value.find((c) => c.id.toString() === clusterId);
    if (cluster) {
        selectedCluster.value = cluster;
        calculatePrice();
    }
};

// 计算价格
const calculatePrice = async () => {
    if (!selectedCluster.value) return;

    // 检查是否有资源配置
    if (resources.value.memory === 0 && resources.value.cpu === 0 && resources.value.storage === 0 && resources.value.loadbalancer === 0) {
        priceResult.value = null;
        return;
    }

    isCalculating.value = true;
    calculationError.value = '';

    try {
        const response = await axios.post(`/api/pricing/cluster/${selectedCluster.value.id}/calculate`, {
            usage: {
                memory_gb: resources.value.memory / 1024, // 转换 MB 到 GB
                cpu_core: resources.value.cpu / 1000, // 转换 m 到 core
                storage_gb: resources.value.storage,
            },
            minutes: 1,
        });
        priceResult.value = response.data;
    } catch (error: any) {
        calculationError.value = error.response?.data?.message || '计算失败，请稍后重试';
        priceResult.value = null;
    } finally {
        isCalculating.value = false;
    }
};

// 防抖计算价格
let calculatePriceTimeout: number;
const debouncedCalculatePrice = () => {
    clearTimeout(calculatePriceTimeout);
    calculatePriceTimeout = setTimeout(calculatePrice, 500);
};

// 监听资源变化并验证上限
watch(
    resources,
    (newResources) => {
        // 验证CPU上限 (128核 = 128000m)
        if (newResources.cpu > 128000) {
            newResources.cpu = 128000;
        }

        // 验证内存上限 (1TB = 1048576MB)
        if (newResources.memory > 1048576) {
            newResources.memory = 1048576;
        }

        debouncedCalculatePrice();
    },
    { deep: true },
);

// 预设配置
const applyPreset = (type: 'light' | 'standard' | 'performance') => {
    const presets = {
        light: { memory: 512, cpu: 500, storage: 0, loadbalancer: 0 },
        standard: { memory: 1024, cpu: 1000, storage: 10, loadbalancer: 0 },
        performance: { memory: 2048, cpu: 2000, storage: 50, loadbalancer: 1 },
    };
    resources.value = { ...presets[type] };
};

// 格式化竞品价格
const formatCompetitorPrice = (priceKey: keyof typeof competitorPrices) => {
    return `HK$${competitorPrices[priceKey]}/月`;
};

// Heroku 智能匹配总价格
const herokuTotalPrice = computed(() => {
    const matchedSpec = matchHerokuSpec(resources.value.memory, resources.value.cpu);

    // 计算总价（美元）
    const dynoPrice = matchedSpec.maxPricePerMonth;

    const totalUSD = dynoPrice;
    const totalHKD = Math.round(totalUSD * USD_TO_HKD);

    // 计算小时价格
    const hourlyUSD = matchedSpec.pricePerHour;
    const hourlyHKD = (hourlyUSD * USD_TO_HKD).toFixed(2);

    return {
        total: totalHKD,
        totalFormatted: `HK$${totalHKD}/月`,
        hourly: parseFloat(hourlyHKD),
        hourlyFormatted: `HK$${hourlyHKD}/小时`,
        spec: matchedSpec,
        breakdown: {
            dyno: Math.round(dynoPrice * USD_TO_HKD),
            dynoHourly: (matchedSpec.pricePerHour * USD_TO_HKD).toFixed(2),
        },
    };
});

// Heroku 兼容旧代码的总价格
const herokuTotalPriceOld = computed(() => {
    return herokuTotalPrice.value.totalFormatted;
});

// 传统云总价格
const cloudTotalPrice = computed(() => {
    return `HK$${aliyunTotalPrice.value.total}/月`;
});

// 传统云按小时价格
const cloudHourlyPrice = computed(() => {
    const totalHourly = aliyunTotalPrice.value.breakdown.serverHourly + aliyunTotalPrice.value.breakdown.storageHourly;
    return `HK$${totalHourly.toFixed(2)}/小时`;
});

// 我们平台的按分钟价格计算
const ourPlatformPrices = computed(() => {
    if (!priceResult.value) return null;

    // 从小时价格计算分钟价格
    const hourlyPriceNumber = parseFloat(priceResult.value.formatted.per_hour.replace(/[^0-9.]/g, ''));
    const monthlyPriceNumber = parseFloat(priceResult.value.formatted.per_month.replace(/[^0-9.]/g, ''));

    // 分钟价格 = 小时价格 / 60
    const minutePrice = hourlyPriceNumber / 60;

    // 根据资源分配计算每个组件的分钟价格（简化估算）
    const totalResources = resources.value.cpu + resources.value.memory + resources.value.storage * 100 + resources.value.loadbalancer * 1000;
    const cpuRatio = totalResources > 0 ? resources.value.cpu / totalResources : 0;
    const memoryRatio = totalResources > 0 ? resources.value.memory / totalResources : 0;
    const storageRatio = totalResources > 0 ? (resources.value.storage * 100) / totalResources : 0;
    const loadbalancerRatio = totalResources > 0 ? (resources.value.loadbalancer * 1000) / totalResources : 0;

    return {
        total: {
            minute: minutePrice,
            hour: hourlyPriceNumber,
            month: monthlyPriceNumber,
        },
        cpu: {
            minute: minutePrice * cpuRatio,
        },
        memory: {
            minute: minutePrice * memoryRatio,
        },
        storage: {
            minute: resources.value.storage > 0 ? minutePrice * storageRatio : 0,
        },
    };
});

// 计算资源浪费率
const calculateWastePercentage = () => {
    const userCpu = resources.value.cpu / 1000; // 转换为核心数
    const userMemory = resources.value.memory; // MB
    const aliyunCpu = aliyunTotalPrice.value.spec.cpu;
    const aliyunMemory = aliyunTotalPrice.value.spec.memory; // MB

    // 计算CPU和内存的浪费率
    const cpuWaste = ((aliyunCpu - userCpu) / aliyunCpu) * 100;
    const memoryWaste = ((aliyunMemory - userMemory) / aliyunMemory) * 100;

    // 取平均值
    const averageWaste = (cpuWaste + memoryWaste) / 2;

    return Math.max(0, Math.round(averageWaste));
};

onMounted(() => {
    fetchClusters();
});
</script>
