<template>
    <div class="space-y-6">
        <div v-if="loading" class="space-y-4">
            <Card>
                <CardContent class="p-6">
                    <div class="space-y-4">
                        <Skeleton class="h-6 w-1/3" />
                        <Skeleton class="h-4 w-full" />
                        <Skeleton class="h-4 w-2/3" />
                    </div>
                </CardContent>
            </Card>
        </div>

        <div v-else-if="error" class="text-center">
            <Card class="border-red-200 dark:border-red-800">
                <CardContent class="p-6">
                    <AlertCircle class="mx-auto mb-4 h-12 w-12 text-red-500" />
                    <h3 class="text-lg font-medium text-red-600 dark:text-red-400">加载失败</h3>
                    <p class="mt-2 text-sm text-red-500">{{ error }}</p>
                    <Button class="mt-4" @click="retryLoad">重试</Button>
                </CardContent>
            </Card>
        </div>

        <div v-else-if="workload" class="space-y-6">
            <!-- 基本信息卡片 -->
            <Card>
                <CardHeader>
                    <div class="flex items-center justify-between">
                        <CardTitle class="flex items-center gap-2">
                            <Component v-if="workloadType === 'deployment'" class="h-5 w-5" />
                            <Database v-else class="h-5 w-5" />
                            基本信息
                        </CardTitle>
                        <Badge :variant="getStatusVariant(workload.status)">
                            {{ formatK8sStatus(workload.status) }}
                        </Badge>
                    </div>
                </CardHeader>
                <CardContent class="grid grid-cols-2 gap-4 md:grid-cols-4">
                    <div>
                        <Label class="text-sm font-medium text-muted-foreground">副本数</Label>
                        <p class="text-lg font-semibold">{{ workload.ready_replicas ?? 0 }} / {{ workload.replicas }}</p>
                    </div>
                    <div>
                        <Label class="text-sm font-medium text-muted-foreground">容器数</Label>
                        <p class="text-lg font-semibold">{{ workload.containers.length }}</p>
                    </div>
                    <div v-if="workloadType === 'statefulset'">
                        <Label class="text-sm font-medium text-muted-foreground">服务名</Label>
                        <p class="text-lg font-semibold">{{ (workload as StatefulSet).service_name || '-' }}</p>
                    </div>
                    <div v-else>
                        <Label class="text-sm font-medium text-muted-foreground">策略</Label>
                        <p class="text-lg font-semibold">{{ formatStrategy((workload as Deployment).strategy) }}</p>
                    </div>
                    <div>
                        <Label class="text-sm font-medium text-muted-foreground">创建时间</Label>
                        <p class="text-lg font-semibold">{{ formatDate(workload.created_at) }}</p>
                    </div>
                </CardContent>
            </Card>

            <!-- 标签页内容 -->
            <Tabs default-value="containers" class="w-full">
                <TabsList class="grid w-full grid-cols-6">
                    <TabsTrigger value="containers">容器</TabsTrigger>
                    <TabsTrigger value="services">服务</TabsTrigger>
                    <TabsTrigger value="pods">Pods</TabsTrigger>
                    <TabsTrigger value="storage">存储</TabsTrigger>
                    <TabsTrigger value="config">配置</TabsTrigger>
                    <TabsTrigger value="events">事件</TabsTrigger>
                </TabsList>

                <!-- 容器标签页 -->
                <TabsContent value="containers" class="space-y-4">
                    <div v-for="(container, index) in workload.containers" :key="index" class="space-y-4">
                        <Card>
                            <CardHeader>
                                <div class="flex items-center justify-between">
                                    <CardTitle class="flex items-center gap-2">
                                        <Box class="h-4 w-4" />
                                        {{ container.name }}
                                    </CardTitle>
                                </div>
                            </CardHeader>
                            <CardContent class="space-y-4">
                                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label class="text-sm font-medium">镜像</Label>
                                        <p class="font-mono text-sm">{{ container.image }}</p>
                                    </div>
                                    <div>
                                        <Label class="text-sm font-medium">资源限制</Label>
                                        <p class="text-sm">CPU: {{ container.resources?.cpu }}m, 内存: {{ container.resources?.memory }}Mi</p>
                                    </div>
                                </div>

                                <!-- 端口 -->
                                <div v-if="container.ports && container.ports.length > 0">
                                    <Label class="text-sm font-medium">端口</Label>
                                    <div class="mt-2 flex flex-wrap gap-2">
                                        <Badge v-for="port in container.ports" :key="port.container_port" variant="outline">
                                            {{ port.container_port }}/{{ port.protocol }}
                                        </Badge>
                                    </div>
                                </div>

                                <!-- 环境变量 -->
                                <div v-if="container.env && container.env.length > 0">
                                    <Label class="text-sm font-medium">环境变量</Label>
                                    <div class="mt-2 space-y-1">
                                        <div v-for="env in container.env" :key="env.name" class="flex items-center space-x-2 text-sm">
                                            <code class="rounded bg-muted px-1">{{ env.name }}</code>
                                            <span>=</span>
                                            <code class="rounded bg-muted px-1">{{ env.value }}</code>
                                        </div>
                                    </div>
                                </div>

                                <!-- 卷挂载 -->
                                <div v-if="container.volume_mounts && container.volume_mounts.length > 0">
                                    <Label class="text-sm font-medium">存储挂载</Label>
                                    <div class="mt-2 space-y-2">
                                        <div v-for="mount in container.volume_mounts" :key="mount.mount_path" class="text-sm">
                                            <code class="rounded bg-muted px-1">{{ mount.storage_name }}</code>
                                            →
                                            <code class="rounded bg-muted px-1">{{ mount.mount_path }}</code>
                                            <Badge v-if="mount.read_only" variant="secondary" class="ml-2">只读</Badge>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <!-- 服务标签页 -->
                <TabsContent value="services" class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">相关服务</h3>
                    </div>

                    <div v-if="relatedServices.length === 0" class="py-8 text-center">
                        <Network class="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                        <p class="text-muted-foreground">暂无相关服务</p>
                    </div>

                    <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <Card v-for="service in relatedServices" :key="service.name">
                            <CardHeader>
                                <CardTitle class="flex items-center justify-between">
                                    <span class="flex items-center gap-2">
                                        <Network class="h-4 w-4" />
                                        {{ service.name }}
                                    </span>
                                    <Badge variant="outline">{{ service.service_type }}</Badge>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div class="space-y-2">
                                    <div v-for="port in service.ports" :key="port.port" class="flex items-center justify-between text-sm">
                                        <span>{{ port.name || port.port }}</span>
                                        <span>{{ port.port }}:{{ port.target_port }}</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <!-- Pods 标签页 -->
                <TabsContent value="pods" class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">Pod 实例</h3>
                    </div>

                    <div v-if="relatedPods.length === 0" class="py-8 text-center">
                        <Container class="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                        <p class="text-muted-foreground">暂无 Pod 实例</p>
                    </div>

                    <div v-else class="space-y-3">
                        <Card v-for="pod in relatedPods" :key="pod.name">
                            <CardContent class="p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex h-3 w-3 rounded-full" :class="getPodStatusColor(pod.status)"></div>
                                        <div>
                                            <p class="font-medium">{{ pod.name }}</p>
                                            <p class="text-sm text-muted-foreground">{{ formatK8sStatus(pod.status) }}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-muted-foreground">重启: {{ pod.restart_count || 0 }}</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <!-- 存储标签页 -->
                <TabsContent value="storage" class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">存储卷</h3>
                    </div>

                    <div v-if="relatedStorages.length === 0" class="py-8 text-center">
                        <HardDrive class="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                        <p class="text-muted-foreground">暂无关联存储</p>
                    </div>

                    <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <Card v-for="storage in relatedStorages" :key="storage.name">
                            <CardHeader>
                                <CardTitle class="flex items-center justify-between">
                                    <span class="flex items-center gap-2">
                                        <HardDrive class="h-4 w-4" />
                                        {{ storage.name }}
                                    </span>
                                    <Badge :variant="getStorageStatusVariant(storage.status)">{{ formatK8sStatus(storage.status) }}</Badge>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between text-sm">
                                        <span>大小</span>
                                        <span>{{ storage.formatted_size }}</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span>访问模式</span>
                                        <span>{{ storage.access_modes?.join(', ') || '-' }}</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <!-- 配置标签页 -->
                <TabsContent value="config" class="space-y-4">
                    <!-- ConfigMaps -->
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold">ConfigMaps</h3>
                        </div>

                        <div v-if="relatedConfigMaps.length === 0" class="py-4 text-center">
                            <FileText class="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
                            <p class="text-sm text-muted-foreground">暂无关联 ConfigMap</p>
                        </div>

                        <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <Card v-for="configMap in relatedConfigMaps" :key="configMap.name">
                                <CardHeader>
                                    <CardTitle class="flex items-center gap-2">
                                        <FileText class="h-4 w-4" />
                                        {{ configMap.name }}
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div class="text-sm text-muted-foreground">{{ configMap.data_count }} 个配置项</div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>

                    <!-- Secrets -->
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold">Secrets</h3>
                        </div>

                        <div v-if="relatedSecrets.length === 0" class="py-4 text-center">
                            <Lock class="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
                            <p class="text-sm text-muted-foreground">暂无关联 Secret</p>
                        </div>

                        <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <Card v-for="secret in relatedSecrets" :key="secret.name">
                                <CardHeader>
                                    <CardTitle class="flex items-center gap-2">
                                        <Lock class="h-4 w-4" />
                                        {{ secret.name }}
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div class="text-sm text-muted-foreground">{{ secret.data_keys.length }} 个密钥项</div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </TabsContent>

                <!-- 事件标签页 -->
                <TabsContent value="events" class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">相关事件</h3>
                    </div>

                    <div v-if="relatedEvents.length === 0" class="py-8 text-center">
                        <Calendar class="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                        <p class="text-muted-foreground">暂无相关事件</p>
                    </div>

                    <div v-else class="space-y-3">
                        <Card v-for="event in relatedEvents" :key="`${event.name}-${event.timestamp}`">
                            <CardContent class="p-4">
                                <div class="flex items-start justify-between">
                                    <div class="flex items-start space-x-3">
                                        <div class="mt-1 flex h-3 w-3 rounded-full" :class="getEventTypeColor(event.type)"></div>
                                        <div class="flex-1">
                                            <p class="font-medium">{{ event.reason }}</p>
                                            <p class="mt-1 text-sm text-muted-foreground">{{ event.message }}</p>
                                            <p class="mt-2 text-xs text-muted-foreground">{{ formatDate(event.timestamp) }} - {{ event.type }}</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatK8sStatus, formatStrategy } from '@/lib/formatK8sStatus';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { Deployment, StatefulSet } from '@/types';
import { AlertCircle, Box, Calendar, Component, Container, Database, FileText, HardDrive, Lock, Network } from 'lucide-vue-next';
import { computed, onMounted, ref, watch } from 'vue';

interface Props {
    workloadName: string;
    workloadType: 'deployment' | 'statefulset';
}

const props = defineProps<Props>();
const resourcesStore = useResourcesStore();

const loading = ref(true);
const error = ref<string | null>(null);

// 辅助函数：检查资源是否与工作负载关联
const hasWorkloadLabel = (resource: any, workloadName: string, workloadType: string) => {
    const labels = resource.labels || {};

    // 检查是否有 workload-name 标签
    const workloadNameLabel = Object.keys(labels).find((key) => key.endsWith('/workload-name'));
    const workloadTypeLabel = Object.keys(labels).find((key) => key.endsWith('/workload-type'));

    if (workloadNameLabel && workloadTypeLabel) {
        return labels[workloadNameLabel] === workloadName && labels[workloadTypeLabel] === workloadType;
    }

    return false;
};

// 辅助函数：检查服务是否与工作负载关联（通过 target 标签）
const hasWorkloadTargetLabel = (resource: any, workloadName: string, workloadType: string) => {
    const labels = resource.labels || {};

    // 检查是否有 workload-name-target 和 workload-type-target 标签
    const workloadNameTargetLabel = Object.keys(labels).find((key) => key.endsWith('/workload-name-target'));
    const workloadTypeTargetLabel = Object.keys(labels).find((key) => key.endsWith('/workload-type-target'));

    if (workloadNameTargetLabel && workloadTypeTargetLabel) {
        return labels[workloadNameTargetLabel] === workloadName && labels[workloadTypeTargetLabel].toLowerCase() === workloadType.toLowerCase();
    }

    return false;
};

// 监听 store 的加载状态和数据变化
const workload = computed(() => {
    if (!resourcesStore.isLoaded) {
        return null;
    }

    const collection = props.workloadType === 'deployment' ? resourcesStore.collections.deployments : resourcesStore.collections.statefulsets;

    return collection.find((w) => w.name === props.workloadName) || null;
});

// 监听工作负载数据的变化，更新加载状态
watch(
    [() => resourcesStore.isLoaded, () => resourcesStore.isLoading, workload],
    ([isLoaded, isLoading, workloadData]) => {
        if (isLoading) {
            loading.value = true;
            error.value = null;
        } else if (isLoaded) {
            loading.value = false;
            if (!workloadData) {
                error.value = `未找到${props.workloadType === 'deployment' ? '无状态应用' : '有状态应用'}: ${props.workloadName}`;
            } else {
                error.value = null;
            }
        }
    },
    { immediate: true },
);

// 重试加载
const retryLoad = async () => {
    loading.value = true;
    error.value = null;
    await resourcesStore.fetchAllResources();
};

// 确保在组件挂载时加载资源
onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }
});

// 计算相关资源
const relatedServices = computed(() => {
    if (!workload.value) return [];

    return resourcesStore.collections.services.filter((service) => {
        // 检查服务是否有与工作负载关联的 target 标签
        return hasWorkloadTargetLabel(service, props.workloadName, props.workloadType);
    });
});

const relatedPods = computed(() => {
    if (!workload.value) return [];

    return resourcesStore.collections.pods.filter((pod) => {
        // 检查 Pod 是否有与工作负载关联的标签
        return hasWorkloadLabel(pod, props.workloadName, props.workloadType);
    });
});

const relatedStorages = computed(() => {
    if (!workload.value) return [];

    const usedStorageNames = new Set<string>();

    // 从容器的卷挂载中提取存储名称
    workload.value.containers.forEach((container) => {
        container.volume_mounts?.forEach((mount) => {
            if (mount.storage_name) {
                usedStorageNames.add(mount.storage_name);
            }
        });
    });

    return resourcesStore.collections.storages.filter((storage) => usedStorageNames.has(storage.name));
});

const relatedConfigMaps = computed(() => {
    if (!workload.value) return [];

    const usedConfigMapNames = new Set<string>();

    // 从容器中提取使用的 ConfigMap
    workload.value.containers.forEach((container) => {
        container.env_from_configmap?.forEach((env) => {
            if (env.configmap_name) {
                usedConfigMapNames.add(env.configmap_name);
            }
        });
        container.configmap_mounts?.forEach((mount) => {
            if (mount.configmap_name) {
                usedConfigMapNames.add(mount.configmap_name);
            }
        });
    });

    return resourcesStore.collections.configmaps.filter((configmap) => usedConfigMapNames.has(configmap.name));
});

const relatedSecrets = computed(() => {
    if (!workload.value) return [];

    const usedSecretNames = new Set<string>();

    // 从容器中提取使用的 Secret
    workload.value.containers.forEach((container) => {
        container.env_from_secret?.forEach((env) => {
            if (env.secret_name) {
                usedSecretNames.add(env.secret_name);
            }
        });
        container.secret_mounts?.forEach((mount) => {
            if (mount.secret_name) {
                usedSecretNames.add(mount.secret_name);
            }
        });
    });

    // 添加镜像拉取密钥
    workload.value.image_pull_secrets?.forEach((secret) => {
        usedSecretNames.add(secret);
    });

    return resourcesStore.collections.secrets.filter((secret) => usedSecretNames.has(secret.name));
});

const relatedEvents = computed(() => {
    if (!workload.value) return [];

    // 过滤与此工作负载相关的事件
    return resourcesStore.collections.events
        .filter((event) => {
            // 检查事件是否与工作负载相关
            return (
                event.name === props.workloadName ||
                event.name?.startsWith(props.workloadName) ||
                event.involved_object?.name === props.workloadName ||
                event.involved_object?.name?.startsWith(props.workloadName)
            );
        })
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
});

// 辅助函数
const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};

const getStatusVariant = (status: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('available') || lowerStatus.includes('running')) return 'secondary';
    if (lowerStatus.includes('progressing') || lowerStatus.includes('pending')) return 'outline';
    if (lowerStatus.includes('failure') || lowerStatus.includes('failed')) return 'destructive';
    return 'default';
};

const getPodStatusColor = (status: string) => {
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('running')) return 'bg-green-500';
    if (lowerStatus.includes('pending')) return 'bg-yellow-500 animate-pulse';
    if (lowerStatus.includes('failed') || lowerStatus.includes('error')) return 'bg-red-500';
    return 'bg-gray-400';
};

const getStorageStatusVariant = (status: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('bound')) return 'secondary';
    if (lowerStatus.includes('pending')) return 'outline';
    if (lowerStatus.includes('lost') || lowerStatus.includes('failed')) return 'destructive';
    return 'default';
};

const getEventTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
        case 'normal':
            return 'bg-green-500';
        case 'warning':
            return 'bg-yellow-500';
        case 'error':
            return 'bg-red-500';
        default:
            return 'bg-gray-400';
    }
};
</script>
