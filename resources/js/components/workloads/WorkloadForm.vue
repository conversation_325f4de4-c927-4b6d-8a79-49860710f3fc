<template>
    <Tabs v-model="activeSection" class="w-full">
        <TabsList class="grid w-full grid-cols-2">
            <TabsTrigger value="general"> 基本配置 </TabsTrigger>
            <TabsTrigger value="containers" :disabled="modelValue.containers.length === 0"> 容器配置 </TabsTrigger>
        </TabsList>
        <TabsContent value="general" class="pt-6">
            <!-- General Settings -->
            <div class="space-y-8">
                <div>
                    <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">基本配置</h3>
                    <p class="mb-6 text-sm text-gray-600 dark:text-gray-400">设置工作负载的基本信息</p>

                    <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                        <div>
                            <div class="flex items-center">
                                <Label for="name">名称 *</Label>
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger as-child>
                                            <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p class="max-w-xs">工作负载的唯一名称，用于在平台中识别您的应用。例如：`my-awesome-app`</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                            <Input id="name" v-model="modelValue.name" :disabled="isUpdate" required class="mt-1" placeholder="例如：my-app" />
                        </div>

                        <div>
                            <div class="flex items-center">
                                <Label for="replicas">副本数 *</Label>
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger as-child>
                                            <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p class="max-w-xs">您希望运行的应用实例数量。平台将确保始终有这个数量的副本在运行，以实现高可用性。</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                            <Input id="replicas" v-model.number="modelValue.replicas" type="number" min="0" required class="mt-1" />
                        </div>
                        <div v-if="workloadType === 'statefulset'">
                            <div class="flex items-center">
                                <Label for="service_name">服务名称</Label>
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger as-child>
                                            <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p class="max-w-xs">
                                                关联的无头服务（Headless Service）的名称。这为每个副本提供了唯一的、稳定的网络标识。
                                            </p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                            <Input
                                id="service_name"
                                v-model="(modelValue as CreateStatefulSetData).service_name"
                                class="mt-1"
                                placeholder="例如：my-headless-service"
                            />
                        </div>
                    </div>
                </div>

                <div>
                    <div class="flex items-center">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">镜像拉取密钥</h3>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger as-child>
                                    <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p class="max-w-xs">
                                        如果您使用私有镜像仓库，请在此处选择相应的密钥。平台将使用这些密钥来安全地拉取您的容器镜像。
                                    </p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <p class="mt-1 mb-6 text-sm text-gray-600 dark:text-gray-400">选择用于从私有镜像仓库拉取镜像的密钥</p>
                    <ImagePullSecretSelector v-model="modelValue.image_pull_secrets" />
                </div>

                <div>
                    <LabelSelector v-model="modelValue.labels" />
                </div>
            </div>
        </TabsContent>
        <TabsContent value="containers" class="pt-6">
            <div class="mb-4 flex items-center justify-between border-b pb-4">
                <div class="flex items-center gap-2 overflow-x-auto p-1">
                    <Button
                        v-for="(c, containerIndex) in modelValue.containers"
                        :key="containerIndex"
                        :variant="activeContainerIndex === containerIndex ? 'secondary' : 'ghost'"
                        size="sm"
                        @click="activeContainerIndex = containerIndex"
                        class="shrink-0"
                    >
                        {{ c.name || `容器 ${containerIndex + 1}` }}
                    </Button>
                </div>
                <div class="flex shrink-0 items-center pl-2">
                    <Button variant="outline" size="sm" title="添加一个新的容器到工作负载中" @click="addContainer">
                        <Plus class="mr-2 h-4 w-4" />
                        添加容器
                    </Button>
                </div>
            </div>

            <!-- Container Settings -->
            <div v-if="container" class="space-y-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">容器 {{ activeContainerIndex + 1 }} 配置</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">为选定的容器进行详细配置</p>
                    </div>
                    <Button
                        v-if="modelValue.containers.length > 1"
                        variant="destructive"
                        title="删除容器"
                        size="sm"
                        @click="removeContainer(activeContainerIndex)"
                    >
                        <Trash2 class="mr-2 h-4 w-4" />
                        删除容器
                    </Button>
                </div>

                <!-- 基本配置 -->
                <div>
                    <h3 class="mb-4 text-base font-medium text-gray-900 dark:text-white">基本配置</h3>
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <div class="flex items-center">
                                    <Label title="容器名称">容器名称 *</Label>
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger as-child>
                                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>容器在应用内部的名称。如果一个应用包含多个容器，此名称用于区分它们。</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <Input
                                    :model-value="container.name"
                                    @update:model-value="updateContainerField(activeContainerIndex, 'name', $event)"
                                    placeholder="例如：nginx"
                                    class="mt-1"
                                />
                            </div>
                            <div>
                                <div class="flex items-center">
                                    <Label title="镜像">镜像 *</Label>
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger as-child>
                                                <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>
                                                    用于创建容器的镜像。格式为 `repository/image:tag`。可以使用公共镜像，或使用拉取密钥访问私有镜像。
                                                </p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <Input
                                    :model-value="container.image"
                                    @update:model-value="updateContainerField(activeContainerIndex, 'image', $event)"
                                    placeholder="例如：nginx:latest"
                                    class="mt-1"
                                />
                            </div>
                            <div v-if="modelValue.image_pull_secrets && modelValue.image_pull_secrets.length > 0">
                                <div class="flex items-center">
                                    <Label title="镜像拉取策略">镜像拉取策略</Label>
                                </div>
                                <Select
                                    :model-value="container.image_pull_policy || 'IfNotPresent'"
                                    @update:model-value="updateContainerField(activeContainerIndex, 'image_pull_policy', $event)"
                                >
                                    <SelectTrigger class="mt-1">
                                        <SelectValue placeholder="选择镜像拉取策略" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Always">总是拉取（每次重启或者新建时都拉取，可能会对网络造成压力）</SelectItem>
                                        <SelectItem value="IfNotPresent">本地不存在时拉取（默认策略）</SelectItem>
                                        <SelectItem value="Never">仅使用本地镜像（不拉取）</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div v-else class="flex items-center text-sm text-yellow-600 dark:text-yellow-400">
                                <AlertTriangle class="mr-2 h-4 w-4" />
                                <span>如需指定镜像拉取策略，请先设置镜像拉取密钥（私有仓库）</span>
                            </div>
                        </div>
                        <ResourceSelector v-model="container.resources!" label="资源限制" />

                        <div>
                            <div class="flex items-center">
                                <Label>工作目录 (可选)</Label>
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger as-child>
                                            <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>设置容器内命令的执行目录。如果未指定，将使用镜像中定义的默认工作目录。</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                            <Input
                                :model-value="container.working_dir"
                                @update:model-value="updateContainerField(activeContainerIndex, 'working_dir', $event)"
                                placeholder="例如：/app"
                                class="mt-1"
                            />
                        </div>
                        <CommandSelector
                            :model-value="{ command: container.command || [], args: container.args || [] }"
                            @update:model-value="
                                (value) => {
                                    updateContainerField(activeContainerIndex, 'command', value.command);
                                    updateContainerField(activeContainerIndex, 'args', value.args);
                                }
                            "
                        />
                    </div>
                </div>

                <!-- 网络配置 -->
                <div>
                    <h3 class="mb-4 text-base font-medium text-gray-900 dark:text-white">网络配置</h3>
                    <div class="space-y-4">
                        <PortSelector v-model="container.ports!" mode="container" label="容器暴露的端口" />
                        <div
                            v-if="!container.ports || container.ports.length === 0"
                            class="flex items-center text-sm text-yellow-600 dark:text-yellow-400"
                        >
                            <AlertTriangle class="mr-2 h-4 w-4" />
                            <span>警告：未添加任何端口，您将无法访问此容器。</span>
                        </div>
                        <div v-else class="flex items-center text-sm text-blue-600 dark:text-blue-400">
                            <Info class="mr-2 h-4 w-4" />
                            <span>提示：请记得到「服务」页面创建服务来暴露端口。</span>
                        </div>
                        <p class="text-sm text-gray-500">
                            注意，此端口并非外部连接端口，仅声明了容器暴露了哪些端口，如果要使用外部连接，请前往
                            <Link :href="route('services.index')" class="text-blue-500">服务</Link> 页面创建服务。
                        </p>
                    </div>
                </div>

                <!-- 环境变量 -->
                <div>
                    <h3 class="mb-4 text-base font-medium text-gray-900 dark:text-white">环境变量</h3>
                    <div class="space-y-6">
                        <EnvSelector v-model="container.env!" label="环境变量" />
                        <EnvFromSelector
                            :model-value="{
                                configmap: container.env_from_configmap || [],
                                secret: container.env_from_secret || [],
                            }"
                            @update:model-value="
                                (value) => {
                                    updateContainerField(activeContainerIndex, 'env_from_configmap', value.configmap);
                                    updateContainerField(activeContainerIndex, 'env_from_secret', value.secret);
                                }
                            "
                        />
                    </div>
                </div>

                <!-- 健康检查 -->
                <div>
                    <h3 class="mb-4 text-base font-medium text-gray-900 dark:text-white">健康检查</h3>
                    <div class="space-y-4">
                        <HealthProbeSelector v-model="container.liveness_probe" label="存活探针 (Liveness Probe)" probe-type="liveness" />
                        <HealthProbeSelector v-model="container.readiness_probe" label="就绪探针 (Readiness Probe)" probe-type="readiness" />
                        <HealthProbeSelector v-model="container.startup_probe" label="启动探针 (Startup Probe)" probe-type="startup" />
                        <div class="rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
                            <div class="flex">
                                <Info class="h-5 w-5 text-blue-400" />
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">探针说明</h3>
                                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                        <ul class="list-inside list-disc space-y-1">
                                            <li><strong>存活探针：</strong>检测容器是否正在运行，失败时重启容器</li>
                                            <li><strong>就绪探针：</strong>检测容器是否准备好接收流量</li>
                                            <li><strong>启动探针：</strong>检测容器是否已成功启动，适用于启动缓慢的应用</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 存储配置 -->
                <div>
                    <h3 class="mb-4 text-base font-medium text-gray-900 dark:text-white">存储配置</h3>
                    <div class="space-y-6">
                        <div>
                            <Label>卷挂载</Label>
                            <div class="mt-2 space-y-2">
                                <div
                                    v-for="(mount, mountIndex) in container.volume_mounts"
                                    :key="mountIndex"
                                    class="grid grid-cols-1 items-end gap-2 md:grid-cols-4"
                                >
                                    <div>
                                        <div class="flex items-center">
                                            <Label>挂载路径 *</Label>
                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger as-child>
                                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>容器内部的路径，存储卷将在此处挂载。例如: /var/www/html</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>
                                        </div>
                                        <Input
                                            :model-value="mount.mount_path"
                                            @update:model-value="updateVolumeMount(activeContainerIndex, mountIndex, 'mount_path', $event)"
                                            placeholder="/data"
                                            title="挂载路径（必填）"
                                        />
                                    </div>
                                    <div>
                                        <StorageSelector
                                            :model-value="mount.storage_name"
                                            @update:model-value="updateVolumeMount(activeContainerIndex, mountIndex, 'storage_name', $event)"
                                            label="存储名称 *"
                                            placeholder="选择存储"
                                            only-bound
                                        />
                                    </div>
                                    <div>
                                        <div class="flex items-center">
                                            <Label>子路径 (可选)</Label>
                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger as-child>
                                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>挂载存储卷内的特定子目录，而不是整个卷。这对于多个应用共享一个存储卷非常有用。</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>
                                        </div>
                                        <Input
                                            :model-value="mount.sub_path"
                                            @update:model-value="updateVolumeMount(activeContainerIndex, mountIndex, 'sub_path', $event)"
                                            placeholder="subdir"
                                            title="子路径（可选）"
                                        />
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <Checkbox
                                            :id="`readonly-${activeContainerIndex}-${mountIndex}`"
                                            :checked="mount.read_only"
                                            title="只读"
                                            @update:checked="updateVolumeMount(activeContainerIndex, mountIndex, 'read_only', $event)"
                                        />
                                        <div class="flex items-center">
                                            <Label :for="`readonly-${activeContainerIndex}-${mountIndex}`">只读</Label>
                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger as-child>
                                                        <Info class="ml-2 h-4 w-4 cursor-help text-muted-foreground" />
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>如果选中，容器将只能读取此挂载卷中的数据，无法写入。</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>
                                        </div>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            title="删除挂载"
                                            @click="removeVolumeMount(activeContainerIndex, mountIndex)"
                                        >
                                            <X class="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                                <Button variant="outline" size="sm" title="添加挂载" @click="addVolumeMount(activeContainerIndex)">
                                    <Plus class="mr-2 h-4 w-4" />
                                    添加卷挂载
                                </Button>
                            </div>
                            <div
                                v-if="!container.volume_mounts || container.volume_mounts.length === 0"
                                class="mt-2 flex items-center text-sm text-yellow-600 dark:text-yellow-400"
                            >
                                <AlertTriangle class="mr-2 h-4 w-4" />
                                <span>警告：未挂载存储卷，容器重启后数据将会丢失。如果你的应用不需要持久化数据，可以忽略此警告。</span>
                            </div>
                        </div>
                        <FileMountSelector
                            :model-value="{
                                configmap_mounts: container.configmap_mounts || [],
                                secret_mounts: container.secret_mounts || [],
                            }"
                            @update:model-value="
                                (value) => {
                                    updateContainerField(activeContainerIndex, 'configmap_mounts', value.configmap_mounts);
                                    updateContainerField(activeContainerIndex, 'secret_mounts', value.secret_mounts);
                                }
                            "
                        />
                    </div>
                </div>
            </div>

            <!-- Placeholder for when no container is selected -->
            <div v-else-if="activeSection === 'containers'" class="flex h-64 items-center justify-center rounded-lg border-2 border-dashed">
                <div class="text-center">
                    <p class="text-gray-500">请从左侧选择一个容器进行配置，或添加一个新容器。</p>
                </div>
            </div>
        </TabsContent>
    </Tabs>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import CommandSelector from '@/components/selectors/CommandSelector.vue';
import EnvFromSelector from '@/components/selectors/EnvFromSelector.vue';
import EnvSelector from '@/components/selectors/EnvSelector.vue';
import FileMountSelector from '@/components/selectors/FileMountSelector.vue';
import HealthProbeSelector from '@/components/selectors/HealthProbeSelector.vue';
import ImagePullSecretSelector from '@/components/selectors/ImagePullSecretSelector.vue';
import LabelSelector from '@/components/selectors/LabelSelector.vue';
import PortSelector from '@/components/selectors/PortSelector.vue';
import ResourceSelector from '@/components/selectors/ResourceSelector.vue';
import StorageSelector from '@/components/selectors/StorageSelector.vue';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { Container, CreateDeploymentData, CreateStatefulSetData } from '@/types';
import { Link } from '@inertiajs/vue3';
import { AlertTriangle, Info, Plus, Trash2, X } from 'lucide-vue-next';
import { computed, onMounted, ref, toRefs } from 'vue';

type WorkloadData = CreateDeploymentData | CreateStatefulSetData;

interface Props {
    modelValue: WorkloadData;
    workloadType: 'deployment' | 'statefulset';
    isUpdate?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
    isUpdate: false,
});

const emit = defineEmits(['update:modelValue']);

const { modelValue, workloadType, isUpdate } = toRefs(props);

const activeSection = ref<'general' | 'containers'>('general');
const activeContainerIndex = ref(0);

const container = computed<Container>(() => {
    return modelValue.value.containers[activeContainerIndex.value];
});

const emitFieldUpdate = (field: string, value: any) => {
    emit('update:modelValue', { ...modelValue.value, [field]: value });
};

const updateContainerField = (containerIndex: number, field: string, value: any) => {
    const newContainers = [...modelValue.value.containers];
    (newContainers[containerIndex] as any)[field] = value;
    emitFieldUpdate('containers', newContainers);
};

const addContainer = () => {
    const newContainers = [
        ...modelValue.value.containers,
        {
            name: '',
            image: '',
            image_pull_policy: 'IfNotPresent',
            working_dir: '',
            command: [],
            args: [],
            ports: [],
            env: [],
            env_from_configmap: [],
            env_from_secret: [],
            volume_mounts: [],
            configmap_mounts: [],
            secret_mounts: [],
            resources: {
                memory: 512,
                cpu: 500,
            },
            liveness_probe: undefined,
            readiness_probe: undefined,
            startup_probe: undefined,
        },
    ];
    emitFieldUpdate('containers', newContainers);
    activeContainerIndex.value = newContainers.length - 1;
};

const removeContainer = (index: number) => {
    const newContainers = [...modelValue.value.containers];
    newContainers.splice(index, 1);
    emitFieldUpdate('containers', newContainers);

    if (activeContainerIndex.value >= newContainers.length) {
        activeContainerIndex.value = newContainers.length - 1;
    }
};

const addVolumeMount = (containerIndex: number) => {
    const newContainers = [...modelValue.value.containers];
    if (!newContainers[containerIndex].volume_mounts) {
        newContainers[containerIndex].volume_mounts = [];
    }
    newContainers[containerIndex].volume_mounts!.push({
        mount_path: '',
        storage_name: '',
        sub_path: '',
        read_only: false,
    });
    emitFieldUpdate('containers', newContainers);
};

const removeVolumeMount = (containerIndex: number, mountIndex: number) => {
    const newContainers = [...modelValue.value.containers];
    newContainers[containerIndex].volume_mounts?.splice(mountIndex, 1);
    emitFieldUpdate('containers', newContainers);
};

const updateVolumeMount = (containerIndex: number, mountIndex: number, field: string, value: any) => {
    const newContainers = [...modelValue.value.containers];
    const mount = newContainers[containerIndex].volume_mounts![mountIndex];
    (mount as any)[field] = value;
    emitFieldUpdate('containers', newContainers);
};

const resourcesStore = useResourcesStore();

onMounted(async () => {
    if (!resourcesStore.isLoaded && !resourcesStore.isLoading) {
        await resourcesStore.fetchAllResources();
    }
});
</script>
