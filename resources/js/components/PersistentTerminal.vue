<template>
    <div
        v-if="visible"
        ref="terminalWindow"
        class="fixed overflow-hidden rounded-lg border border-border bg-background shadow-2xl"
        :style="{
            left: `${position.x}px`,
            top: `${position.y}px`,
            width: `${size.width}px`,
            height: `${size.height}px`,
            zIndex: zIndex,
        }"
        @mousedown="bringToFront"
    >
        <!-- Terminal Header -->
        <div
            ref="titleBar"
            class="flex cursor-move items-center justify-between border-b border-border bg-muted/50 px-4 py-2 select-none"
            :class="{ 'border-b-0': isMinimized }"
            @mousedown="startDrag"
        >
            <div class="flex min-w-0 flex-1 items-center gap-2">
                <Terminal class="h-4 w-4 flex-shrink-0" />
                <span class="truncate text-sm font-medium" :title="title">{{ title }}</span>
                <div v-if="!isMinimized" class="flex flex-shrink-0 items-center gap-1">
                    <div
                        :class="[
                            'h-2 w-2 rounded-full',
                            connectionStatus === 'connected'
                                ? 'bg-green-500'
                                : connectionStatus === 'connecting'
                                  ? 'animate-pulse bg-yellow-500'
                                  : 'bg-red-500',
                        ]"
                    />
                    <span class="text-xs whitespace-nowrap text-muted-foreground">
                        {{ connectionStatus === 'connected' ? '已连接' : connectionStatus === 'connecting' ? '连接中' : '未连接' }}
                    </span>
                </div>
            </div>
            <div class="flex flex-shrink-0 items-center gap-1">
                <Button variant="ghost" size="sm" @click="minimizeTerminal" class="h-6 w-6 p-0 hover:bg-muted">
                    <Minus class="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" @click="closeTerminal" class="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground">
                    <X class="h-3 w-3" />
                </Button>
            </div>
        </div>

        <!-- Terminal Content -->
        <div v-if="!isMinimized" class="h-full" :style="{ height: `${size.height - 41}px` }">
            <PodTerminal
                ref="podTerminalRef"
                :pod-name="podName"
                :container-name="containerName"
                :mode="mode"
                :auto-connect="true"
                :show-status-bar="false"
                class="h-full w-full"
                @connection-status-changed="onConnectionStatusChanged"
            />

            <!-- Command execution overlay -->
            <div v-if="isExecutingCommand" class="absolute inset-0 flex items-center justify-center bg-black/50">
                <div class="mx-4 w-full max-w-md rounded-lg border border-border bg-background p-4">
                    <div class="mb-2 flex items-center gap-2">
                        <div class="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                        <span class="font-medium">执行命令中...</span>
                    </div>
                    <p class="text-sm text-muted-foreground">{{ currentCommand }}</p>
                </div>
            </div>
        </div>

        <!-- Resize handle -->
        <div v-if="!isMinimized" ref="resizeHandle" class="absolute right-0 bottom-0 h-4 w-4 cursor-nw-resize" @mousedown="startResize">
            <div class="absolute right-1 bottom-1 h-2 w-2 border-r border-b border-border" />
        </div>
    </div>
</template>

<script setup lang="ts">
import PodTerminal from '@/components/PodTerminal.vue';
import { Button } from '@/components/ui/button';
import eventBus, { type CommandExecutionResult, type TerminalInstance } from '@/lib/eventBus';
import { TerminalExecutor } from '@/lib/terminalExecutor';
import { Minus, Terminal, X } from 'lucide-vue-next';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

interface Props {
    terminalId: string;
    podName: string;
    containerName: string;
    mode?: 'shell' | 'attach';
    initialPosition?: { x: number; y: number };
    initialSize?: { width: number; height: number };
    zIndex?: number;
}

const props = withDefaults(defineProps<Props>(), {
    mode: 'shell',
    initialPosition: () => ({ x: 100, y: 100 }),
    initialSize: () => ({ width: 800, height: 600 }),
    zIndex: 1000,
});

const emit = defineEmits<{
    close: [terminalId: string];
    minimize: [terminalId: string];
    maximize: [terminalId: string];
    focus: [terminalId: string];
    'update-instance': [instance: TerminalInstance];
}>();

// Refs
const terminalWindow = ref<HTMLElement>();
const titleBar = ref<HTMLElement>();
const resizeHandle = ref<HTMLElement>();
const podTerminalRef = ref<InstanceType<typeof PodTerminal>>();

// State
const visible = ref(true);
const isMinimized = ref(false);
const connectionStatus = ref<'connecting' | 'connected' | 'disconnected'>('connecting');
const position = ref(props.initialPosition);
const size = ref(props.initialSize);
const zIndex = ref(props.zIndex);

// Store original size for restore
const originalSize = ref(props.initialSize);

// Command execution state
const isExecutingCommand = ref(false);
const currentCommand = ref('');

// Computed
const title = computed(() => {
    return `${props.podName} (${props.containerName})`;
});

// Drag functionality
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

const startDrag = (event: MouseEvent) => {
    isDragging.value = true;
    dragOffset.value = {
        x: event.clientX - position.value.x,
        y: event.clientY - position.value.y,
    };

    document.addEventListener('mousemove', handleDrag);
    document.addEventListener('mouseup', stopDrag);
    event.preventDefault();
};

const handleDrag = (event: MouseEvent) => {
    if (!isDragging.value) return;

    const maxX = isMinimized.value ? window.innerWidth - size.value.width : window.innerWidth - size.value.width;
    const maxY = isMinimized.value ? window.innerHeight - size.value.height : window.innerHeight - size.value.height;

    position.value = {
        x: Math.max(0, Math.min(maxX, event.clientX - dragOffset.value.x)),
        y: Math.max(0, Math.min(maxY, event.clientY - dragOffset.value.y)),
    };
};

const stopDrag = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleDrag);
    document.removeEventListener('mouseup', stopDrag);
};

// Resize functionality
const isResizing = ref(false);
const resizeStart = ref({ x: 0, y: 0, width: 0, height: 0 });

const startResize = (event: MouseEvent) => {
    isResizing.value = true;
    resizeStart.value = {
        x: event.clientX,
        y: event.clientY,
        width: size.value.width,
        height: size.value.height,
    };

    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', stopResize);
    event.preventDefault();
    event.stopPropagation();
};

const handleResize = (event: MouseEvent) => {
    if (!isResizing.value) return;

    const deltaX = event.clientX - resizeStart.value.x;
    const deltaY = event.clientY - resizeStart.value.y;

    size.value = {
        width: Math.max(400, Math.min(window.innerWidth - position.value.x, resizeStart.value.width + deltaX)),
        height: Math.max(300, Math.min(window.innerHeight - position.value.y, resizeStart.value.height + deltaY)),
    };

    // Update original size when resizing
    if (!isMinimized.value) {
        originalSize.value = { ...size.value };
    }
};

const stopResize = () => {
    isResizing.value = false;
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', stopResize);

    // Terminal will automatically resize with the container
};

// Terminal actions
const bringToFront = () => {
    emit('focus', props.terminalId);
};

const minimizeTerminal = () => {
    if (isMinimized.value) {
        // Restore to original size
        isMinimized.value = false;
        size.value = { ...originalSize.value };
        emit('maximize', props.terminalId);
    } else {
        // Store current size before minimizing
        originalSize.value = { ...size.value };
        isMinimized.value = true;
        size.value = { width: 280, height: 41 }; // 只保留标题栏高度
        emit('minimize', props.terminalId);
    }
};

const closeTerminal = () => {
    visible.value = false;
    emit('close', props.terminalId);
};

// Connection status handling
const onConnectionStatusChanged = (status: string) => {
    connectionStatus.value = status as 'connecting' | 'connected' | 'disconnected';
    updateInstance();
};

// Instance management
const updateInstance = () => {
    const instance: TerminalInstance = {
        id: props.terminalId,
        podName: props.podName,
        containerName: props.containerName,
        mode: props.mode,
        title: title.value,
        isConnected: connectionStatus.value === 'connected',
        isMinimized: isMinimized.value,
        zIndex: zIndex.value,
    };

    emit('update-instance', instance);
};

// Command execution
const executeCommand = async (command: string): Promise<CommandExecutionResult> => {
    isExecutingCommand.value = true;
    currentCommand.value = command;

    try {
        const result = await TerminalExecutor.executeCommand({
            podName: props.podName,
            containerName: props.containerName,
            command: command,
            timeout: 30000,
            mode: props.mode,
        });

        return result;
    } finally {
        isExecutingCommand.value = false;
        currentCommand.value = '';
    }
};

// Event listeners
const handleCommandExecution = async (payload: { terminalId: string; command: string }) => {
    if (payload.terminalId === props.terminalId) {
        try {
            const result = await executeCommand(payload.command);
            eventBus.emit('terminal:command:result', {
                terminalId: props.terminalId,
                result,
            });
        } catch (error) {
            eventBus.emit('terminal:command:result', {
                terminalId: props.terminalId,
                result: {
                    success: false,
                    output: '',
                    exitCode: 1,
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            });
        }
    }
};

const handleFocus = (terminalId: string) => {
    if (terminalId === props.terminalId) {
        zIndex.value = Date.now(); // Simple z-index management
    }
};

// Lifecycle
onMounted(() => {
    eventBus.on('terminal:command:execute', handleCommandExecution);
    eventBus.on('terminal:focus', handleFocus);
    updateInstance();
});

onUnmounted(() => {
    eventBus.off('terminal:command:execute', handleCommandExecution);
    eventBus.off('terminal:focus', handleFocus);
});

// Watchers
watch([isMinimized, connectionStatus], updateInstance);
watch(zIndex, (newZIndex) => {
    if (terminalWindow.value) {
        terminalWindow.value.style.zIndex = newZIndex.toString();
    }
});

// Expose methods
defineExpose({
    executeCommand,
    bringToFront,
    minimize: minimizeTerminal,
    close: closeTerminal,
});
</script>

<style scoped>
.cursor-move {
    cursor: move;
}

.cursor-nw-resize {
    cursor: nw-resize;
}
</style>
