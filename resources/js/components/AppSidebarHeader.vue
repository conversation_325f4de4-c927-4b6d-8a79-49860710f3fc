<script setup lang="ts">
import ApplyDialog from '@/components/ApplyDialog.vue';
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import TaskPopover from '@/components/TaskPopover.vue';
import { Button } from '@/components/ui/button';
import { SidebarTrigger } from '@/components/ui/sidebar';
import type { BreadcrumbItemType } from '@/types';
import { Link, router } from '@inertiajs/vue3';
import { CircleDollarSign, FileText, Grid2x2 } from 'lucide-vue-next';
import { ref } from 'vue';

withDefaults(
    defineProps<{
        breadcrumbs?: BreadcrumbItemType[];
    }>(),
    {
        breadcrumbs: () => [],
    },
);

const showApplyDialog = ref(false);
</script>

<template>
    <header
        class="flex h-16 shrink-0 items-center gap-2 border-b border-sidebar-border/70 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4"
    >
        <div class="flex items-center gap-2">
            <SidebarTrigger class="-ml-1" />
            <template v-if="breadcrumbs && breadcrumbs.length > 0">
                <Breadcrumbs :breadcrumbs="breadcrumbs" />
            </template>
        </div>

        <!-- 右侧操作区域 -->
        <div class="ml-auto flex items-center gap-2">
            <!-- 切换到桌面模式 -->
            <Button variant="ghost" size="icon" class="group h-9 w-9 cursor-pointer" @click="router.visit(route('desktop.index'))">
                <Grid2x2 class="size-5 opacity-80 group-hover:opacity-100" />
            </Button>

            <!-- 任务中心 -->
            <TaskPopover />

            <!-- YAML 资源应用 -->
            <Button variant="ghost" size="icon" class="group h-9 w-9 cursor-pointer" @click="showApplyDialog = true">
                <FileText class="size-5 opacity-80 group-hover:opacity-100" />
            </Button>

            <!-- 余额 -->
            <Link :href="route('balance.index')">
                <Button variant="ghost" size="icon" class="group h-9 w-9 cursor-pointer">
                    <CircleDollarSign class="size-5 opacity-80 group-hover:opacity-100" />
                </Button>
            </Link>
        </div>

        <!-- Apply Dialog -->
        <ApplyDialog v-model:open="showApplyDialog" />
    </header>
</template>
