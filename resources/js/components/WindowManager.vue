<template>
    <div class="window-manager">
        <!-- 窗口容器 -->
        <div class="windows-container">
            <!-- 通用窗口组件 -->
            <UniversalWindow
                v-for="window in visibleWindows"
                :key="window.id"
                :window="window"
                @close="closeWindow"
                @minimize="minimizeWindow"
                @maximize="maximizeWindow"
                @restore="restoreWindow"
                @focus="focusWindow"
                @update="updateWindow"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import UniversalWindow from '@/components/windows/UniversalWindow.vue';
import eventBus from '@/lib/eventBus';
import type { WindowInstance, WindowManagerOptions, WindowType } from '@/types/window';
import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';

defineOptions({ inheritAttrs: false });

interface Props {
    options?: WindowManagerOptions;
}

const props = withDefaults(defineProps<Props>(), {
    options: () => ({
        defaultPosition: { x: 100, y: 100 },
        defaultSize: { width: 800, height: 600 },
        stackOffset: 30,
    }),
});

// 状态管理
const windows = ref<WindowInstance[]>([]);
const activeWindowId = ref<string | null>(null);
const nextZIndex = ref(1000);

// 合并默认选项
const options = reactive({
    defaultPosition: { x: 100, y: 100 },
    defaultSize: { width: 800, height: 600 },
    stackOffset: 30,
    ...props.options,
});

// 计算属性
// 对于终端窗口需要在最小化时保持挂载以持续连接，因此渲染所有 isVisible 的窗口，具体可在子组件中用 v-show 隐藏
const visibleWindows = computed(() => windows.value.filter((window) => window.isVisible));

// 窗口管理方法
const generateWindowId = (): string => {
    return `window-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

const calculatePosition = (index: number) => {
    const offset = index * options.stackOffset;
    return {
        x: options.defaultPosition.x + offset,
        y: options.defaultPosition.y + offset,
    };
};

const getDefaultWindowConfig = (type: WindowType): Partial<WindowInstance> => {
    const baseConfig = {
        isDraggable: true,
        isResizable: true,
        isVisible: true,
        isMinimized: false,
        isMaximized: false,
    };

    switch (type) {
        case 'terminal':
            return {
                ...baseConfig,
                size: { width: 800, height: 600 },
                minSize: { width: 400, height: 300 },
                icon: 'Terminal',
            };
        case 'ai-assistant':
            return {
                ...baseConfig,
                size: { width: 400, height: 600 },
                minSize: { width: 300, height: 400 },
                icon: 'Sparkles',
            };
        default:
            return {
                ...baseConfig,
                size: options.defaultSize,
                minSize: { width: 300, height: 200 },
            };
    }
};

const openWindow = (payload: { type: WindowType; data?: Record<string, any>; options?: Partial<WindowInstance> }) => {
    const { type, data = {}, options: windowOptions = {} } = payload;

    // 检查是否已存在相同类型的窗口（某些类型只允许单例）
    if (type === 'ai-assistant') {
        const existingWindow = windows.value.find((w) => w.type === type);
        if (existingWindow) {
            focusWindow(existingWindow.id);
            return existingWindow;
        }
    }

    // 为终端类型检查是否已存在相同 pod 和容器的窗口
    if (type === 'terminal' && data.podName && data.containerName) {
        const existingTerminal = windows.value.find(
            (w) => w.type === 'terminal' && w.data?.podName === data.podName && w.data?.containerName === data.containerName,
        );
        if (existingTerminal) {
            focusWindow(existingTerminal.id);
            return existingTerminal;
        }
    }

    const windowId = generateWindowId();
    const defaultConfig = getDefaultWindowConfig(type);
    const position = calculatePosition(windows.value.length);

    const newWindow: WindowInstance = {
        id: windowId,
        type,
        title: getWindowTitle(type, data),
        position,
        zIndex: nextZIndex.value++,
        data,
        ...defaultConfig,
        ...windowOptions,
    } as WindowInstance;

    windows.value.push(newWindow);
    focusWindow(windowId);

    return newWindow;
};

const closeWindow = (windowId: string) => {
    const index = windows.value.findIndex((w) => w.id === windowId);
    if (index !== -1) {
        windows.value.splice(index, 1);

        // 如果关闭的是活动窗口，激活下一个窗口
        if (activeWindowId.value === windowId) {
            const visibleWindows = windows.value.filter((w) => w.isVisible && !w.isMinimized);
            if (visibleWindows.length > 0) {
                const nextWindow = visibleWindows.reduce((prev, current) => (prev.zIndex > current.zIndex ? prev : current));
                activeWindowId.value = nextWindow.id;
            } else {
                activeWindowId.value = null;
            }
        }
    }
};

const minimizeWindow = (windowId: string) => {
    const window = windows.value.find((w) => w.id === windowId);
    if (window) {
        window.isMinimized = true;

        // 如果最小化的是活动窗口，激活下一个窗口
        if (activeWindowId.value === windowId) {
            const visibleWindows = windows.value.filter((w) => w.isVisible && !w.isMinimized);
            if (visibleWindows.length > 0) {
                const nextWindow = visibleWindows.reduce((prev, current) => (prev.zIndex > current.zIndex ? prev : current));
                activeWindowId.value = nextWindow.id;
            } else {
                activeWindowId.value = null;
            }
        }
    }
};

const maximizeWindow = (windowId: string) => {
    const window = windows.value.find((w) => w.id === windowId);
    if (window) {
        if (window.isMaximized) {
            restoreWindow(windowId);
        } else {
            window.isMaximized = true;
            window.isMinimized = false;
            focusWindow(windowId);
        }
    }
};

const restoreWindow = (windowId: string) => {
    const window = windows.value.find((w) => w.id === windowId);
    if (window) {
        window.isMinimized = false;
        window.isMaximized = false;
        focusWindow(windowId);
    }
};

const focusWindow = (windowId: string) => {
    const window = windows.value.find((w) => w.id === windowId);
    if (window) {
        window.zIndex = nextZIndex.value++;
        activeWindowId.value = windowId;

        // 如果窗口被最小化，恢复它
        if (window.isMinimized) {
            window.isMinimized = false;
        }
    }
};

const updateWindow = (payload: { id: string; updates: Partial<WindowInstance> }) => {
    const window = windows.value.find((w) => w.id === payload.id);
    if (window) {
        Object.assign(window, payload.updates);
    }
};

const toggleWindow = (type: WindowType) => {
    const existingWindow = windows.value.find((w) => w.type === type);

    if (existingWindow) {
        if (existingWindow.isMinimized) {
            restoreWindow(existingWindow.id);
        } else if (activeWindowId.value === existingWindow.id) {
            minimizeWindow(existingWindow.id);
        } else {
            focusWindow(existingWindow.id);
        }
    } else {
        openWindow({ type });
    }
};

const getWindowTitle = (type: WindowType, data: Record<string, any>): string => {
    switch (type) {
        case 'terminal':
            return data.podName && data.containerName ? `${data.podName} (${data.containerName})` : '终端';
        case 'ai-assistant':
            return 'AI 助手';
        default:
            return data.title || '窗口';
    }
};

// 事件监听器
const handleWindowOpen = (payload: { type: WindowType; data?: Record<string, any>; options?: Partial<WindowInstance> }) => {
    openWindow(payload);
};

const handleWindowClose = (windowId: string) => {
    closeWindow(windowId);
};

const handleWindowMinimize = (windowId: string) => {
    minimizeWindow(windowId);
};

const handleWindowMaximize = (windowId: string) => {
    maximizeWindow(windowId);
};

const handleWindowRestore = (windowId: string) => {
    restoreWindow(windowId);
};

const handleWindowFocus = (windowId: string) => {
    focusWindow(windowId);
};

const handleWindowUpdate = (payload: { id: string; updates: Partial<WindowInstance> }) => {
    updateWindow(payload);
};

const handleWindowToggle = (type: WindowType) => {
    toggleWindow(type);
};

// 向后兼容的事件处理器
const handleTerminalOpen = (payload: { podName: string; containerName?: string; mode?: 'shell' | 'attach' }) => {
    openWindow({
        type: 'terminal',
        data: {
            podName: payload.podName,
            containerName: payload.containerName || 'main',
            mode: payload.mode || 'shell',
            isConnected: false,
        },
    });
};

const handleAIAssistantToggle = () => {
    toggleWindow('ai-assistant');
};

const handleAIAssistantOpen = () => {
    openWindow({ type: 'ai-assistant' });
};

const handleAIAssistantClose = () => {
    const aiWindow = windows.value.find((w) => w.type === 'ai-assistant');
    if (aiWindow) {
        closeWindow(aiWindow.id);
    }
};

// 生命周期
onMounted(() => {
    // 新的窗口管理事件
    eventBus.on('window:open', handleWindowOpen);
    eventBus.on('window:close', handleWindowClose);
    eventBus.on('window:minimize', handleWindowMinimize);
    eventBus.on('window:maximize', handleWindowMaximize);
    eventBus.on('window:restore', handleWindowRestore);
    eventBus.on('window:focus', handleWindowFocus);
    eventBus.on('window:update', handleWindowUpdate);
    eventBus.on('window:toggle', handleWindowToggle);

    // 向后兼容的事件
    eventBus.on('terminal:open', handleTerminalOpen);
    eventBus.on('ai-assistant:toggle', handleAIAssistantToggle);
    eventBus.on('ai-assistant:open', handleAIAssistantOpen);
    eventBus.on('ai-assistant:close', handleAIAssistantClose);
});

onUnmounted(() => {
    // 移除新的窗口管理事件
    eventBus.off('window:open', handleWindowOpen);
    eventBus.off('window:close', handleWindowClose);
    eventBus.off('window:minimize', handleWindowMinimize);
    eventBus.off('window:maximize', handleWindowMaximize);
    eventBus.off('window:restore', handleWindowRestore);
    eventBus.off('window:focus', handleWindowFocus);
    eventBus.off('window:update', handleWindowUpdate);
    eventBus.off('window:toggle', handleWindowToggle);

    // 移除向后兼容的事件
    eventBus.off('terminal:open', handleTerminalOpen);
    eventBus.off('ai-assistant:toggle', handleAIAssistantToggle);
    eventBus.off('ai-assistant:open', handleAIAssistantOpen);
    eventBus.off('ai-assistant:close', handleAIAssistantClose);
});

// 暴露方法
defineExpose({
    openWindow,
    closeWindow,
    minimizeWindow,
    maximizeWindow,
    restoreWindow,
    focusWindow,
    updateWindow,
    toggleWindow,
    windows,
    activeWindowId,
});
</script>

<style scoped>
.window-manager {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 1000;
}

.windows-container {
    position: relative;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.windows-container > * {
    pointer-events: auto;
}
</style>
