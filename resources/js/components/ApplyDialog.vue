<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useFileDropzone } from '@/composables/useFileDropzone';
import { useYamlEditor } from '@/composables/useYamlEditor';
import { convertContentToCustomYaml } from '@/lib/dockerConvert';
import { FileText, Loader2, Send, Upload } from 'lucide-vue-next';
import { onMounted } from 'vue';

interface Props {
    open: boolean;
}

interface Emits {
    (e: 'update:open', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const {
    yamlText,
    isSubmittingState,
    hasWorkspaceState,
    currentProgressValue,
    totalProgressValue,
    progressValue,
    currentApplyingText,
    initializeDefaultYaml,
    submitYaml,
} = useYamlEditor();

// 拖拽功能
const { isDragging, onDragEnter, onDragLeave, onDragOver, onDrop } = useFileDropzone({
    onFileRead: (content: string) => {
        yamlText.value = convertContentToCustomYaml(content);
    },
});

// 初始化默认 YAML
onMounted(() => {
    initializeDefaultYaml();
});

// 提交 YAML
const handleSubmit = async () => {
    await submitYaml(() => {
        // 成功后关闭对话框
        emit('update:open', false);
    });
};

// 关闭对话框
const handleClose = () => {
    emit('update:open', false);
};
</script>

<template>
    <Dialog :open="props.open" @update:open="handleClose">
        <DialogContent class="flex max-h-[100dvh] max-w-4xl flex-col">
            <DialogHeader>
                <DialogTitle class="flex items-center gap-2">
                    <FileText class="size-5" />
                    YAML 资源应用
                </DialogTitle>
                <DialogDescription>
                    {{ hasWorkspaceState ? '输入 YAML 配置来创建或更新资源' : '请先选择工作空间' }}
                </DialogDescription>
            </DialogHeader>

            <!-- 进度显示 -->
            <div v-if="isSubmittingState" class="space-y-3 border-b pb-4">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-muted-foreground">应用进度</span>
                    <span class="font-medium">{{ currentProgressValue }} / {{ totalProgressValue }}</span>
                </div>
                <Progress :model-value="progressValue" class="h-2" />
                <div v-if="currentApplyingText" class="text-sm text-muted-foreground">正在处理: {{ currentApplyingText }}</div>
            </div>

            <!-- 无工作空间提示 -->
            <div v-if="!hasWorkspaceState" class="flex flex-1 items-center justify-center">
                <div class="text-center">
                    <FileText class="mx-auto h-12 w-12 text-muted-foreground" />
                    <p class="mt-4 text-sm text-muted-foreground">请先选择一个工作空间来应用资源</p>
                </div>
            </div>

            <!-- YAML 编辑器 -->
            <div v-else class="flex flex-1 flex-col gap-4">
                <div class="space-y-2">
                    <Label for="yaml-content">YAML 配置</Label>
                    <div class="text-xs text-muted-foreground">
                        使用 <code>---</code> 分隔多个资源配置。每个配置必须包含 <code>kind</code> 和 <code>name</code> 字段。 支持使用
                        <code>${`{变量名}`}</code> 格式的变量。可以拖拽 YAML/YML 文件到编辑器中导入。
                    </div>
                </div>

                <div class="flex flex-1 gap-4">
                    <!-- 左侧：YAML 编辑器 -->
                    <div class="relative flex-1" @dragenter="onDragEnter" @dragleave="onDragLeave" @dragover="onDragOver" @drop="onDrop">
                        <!-- 拖拽覆盖层 -->
                        <div
                            v-if="isDragging"
                            class="absolute inset-0 z-10 flex items-center justify-center rounded-md border-2 border-dashed border-primary bg-primary/5"
                        >
                            <div class="text-center">
                                <Upload class="mx-auto h-8 w-8 text-primary" />
                                <p class="mt-2 text-sm font-medium text-primary">释放文件以导入 YAML</p>
                                <p class="text-xs text-muted-foreground">支持 .yaml 和 .yml 文件</p>
                            </div>
                        </div>

                        <ScrollArea class="h-[400px] rounded-md border">
                            <textarea
                                id="yaml-content"
                                v-model="yamlText"
                                class="h-full min-h-[400px] w-full resize-none border-0 bg-transparent p-4 font-mono text-sm focus:ring-0 focus:outline-none"
                                placeholder="输入 YAML 配置..."
                                spellcheck="false"
                                :disabled="isSubmittingState"
                            />
                        </ScrollArea>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-end gap-2">
                    <Button variant="outline" @click="handleClose" :disabled="isSubmittingState"> 取消 </Button>
                    <Button @click="handleSubmit" :disabled="isSubmittingState || !hasWorkspaceState">
                        <Loader2 v-if="isSubmittingState" class="mr-2 h-4 w-4 animate-spin" />
                        <Send v-else class="mr-2 h-4 w-4" />
                        {{ isSubmittingState ? '应用中...' : '应用资源' }}
                    </Button>
                </div>
            </div>
        </DialogContent>
    </Dialog>
</template>
