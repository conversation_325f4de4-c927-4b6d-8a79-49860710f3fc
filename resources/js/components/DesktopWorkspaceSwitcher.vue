<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useWorkspaceStore } from '@/stores/workspaceStore';
import { Link, router, usePage } from '@inertiajs/vue3';
import { Building2, Check } from 'lucide-vue-next';
import { computed, onMounted } from 'vue';

const workspaceStore = useWorkspaceStore();
const page = usePage();

// 当前工作区 (来自 Inertia props)
const currentWorkspace = computed(() => (page.props.auth as any)?.workspace || null);

// 在 Desktop 页面首次挂载时，如 store 中空则拉取列表
onMounted(() => {
    if (workspaceStore.workspaces.length === 0 && page.props.auth?.user) {
        workspaceStore.fetchWorkspaces();
    }
});

const workspaces = computed(() => workspaceStore.workspaces);

const switchWorkspace = async (workspaceId: number) => {
    try {
        await workspaceStore.switchWorkspace(workspaceId);
        router.visit(window.location.href);
    } catch (e) {
        console.error('切换工作区失败', e);
    }
};
</script>

<template>
    <DropdownMenu>
        <DropdownMenuTrigger as-child>
            <Button variant="ghost" size="sm" class="flex items-center gap-2">
                <Building2 class="h-4 w-4" />
                <span class="text-sm font-medium">{{ currentWorkspace?.name || '选择工作区' }}</span>
            </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent class="min-w-40" align="start">
            <template v-if="workspaces.length">
                <DropdownMenuItem v-for="ws in workspaces" :key="ws.id" @select="switchWorkspace(ws.id)" class="flex items-center gap-2">
                    <Building2 class="h-4 w-4" />
                    <span>{{ ws.name }}</span>
                    <Check v-if="currentWorkspace && currentWorkspace.id === ws.id" class="ml-auto h-4 w-4" />
                </DropdownMenuItem>
            </template>
            <template v-else>
                <div class="px-4 py-3 text-xs text-muted-foreground">暂无可用工作区</div>
            </template>
            <DropdownMenuItem as-child>
                <Link :href="route('workspaces.create')" class="flex items-center gap-2">
                    <Building2 class="h-4 w-4" />
                    <span>创建工作区</span>
                </Link>
            </DropdownMenuItem>
        </DropdownMenuContent>
    </DropdownMenu>
</template>
