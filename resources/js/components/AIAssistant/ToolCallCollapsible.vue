<template>
    <div class="rounded-lg border border-border bg-background/50 text-xs dark:bg-background/30">
        <!-- 工具调用头部 - 可点击折叠/展开 -->
        <div class="flex cursor-pointer items-center gap-2 p-3 font-medium hover:bg-muted/20" @click="toggleCollapsed">
            <!-- 折叠/展开图标 -->
            <ChevronRight class="h-3 w-3 transition-transform duration-200" :class="{ 'rotate-90': !isCollapsed }" />

            <!-- 工具图标 -->
            <span class="flex h-4 w-4 items-center justify-center rounded bg-primary/20 text-xs text-primary"> 🔧 </span>

            <!-- 标题 -->

            <!-- 工具名称（如果有） -->
            <span v-if="toolName" class="ml-auto text-xs text-muted-foreground">
                {{ toolName }}
            </span>

            <!-- 成功/失败状态 -->
            <span v-if="isToolSuccess" class="text-green-600 dark:text-green-400">✓</span>
            <span v-else class="text-red-600 dark:text-red-400">✗</span>
        </div>

        <!-- 工具调用详细内容 - 可折叠 -->
        <div v-if="!isCollapsed" class="border-t border-border/50 p-3 pt-2">
            <!-- 工具调用ID -->
            <div v-if="toolMessage.tool_call_id" class="mb-2">
                <div class="font-medium text-muted-foreground">调用ID:</div>
                <code class="text-xs">{{ toolMessage.tool_call_id }}</code>
            </div>

            <!-- 工具结果 -->
            <div>
                <div class="mb-2 font-medium text-muted-foreground">结果:</div>
                <div class="mt-1">
                    <pre
                        class="overflow-x-auto rounded bg-muted/30 p-3 text-xs leading-relaxed break-words whitespace-pre-wrap"
                        style="max-height: 300px; overflow-y: auto"
                        >{{ formatToolContent(toolMessage.content) }}</pre
                    >
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ChevronRight } from 'lucide-vue-next';
import { computed, ref } from 'vue';

import type { Message } from '@/stores/aiAssistantStore';

interface Props {
    toolMessage: Message;
    defaultCollapsed?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    defaultCollapsed: true,
});

// 状态
const isCollapsed = ref(props.defaultCollapsed);

// 计算属性
const isToolSuccess = computed(() => {
    if (!props.toolMessage.content) return false;

    try {
        const result = JSON.parse(props.toolMessage.content);
        return result.success !== false;
    } catch {
        // 如果不是JSON格式，假设成功
        return true;
    }
});

const toolName = computed(() => {
    return '工具调用';
    // console.log(props.toolMessage);
    // 尝试从tool_call_id或内容中提取工具名称
    if (props.toolMessage.tool_call_id) {
        return props.toolMessage.tool_call_id;
    }

    if (props.toolMessage.content) {
        try {
            const parsed = JSON.parse(props.toolMessage.content);
            if (parsed.tool_name || parsed.function_name) {
                return parsed.tool_name || parsed.function_name;
            }
        } catch {
            // 忽略解析错误
        }
    }

    return null;
});

// 方法
const toggleCollapsed = () => {
    isCollapsed.value = !isCollapsed.value;
};

const formatToolContent = (content: string | null) => {
    if (!content) return '';

    try {
        const parsed = JSON.parse(content);
        return JSON.stringify(parsed, null, 2);
    } catch {
        return content;
    }
};
</script>

<style scoped>
/* 自定义滚动条样式 */
pre::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

pre::-webkit-scrollbar-track {
    background: transparent;
}

pre::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 2px;
}

pre::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
}
</style>
