<template>
    <div class="ai-assistant-content flex h-full flex-col">
        <!-- 消息列表 -->
        <div ref="messagesContainer" class="flex-1 space-y-4 overflow-y-auto p-4">
            <div v-if="messages.length === 0" class="flex h-full items-center justify-center text-muted-foreground">
                <div class="text-center">
                    <Sparkles class="mx-auto mb-4 h-12 w-12 text-primary" />
                    <h3 class="mb-2 text-lg font-medium">AI 助手</h3>
                    <p class="text-sm" @click="console.log(aiStore.getCurrentWebContent())">我是您的 AI 助手，有什么可以帮助您的吗？</p>
                </div>
            </div>

            <div v-for="message in messages" :key="message.id" class="message-item">
                <!-- 工具调用消息使用特殊组件显示 -->
                <ToolCallCollapsible v-if="message.role === 'tool'" :tool-message="message" :default-collapsed="true" class="mx-8" />

                <!-- 普通消息显示，只有当 content 不为空时才显示 -->
                <div
                    v-else-if="message.content && message.content.trim()"
                    :class="['flex gap-3 rounded-lg p-3', message.role === 'user' ? 'ml-8 bg-primary/10' : 'mr-8 bg-muted/50']"
                >
                    <div class="flex-shrink-0">
                        <div
                            :class="[
                                'flex h-8 w-8 items-center justify-center rounded-full',
                                message.role === 'user' ? 'bg-primary text-primary-foreground' : 'border bg-background',
                            ]"
                        >
                            <User v-if="message.role === 'user'" class="h-4 w-4" />
                            <Sparkles v-else class="h-4 w-4" />
                        </div>
                    </div>
                    <div class="min-w-0 flex-1">
                        <div class="mb-1 text-sm font-medium">
                            {{ message.role === 'user' ? '您' : 'AI 助手' }}
                        </div>
                        <div class="prose prose-sm max-w-none dark:prose-invert" v-html="formatMessage(message.content)" />
                        <div class="mt-2 text-xs text-muted-foreground" v-if="message.created_at">
                            {{ formatTime(message.created_at) }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载指示器 -->
            <div v-if="isLoading" class="mr-8 flex gap-3 rounded-lg bg-muted/50 p-3">
                <div class="flex-shrink-0">
                    <div class="flex h-8 w-8 items-center justify-center rounded-full border bg-background">
                        <Sparkles class="h-4 w-4" />
                    </div>
                </div>
                <div class="flex-1">
                    <div class="mb-1 text-sm font-medium">AI 助手</div>
                    <div class="flex items-center gap-2">
                        <Loader2Icon class="h-4 w-4 animate-spin" />
                        <span class="text-sm text-muted-foreground">正在思考...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="border-t border-border p-4">
            <form @submit.prevent="sendMessage" class="flex gap-2">
                <Button size="sm" class="px-3" @click="clearConversation">
                    <Trash2 class="h-4 w-4" />
                </Button>
                <Input v-model="userInput" placeholder="输入您的问题..." :disabled="isLoading" class="flex-1" @keydown="handleKeyDown" />
                <Button type="submit" :disabled="!userInput.trim() || isLoading" size="sm" class="px-3">
                    <SendIcon class="h-4 w-4" />
                </Button>
            </form>
        </div>
    </div>
</template>

<script setup lang="ts">
import ToolCallCollapsible from '@/components/AIAssistant/ToolCallCollapsible.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAIAssistantStore } from '@/stores/aiAssistantStore';
import { format } from 'date-fns';
import { Loader2Icon, SendIcon, Sparkles, Trash2, User } from 'lucide-vue-next';
import { marked } from 'marked';
import { computed, nextTick, onMounted, ref, watch } from 'vue';

interface Emits {
    'loading-change': [isLoading: boolean];
}

const emit = defineEmits<Emits>();

// Store
const aiStore = useAIAssistantStore();

// Refs
const messagesContainer = ref<HTMLElement>();
const userInput = ref('');

// 计算属性
const messages = computed(() => aiStore.currentMessages);
const isLoading = computed(() => aiStore.isLoading);

// 监听加载状态变化
watch(isLoading, (newValue) => {
    emit('loading-change', newValue);
});

// 格式化消息内容
const formatMessage = (content: string): string => {
    if (!content) return '';

    try {
        // 移除网页内容标签用于显示
        const displayContent = content.replace(/<current_web_content>.*?<\/current_web_content>/s, '').trim();

        const result = marked(displayContent, {
            breaks: true,
            gfm: true,
        });

        // 确保返回字符串类型
        return typeof result === 'string' ? result : displayContent;
    } catch {
        return content;
    }
};

// 格式化时间
const formatTime = (dateStr: string): string => {
    if (!dateStr) return '';
    try {
        return format(new Date(dateStr), 'HH:mm:ss');
    } catch {
        return dateStr;
    }
};

// 发送消息
const sendMessage = async () => {
    const message = userInput.value.trim();
    if (!message || isLoading.value) return;

    userInput.value = '';

    try {
        await aiStore.sendMessage(message);
        await scrollToBottom();
    } catch (error) {
        console.error('发送消息失败:', error);
    }
};

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
    // 只有在按下 Enter 键且没有组合键（Shift、Ctrl、Alt、Meta）时才提交
    if (event.key === 'Enter' && !event.shiftKey && !event.ctrlKey && !event.altKey && !event.metaKey) {
        // 检查是否正在输入法组合中
        if (event.isComposing) {
            return;
        }

        event.preventDefault();
        sendMessage();
    }
};

// 滚动到底部
const scrollToBottom = async () => {
    await nextTick();
    if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
};

const clearConversation = async () => {
    try {
        await aiStore.createConversation();
    } catch (error) {
        console.error('清空对话失败:', error);
    }
};

// 监听消息变化，自动滚动到底部
watch(
    () => messages.value.length,
    () => {
        scrollToBottom();
    },
);

// 组件挂载时滚动到底部
onMounted(() => {
    scrollToBottom();
});
</script>

<style scoped>
.ai-assistant-content {
    background: var(--background);
}

.message-item {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
}

/* Prose 样式调整 */
:deep(.prose) {
    color: inherit;
}

:deep(.prose h1),
:deep(.prose h2),
:deep(.prose h3),
:deep(.prose h4),
:deep(.prose h5),
:deep(.prose h6) {
    color: inherit;
    margin-top: 1em;
    margin-bottom: 0.5em;
}

:deep(.prose p) {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

:deep(.prose code) {
    background: hsl(var(--muted));
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

:deep(.prose pre) {
    background: hsl(var(--muted));
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
}

:deep(.prose pre code) {
    background: transparent;
    padding: 0;
}

:deep(.prose ul),
:deep(.prose ol) {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

:deep(.prose li) {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
}
</style>
