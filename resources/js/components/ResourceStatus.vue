<template>
    <div class="resource-status rounded-lg bg-white p-6 shadow dark:bg-gray-800">
        <div class="mb-4 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">资源状态</h3>
            <div class="flex items-center space-x-2">
                <div class="h-2 w-2 rounded-full" :class="isListening ? 'bg-green-500' : 'bg-red-500'" />
                <span class="text-sm text-gray-600 dark:text-gray-300">
                    {{ isListening ? '监听中' : '未监听' }}
                </span>
                <button @click="refreshResources" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400">刷新</button>
            </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="!isLoaded && isListening" class="py-4 text-center">
            <div class="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">正在等待数据...</p>
        </div>

        <!-- 资源统计 -->
        <div v-else-if="isLoaded" class="space-y-4">
            <div class="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                <div v-for="(count, type) in resourceCounts" :key="type" class="rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                        {{ getResourceTypeDisplayName(type as keyof ResourceCollections) }}
                    </div>
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ count }}
                    </div>
                </div>
            </div>

            <!-- 最后更新时间 -->
            <div v-if="lastUpdate" class="text-center text-xs text-gray-500 dark:text-gray-400">最后更新: {{ formatTime(lastUpdate) }}</div>

            <!-- 实时变更日志 -->
            <div class="mt-6">
                <h4 class="mb-2 text-sm font-medium text-gray-900 dark:text-white">实时变更 (最近 {{ recentChanges.length }} 条)</h4>
                <div class="max-h-40 space-y-2 overflow-y-auto">
                    <div v-for="change in recentChanges" :key="change.id" class="rounded bg-gray-50 p-2 text-xs dark:bg-gray-700">
                        <div class="flex items-start justify-between">
                            <div>
                                <span class="font-medium">{{ change.resourceType }}</span>
                                <span class="ml-2 text-gray-600 dark:text-gray-300">
                                    +{{ change.created }} ~{{ change.updated }} -{{ change.deleted }}
                                </span>
                            </div>
                            <span class="text-gray-500 dark:text-gray-400">
                                {{ formatTime(change.timestamp) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 未监听状态 -->
        <div v-else class="py-8 text-center">
            <div class="text-gray-400 dark:text-gray-500">
                <svg class="mx-auto mb-2 h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                    />
                </svg>
                <p class="text-sm">请选择工作区以开始监听资源变更</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useResourcesData } from '@/composables/useResourcesData';
import type { ResourceChangeEvent, ResourceCollections } from '@/stores/resourcesStore';
import { onMounted, onUnmounted, ref } from 'vue';

// 资源变更记录
interface ChangeRecord {
    id: string;
    resourceType: string;
    created: number;
    updated: number;
    deleted: number;
    timestamp: string;
}

const { isLoaded, lastUpdate, isListening, resourceCounts, refreshResources, onResourceChange, getResourceTypeDisplayName } = useResourcesData();

const recentChanges = ref<ChangeRecord[]>([]);

// 监听资源变更
let unsubscribeResourceChange: (() => void) | null = null;

onMounted(() => {
    // 监听资源变更事件
    unsubscribeResourceChange = onResourceChange((event: ResourceChangeEvent) => {
        // 添加到变更记录
        const changeRecord: ChangeRecord = {
            id: `${event.resource_type}-${Date.now()}`,
            resourceType: getResourceTypeDisplayName(event.resource_type as keyof ResourceCollections),
            created: event.summary.created_count,
            updated: event.summary.updated_count,
            deleted: event.summary.deleted_count,
            timestamp: event.timestamp,
        };

        recentChanges.value.unshift(changeRecord);

        // 只保留最近20条记录
        if (recentChanges.value.length > 20) {
            recentChanges.value = recentChanges.value.slice(0, 20);
        }
    });
});

onUnmounted(() => {
    // 清理监听器
    if (unsubscribeResourceChange) {
        unsubscribeResourceChange();
    }
});

// 格式化时间
const formatTime = (timeString: string) => {
    const date = new Date(timeString);
    return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
    });
};
</script>

<style scoped>
/* 可以添加自定义样式 */
.resource-status {
    transition: all 0.3s ease;
}
</style>
