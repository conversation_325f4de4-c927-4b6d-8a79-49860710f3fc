<template>
    <div class="pricing-history-chart">
        <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-4">
                <div v-if="props.selectedCluster" class="flex items-center space-x-2 rounded-lg bg-primary/10 px-3 py-2">
                    <span class="text-sm font-medium">当前集群:</span>
                    <span class="text-sm font-semibold text-primary">{{ props.selectedCluster.name }}</span>
                </div>

                <Select v-model="selectedPeriod" @update:model-value="loadPricingHistory">
                    <SelectTrigger class="w-32">
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="1h">1小时</SelectItem>
                        <SelectItem value="6h">6小时</SelectItem>
                        <SelectItem value="1d">1天</SelectItem>
                        <SelectItem value="7d">7天</SelectItem>
                        <SelectItem value="30d">30天</SelectItem>
                    </SelectContent>
                </Select>

                <Select v-model="selectedResource" @update:model-value="updateChart">
                    <SelectTrigger class="w-40">
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">全部资源</SelectItem>
                        <SelectItem value="memory">内存价格</SelectItem>
                        <SelectItem value="cpu">CPU价格</SelectItem>
                        <SelectItem value="storage">存储价格</SelectItem>
                        <SelectItem value="loadbalancer">负载均衡价格</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            <div v-if="isLoading" class="flex items-center text-sm text-muted-foreground">
                <LoaderCircle class="mr-2 h-4 w-4 animate-spin" />
                加载中...
            </div>
        </div>

        <div v-if="!selectedClusterId" class="py-16 text-center text-muted-foreground">
            <TrendingUp class="mx-auto mb-4 h-16 w-16 text-muted-foreground/30" />
            <h3 class="mb-2 text-lg font-medium">请在价格计算器中选择一个集群</h3>
            <p class="text-sm">选择集群后即可查看价格变动趋势</p>
        </div>

        <div v-else-if="error" class="py-16 text-center">
            <AlertCircle class="mx-auto mb-4 h-16 w-16 text-destructive/50" />
            <h3 class="mb-2 text-lg font-medium text-destructive">加载失败</h3>
            <p class="mb-4 text-sm text-destructive">{{ error }}</p>
            <Button variant="outline" size="sm" @click="loadPricingHistory">
                <RefreshCw class="mr-2 h-4 w-4" />
                重试
            </Button>
        </div>

        <div v-else-if="!historyData?.kline_data || !hasKlineData" class="py-16 text-center text-muted-foreground">
            <BarChart3 class="mx-auto mb-4 h-16 w-16 text-muted-foreground/30" />
            <h3 class="mb-2 text-lg font-medium">暂无价格变动数据</h3>
            <p class="text-sm">当价格发生变化时，这里将显示历史趋势</p>
        </div>

        <div v-else class="space-y-6">
            <!-- 图表标题和图例 -->
            <div class="border-b pb-4">
                <h3 class="text-xl font-semibold text-foreground">{{ historyData.cluster_name }} - 价格变动趋势</h3>
                <div class="mt-2 flex items-center justify-between">
                    <p class="text-sm text-muted-foreground">
                        时间范围：{{ getPeriodLabel(selectedPeriod) }} | 资源类型：{{ getResourceLabel(selectedResource) }}
                    </p>
                    <div class="flex items-center space-x-4 text-xs">
                        <div class="flex items-center space-x-1">
                            <div class="h-3 w-3 rounded-full bg-green-500"></div>
                            <span class="text-muted-foreground">价格上涨</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <div class="h-3 w-3 rounded-full bg-red-500"></div>
                            <span class="text-muted-foreground">价格下跌</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- K线图容器 -->
            <div class="rounded-lg border bg-background/50 p-4">
                <div ref="chartContainer" class="h-96 w-full"></div>
            </div>

            <!-- 当前价格信息 -->
            <div v-if="currentPrices" class="grid grid-cols-2 gap-4 sm:grid-cols-4">
                <div
                    v-if="selectedResource === 'all' || selectedResource === 'memory'"
                    class="rounded-lg border bg-background/50 p-4 transition-colors hover:bg-background/80"
                >
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-foreground">内存价格</span>
                        <div class="flex items-center space-x-1">
                            <span :class="getPriceChangeClass(currentPrices.memory_gb_change)" class="text-sm font-semibold">
                                ¥{{ formatPrice(currentPrices.memory_gb_price) }}
                            </span>
                            <component
                                :is="getPriceChangeIcon(currentPrices.memory_gb_change)"
                                :class="getPriceChangeClass(currentPrices.memory_gb_change)"
                                class="h-3 w-3"
                            />
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-muted-foreground">每GB/月</p>
                </div>

                <div
                    v-if="selectedResource === 'all' || selectedResource === 'cpu'"
                    class="rounded-lg border bg-background/50 p-4 transition-colors hover:bg-background/80"
                >
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-foreground">CPU价格</span>
                        <div class="flex items-center space-x-1">
                            <span :class="getPriceChangeClass(currentPrices.cpu_core_change)" class="text-sm font-semibold">
                                ¥{{ formatPrice(currentPrices.cpu_core_price) }}
                            </span>
                            <component
                                :is="getPriceChangeIcon(currentPrices.cpu_core_change)"
                                :class="getPriceChangeClass(currentPrices.cpu_core_change)"
                                class="h-3 w-3"
                            />
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-muted-foreground">每Core/月</p>
                </div>

                <div
                    v-if="selectedResource === 'all' || selectedResource === 'storage'"
                    class="rounded-lg border bg-background/50 p-4 transition-colors hover:bg-background/80"
                >
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-foreground">存储价格</span>
                        <div class="flex items-center space-x-1">
                            <span :class="getPriceChangeClass(currentPrices.storage_gb_change)" class="text-sm font-semibold">
                                ¥{{ formatPrice(currentPrices.storage_gb_price) }}
                            </span>
                            <component
                                :is="getPriceChangeIcon(currentPrices.storage_gb_change)"
                                :class="getPriceChangeClass(currentPrices.storage_gb_change)"
                                class="h-3 w-3"
                            />
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-muted-foreground">每GB/月</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import axios from '@/lib/axios';
import type { Chart } from 'klinecharts';
import { dispose, init } from 'klinecharts';
import { AlertCircle, BarChart3, LoaderCircle, Minus, RefreshCw, TrendingDown, TrendingUp } from 'lucide-vue-next';
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

// Props 定义
interface Props {
    selectedCluster?: { id: number; name: string; description?: string } | null;
}

const props = withDefaults(defineProps<Props>(), {
    selectedCluster: null,
});

// 响应式数据
const selectedPeriod = ref<string>('7d');
const selectedResource = ref<string>('all');
const historyData = ref<any>(null);
const currentPrices = ref<any>(null);
const isLoading = ref(false);
const error = ref('');

// 计算属性：当前选中的集群ID
const selectedClusterId = computed(() => props.selectedCluster?.id?.toString() || '');
const chartContainer = ref<HTMLElement>();
const chartInstance = ref<Chart | null>(null);

// 检查是否有K线数据
const hasKlineData = computed(() => {
    if (!historyData.value?.kline_data) return false;
    const selectedData = historyData.value.kline_data[selectedResource.value];
    return selectedData && selectedData.length > 0;
});

// 移除了获取集群列表的函数，现在从 props 接收选中的集群

// 加载价格历史数据
const loadPricingHistory = async () => {
    if (!selectedClusterId.value) return;

    isLoading.value = true;
    error.value = '';

    try {
        const response = await axios.get(`/api/clusters/${selectedClusterId.value}/pricing-history`, {
            params: {
                period: selectedPeriod.value,
                limit: 200,
            },
        });

        historyData.value = response.data;
        currentPrices.value = response.data.current_prices;

        // 等待DOM更新后渲染图表
        await nextTick();
        renderChart();
    } catch (err: any) {
        error.value = err.response?.data?.message || '加载价格历史失败';
        console.error('Failed to load pricing history:', err);
    } finally {
        isLoading.value = false;
    }
};

// 渲染K线图
const renderChart = () => {
    if (!chartContainer.value || !hasKlineData.value) return;

    // 销毁现有图表实例
    if (chartInstance.value) {
        dispose(chartInstance.value);
    }

    try {
        // 创建新的图表实例
        chartInstance.value = init(chartContainer.value);

        if (!chartInstance.value) return;

        // 配置图表样式
        chartInstance.value.setStyles({
            candle: {
                type: 'candle_solid',
                bar: {
                    upColor: '#10B981', // 绿色表示上涨
                    downColor: '#EF4444', // 红色表示下跌
                    noChangeColor: '#6B7280',
                },
            },
        } as any);

        // 设置数据
        updateChart();
    } catch (error) {
        console.error('Failed to create chart:', error);
    }
};

// 更新图表数据
const updateChart = () => {
    if (!chartInstance.value || !hasKlineData.value) return;

    try {
        const klineData = historyData.value.kline_data[selectedResource.value];
        if (klineData && klineData.length > 0) {
            // 转换数据格式为 KLineChart 需要的格式
            const formattedData = klineData.map((item: any) => ({
                timestamp: item.timestamp,
                open: item.open,
                high: item.high,
                low: item.low,
                close: item.close,
            }));

            chartInstance.value.applyNewData(formattedData);
        }
    } catch (error) {
        console.error('Failed to update chart:', error);
    }
};

// 获取周期标签
const getPeriodLabel = (period: string): string => {
    const labels: Record<string, string> = {
        '1h': '最近1小时',
        '6h': '最近6小时',
        '1d': '最近1天',
        '7d': '最近7天',
        '30d': '最近30天',
    };
    return labels[period] || period;
};

// 获取资源标签
const getResourceLabel = (resource: string): string => {
    const labels: Record<string, string> = {
        all: '全部资源',
        memory: '内存价格',
        cpu: 'CPU价格',
        storage: '存储价格',
        loadbalancer: '负载均衡价格',
    };
    return labels[resource] || resource;
};

// 格式化价格
const formatPrice = (price: number | undefined | null): string => {
    if (price === undefined || price === null || isNaN(price)) {
        return '0.00';
    }
    return Number(price).toFixed(2);
};

// 获取价格变化样式类
const getPriceChangeClass = (change: 'up' | 'down' | 'same'): string => {
    switch (change) {
        case 'up':
            return 'text-green-600';
        case 'down':
            return 'text-red-600';
        default:
            return 'text-muted-foreground';
    }
};

// 获取价格变化图标
const getPriceChangeIcon = (change: 'up' | 'down' | 'same') => {
    switch (change) {
        case 'up':
            return TrendingUp;
        case 'down':
            return TrendingDown;
        default:
            return Minus;
    }
};

// 响应式处理
const handleResize = () => {
    if (chartInstance.value) {
        chartInstance.value.resize();
    }
};

// 处理价格实时更新
const handlePricingUpdate = (event: any) => {
    // 如果当前选中的集群价格发生变化，重新加载数据
    if (selectedClusterId.value && event.cluster_id.toString() === selectedClusterId.value) {
        // 立即更新当前价格信息
        if (currentPrices.value && event.pricing) {
            // 更新当前价格数据 - 使用新的 API 字段名称
            if (event.pricing.memory_gb) {
                currentPrices.value.memory_gb_price = event.pricing.memory_gb.price_per_hour;
            }
            if (event.pricing.cpu_core) {
                currentPrices.value.cpu_core_price = event.pricing.cpu_core.price_per_hour;
            }
            if (event.pricing.storage_gb) {
                currentPrices.value.storage_gb_price = event.pricing.storage_gb.price_per_hour;
            }
            if (event.pricing.loadbalancer) {
                currentPrices.value.loadbalancer_price = event.pricing.loadbalancer.price_per_hour;
            }

            // 根据变化确定价格变化趋势
            if (event.changes) {
                Object.keys(event.changes).forEach((field) => {
                    const change = event.changes[field];
                    const oldValue = parseFloat(change.old);
                    const newValue = parseFloat(change.new);

                    // 根据新的字段名称映射价格变化
                    if (field === 'memory_gb') {
                        currentPrices.value.memory_gb_change = newValue > oldValue ? 'up' : newValue < oldValue ? 'down' : 'same';
                    } else if (field === 'cpu_core') {
                        currentPrices.value.cpu_core_change = newValue > oldValue ? 'up' : newValue < oldValue ? 'down' : 'same';
                    } else if (field === 'storage_gb') {
                        currentPrices.value.storage_gb_change = newValue > oldValue ? 'up' : newValue < oldValue ? 'down' : 'same';
                    } else if (field === 'loadbalancer') {
                        currentPrices.value.loadbalancer_change = newValue > oldValue ? 'up' : newValue < oldValue ? 'down' : 'same';
                    }
                });
            }
        }

        // 重新加载历史数据以更新K线图
        loadPricingHistory();

        console.log(`集群 ${event.cluster_name} 价格已更新`, event.changes);
    }
};

// 监听选择变化
watch([selectedClusterId, selectedPeriod], () => {
    if (selectedClusterId.value) {
        loadPricingHistory();
    }
});

watch(selectedResource, () => {
    updateChart();
});

// 监听 props.selectedCluster 变化
watch(
    () => props.selectedCluster,
    (newCluster) => {
        if (newCluster) {
            loadPricingHistory();
        } else {
            // 清空数据
            historyData.value = null;
            currentPrices.value = null;
        }
    },
    { immediate: true },
);

// 生命周期
onMounted(async () => {
    if (selectedClusterId.value) {
        await loadPricingHistory();
    }

    window.addEventListener('resize', handleResize);

    // 监听价格实时更新
    window.Echo.channel('cluster-pricing').listen('.pricing.updated', (event: any) => {
        handlePricingUpdate(event);
    });
});

onUnmounted(() => {
    if (chartInstance.value) {
        dispose(chartInstance.value);
    }
    window.removeEventListener('resize', handleResize);

    // 离开价格更新频道
    window.Echo.leave('cluster-pricing');
});
</script>

<style scoped>
.pricing-history-chart {
    width: 100%;
}

/* K线图容器样式 */
.pricing-history-chart :deep(.klinecharts-container) {
    border-radius: 0.5rem;
    background: hsl(var(--background));
}

/* 深色模式适配 */
.dark .pricing-history-chart :deep(.klinecharts-container) {
    background: hsl(var(--background));
}
</style>
