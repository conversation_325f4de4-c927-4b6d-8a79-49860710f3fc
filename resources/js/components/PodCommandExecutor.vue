<template>
    <Dialog v-model:open="isOpen">
        <DialogContent class="flex max-h-[80vh] max-w-4xl flex-col">
            <DialogHeader>
                <DialogTitle>命令执行 - {{ podName }}</DialogTitle>
                <DialogDescription> 在 Pod 中执行命令并查看输出 </DialogDescription>
            </DialogHeader>

            <div class="flex min-h-0 flex-1 flex-col space-y-4">
                <!-- 命令输入区 -->
                <div class="space-y-4 border-b pb-4">
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <Label for="container">容器</Label>
                            <Select v-model="selectedContainer" :disabled="isExecuting">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择容器" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="container in containers" :key="container.name" :value="container.name">
                                        {{ container.name }}
                                        <Badge v-if="container.ready === false" variant="secondary" class="ml-2"> 未就绪 </Badge>
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label for="timeout">超时时间（秒）</Label>
                            <Input id="timeout" v-model.number="timeout" type="number" min="1" max="300" :disabled="isExecuting" />
                        </div>
                    </div>

                    <div>
                        <Label for="command">命令</Label>
                        <Input
                            id="command"
                            v-model="command"
                            placeholder="输入要执行的命令，例如：ls -la"
                            :disabled="isExecuting"
                            @keyup.enter="executeCommand"
                        />
                    </div>

                    <div class="flex space-x-2">
                        <Button @click="executeCommand" :disabled="!command.trim() || isExecuting" class="flex-1">
                            <Play v-if="!isExecuting" class="mr-2 h-4 w-4" />
                            <Loader2 v-else class="mr-2 h-4 w-4 animate-spin" />
                            {{ isExecuting ? '执行中...' : '执行命令' }}
                        </Button>

                        <Button @click="clearTerminal" variant="outline" :disabled="isExecuting">
                            <Trash2 class="mr-2 h-4 w-4" />
                            清除
                        </Button>
                    </div>
                </div>

                <!-- 终端输出区 -->
                <div class="min-h-0 flex-1">
                    <div class="h-full rounded-md border bg-black">
                        <div ref="terminalContainer" class="h-full w-full p-2" style="min-height: 400px" />
                    </div>
                </div>

                <!-- 执行结果摘要 -->
                <div v-if="lastResult" class="border-t pt-4">
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <span class="font-medium">状态:</span>
                                <Badge :variant="lastResult.success ? 'default' : 'destructive'">
                                    {{ lastResult.success ? '成功' : '失败' }}
                                </Badge>
                            </div>

                            <div v-if="lastResult.exitCode !== null">
                                <span class="font-medium">退出码:</span>
                                <span class="ml-1">{{ lastResult.exitCode }}</span>
                            </div>

                            <div v-if="lastResult.executionTime">
                                <span class="font-medium">执行时间:</span>
                                <span class="ml-1">{{ lastResult.executionTime.toFixed(3) }}s</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import axios from '@/lib/axios';
import type { PodContainer } from '@/types';
import { FitAddon } from '@xterm/addon-fit';
import { Terminal } from '@xterm/xterm';
import '@xterm/xterm/css/xterm.css';
import { Loader2, Play, Trash2 } from 'lucide-vue-next';
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

interface Props {
    podName: string;
    containers: PodContainer[];
    modelValue: boolean;
}

interface ExecutionResult {
    success: boolean;
    exitCode: number | null;
    executionTime: number;
    error?: string;
}

const props = defineProps<Props>();
const emit = defineEmits<{
    'update:modelValue': [value: boolean];
}>();

// 响应式数据
const isOpen = ref(props.modelValue);
const selectedContainer = ref('');
const command = ref('');
const timeout = ref(30);
const isExecuting = ref(false);
const lastResult = ref<ExecutionResult | null>(null);

// 终端相关
const terminalContainer = ref<HTMLElement>();
let terminal: Terminal | null = null;
let fitAddon: FitAddon | null = null;

// 监听 modelValue 变化
watch(
    () => props.modelValue,
    (newValue) => {
        isOpen.value = newValue;
    },
);

watch(isOpen, (newValue) => {
    emit('update:modelValue', newValue);
    if (newValue) {
        nextTick(() => {
            initializeTerminal();
        });
    } else {
        cleanupTerminal();
    }
});

// 初始化默认容器
watch(
    () => props.containers,
    (containers) => {
        if (containers.length > 0 && !selectedContainer.value) {
            selectedContainer.value = containers[0].name;
        }
    },
    { immediate: true },
);

// 初始化终端
const initializeTerminal = () => {
    if (!terminalContainer.value || terminal) return;

    terminal = new Terminal({
        theme: {
            background: '#000000',
            foreground: '#00ff00',
            cursor: '#00ff00',
            selectionBackground: '#ffffff20',
        },
        fontSize: 14,
        fontFamily: 'Monaco, "Cascadia Code", "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
        cursorBlink: true,
        convertEol: true,
        scrollback: 1000,
    });

    fitAddon = new FitAddon();
    terminal.loadAddon(fitAddon);

    terminal.open(terminalContainer.value);
    fitAddon.fit();

    // 监听窗口大小变化
    const resizeObserver = new ResizeObserver(() => {
        if (fitAddon) {
            fitAddon.fit();
        }
    });
    resizeObserver.observe(terminalContainer.value);

    // 显示欢迎信息
    terminal.writeln('\x1b[32m=== Pod 命令执行终端 ===\x1b[0m');
    terminal.writeln(`\x1b[36mPod: ${props.podName}\x1b[0m`);
    terminal.writeln('');
};

// 清理终端
const cleanupTerminal = () => {
    if (terminal) {
        terminal.dispose();
        terminal = null;
    }

    if (fitAddon) {
        fitAddon = null;
    }
};

// 执行命令
const executeCommand = async () => {
    if (!command.value.trim() || !selectedContainer.value || isExecuting.value) return;

    isExecuting.value = true;
    lastResult.value = null;

    // 在终端显示执行的命令
    if (terminal) {
        terminal.writeln(`\x1b[33m$ ${command.value}\x1b[0m`);
    }

    try {
        const response = await axios.post(`/api/pods/${props.podName}/exec`, {
            command: command.value.split(' ').filter((cmd) => cmd.trim()),
            container: selectedContainer.value,
            timeout: timeout.value,
            stream: false,
        });

        const result = response.data;
        lastResult.value = {
            success: result.exit_code === 0,
            exitCode: result.exit_code,
            executionTime: result.execution_time,
        };

        if (terminal) {
            if (result.stdout) {
                terminal.write(result.stdout);
            }
            if (result.stderr) {
                terminal.writeln(`\x1b[31m${result.stderr}\x1b[0m`);
            }
            if (result.error) {
                terminal.writeln(`\x1b[31mError: ${result.error}\x1b[0m`);
            }
            terminal.writeln('');
        }
    } catch (error: any) {
        console.error('Command execution failed:', error);
        if (terminal) {
            terminal.writeln(`\x1b[31mAPI Error: ${error.response?.data?.message || error.message}\x1b[0m`);
        }
    } finally {
        isExecuting.value = false;
    }
};

// 清除终端
const clearTerminal = () => {
    if (terminal) {
        terminal.clear();
        terminal.writeln('\x1b[32m=== Pod 命令执行终端 ===\x1b[0m');
        terminal.writeln(`\x1b[36mPod: ${props.podName}\x1b[0m`);
        terminal.writeln('');
    }
    lastResult.value = null;
};

onMounted(() => {
    if (isOpen.value) {
        nextTick(() => {
            initializeTerminal();
        });
    }
});

onUnmounted(() => {
    cleanupTerminal();
});
</script>

<style scoped>
:deep(.xterm-viewport) {
    overflow-y: auto;
}

:deep(.xterm-screen) {
    padding: 8px;
}
</style>
