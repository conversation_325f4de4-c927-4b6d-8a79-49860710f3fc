<template>
    <div :class="['fixed z-[99999] rounded-lg border border-border bg-background/95 shadow-lg backdrop-blur-sm', getTaskbarPositionClass()]">
        <div class="flex items-center gap-2 p-1.5">
            <!-- 任务栏标题 -->
            <div class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <Monitor class="h-4 w-4" />
                <span>窗口</span>
                <Separator orientation="vertical" class="h-4" />
            </div>

            <!-- 窗口按钮列表 -->
            <div class="no-scrollbar max-w-[calc(100vw-350px)] overflow-x-auto">
                <div class="flex items-center gap-1">
                    <Button
                        v-for="window in windows"
                        :key="window.id"
                        variant="ghost"
                        size="sm"
                        :class="[
                            'group h-7 flex-shrink-0 px-2 text-xs transition-all', // group and flex-shrink-0 added
                            window.isMinimized ? 'opacity-60' : '',
                            activeWindowId === window.id ? 'border-primary/50 bg-primary/20 text-primary' : '',
                            !getWindowStatus(window) ? 'text-muted-foreground' : '',
                        ]"
                        @click="$emit('window-click', window.id)"
                        @contextmenu.prevent="showContextMenu(window, $event)"
                        :title="getWindowTooltip(window)"
                    >
                        <div class="flex max-w-[150px] items-center gap-2">
                            <!-- 窗口图标 -->
                            <component :is="getWindowIcon(window)" class="h-3 w-3 flex-shrink-0" :class="getWindowIconClass(window)" />

                            <!-- 状态指示器 -->
                            <div
                                v-if="showStatusIndicator(window)"
                                :class="['h-2 w-2 flex-shrink-0 rounded-full', getStatusIndicatorClass(window)]"
                            />

                            <!-- 窗口标题 -->
                            <span class="truncate">
                                {{ window.title }}
                            </span>

                            <!-- 关闭按钮 -->
                            <Button
                                variant="ghost"
                                size="sm"
                                class="ml-1 h-3.5 w-3.5 p-0 opacity-0 group-hover:opacity-100 hover:bg-destructive hover:text-destructive-foreground"
                                @click.stop="$emit('window-close', window.id)"
                                title="关闭窗口"
                            >
                                <X class="h-2 w-2" />
                            </Button>
                        </div>
                    </Button>
                </div>
            </div>

            <!-- 任务栏控制 -->
            <div v-if="windows.length > 1" class="ml-2 flex items-center gap-1">
                <Separator orientation="vertical" class="h-4" />
                <Button variant="ghost" size="sm" class="h-5 w-5 p-0" @click="minimizeAll" title="最小化全部">
                    <Minimize class="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" class="h-5 w-5 p-0" @click="closeAll" title="关闭全部">
                    <XCircle class="h-3 w-3" />
                </Button>
            </div>
        </div>

        <!-- 右键菜单 -->
        <div
            v-if="contextMenu.visible"
            ref="contextMenuRef"
            class="fixed z-[100000] min-w-[120px] rounded-md border border-border bg-background py-1 shadow-lg"
            :style="{ left: `${contextMenu.x}px`, top: `${contextMenu.y}px` }"
            @click="hideContextMenu"
        >
            <button
                v-if="contextMenu.window && !contextMenu.window.isMinimized"
                class="flex w-full items-center gap-2 px-3 py-1.5 text-left text-sm hover:bg-muted"
                @click="$emit('window-click', contextMenu.window.id)"
            >
                <Minimize class="h-3 w-3" />
                最小化
            </button>
            <button
                v-if="contextMenu.window && contextMenu.window.isMinimized"
                class="flex w-full items-center gap-2 px-3 py-1.5 text-left text-sm hover:bg-muted"
                @click="$emit('window-click', contextMenu.window.id)"
            >
                <Maximize class="h-3 w-3" />
                还原
            </button>
            <button
                class="flex w-full items-center gap-2 px-3 py-1.5 text-left text-sm hover:bg-muted"
                @click="contextMenu.window && $emit('window-close', contextMenu.window.id)"
            >
                <X class="h-3 w-3" />
                关闭
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import type { WindowInstance } from '@/types/window';
import { File, Folder, Globe, Maximize, Minimize, Monitor, Sparkles, Terminal, X, XCircle } from 'lucide-vue-next';
import { onMounted, onUnmounted, reactive, ref } from 'vue';

interface Props {
    windows: WindowInstance[];
    activeWindowId: string | null;
    position?: 'bottom' | 'top' | 'left' | 'right';
}

interface Emits {
    'window-click': [windowId: string];
    'window-close': [windowId: string];
}

const props = withDefaults(defineProps<Props>(), {
    position: 'bottom',
});

const emit = defineEmits<Emits>();

// Refs
const contextMenuRef = ref<HTMLElement>();

// 右键菜单状态
const contextMenu = reactive({
    visible: false,
    x: 0,
    y: 0,
    window: null as WindowInstance | null,
});

// 计算属性
const getTaskbarPositionClass = () => {
    const baseClasses = 'px-2 py-1.5';

    switch (props.position) {
        case 'top':
            return `top-2 left-1/2 -translate-x-1/2 ${baseClasses}`;
        case 'left':
            return `left-4 top-1/2 -translate-y-1/2 flex-col ${baseClasses}`;
        case 'right':
            return `right-4 top-1/2 -translate-y-1/2 flex-col ${baseClasses}`;
        case 'bottom':
        default:
            return `bottom-2 left-1/2 -translate-x-1/2 ${baseClasses}`;
    }
};

// 窗口图标映射
const getWindowIcon = (window: WindowInstance) => {
    const iconMap: Record<string, any> = {
        terminal: Terminal,
        'ai-assistant': Sparkles,
        'file-manager': Folder,
        editor: File,
        browser: Globe,
    };
    return iconMap[window.type] || File;
};

const getWindowIconClass = (window: WindowInstance) => {
    const classMap: Record<string, string> = {
        terminal: 'text-green-500',
        'ai-assistant': 'text-primary',
        'file-manager': 'text-blue-500',
        editor: 'text-orange-500',
        browser: 'text-purple-500',
    };
    return classMap[window.type] || 'text-muted-foreground';
};

// 状态相关
const getWindowStatus = (window: WindowInstance): boolean => {
    if (window.type === 'terminal') {
        return window.data?.isConnected ?? false;
    }
    return true;
};

const showStatusIndicator = (window: WindowInstance): boolean => {
    return window.type === 'terminal' && window.data?.isConnected !== undefined;
};

const getStatusIndicatorClass = (window: WindowInstance): string => {
    if (window.type === 'terminal') {
        return window.data?.isConnected ? 'bg-green-500' : 'bg-red-500';
    }
    return 'bg-gray-500';
};

const getWindowTooltip = (window: WindowInstance): string => {
    let tooltip = window.title;

    if (window.isMinimized) {
        tooltip += ' (已最小化)';
    }

    if (window.type === 'terminal' && window.data?.isConnected !== undefined) {
        tooltip += window.data.isConnected ? ' - 已连接' : ' - 未连接';
    }

    return tooltip;
};

// 右键菜单
const showContextMenu = (window: WindowInstance, event: MouseEvent) => {
    contextMenu.window = window;
    contextMenu.x = event.clientX;
    contextMenu.y = event.clientY;
    contextMenu.visible = true;

    // 确保菜单不会超出屏幕
    setTimeout(() => {
        if (contextMenuRef.value) {
            const rect = contextMenuRef.value.getBoundingClientRect();
            const viewportWidth = globalThis.window.innerWidth;
            const viewportHeight = globalThis.window.innerHeight;

            if (contextMenu.x + rect.width > viewportWidth) {
                contextMenu.x = viewportWidth - rect.width - 10;
            }

            if (contextMenu.y + rect.height > viewportHeight) {
                contextMenu.y = viewportHeight - rect.height - 10;
            }
        }
    }, 0);
};

const hideContextMenu = () => {
    contextMenu.visible = false;
    contextMenu.window = null;
};

// 全局操作
const minimizeAll = () => {
    props.windows.forEach((window) => {
        if (!window.isMinimized) {
            emit('window-click', window.id);
        }
    });
};

const closeAll = () => {
    props.windows.forEach((window) => {
        emit('window-close', window.id);
    });
};

// 点击外部关闭右键菜单
const handleClickOutside = (event: MouseEvent) => {
    if (contextMenu.visible && contextMenuRef.value && !contextMenuRef.value.contains(event.target as Node)) {
        hideContextMenu();
    }
};

// 生命周期
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('contextmenu', hideContextMenu);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
    document.removeEventListener('contextmenu', hideContextMenu);
});
</script>

<style scoped>
/* 任务栏样式 */
.group:hover .group-hover\:opacity-100 {
    opacity: 1;
}

/* 右键菜单动画 */
.context-menu-enter-active,
.context-menu-leave-active {
    transition: all 0.2s ease;
}

.context-menu-enter-from,
.context-menu-leave-to {
    opacity: 0;
    transform: scale(0.95);
}
</style>
