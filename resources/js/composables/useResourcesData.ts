import eventBus from '@/lib/eventBus';
import resourceManager from '@/lib/resourceManager';
import type { ResourceChangeEvent, ResourceCollections } from '@/stores/resourcesStore';
import { useResourcesStore } from '@/stores/resourcesStore';
import { computed } from 'vue';

/**
 * 资源数据 Composable
 * 提供便于在组件中使用资源数据的响应式接口
 */
export function useResourcesData() {
    const resourcesStore = useResourcesStore();

    // 响应式计算属性
    const isLoaded = computed(() => resourcesStore.isLoaded);
    const lastUpdate = computed(() => resourcesStore.lastUpdate);
    const isListening = computed(() => resourcesStore.isListening);
    const currentNamespace = computed(() => resourcesStore.currentNamespace);

    // 资源数量统计
    const resourceCounts = computed(() => resourcesStore.getResourceCounts);
    const totalResourceCount = computed(() => resourcesStore.getTotalResourceCount);

    // 各类型资源的快捷访问
    const deployments = computed(() => resourcesStore.getResourcesByType('deployments'));
    const statefulsets = computed(() => resourcesStore.getResourcesByType('statefulsets'));
    const services = computed(() => resourcesStore.getResourcesByType('services'));
    const pods = computed(() => resourcesStore.getResourcesByType('pods'));
    const ingresses = computed(() => resourcesStore.getResourcesByType('ingresses'));
    const storages = computed(() => resourcesStore.getResourcesByType('storages'));
    const secrets = computed(() => resourcesStore.getResourcesByType('secrets'));
    const configmaps = computed(() => resourcesStore.getResourcesByType('configmaps'));
    const hpas = computed(() => resourcesStore.getResourcesByType('horizontalpodautoscalers'));

    /**
     * 获取指定类型的资源
     */
    const getResources = (resourceType: keyof ResourceCollections) => {
        return resourcesStore.getResourcesByType(resourceType);
    };

    /**
     * 监听资源变更事件
     */
    const onResourceChange = (callback: (event: ResourceChangeEvent) => void) => {
        eventBus.on('resource:changed', callback);

        // 返回取消监听的函数
        return () => {
            eventBus.off('resource:changed', callback);
        };
    };

    /**
     * 手动刷新资源监听
     */
    const refreshResources = () => {
        resourceManager.refresh();
    };

    /**
     * 获取资源管理器状态
     */
    const getManagerStatus = () => {
        return resourceManager.getStatus();
    };

    /**
     * 检查某类资源是否为空
     */
    const isResourceTypeEmpty = (resourceType: keyof ResourceCollections) => {
        return resourcesStore.getResourcesByType(resourceType).length === 0;
    };

    /**
     * 检查是否有任何资源
     */
    const hasAnyResources = computed(() => {
        return totalResourceCount.value > 0;
    });

    /**
     * 获取资源类型的中文名称映射
     */
    const getResourceTypeDisplayName = (resourceType: keyof ResourceCollections) => {
        const nameMap = {
            deployments: 'Deployment',
            statefulsets: 'StatefulSet',
            services: 'Service',
            pods: 'Pod',
            ingresses: 'Ingress',
            storages: 'Storage',
            secrets: 'Secret',
            configmaps: 'ConfigMap',
            horizontalpodautoscalers: 'HPA',
        };
        return nameMap[resourceType] || resourceType;
    };

    return {
        // 状态
        isLoaded,
        lastUpdate,
        isListening,
        currentNamespace,

        // 统计
        resourceCounts,
        totalResourceCount,
        hasAnyResources,

        // 资源数据
        deployments,
        statefulsets,
        services,
        pods,
        ingresses,
        storages,
        secrets,
        configmaps,
        hpas,

        // 方法
        getResources,
        onResourceChange,
        refreshResources,
        getManagerStatus,
        isResourceTypeEmpty,
        getResourceTypeDisplayName,
    };
}
