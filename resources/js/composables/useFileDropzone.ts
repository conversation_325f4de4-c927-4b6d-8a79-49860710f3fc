import { ref, type Ref } from 'vue';
import { toast } from 'vue-sonner';

export interface UseFileDropzoneOptions {
    allowedExtensions?: string[];
    maxFileSize?: number; // in bytes
    onFileRead?: (content: string, file: File) => void;
}

export interface UseFileDropzoneReturn {
    isDragging: Ref<boolean>;
    onDragEnter: (event: DragEvent) => void;
    onDragLeave: (event: DragEvent) => void;
    onDragOver: (event: DragEvent) => void;
    onDrop: (event: DragEvent) => void;
}

export function useFileDropzone(options: UseFileDropzoneOptions = {}): UseFileDropzoneReturn {
    const {
        allowedExtensions = ['.yaml', '.yml'],
        maxFileSize = 5 * 1024 * 1024, // 5MB
        onFileRead,
    } = options;

    const isDragging = ref(false);
    let dragCounter = 0;

    const validateFile = (file: File): boolean => {
        // 检查文件扩展名
        const fileName = file.name.toLowerCase();
        const hasValidExtension = allowedExtensions.some((ext) => fileName.endsWith(ext));

        if (!hasValidExtension) {
            toast.error(`只支持 ${allowedExtensions.join(', ')} 文件格式`);
            return false;
        }

        // 检查文件大小
        if (file.size > maxFileSize) {
            toast.error(`文件大小不能超过 ${Math.round(maxFileSize / 1024 / 1024)}MB`);
            return false;
        }

        return true;
    };

    const readFileContent = async (file: File): Promise<void> => {
        try {
            const content = await file.text();
            onFileRead?.(content, file);
            toast.success(`已导入文件: ${file.name}`);
        } catch (error) {
            console.error('读取文件失败:', error);
            toast.error('读取文件失败');
        }
    };

    const onDragEnter = (event: DragEvent): void => {
        event.preventDefault();
        event.stopPropagation();
        dragCounter++;
        isDragging.value = true;
    };

    const onDragLeave = (event: DragEvent): void => {
        event.preventDefault();
        event.stopPropagation();
        dragCounter--;
        if (dragCounter === 0) {
            isDragging.value = false;
        }
    };

    const onDragOver = (event: DragEvent): void => {
        event.preventDefault();
        event.stopPropagation();
    };

    const onDrop = async (event: DragEvent): Promise<void> => {
        event.preventDefault();
        event.stopPropagation();

        dragCounter = 0;
        isDragging.value = false;

        const files = Array.from(event.dataTransfer?.files || []);

        if (files.length === 0) {
            return;
        }

        if (files.length > 1) {
            toast.warning('一次只能导入一个文件');
            return;
        }

        const file = files[0];

        if (validateFile(file)) {
            await readFileContent(file);
        }
    };

    return {
        isDragging,
        onDragEnter,
        onDragLeave,
        onDragOver,
        onDrop,
    };
}
