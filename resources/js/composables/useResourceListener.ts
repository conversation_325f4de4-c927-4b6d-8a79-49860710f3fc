import eventBus from '@/lib/eventBus';
import type { ResourceChangeEvent } from '@/stores/resourcesStore';
import { useResourcesStore } from '@/stores/resourcesStore';
import type { Workspace } from '@/types';
import { onUnmounted, readonly, ref } from 'vue';

interface UseResourceListenerOptions {
    autoStart?: boolean;
    enableDebugLog?: boolean;
    autoFetchOnSwitch?: boolean; // 切换工作区时是否自动获取资源
    /**
     * 组件卸载时是否自动停止监听，默认 false
     * 如果希望资源监听在不同页面之间保持活动（例如全局监听），
     * 可以将其设置为 false。
     */
    cleanupOnUnmount?: boolean;
}

/**
 * 资源监听器 Composable
 * 用于监听 Laravel Echo 资源变更事件，并同步到资源存储中
 * 同时负责在工作区切换时获取完整的资源数据
 */
export function useResourceListener(options: UseResourceListenerOptions = {}) {
    const { autoStart = true, enableDebugLog = false, autoFetchOnSwitch = true, cleanupOnUnmount = false } = options;

    const resourcesStore = useResourcesStore();
    const isListening = ref(false);
    const currentChannel = ref<any>(null);
    const currentNamespace = ref<string | null>(null);
    const error = ref<string | null>(null);
    const isFetching = ref(false);

    // 记录已注册的监听器，用于清理
    const registeredListeners = new Set<() => void>();

    /**
     * 开始监听指定工作区的资源变更
     */
    const startListening = async (workspace: Workspace | null) => {
        if (!workspace || !workspace.namespace) {
            if (enableDebugLog) {
                console.warn('无效的工作区，停止监听');
            }
            stopListening();
            return;
        }

        // 如果已经在监听相同的命名空间，不需要重新监听
        if (isListening.value && currentNamespace.value === workspace.namespace) {
            if (enableDebugLog) {
                console.log(`已经在监听命名空间: ${workspace.namespace}`);
            }
            return;
        }

        // 停止之前的监听
        stopListening();

        try {
            // 清空资源存储
            resourcesStore.clearAllResources();

            // 如果启用自动获取，先获取完整资源数据
            if (autoFetchOnSwitch) {
                if (enableDebugLog) {
                    console.log(`获取工作区 ${workspace.namespace} 的完整资源数据`);
                }

                isFetching.value = true;
                try {
                    await resourcesStore.fetchAllResources();
                    if (enableDebugLog) {
                        console.log('资源数据获取完成');
                    }
                } catch (fetchError) {
                    console.error('获取资源数据失败:', fetchError);
                    // 获取失败不阻止监听启动
                } finally {
                    isFetching.value = false;
                }
            }

            const channelName = `workspace.${workspace.namespace}.resources`;

            if (enableDebugLog) {
                console.log(`开始监听资源变更频道: ${channelName}`);
            }

            // 创建私有频道监听
            const channel = window.Echo.private(channelName);

            if (!channel) {
                throw new Error('无法创建 Echo 频道');
            }

            // 监听资源变更事件
            const eventHandler = (data: ResourceChangeEvent) => {
                if (enableDebugLog) {
                    console.log('收到资源变更事件:', data);
                }

                // 更新资源存储（会自动检查 resourceVersion）
                resourcesStore.handleResourceChange(data);

                // 发送到事件总线（其他组件可以监听）
                eventBus.emit('resource:changed', data);
            };

            channel.listen('.resource.changed', eventHandler);

            // 处理连接状态
            channel.subscribed(() => {
                if (enableDebugLog) {
                    console.log(`成功订阅频道: ${channelName}`);
                }
                isListening.value = true;
                currentNamespace.value = workspace.namespace;
                currentChannel.value = channel;
                error.value = null;

                // 更新存储状态
                resourcesStore.setListeningState(true, workspace.namespace);
            });

            channel.error((err: any) => {
                console.error('Echo 频道错误:', err);
                error.value = err.message || '连接错误';
                isListening.value = false;
                resourcesStore.setListeningState(false);
            });

            // 记录清理函数
            const cleanup = () => {
                channel.stopListening('.resource.changed');
                window.Echo.leave(channelName);
            };
            registeredListeners.add(cleanup);
        } catch (err: any) {
            console.error('启动资源监听失败:', err);
            error.value = err.message || '启动监听失败';
            isListening.value = false;
            resourcesStore.setListeningState(false);
        }
    };

    /**
     * 停止监听
     */
    const stopListening = () => {
        if (currentChannel.value) {
            try {
                const channelName = `workspace.${currentNamespace.value}.resources`;

                if (enableDebugLog) {
                    console.log(`停止监听频道: ${channelName}`);
                }

                // 执行所有清理函数
                registeredListeners.forEach((cleanup) => {
                    try {
                        cleanup();
                    } catch (err) {
                        console.warn('清理监听器时出错:', err);
                    }
                });
                registeredListeners.clear();

                currentChannel.value = null;
            } catch (err) {
                console.warn('停止监听时出错:', err);
            }
        }

        isListening.value = false;
        currentNamespace.value = null;
        error.value = null;

        // 更新存储状态
        resourcesStore.setListeningState(false);
    };

    /**
     * 切换工作区监听
     */
    const switchWorkspace = async (newWorkspace: Workspace | null) => {
        if (enableDebugLog) {
            console.log('切换工作区监听:', newWorkspace?.name || 'null');
        }

        // 停止当前监听
        stopListening();

        // 清空资源存储
        resourcesStore.clearAllResources();

        // 开始监听新工作区（会自动获取资源）
        if (newWorkspace) {
            await startListening(newWorkspace);
        }
    };

    /**
     * 重新连接
     */
    const reconnect = async (workspace: Workspace | null) => {
        if (enableDebugLog) {
            console.log('重新连接资源监听');
        }

        stopListening();

        if (workspace) {
            // 短暂延迟后重新连接
            setTimeout(async () => {
                await startListening(workspace);
            }, 1000);
        }
    };

    /**
     * 手动刷新资源数据
     */
    const refreshResources = async () => {
        if (enableDebugLog) {
            console.log('手动刷新资源数据');
        }

        isFetching.value = true;
        try {
            await resourcesStore.fetchAllResources();
            if (enableDebugLog) {
                console.log('资源数据刷新完成');
            }
        } catch (fetchError) {
            console.error('刷新资源数据失败:', fetchError);
            throw fetchError;
        } finally {
            isFetching.value = false;
        }
    };

    /**
     * 获取当前监听状态
     */
    const getListeningStatus = () => ({
        isListening: isListening.value,
        namespace: currentNamespace.value,
        error: error.value,
        hasChannel: !!currentChannel.value,
        isFetching: isFetching.value,
    });

    // 组件卸载时清理（可选）
    onUnmounted(() => {
        if (!cleanupOnUnmount) {
            return;
        }

        if (enableDebugLog) {
            console.log('资源监听器组件卸载，清理监听');
        }

        stopListening();
    });

    // 如果启用自动开始，这里可以添加逻辑
    if (autoStart && enableDebugLog) {
        console.log('资源监听器已创建，等待工作区信息启动监听');
    }

    return {
        // 状态
        isListening: readonly(isListening),
        currentNamespace: readonly(currentNamespace),
        error: readonly(error),
        isFetching: readonly(isFetching),

        // 方法
        startListening,
        stopListening,
        switchWorkspace,
        reconnect,
        refreshResources,
        getListeningStatus,
    };
}
