import { useI18n as useVueI18n } from 'vue-i18n';

export const useI18n = () => {
    const { t, rt, tm, locale, availableLocales } = useVueI18n();

    const setLocale = (newLocale: string) => {
        if (availableLocales.includes(newLocale)) {
            locale.value = newLocale;
            localStorage.setItem('locale', newLocale);
        }
    };

    const getLocale = () => {
        return locale.value;
    };

    const initLocale = () => {
        const savedLocale = localStorage.getItem('locale');
        if (savedLocale && availableLocales.includes(savedLocale)) {
            locale.value = savedLocale;
        }
    };

    return {
        t,
        locale,
        availableLocales,
        setLocale,
        getLocale,
        initLocale,
        rt,
        tm,
    };
};
