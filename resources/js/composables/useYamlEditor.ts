import { computed } from 'vue';
import { useYamlApply } from './useYamlApply';

export function useYamlEditor() {
    const yamlApply = useYamlApply();

    // 为模板提供安全的计算属性
    const yamlText = computed({
        get: () => yamlApply.yamlContent.value,
        set: (value: string) => {
            yamlApply.yamlContent.value = value;
        },
    });

    const isSubmittingState = computed(() => yamlApply.isSubmitting.value);
    const hasWorkspaceState = computed(() => yamlApply.hasWorkspace.value);
    const currentProgressValue = computed(() => yamlApply.currentProgress.value);
    const totalProgressValue = computed(() => yamlApply.totalProgress.value);
    const progressValue = computed(() => yamlApply.progressPercentage.value);

    const currentApplyingText = computed(() => {
        const item = yamlApply.currentApplyingItem.value;
        if (!item) return '';
        return `${item.method} ${item.api}`;
    });

    return {
        // 原始的 composable 方法
        ...yamlApply,

        // 模板安全的计算属性
        yamlText,
        isSubmittingState,
        hasWorkspaceState,
        currentProgressValue,
        totalProgressValue,
        progressValue,
        currentApplyingText,
    };
}
