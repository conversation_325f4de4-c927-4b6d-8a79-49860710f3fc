import axios from '@/lib/axios';
import { getResourceMapping, isResourceKindSupported } from '@/lib/resourceMapping';
import { createVariableResolver } from '@/lib/variableResolver';
import { useResourcesStore } from '@/stores/resourcesStore';
import { useWorkspaceStore } from '@/stores/workspaceStore';
import type { ApplyResult, CurrentApplyingItem, UseYamlApplyReturn, YamlDocument, YamlValidationResult } from '@/types/yaml';
import * as yaml from 'js-yaml';
import { computed, ref, type ComputedRef, type Ref } from 'vue';
import { toast } from 'vue-sonner';

export function useYamlApply(): UseYamlApplyReturn {
    const workspaceStore = useWorkspaceStore();
    const resourcesStore = useResourcesStore();
    const yamlContent: Ref<string> = ref('');
    const isSubmitting: Ref<boolean> = ref(false);
    const currentProgress: Ref<number> = ref(0);
    const totalProgress: Ref<number> = ref(0);
    const currentApplyingItem: Ref<CurrentApplyingItem | null> = ref(null);
    const variableResolver = createVariableResolver();

    // 计算属性：判断是否有工作空间
    const hasWorkspace: ComputedRef<boolean> = computed(() => !!workspaceStore.currentWorkspace);

    // 计算进度百分比
    const progressPercentage: ComputedRef<number> = computed(() => {
        if (totalProgress.value === 0) return 0;
        return Math.round((currentProgress.value / totalProgress.value) * 100);
    });

    // 默认 YAML 模板
    const defaultYaml: string = ``;

    // 初始化时设置默认值
    const initializeDefaultYaml = (): void => {
        if (!yamlContent.value) {
            yamlContent.value = defaultYaml;
        }
    };

    // 重置进度
    const resetProgress = (): void => {
        currentProgress.value = 0;
        totalProgress.value = 0;
        currentApplyingItem.value = null;
    };

    // 验证 YAML 格式
    const validateYaml = (content: string): YamlValidationResult => {
        try {
            // 预处理：移除注释并清理空块
            const cleanedContent = preprocessYamlContent(content);

            if (!cleanedContent.trim()) {
                return { isValid: false, error: 'YAML 内容不能为空' };
            }

            const documents = yaml.loadAll(cleanedContent) as YamlDocument[];

            if (!documents || documents.length === 0) {
                return { isValid: false, error: 'YAML 内容不能为空' };
            }

            // 验证每个文档是否有必需的字段
            for (let i = 0; i < documents.length; i++) {
                const doc = documents[i];
                if (!doc || typeof doc !== 'object') {
                    return { isValid: false, error: `第 ${i + 1} 个文档格式不正确` };
                }

                if (!doc.kind) {
                    return { isValid: false, error: `第 ${i + 1} 个文档缺少必需的 kind 字段` };
                }

                if (!doc.name) {
                    return { isValid: false, error: `第 ${i + 1} 个文档缺少必需的 name 字段` };
                }

                // 验证资源类型是否支持
                if (!isResourceKindSupported(doc.kind)) {
                    return { isValid: false, error: `第 ${i + 1} 个文档的资源类型 "${doc.kind}" 不支持` };
                }
            }

            return { isValid: true, documents };
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            return { isValid: false, error: `YAML 解析错误: ${errorMessage}` };
        }
    };

    // 预处理 YAML 内容：移除注释并清理空块
    const preprocessYamlContent = (content: string): string => {
        // 按 --- 分割文档
        const yamlParts = content.split(/^---\s*$/m);
        const processedParts: string[] = [];

        for (const part of yamlParts) {
            // 移除注释并清理每个部分
            const cleanedPart = removeCommentsAndCleanup(part.trim());

            // 如果清理后有内容，则保留
            if (cleanedPart) {
                processedParts.push(cleanedPart);
            }
        }

        return processedParts.join('\n---\n');
    };

    // 移除注释并清理内容
    const removeCommentsAndCleanup = (content: string): string => {
        const lines = content.split('\n');
        const cleanedLines: string[] = [];

        for (const line of lines) {
            // 检查是否为纯注释行
            const trimmedLine = line.trim();
            if (trimmedLine.startsWith('#') || trimmedLine === '') {
                continue; // 跳过注释行和空行
            }

            // 处理行内注释 - 但要小心字符串中的 #
            let cleanedLine = line;
            let inString = false;
            let stringChar = '';
            let result = '';

            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                const prevChar = i > 0 ? line[i - 1] : '';

                if (!inString && (char === '"' || char === "'")) {
                    inString = true;
                    stringChar = char;
                    result += char;
                } else if (inString && char === stringChar && prevChar !== '\\') {
                    inString = false;
                    stringChar = '';
                    result += char;
                } else if (!inString && char === '#') {
                    // 找到注释开始，截断这里
                    break;
                } else {
                    result += char;
                }
            }

            cleanedLine = result.trimEnd();
            if (cleanedLine) {
                cleanedLines.push(cleanedLine);
            }
        }

        return cleanedLines.join('\n').trim();
    };

    // 构建请求参数
    const buildRequestParams = (data: YamlDocument): Record<string, any> => {
        // 移除 kind 字段，其余作为请求参数
        const { kind, ...params } = data;
        // 使用变量解析器解析所有参数中的变量
        return variableResolver.resolveObject(params);
    };

    // 执行应用/更新操作
    const applyYaml = async (onSuccess?: () => void): Promise<void> => {
        await executeYamlOperation('apply', onSuccess);
    };

    // 执行删除操作
    const deleteYaml = async (onSuccess?: () => void): Promise<void> => {
        await executeYamlOperation('delete', onSuccess);
    };

    // 通用的 YAML 操作执行函数
    const executeYamlOperation = async (operation: 'apply' | 'delete', onSuccess?: () => void): Promise<void> => {
        if (!hasWorkspace.value) {
            toast.error('请先选择工作空间');
            return;
        }

        if (!yamlContent.value.trim()) {
            toast.error('请输入 YAML 内容');
            return;
        }

        const validation = validateYaml(yamlContent.value);
        if (!validation.isValid) {
            toast.error(validation.error || 'YAML 格式错误');
            return;
        }

        isSubmitting.value = true;
        const documents = validation.documents!;
        totalProgress.value = documents.length;
        currentProgress.value = 0;

        try {
            const results: ApplyResult[] = [];

            for (let i = 0; i < documents.length; i++) {
                const doc = documents[i];
                const resourceMapping = getResourceMapping(doc.kind);

                if (!resourceMapping) {
                    results.push({
                        index: i + 1,
                        api: '',
                        method: '',
                        status: 'error',
                        error: `不支持的资源类型: ${doc.kind}`,
                    });
                    currentProgress.value = i + 1;
                    continue;
                }

                const params = buildRequestParams(doc);
                let method: string;
                let requestUrl: string;
                let requestData: any;

                if (operation === 'delete') {
                    method = 'DELETE';
                    // 删除操作需要在 URL 中包含资源名称
                    // 优先使用自定义删除路由，如果没有则使用默认路由
                    const deleteEndpoint = resourceMapping.deleteApiEndpoint || resourceMapping.apiEndpoint;
                    requestUrl = `${deleteEndpoint}/${encodeURIComponent(doc.name)}`;
                    requestData = undefined;
                } else {
                    // 应用/更新操作，需要检查资源是否存在
                    const resourceExists = resourcesStore.hasResource(resourceMapping.resourceStoreKey as any, doc.name);

                    if (resourceExists) {
                        // 资源存在，使用 PUT 更新
                        method = 'PUT';
                        requestUrl = `${resourceMapping.apiEndpoint}/${encodeURIComponent(doc.name)}`;
                        requestData = params;
                    } else {
                        // 资源不存在，使用 POST 创建
                        method = 'POST';
                        requestUrl = resourceMapping.apiEndpoint;
                        requestData = params;
                    }
                }

                // 更新当前正在处理的项目
                currentApplyingItem.value = { api: requestUrl, method };

                try {
                    let response;

                    switch (method) {
                        case 'DELETE':
                            response = await axios.delete(requestUrl);
                            break;
                        case 'PUT':
                            response = await axios.put(requestUrl, requestData);
                            break;
                        case 'POST':
                            response = await axios.post(requestUrl, requestData);
                            break;
                        default:
                            throw new Error(`不支持的 HTTP 方法: ${method}`);
                    }

                    results.push({
                        index: i + 1,
                        api: requestUrl,
                        method,
                        status: 'success',
                        data: response.data,
                    });

                    // 更新本地资源存储
                    if (operation === 'apply') {
                        if (method === 'POST') {
                            // 创建资源，添加到本地存储
                            resourcesStore.addResource(resourceMapping.resourceStoreKey as any, response.data);
                        } else if (method === 'PUT') {
                            // 更新资源，替换本地存储中的资源
                            resourcesStore.addResource(resourceMapping.resourceStoreKey as any, response.data);
                        }
                    } else if (operation === 'delete') {
                        // 删除资源，从本地存储中移除
                        resourcesStore.removeResource(resourceMapping.resourceStoreKey as any, doc.name);
                    }
                } catch (error: unknown) {
                    const errorMessage = error instanceof Error ? error.message : (error as any)?.response?.data?.message || '未知错误';

                    results.push({
                        index: i + 1,
                        api: requestUrl,
                        method,
                        status: 'error',
                        error: errorMessage,
                    });
                }

                // 更新进度
                currentProgress.value = i + 1;
            }

            // 清除当前处理项目
            currentApplyingItem.value = null;

            // 显示结果
            const successCount = results.filter((r) => r.status === 'success').length;
            const errorCount = results.filter((r) => r.status === 'error').length;

            const operationText = operation === 'delete' ? '删除' : '应用';

            if (errorCount === 0) {
                toast.success(`所有 ${successCount} 个资源都${operationText}成功`);
                onSuccess?.();
            } else if (successCount === 0) {
                toast.error(`所有 ${errorCount} 个资源都${operationText}失败`);
            } else {
                toast.warning(`${successCount} 个成功，${errorCount} 个失败`);
            }

            // 显示详细错误信息
            // results
            //     .filter((r) => r.status === 'error')
            //     .forEach((result) => {
            //         toast.error(`资源 ${result.index} (${result.method} ${result.api}) ${operationText}失败: ${result.error}`);
            //     });
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            const operationText = operation === 'delete' ? '删除' : '应用';
            toast.error(`${operationText}失败: ` + errorMessage);
        } finally {
            isSubmitting.value = false;
            resetProgress();
        }
    };

    // 保持向后兼容性的 submitYaml 方法
    const submitYaml = async (onSuccess?: () => void): Promise<void> => {
        await applyYaml(onSuccess);
    };

    return {
        yamlContent,
        isSubmitting,
        hasWorkspace,
        currentProgress,
        totalProgress,
        progressPercentage,
        currentApplyingItem,
        defaultYaml,
        initializeDefaultYaml,
        validateYaml,
        submitYaml,
        applyYaml,
        deleteYaml,
        variableResolver,
    };
}
