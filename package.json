{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/typography": "^0.5.16", "@types/lodash-es": "^4.17.12", "@types/node": "^22.16.2", "@vue/eslint-config-typescript": "^14.6.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-vue": "^9.33.0", "laravel-echo": "^2.1.6", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.14", "pusher-js": "^8.4.0", "typescript-eslint": "^8.36.0", "vue-tsc": "^2.2.12"}, "dependencies": {"@inertiajs/core": "^2.0.14", "@inertiajs/vue3": "^2.0.14", "@tailwindcss/vite": "^4.1.11", "@tanstack/vue-table": "^8.21.3", "@types/js-yaml": "^4.0.9", "@vee-validate/zod": "^4.15.1", "@vitejs/plugin-vue": "^5.2.4", "@vueuse/core": "^12.8.2", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.2.0", "date-fns": "^4.1.0", "echarts": "^5.6.0", "eventsource": "^4.0.0", "gsap": "^3.13.0", "js-yaml": "^4.1.0", "klinecharts": "10.0.0-alpha5", "laravel-vite-plugin": "^1.3.0", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.468.0", "marked": "^16.0.0", "mitt": "^3.0.1", "openai": "^5.8.3", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "reka-ui": "^2.3.2", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "uuid": "^11.1.0", "vee-validate": "^4.15.1", "vite": "^6.3.5", "vue": "^3.5.17", "vue-echarts": "^7.0.3", "vue-i18n": "^11.1.9", "vue-sonner": "^2.0.1", "ziggy-js": "^2.5.3", "zod": "^3.25.76"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.1.11", "lightningcss-linux-x64-gnu": "^1.30.1"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}