<?php

use Illuminate\Support\Facades\Schedule;

// 批量异步计费流水线任务 - 批量收集资源、恢复欠费、队列分发异步计费
Schedule::command('billing:process')
    ->everyMinute()
    ->name('billing-process')
    ->description('每分钟执行批量异步计费流水线：批量资源收集、欠费恢复、队列分发异步处理')
    ->onOneServer()
    ->runInBackground();

Schedule::command('passport:purge')->hourly();

// 清理过期工作空间任务 - 删除长期欠费的工作空间
// Schedule::command('billing:cleanup-overdue-workspaces')
//     ->daily()
//     ->at('02:00')
//     ->name('billing-cleanup-overdue-workspaces')
//     ->description('每天凌晨2点清理长期欠费的过期工作空间')
//     ->onOneServer()
//     ->withoutOverlapping()
//     ->runInBackground();

// 证书缓存清理任务
Schedule::call(function () {
    \App\Service\CertificateCacheService::cleanupExpiredCertificates();
})->weekly()->mondays()->at('03:00')
    ->name('cleanup-certificate-cache')
    ->description('每周一凌晨3点清理过期的证书缓存文件')
    ->onOneServer();
