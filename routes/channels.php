<?php

use App\Models\User;
use App\Models\Workspace;
use Illuminate\Support\Facades\Broadcast;

Broadcast::channel('App.Models.User.{id}', function (User $user, $id) {
    return (int) $user->id === (int) $id;
});

// 接收 Namespace 的资源更新（StatefulSet 等）
Broadcast::channel('workspace.{namespace}.resources', function (User $user, string $namespace) {
    return $user->inWorkspace(Workspace::getWorkspaceByNamespace($namespace));
});

// 价格更新频道 - 无需认证，任何人都可以监听价格变化
Broadcast::channel('cluster-pricing', function () {
    return true;
});
