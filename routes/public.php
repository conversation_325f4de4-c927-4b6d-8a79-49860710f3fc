<?php

use App\Http\Controllers\PaymentCallbackController;
use App\Http\Controllers\Public\OpenIDController;
use Illuminate\Support\Facades\Route;

Route::get('/.well-known/openid-configuration', [OpenIDController::class, 'discovery'])
    ->name('openid.discovery');

Route::get('/.well-known/jwks', [OpenIDController::class, 'jwks'])
    ->name('openid.jwks');

// 支付回调路由（不需要认证）
Route::any('/payment/callback/{gateway}', [PaymentCallbackController::class, 'handleCallback'])
    ->name('payment.callback');
