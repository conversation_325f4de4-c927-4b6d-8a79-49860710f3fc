<?php

use Illuminate\Support\Facades\Route;

Route::post('/token', [
    'uses' => 'AccessTokenController@issueToken',
    'as' => 'token',
    'middleware' => 'throttle',
]);

Route::get('/authorize', [
    'uses' => 'AuthorizationController@authorize',
    'as' => 'authorizations.authorize',
    'middleware' => 'web',
]);

Route::get('/device', [
    'uses' => 'DeviceUserCodeController',
    'as' => 'device',
    'middleware' => 'web',
]);

Route::post('/device/code', [
    'uses' => 'DeviceCodeController',
    'as' => 'device.code',
    'middleware' => 'throttle',
]);

Route::middleware(['web', 'auth:web'])->group(function () {
    Route::post('/token/refresh', [
        'uses' => 'TransientTokenController@refresh',
        'as' => 'token.refresh',
    ]);

    Route::post('/authorize', [
        'uses' => 'ApproveAuthorizationController@approve',
        'as' => 'authorizations.approve',
    ]);

    Route::delete('/authorize', [
        'uses' => 'DenyAuthorizationController@deny',
        'as' => 'authorizations.deny',
    ]);

    Route::get('/device/authorize', [
        'uses' => 'DeviceAuthorizationController',
        'as' => 'device.authorizations.authorize',
    ]);

    Route::post('/device/authorize', [
        'uses' => 'ApproveDeviceAuthorizationController',
        'as' => 'device.authorizations.approve',
    ]);

    Route::delete('/device/authorize', [
        'uses' => 'DenyDeviceAuthorizationController',
        'as' => 'device.authorizations.deny',
    ]);

});
