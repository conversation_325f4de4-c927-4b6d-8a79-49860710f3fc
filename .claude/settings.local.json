{"permissions": {"allow": ["Bash(php artisan make:migration migrate_old_pricing_to_resource_pricing --table=resource_pricings)", "Bash(php artisan make:migration drop_old_pricing_tables --table=cluster_pricings)", "Bash(php artisan make:service Billing/PricingService)", "<PERSON><PERSON>(php artisan migrate)", "Bash(php artisan make:model ResourcePricing)", "Bash(php artisan make:migration create_resource_pricings_table)", "Bash(php artisan workspace:list)", "Bash(php artisan workspace:create test-workspace-2 1 1)", "Bash(php artisan workspace:list 1)", "<PERSON><PERSON>(php artisan tinker)", "Bash(zsh -c \"php artisan tinker -c\")", "Bash(zsh -c \"php artisan tinker\")", "Bash(php artisan pricing:set --help)", "Bash(php artisan pricing:set cluster 1 cpu 100 --description=\"测试每月价格\")", "Bash(php artisan pricing:set --list-resources)", "Bash(php artisan pricing:set cluster 12 cpu 100 --description=\"测试每月价格\")", "Bash(php artisan pricing:set cluster 12 cpu_core 100 --description=\"测试每月价格\")", "Bash(php artisan pricing:set cluster 12 cpu_core 1000 --description=\"测试每月价格\")", "Bash(php artisan pricing:set cluster 12 cpu_core 10000 --description=\"测试每月价格\")", "Bash(php artisan pricing:set cluster 12 cpu_core 100000 --description=\"测试每月价格\")", "Bash(php artisan pricing:set cluster 12 cpu_core 1000000 --description=\"测试每月价格\")", "Bash(php artisan pricing:set cluster 12 cpu_core 10000000 --description=\"测试每月价格\")"], "deny": []}}