<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('resource_pricings', function (Blueprint $table) {
            $table->id();

            // 多态关联字段
            $table->unsignedBigInteger('resource_id');
            $table->string('resource_type');

            // 定价信息
            $table->string('unit_type')->comment('单位类型 (如 memory_gb, cpu_core, ip_address)');
            $table->decimal('price_per_unit_per_hour', 12, 8)->comment('每单位每小时价格');
            $table->dateTime('effective_date')->comment('生效日期');
            $table->text('description')->nullable()->comment('定价说明');

            $table->timestamps();

            // 索引
            $table->index(['resource_id', 'resource_type'], 'resource_pricings_resource_index');
            $table->index(['resource_type', 'unit_type'], 'resource_pricings_type_unit_index');
            $table->index(['effective_date'], 'resource_pricings_effective_date_index');

            // 复合索引用于查询当前有效定价
            $table->index(['resource_id', 'resource_type', 'unit_type', 'effective_date'], 'resource_pricings_current_pricing_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('resource_pricings');
    }
};
