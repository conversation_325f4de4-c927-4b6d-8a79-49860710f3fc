<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cluster_pricing_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cluster_id')->constrained()->onDelete('cascade');
            $table->json('pricing_data')->comment('价格数据快照');
            $table->timestamps();

            $table->index(['cluster_id', 'created_at']);
        });
    }

    /**
     * Down the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cluster_pricing_histories');
    }
};
