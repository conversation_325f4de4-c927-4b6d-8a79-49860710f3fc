<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ip_pools', function (Blueprint $table) {
            $table->enum('sharing_strategy', ['shared', 'dedicated'])
                ->default('shared')
                ->after('ip_version')
                ->comment('IP共享策略：shared=共享，dedicated=独享');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ip_pools', function (Blueprint $table) {
            $table->dropColumn('sharing_strategy');
        });
    }
};
