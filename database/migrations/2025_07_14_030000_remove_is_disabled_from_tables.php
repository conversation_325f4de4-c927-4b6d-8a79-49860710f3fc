<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('pool_ips', 'is_disabled')) {
            Schema::table('pool_ips', function (Blueprint $table) {
                $table->dropColumn('is_disabled');
            });
        }

        if (Schema::hasColumn('port_allocations', 'is_disabled')) {
            Schema::table('port_allocations', function (Blueprint $table) {
                $table->dropColumn('is_disabled');
            });
        }

        if (Schema::hasColumn('port_allocations', 'reason')) {
            Schema::table('port_allocations', function (Blueprint $table) {
                $table->dropColumn('reason');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pool_ips', function (Blueprint $table) {
            $table->boolean('is_disabled')->default(false)->after('is_active');
        });

        Schema::table('port_allocations', function (Blueprint $table) {
            $table->boolean('is_disabled')->default(false)->after('status');
            $table->text('reason')->nullable()->after('is_disabled');
        });
    }
};
