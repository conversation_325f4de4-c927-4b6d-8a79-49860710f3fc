(function (win) {
    if (win.desk) return;

    const WINDOW_ID = 'win-' + Date.now().toString(36) + Math.random().toString(36).slice(2, 8);
    const listeners = {};
    const pendingRequests = {};

    function post(message) {
        message.__desk = true;
        message.senderId = WINDOW_ID;
        message.messageId = `msg-${Date.now().toString(36)}-${Math.random().toString(36).slice(2, 6)}`;
        if (win.top) {
            win.top.postMessage(JSON.stringify(message), '*');
        }
    }

    win.addEventListener('message', (event) => {
        let msg;
        try {
            if (typeof event.data !== 'string') return;
            msg = JSON.parse(event.data);
        } catch (e) {
            return;
        }

        if (!msg || !msg.__desk) return;

        // Handle standard broadcast events
        if (msg.name && listeners[msg.name]) {
            listeners[msg.name].forEach(cb => cb(msg.payload));
        }

        // Handle RPC responses targeted at this window
        if (msg.name === 'rpc:response' && msg.payload && msg.payload.id) {
            const waiter = pendingRequests[msg.payload.id];
            if (waiter) {
                if (msg.payload.error) {
                    waiter.reject(new Error(msg.payload.error));
                } else {
                    waiter.resolve(msg.payload.payload);
                }
                delete pendingRequests[msg.payload.id];
            }
        }
    });

    win.desk = {
        on(type, callback) {
            if (!listeners[type]) {
                listeners[type] = [];
            }
            listeners[type].push(callback);
            return () => {
                listeners[type] = listeners[type].filter(cb => cb !== callback);
            };
        },

        emit(type, payload) {
            post({ name: type, payload: payload });
        },

        request(method, payload, timeout = 10000) {
            return new Promise((resolve, reject) => {
                const requestId = `rpc-req-${Date.now().toString(36)}-${Math.random().toString(36).slice(2, 6)}`;
                pendingRequests[requestId] = { resolve, reject };

                setTimeout(() => {
                    if (pendingRequests[requestId]) {
                        reject(new Error(`RPC request for '${method}' timed out after ${timeout}ms`));
                        delete pendingRequests[requestId];
                    }
                }, timeout);

                post({
                    name: 'rpc:request',
                    payload: {
                        id: requestId,
                        method: method,
                        payload: payload
                    }
                });
            });
        }
    };
})(window); 