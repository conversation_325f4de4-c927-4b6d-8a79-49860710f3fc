<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>External SDK Test</title>
    <script src="/desk-sdk.js"></script>
    <style>
        body{font-family:sans-serif;padding:20px}
        button{margin-right:10px}
    </style>
</head>
<body>
    <h1>External SDK Test Page</h1>
    <button onclick="sendPing()">desk.emit PING</button>
    <button onclick="requestTime()">desk.request getTime (async/await)</button>
    <pre id="log"></pre>
    <script>
        const log = (txt)=>{
            const el = document.getElementById('log');
            if(el) el.textContent += txt + "\n";
        };
        window.desk.on('sdk:ping', payload=>{
            log('收到 PING: '+JSON.stringify(payload));
        });
        function sendPing(){
            log('发送 PING...');
            window.desk.emit('sdk:ping',{from:'external',time:Date.now()});
        }
        async function requestTime() {
            log('请求时间 (awaiting)...');
            try {
                const time = await window.desk.request('getTime');
                log('✅ 成功! 时间: ' + time);
            } catch (e) {
                log('❌ 失败: ' + e.message);
            }
        }
    </script>
</body>
</html> 