kind: GenericSecret
name: mysql-root-password
data:
  password: "Qwerty123..."
---
kind: ConfigMap
name: wordpress
data:
  WORDPRESS_DB_HOST: mysql8-headless.${namespace}.svc.cluster.local
  WORDPRESS_DB_NAME: wordpress
  WORDPRESS_DB_PASSWORD: Qwerty123...
  WORDPRESS_DB_USER: root
---
kind: Storage
name: wordpress
size: 1024
---
kind: Storage
name: mysql
size: 1024
---
kind: Deployment
name: wordpress
replicas: 1
image_pull_secrets: []
containers:
  - name: wordpress
    image: wordpress:6.8.1-php8.1-apache
    working_dir: ''
    command: []
    args: []
    ports:
      - name: http
        container_port: 80
        protocol: TCP
    env: []
    env_from_configmap:
      - configmap_name: wordpress
        key: null
        env_name: null
    env_from_secret: []
    resources:
      cpu: 1000
      memory: 1024
    volume_mounts:
      - mount_path: /var/www/html
        storage_name: wordpress
        sub_path: ''
        read_only: false
    configmap_mounts: []
    secret_mounts: []
---
kind: StatefulSet
name: mysql8
replicas: 1
service_name: ''
image_pull_secrets: []
containers:
  - name: mysql
    image: mysql:8
    working_dir: ''
    command: []
    args: []
    ports:
      - name: mysql
        container_port: 3306
        protocol: TCP
    env: []
    env_from_configmap: []
    env_from_secret:
      - secret_name: mysql-root-password
        key: password
        env_name: MYSQL_ROOT_PASSWORD
    resources:
      memory: 1024
      cpu: 1000
    volume_mounts:
      - mount_path: /var/lib/mysql
        storage_name: mysql
        sub_path: ''
        read_only: false
    configmap_mounts: []
    secret_mounts: []
---
kind: Service
name: wordpress
type: ClusterIP
target_workload_type: Deployment
target_workload_name: wordpress
ports:
  - name: http
    port: 80
    target_port: 80
    protocol: TCP
session_affinity: None
external_traffic_policy: Cluster
allow_shared_ip: true