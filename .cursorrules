项目使用 Laravel 12 + Inertia.js + Vue3 + Shadcn-vue + TailwindCSS + Lucide Icon Vue，包管理器 Composer + pnpm
不要使用 $page，而是使用 usePage，不要使用 $route，而是使用 import { router, usePage } from '@inertiajs/vue3';

关于 Laravel 12
Laravel 12 中，不需要注册 Provider 到 app.php 中，需要将 Provider 注册到 bootstrap/providers.php 中。
Laravel 12 中，不存在 Kernel.php，中间件需要到 bootstrap/app.php 中，Schedule 需要到 routes/console.php 中。

当你自定义组件或者使用 tailwindcss 时，都必须使用 tailwindcss 的指令，不要使用其他方式。此外，如果你用的不是 shadcn-vue 的组件，你必须考虑暗色模式。

所有的 types 类型定义都得放在 resources/js/types 目录下。

关于 resources/js/components/ui 中的文件，你只可以读取，不可以修改，这些文件都是通过 pnpm dlx shadcn-vue@latest add <name> 添加的。如果你有需要，也可以通过这个命令添加。

关于 Shadcn Vue
Shadcn Vue 中，如果你需要用到 Toast 组件，Toast 组件已经被废弃了，请使用 Sonner 替代。如果你用到了这个组件不知道文档，请查阅文档。

前端侧边栏在 resources/js/components/AppSidebar.vue 中，你可以直接使用。
侧边栏菜单在 resources/js/components/NavMain.vue 中。
每个页面都需要一个标题，标题正确加法：
import { Head } from '@inertiajs/vue3';
<Head title="团队文件管理" />

如果前端需要分页，用 resources/js/components/Pagination.vue 组件
页面中不得使用 confirm / alert，都必须使用 shadcn 的组件来统一体验
确认操作不能使用 confirm，必须使用 shadcn 的组件



关于事件总线
如果你在一个页面中使用了事件总线并且需要写 onUnmount 时，不能移除所有的监听，只能移除你在这个页面中注册的。

关于前端图标
不能使用 <i class="i-lucide-more-horizontal"></i>，而是必须导入图标

网页请求部分
程序内请求使用 Inertia 的 Link 组件。API 请求必须使用 axios

写代码的时候，所有的模块都要履行单一职责，单向转播

更新：由于权限管理没有完善，请你在写控制器和 Policy 的时候，只需要判断用户是否在这个团队中以及防止用户操作其他团队的内容即可，不需要创造权限

app/Http/Controllers/Controller.php 中提供了一些助手方法，如果你需要响应 API 的，JSON，必须查看这个文件来获取规范化响应的方法

API 响应不要 success, message,data 等结构和字段，应该直接返回实际的 JSON 数据并使用 HTTP 状态码来表示结果
API 响应必须使用 Laravel 的 Resource API 资源来返回数据，要用 php artisan make:resource 来创建。

你无须启动开发服务器，开发服务器已经启动了，访问 http://paasnew.test 即可

这个项目是一个基于 K8s 的 PaaS 平台，用于无需关心底层实现，也不能操作 K8s 集群，而是直接在我们的上面填写镜像环境变量等部署应用即可。
并且不能直接返回 K8s 的原始数据，必须使用 DTO 来包裹数据，去除不必要的信息，比如 labels，annotations 等。
由于使用了 DTO 和改变了信息，所以你必须在写前端时格外注意，不要出现数据不一致的情况。

Cluster 模型里面包含真实可用的 K8s 集群的配置，其对应的 kubeconfig 在 testdata/k8s.yaml 中，你可以直接使用。
在设计中，模型只需要做它该做的事情，不要在模型中实现业务逻辑，业务逻辑应该在 Service 中实现。
app/Service/KubernetesService.php 未来可能会变得冗余，所以请你在写逻辑时比如 ConfigMap 等，使用专门的 class 实现，不要在 KubernetesService 中实现来拆分职责。

所有创建的资源必须包裹 app/ClusterLabel.php 中的标签，Workspace 模型中有个 buildDefaultLabels 方法，你可以直接使用。
Cluster 模型中有个真实可用的 K8s 集群，其 Kubeconfig 文件在/Users/<USER>/Herd/paas/testdata/k8s.yaml，你可以使用 kubectl 测试功能直到预期

集群需要有 IP 池，使用 rlanvin/php-ip 来实现。也需要有端口池，端口池要关联 IP 池中的 IP。

端口分配是从端口池中分配，端口池中要关联 IP 池中的 IP。比如这个 IP 的一个端口没有被使用，则返回这个端口并标记为已使用。当对应的服务删除时，释放这个端口，并且要保证事务。
端口池中要记录 IP 和端口，并且要记录 IP 和端口的使用状态。
IP 池和端口池的实现要考虑并发，要保证事务。

用户不能与 K8s API 直接交互，必须走这个平台的 API。并且用户不应该解除到 labels 和 annotations 等 K8s 的原始信息，而是使用 DTO 来包裹数据。
关于存储，这个平台的存储叫 Storage，而不是 PVC，但是实际上操作的是 K8s 的 PVC。每个节点的 storageclass 都为 longhorn，这些都是固定的。并且创建的 pvc 的读写模式都是 RWX，用户不能选择也不能看见更不能修改。

关于资源限制，用户不能直接操作 K8s 的资源限制，必须走这个平台的 API。并且在填写资源限制时，直接填写数字即可，不要填写字符串。比如："resources": {"memory": 1024,"cpu": 1000}，单位由程序自动添加，并且内存单位是 Mi，CPU 单位是 m。resources 这里指的是 limit 并非 request
其中 内存 和 CPU 的单位是 Mi 和 m，不要使用其他单位。内存的数值不能小于 512 且必须为 512 的倍数。CPU 不能小于 500 且必须为 500 的倍数。
用户提交的数值转换成 K8s 的资源限制时，需要同时变成 request 和 limits，并且它们是不对等的，如果 limits 为 1000，则 request 为 500。如果内存超过 1000，那么它们将乘以 0.5 变成 request，如果内存小于 1000，那么它们将乘以 0.5 变成 request。如果是 CPU，小于 1000 则乘以 0.5，大于 1000 则乘以 0.5。request 都是 limit 的一半
这是防止用户恶意填写资源限制。

Shadcn Vue 中，不要滥用卡片布局，卡片布局一点也不美观实在，请使用其他组件来替代。

Apply 中的 Yaml 编辑器中的格式是我们专有的格式，而不是 K8s 的格式。
我们专有的格式如下：
<专有格式>
api: /api/secrets/generic # 请求的 API 路径，参考 routes/api.php
method: POST # 请求的方法，参考 routes/api.php
type: generic # 请求的类型，参考 routes/api.php
name: mysql-root-password # 名称，和 K8s 的名称一致
data: # body 的数据内容，如果是 get，则为 query 参数
  password: "Qwerty123..."
---
api: /api/configmaps
method: POST
name: wordpress
data:
  WORDPRESS_DB_HOST: mysql8-headless.${namespace}.svc.cluster.local
  WORDPRESS_DB_NAME: wordpress
  WORDPRESS_DB_PASSWORD: Qwerty123...
  WORDPRESS_DB_USER: root
---
api: /api/storages # 对应 K8s 的 PVC
method: POST
name: wordpress
size: 1024 # 大小，单位 Mi（MiB）
---
api: /api/deployments # 无状态服务
method: POST
name: wordpress
replicas: 1 # 副本数
image_pull_secrets: [] # 镜像拉取 secret
containers: # 容器列表
  - name: wordpress # 容器名称
    image: wordpress:6.8.1-php8.1-apache # 镜像
    working_dir: '' # 工作目录
    command: [] # 命令
    args: [] # 参数
    ports: # 端口列表
      - name: http # 端口名称
        container_port: 80 # 容器端口
        protocol: TCP # 协议
    env: [] # 环境变量列表，这是为空的示例
    env_from_configmap: # 从 configmap 中获取环境变量
      - configmap_name: wordpress # configmap 名称
        key: null # 键
        env_name: null # 环境变量名称
    env_from_secret: [] # 从 secret 中获取环境变量
    resources: # 资源限制
      cpu: 1000 # CPU
      memory: 1024 # 内存
    volume_mounts: # 挂载卷列表
      - mount_path: /var/www/html # 挂载路径
        storage_name: wordpress # 存储名称
        sub_path: '' # 子路径
        read_only: false # 是否只读
    configmap_mounts: [] # 从 configmap 中获取环境变量
    secret_mounts: [] # 从 secret 中获取环境变量
---
api: /api/statefulsets # 有状态服务
method: POST
name: mysql8 # 名称，和 K8s 的名称一致
replicas: 1 # 副本数
service_name: '' # 服务名称
image_pull_secrets: [] # 镜像拉取 secret
containers: # 容器列表
  - name: mysql # 容器名称
    image: mysql:8 # 镜像
    working_dir: '' # 工作目录
    command: [] # 命令
    args: [] # 参数
    ports: # 端口列表
      - name: mysql # 端口名称
        container_port: 3306 # 容器端口
        protocol: TCP # 协议
    env: # 环境变量列表
      - name: TEST_ENV # 环境变量名称
        value: test_value # 环境变量值
    env_from_configmap: [] # 从 configmap 中获取环境变量
    env_from_secret: # 从 secret 中获取环境变量
      - secret_name: mysql-root-password # secret 名称
        key: password # 键
        env_name: MYSQL_ROOT_PASSWORD # 环境变量名称
    resources: # 资源限制
      memory: 1024 # 内存
      cpu: 1000 # CPU
    volume_mounts: # 挂载卷列表
      - mount_path: /var/lib/mysql # 挂载路径
        storage_name: mysql # 存储名称
        sub_path: '' # 子路径
        read_only: false # 是否只读
    configmap_mounts: [] # 从 configmap 中获取环境变量
    secret_mounts: [] # 从 secret 中获取环境变量
---
api: /api/services # 服务
method: POST
name: wordpress # 名称，和 K8s 的名称一致
type: ClusterIP # 类型（和 K8s 的类型一致）
target_workload_type: Deployment # 目标工作负载类型
target_workload_name: wordpress # 目标工作负载名称
ports: # 端口列表
  - name: http # 端口名称
    port: 80 # 端口
    target_port: 80 # 目标端口
    protocol: TCP # 协议
session_affinity: None # 会话亲和性（和 K8s 的会话亲和性一致）
external_traffic_policy: Cluster # 外部流量策略（和 K8s 的外部流量策略一致）
</专有格式>
