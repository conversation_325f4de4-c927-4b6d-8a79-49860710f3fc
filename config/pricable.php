<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Pricable Models Configuration
    |--------------------------------------------------------------------------
    |
    | Register all models that use the Pricable trait here.
    | Format: 'model_class' => 'resource_type'
    |
    | This configuration is used to determine which models can have pricing
    | and what unit types are available for each resource type.
    |
    */

    'models' => [
        App\Models\Cluster::class => 'cluster',
        App\Models\IpPool::class => 'ip',
        // Add future models here (e.g. GPU resources)
        // App\Models\GpuPool::class => 'gpu',
    ],

    /*
    |--------------------------------------------------------------------------
    | Unit Types Configuration
    |--------------------------------------------------------------------------
    |
    | Define what unit types are available for each resource type.
    | This helps in validation and UI generation.
    |
    */
    'unit_types' => [
        'cluster' => [
            'memory_gb' => [
                'name' => '内存 (GB)',
                'description' => '内存使用量，按GB计费',
                'precision' => 8,
            ],
            'cpu_core' => [
                'name' => 'CPU (核)',
                'description' => 'CPU使用量，按核计费',
                'precision' => 8,
            ],
            'storage_gb' => [
                'name' => '存储 (GB)',
                'description' => '存储使用量，按GB计费',
                'precision' => 8,
            ],
        ],
        'ip' => [
            'ip_address' => [
                'name' => 'IP 地址 (个)',
                'description' => 'IP地址使用量，按个计费',
                'precision' => 8,
            ],
        ],
        'gpu' => [
            'gpu_hour' => [
                'name' => 'GPU (小时)',
                'description' => 'GPU使用量，按小时计费',
                'precision' => 8,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Billing Resource Handlers
    |--------------------------------------------------------------------------
    |
    | These classes handle the billing calculation for different resource types.
    | They are responsible for converting raw usage data into billable amounts.
    |
    */
    'handlers' => [
        'pod' => App\Service\Billing\Handlers\PodBillingHandler::class,
        'service' => App\Service\Billing\Handlers\ServiceBillingHandler::class,
        'storage' => App\Service\Billing\Handlers\StorageBillingHandler::class,
        // 'gpu' => App\Service\Billing\Handlers\GpuResourceHandler::class,
    ],

    'default_cost' => '0.00000000',
];
