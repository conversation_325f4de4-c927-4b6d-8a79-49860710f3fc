<?php

return [
    'defaults' => [
        'securityContext' => [
            'allowPrivilegeEscalation' => false,
            'runAsNonRoot' => false, // 允许以 root 运行，提高兼容性
            'seccompProfile' => [
                'type' => 'RuntimeDefault',
            ],
            // 'runAsUser' => 1000, // 不需要，会影响应用的运行
            // 'fsGroup' => 1000, // 不需要，会影响应用的运行
            'capabilities' => [
                'drop' => ['ALL'],
                'add' => [
                    // 保留必要的权限以支持大多数应用
                    'NET_BIND_SERVICE',  // 绑定端口
                    'CHOWN',            // 文件所有权
                    'DAC_OVERRIDE',     // 覆盖文件权限
                    'SETGID',           // 设置组ID
                    'SETUID',           // 设置用户ID
                    'SYS_RESOURCE',     // 系统资源管理
                    'KILL',             // 终止进程
                    'SETPCAP',          // 设置进程权限
                ],
            ],
            'readOnlyRootFilesystem' => false, // 允许写入根文件系统
        ],
    ],

    // Pod Security Standards 配置
    'podSecurity' => [
        // 默认使用 privileged 级别，确保最大兼容性
        'enforce' => env('K8S_POD_SECURITY_ENFORCE', 'privileged'),
        'audit' => env('K8S_POD_SECURITY_AUDIT', 'baseline'),
        'warn' => env('K8S_POD_SECURITY_WARN', 'baseline'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Workspace 资源配额配置
    |--------------------------------------------------------------------------
    |
    | 每个 Workspace 的默认资源配额限制
    |
    */
    'workspace' => [
        'resourceQuota' => [
            // 内存限制 (单位: Mi)
            'memory' => env('K8S_WORKSPACE_MEMORY_LIMIT', 131072), // 128GB = 128 * 1024 Mi

            // CPU 限制 (单位: m)
            'cpu' => env('K8S_WORKSPACE_CPU_LIMIT', 64000), // 64000m = 64 cores

            // 存储限制 (单位: Gi)
            'storage' => env('K8S_WORKSPACE_STORAGE_LIMIT', 1024), // 1024Gi = 1TB

            // Pod 数量限制
            'pods' => env('K8S_WORKSPACE_POD_LIMIT', 100),

            // PVC 数量限制
            'persistentvolumeclaims' => env('K8S_WORKSPACE_PVC_LIMIT', 50),

            // Service 数量限制
            'services' => env('K8S_WORKSPACE_SERVICE_LIMIT', 50),

            // ConfigMap 数量限制
            'configmaps' => env('K8S_WORKSPACE_CONFIGMAP_LIMIT', 100),

            // Secret 数量限制
            'secrets' => env('K8S_WORKSPACE_SECRET_LIMIT', 100),
        ],

        'networkPolicy' => [
            // 是否启用网络策略隔离
            'enabled' => env('K8S_WORKSPACE_NETWORK_POLICY_ENABLED', false),

            // 默认拒绝所有入站流量
            'defaultDenyIngress' => env('K8S_WORKSPACE_DEFAULT_DENY_INGRESS', false),

            // 默认拒绝所有出站流量
            'defaultDenyEgress' => env('K8S_WORKSPACE_DEFAULT_DENY_EGRESS', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 容器镜像验证配置
    |--------------------------------------------------------------------------
    |
    | 配置容器镜像验证相关设置
    |
    */
    'imageValidation' => [
        // 是否启用镜像验证
        'enabled' => env('K8S_IMAGE_VALIDATION_ENABLED', true),

        // 是否启用严格验证模式（验证失败时拒绝创建）
        'strict_mode' => env('K8S_IMAGE_VALIDATION_STRICT_MODE', false),

        // 镜像大小限制 (单位: MB)
        'max_size_mb' => env('K8S_IMAGE_VALIDATION_MAX_SIZE_MB', 10240), // 10GB

        // 验证超时时间 (单位: 秒)
        'timeout_seconds' => env('K8S_IMAGE_VALIDATION_TIMEOUT', 30),

        // 是否跳过私有镜像验证（当无法获取凭据时）
        'skip_private_images' => env('K8S_IMAGE_VALIDATION_SKIP_PRIVATE', true),

        // 允许的镜像仓库白名单（空数组表示允许所有）
        'allowed_registries' => array_filter(explode(',', env('K8S_IMAGE_VALIDATION_ALLOWED_REGISTRIES', ''))),

        // 禁止的镜像仓库黑名单
        'blocked_registries' => array_filter(explode(',', env('K8S_IMAGE_VALIDATION_BLOCKED_REGISTRIES', ''))),
    ],

    /*
    |--------------------------------------------------------------------------
    | 欠费处理配置
    |--------------------------------------------------------------------------
    |
    | 配置用户欠费后的处理策略
    |
    */
    'billing' => [
        // 欠费超过多少天后删除工作区
        'max_overdue_days' => env('K8S_BILLING_MAX_OVERDUE_DAYS', 14),

        // 是否启用自动暂停欠费用户的工作区
        'auto_suspend_overdue' => env('K8S_BILLING_AUTO_SUSPEND_OVERDUE', true),

        // 是否启用自动删除长期欠费的工作区
        'auto_delete_overdue' => env('K8S_BILLING_AUTO_DELETE_OVERDUE', true),
    ],

    'resources' => [
        'disable_requests' => true,
    ],

    'max_labels' => 16,
    'allow_label_prefix' => [
        'x-',
    ],
];
