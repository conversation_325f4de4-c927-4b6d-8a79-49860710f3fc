<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default LoadBalancer Driver
    |--------------------------------------------------------------------------
    |
    | This option controls the default LoadBalancer driver that will be used
    | when no specific driver is requested.
    |
    */
    'default' => env('LOADBALANCER_DEFAULT_DRIVER', 'cilium'),

    /*
    |--------------------------------------------------------------------------
    | LoadBalancer Drivers
    |--------------------------------------------------------------------------
    |
    | Here you may configure the LoadBalancer drivers for your application.
    | Each driver has its own configuration and implementation class.
    |
    */
    'drivers' => [
        'metallb' => [
            'name' => 'MetalLB',
            'class' => \App\Service\LoadBalancerDrivers\MetallbDriver::class,
            'description' => 'MetalLB is a load-balancer implementation for bare metal Kubernetes clusters.',
        ],

        'purelb' => [
            'name' => 'PureLB',
            'class' => \App\Service\LoadBalancerDrivers\PureLbDriver::class,
            'description' => 'PureLB is a service load balancer for Kubernetes that uses standard Linux networking.',
        ],

        'cilium' => [
            'name' => 'Cilium',
            'class' => \App\Service\LoadBalancerDrivers\CiliumDriver::class,
            'description' => 'Cilium LoadBalancer IPAM provides IP address management for LoadBalancer services.',
        ],
    ],
];
