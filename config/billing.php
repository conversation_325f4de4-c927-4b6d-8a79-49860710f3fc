<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 计费配置
    |--------------------------------------------------------------------------
    |
    | 此文件包含了PaaS平台计费系统的所有配置选项
    |
    */

    /*
    |--------------------------------------------------------------------------
    | 欠费处理配置
    |--------------------------------------------------------------------------
    */
    'overdue' => [
        /*
        | 工作空间暂停后多少天将被彻底删除（天）
        */
        'deletion_days' => env('BILLING_DELETION_DAYS', 7),

        /*
        | 欠费通知配置（天）
        */
        'notification' => [
            'first_warning_days' => env('BILLING_FIRST_WARNING_DAYS', 3),  // 首次警告（欠费3天后）
            'final_warning_days' => env('BILLING_FINAL_WARNING_DAYS', 6),  // 最终警告（欠费6天后）
        ],

        /*
        | 欠费宽限期配置（天）
        */
        'grace_period' => [
            'payment_grace_days' => env('BILLING_PAYMENT_GRACE_DAYS', 1),  // 支付宽限期（1天）
            'service_grace_days' => env('BILLING_SERVICE_GRACE_DAYS', 0),  // 服务宽限期（0天，立即暂停）
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 计费周期配置
    |--------------------------------------------------------------------------
    */
    'cycle' => [
        /*
        | 计费频率（分钟）
        */
        'billing_interval_minutes' => env('BILLING_INTERVAL_MINUTES', 1),

        /*
        | 计费记录保留天数
        */
        'record_retention_days' => env('BILLING_RECORD_RETENTION_DAYS', 30),

        /*
        | 用户余额历史保留天数
        */
        'balance_history_retention_days' => env('BILLING_BALANCE_HISTORY_RETENTION_DAYS', 1095), // 3年
    ],

    /*
    |--------------------------------------------------------------------------
    | 暂停和恢复配置
    |--------------------------------------------------------------------------
    */
    'suspension' => [
        /*
        | 资源暂停配置
        */
        'auto_suspend_on_overdue' => env('BILLING_AUTO_SUSPEND', true),

        /*
        | 资源恢复配置
        */
        'auto_resume_on_payment' => env('BILLING_AUTO_RESUME', true),

        /*
        | 暂停保护期（分钟）- 防止频繁暂停/恢复
        */
        'protection_minutes' => env('BILLING_PROTECTION_MINUTES', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | 清理任务配置
    |--------------------------------------------------------------------------
    */
    'cleanup' => [
        /*
        | 定时清理任务配置
        */
        'enabled' => env('BILLING_CLEANUP_ENABLED', true),

        /*
        | 每次清理任务的最大处理数量
        */
        'batch_size' => env('BILLING_CLEANUP_BATCH_SIZE', 50),

        /*
        | 清理任务运行频率（cron表达式）
        */
        'schedule' => env('BILLING_CLEANUP_SCHEDULE', '0 2 * * *'), // 每天凌晨2点

        /*
        | 是否在清理前发送通知
        */
        'send_notification' => env('BILLING_CLEANUP_NOTIFICATION', true),

        /*
        | 清理日志保留天数
        */
        'log_retention_days' => env('BILLING_CLEANUP_LOG_RETENTION_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | 通知配置
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        /*
        | 余额不足通知阈值（元）
        */
        'low_balance_threshold' => env('BILLING_LOW_BALANCE_THRESHOLD', 10.00),

        /*
        | 通知渠道
        */
        'channels' => [
            'email' => env('BILLING_NOTIFICATION_EMAIL', true),
            'sms' => env('BILLING_NOTIFICATION_SMS', false),
            'webhook' => env('BILLING_NOTIFICATION_WEBHOOK', false),
        ],

        /*
        | 通知频率限制（小时）
        */
        'rate_limit_hours' => env('BILLING_NOTIFICATION_RATE_LIMIT', 6),
    ],

    /*
    |--------------------------------------------------------------------------
    | 调试和测试配置
    |--------------------------------------------------------------------------
    */
    'debug' => [
        /*
        | 是否启用计费调试模式
        */
        'enabled' => env('BILLING_DEBUG', false),

        /*
        | 调试模式下的虚拟扣费（不实际扣费）
        */
        'mock_charging' => env('BILLING_MOCK_CHARGING', false),

        /*
        | 测试模式配置
        */
        'test_mode' => env('BILLING_TEST_MODE', false),
    ],
];
