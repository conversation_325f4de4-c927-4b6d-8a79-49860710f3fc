<?php

namespace App\Exceptions\K8s;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class K8sConnectionException extends Exception
{
    protected $code = Response::HTTP_SERVICE_UNAVAILABLE;

    public function __construct(string $message = '集群连接失败', ?\Throwable $previous = null)
    {
        parent::__construct($message, $this->code, $previous);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'k8s_connection_error',
            ], $this->code);
        }

        return response()->view('errors.503', ['message' => $this->getMessage()], $this->code);
    }
}
