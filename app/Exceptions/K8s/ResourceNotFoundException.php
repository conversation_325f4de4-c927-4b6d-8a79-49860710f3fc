<?php

namespace App\Exceptions\K8s;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class ResourceNotFoundException extends Exception
{
    protected $code = Response::HTTP_NOT_FOUND;

    public function __construct(string $resourceType, string $resourceName, ?string $namespace = null)
    {
        $message = "资源 {$resourceType} '{$resourceName}' 不存在";
        if ($namespace) {
            $message .= " (在命名空间 '{$namespace}' 中)";
        }

        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'resource_not_found',
            ], $this->code);
        }

        return response()->view('errors.404', ['message' => $this->getMessage()], $this->code);
    }
}
