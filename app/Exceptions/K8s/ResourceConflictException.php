<?php

namespace App\Exceptions\K8s;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class ResourceConflictException extends Exception
{
    protected $code = Response::HTTP_CONFLICT;

    public function __construct(string $message = '资源冲突', array $conflicts = [])
    {
        if (! empty($conflicts)) {
            $message .= ': '.implode('; ', $conflicts);
        }

        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'resource_conflict',
            ], $this->code);
        }

        return response()->view('errors.409', ['message' => $this->getMessage()], $this->code);
    }
}
