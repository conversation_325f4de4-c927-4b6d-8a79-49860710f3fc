<?php

namespace App\Exceptions\K8s;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class ResourceValidationException extends Exception
{
    protected $code = Response::HTTP_UNPROCESSABLE_ENTITY;

    private array $validationErrors;

    public function __construct(string $message = '资源验证失败', array $validationErrors = [])
    {
        $this->validationErrors = $validationErrors;

        if (! empty($validationErrors)) {
            $message .= ': '.implode('; ', $validationErrors);
        }

        parent::__construct($message, $this->code);
    }

    public function getValidationErrors(): array
    {
        return $this->validationErrors;
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'validation_error',
                'validation_errors' => $this->validationErrors,
            ], $this->code);
        }

        return response()->view('errors.422', [
            'message' => $this->getMessage(),
            'errors' => $this->validationErrors,
        ], $this->code);
    }
}
