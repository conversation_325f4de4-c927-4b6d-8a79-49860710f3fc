<?php

namespace App\Exceptions\Payment;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class PaymentAmountException extends Exception
{
    protected $code = Response::HTTP_BAD_REQUEST;

    public function __construct(string $message)
    {
        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'payment_amount_invalid',
            ], $this->code);
        }

        return response()->view('errors.400', ['message' => $this->getMessage()], $this->code);
    }
}
