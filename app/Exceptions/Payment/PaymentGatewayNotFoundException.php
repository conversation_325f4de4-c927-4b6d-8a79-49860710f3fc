<?php

namespace App\Exceptions\Payment;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class PaymentGatewayNotFoundException extends Exception
{
    protected $code = Response::HTTP_NOT_FOUND;

    public function __construct(string $gatewayIdentifier)
    {
        parent::__construct("支付网关 '{$gatewayIdentifier}' 不存在", $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'payment_gateway_not_found',
            ], $this->code);
        }

        return response()->view('errors.404', ['message' => $this->getMessage()], $this->code);
    }
}
