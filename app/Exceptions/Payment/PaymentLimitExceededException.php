<?php

namespace App\Exceptions\Payment;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class PaymentLimitExceededException extends Exception
{
    protected $code = Response::HTTP_PAYMENT_REQUIRED;

    public function __construct(string $message)
    {
        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'payment_limit_exceeded',
            ], $this->code);
        }

        return response()->view('errors.402', ['message' => $this->getMessage()], $this->code);
    }
}
