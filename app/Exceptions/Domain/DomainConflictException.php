<?php

namespace App\Exceptions\Domain;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class DomainConflictException extends Exception
{
    protected $code = Response::HTTP_CONFLICT;

    private array $conflicts;

    public function __construct(array $conflicts = [], string $message = '域名冲突')
    {
        $this->conflicts = $conflicts;

        if (! empty($conflicts)) {
            $conflictMessages = array_column($conflicts, 'message');
            $message .= ': '.implode('; ', $conflictMessages);
        }

        parent::__construct($message, $this->code);
    }

    public function getConflicts(): array
    {
        return $this->conflicts;
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'domain_conflict',
                'conflicts' => $this->conflicts,
            ], $this->code);
        }

        return response()->view('errors.409', [
            'message' => $this->getMessage(),
            'conflicts' => $this->conflicts,
        ], $this->code);
    }
}
