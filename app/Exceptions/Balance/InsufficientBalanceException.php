<?php

namespace App\Exceptions\Balance;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class InsufficientBalanceException extends Exception
{
    protected $code = Response::HTTP_PAYMENT_REQUIRED;

    public function __construct(string $message = '余额不足')
    {
        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'insufficient_balance',
            ], $this->code);
        }

        return response()->view('errors.402', ['message' => $this->getMessage()], $this->code);
    }
}
