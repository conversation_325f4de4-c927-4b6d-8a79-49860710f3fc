<?php

namespace App\Exceptions\Balance;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class InvalidAmountException extends Exception
{
    protected $code = Response::HTTP_BAD_REQUEST;

    public function __construct(string $message = '无效的金额')
    {
        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'invalid_amount',
            ], $this->code);
        }

        return response()->view('errors.400', ['message' => $this->getMessage()], $this->code);
    }
}
