<?php

namespace App\Exceptions\Network;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class IpPoolExhaustedException extends Exception
{
    protected $code = Response::HTTP_SERVICE_UNAVAILABLE;

    public function __construct(string $message = 'IP池已耗尽，无可用IP地址')
    {
        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'ip_pool_exhausted',
            ], $this->code);
        }

        return response()->view('errors.503', ['message' => $this->getMessage()], $this->code);
    }
}
