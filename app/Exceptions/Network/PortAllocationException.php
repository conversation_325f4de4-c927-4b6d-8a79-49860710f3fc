<?php

namespace App\Exceptions\Network;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class PortAllocationException extends Exception
{
    protected $code = Response::HTTP_SERVICE_UNAVAILABLE;

    public function __construct(string $message = '端口分配失败')
    {
        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'port_allocation_failed',
            ], $this->code);
        }

        return response()->view('errors.503', ['message' => $this->getMessage()], $this->code);
    }
}
