<?php

namespace App\Exceptions\Service;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class ServiceConflictException extends Exception
{
    protected $code = Response::HTTP_CONFLICT;

    public function __construct(string $serviceName, ?string $message = null)
    {
        $message = $message ?: "Service '{$serviceName}' 已存在";
        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'service_conflict',
            ], $this->code);
        }

        return response()->view('errors.409', ['message' => $this->getMessage()], $this->code);
    }
}
