<?php

namespace App\Exceptions\Device;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class DeviceAlreadyBoundException extends Exception
{
    protected $code = Response::HTTP_CONFLICT;

    public function __construct(string $deviceId, ?string $message = null)
    {
        $message = $message ?: "设备 '{$deviceId}' 已被其他用户绑定";
        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'device_already_bound',
            ], $this->code);
        }

        return response()->view('errors.409', ['message' => $this->getMessage()], $this->code);
    }
}
