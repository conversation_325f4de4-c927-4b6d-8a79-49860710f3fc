<?php

namespace App\Exceptions\User;

use Exception;
use Symfony\Component\HttpFoundation\Response;

class UserBelongsToTeamException extends Exception
{
    protected $code = Response::HTTP_FORBIDDEN;

    public function __construct(string $message = '您不属于此团队或没有相应权限')
    {
        parent::__construct($message, $this->code);
    }

    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $this->getMessage(),
                'type' => 'user_not_belongs_to_team',
            ], $this->code);
        }

        return response()->view('errors.403', ['message' => $this->getMessage()], $this->code);
    }
}
