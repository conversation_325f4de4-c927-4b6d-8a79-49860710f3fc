<?php

namespace App;

enum ClusterLabel: string
{
    case PLATFORM = 'run.leaflow.cn';
    case WORKSPACE = 'run.leaflow.cn/workspace';
    case RESOURCE_ID = 'run.leaflow.cn/resource-id';
    case USER = 'run.leaflow.cn/user';
    case MANAGED_BY = 'run.leaflow.cn/managed-by';

    case WORKLOAD_TYPE = 'run.leaflow.cn/workload-type';
    case WORKLOAD_NAME = 'run.leaflow.cn/workload-name';

    case ORIGINAL_REPLICAS = 'run.leaflow.cn/original-replicas';
    case SUSPENDED_REASON = 'run.leaflow.cn/suspended-reason';
    case SUSPENDED_AT = 'run.leaflow.cn/suspended-at';
    case RESUMED_AT = 'run.leaflow.cn/resumed-at';

    case WORKLOAD_TYPE_STATEFULSET = 'statefulset';
    case WORKLOAD_TYPE_DEPLOYMENT = 'deployment';
    case WORKLOAD_TYPE_DAEMONSET = 'daemonset';
    case WORKLOAD_TYPE_JOB = 'job';
    case WORKLOAD_TYPE_CRONJOB = 'cronjob';
    case WORKLOAD_TYPE_POD = 'pod';
    case WORKLOAD_TYPE_SERVICE = 'service';
    case WORKLOAD_TYPE_INGRESS = 'ingress';
}
