<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreStorageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $workspace = $this->user()->getWorkspace();

        return $this->user()->can('update', $workspace);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:63',
                'regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/',
            ],
            'labels' => ['nullable', 'array', new \App\Rules\CustomLabelRule],
            'size' => [
                'required',
                'integer',
                'min:512',
                function ($attribute, $value, $fail) {
                    if ($value % 512 !== 0) {
                        $fail('存储容量必须是 512Mi 的倍数');
                    }
                },
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'Storage 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'size.min' => '存储容量不能小于 512M',
        ];
    }
}
