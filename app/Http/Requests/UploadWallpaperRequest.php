<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UploadWallpaperRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * 限制最多 2MB，仅允许常见图片类型（使用 fileinfo 进行 mime 检测）
     */
    public function rules(): array
    {
        return [
            'wallpaper' => [
                'required',
                'file',
                'mimetypes:image/jpeg,image/png,image/webp,image/gif',
                'max:2048', // 单位 KB => 2MB
            ],
        ];
    }

    /**
     * 自定义错误消息
     */
    public function messages(): array
    {
        return [
            'wallpaper.required' => '请选择要上传的壁纸文件。',
            'wallpaper.file' => '壁纸必须是文件。',
            'wallpaper.mimetypes' => '仅支持 JPG、PNG、WEBP、GIF 图片格式。',
            'wallpaper.max' => '壁纸大小不能超过 2MB。',
        ];
    }
}
