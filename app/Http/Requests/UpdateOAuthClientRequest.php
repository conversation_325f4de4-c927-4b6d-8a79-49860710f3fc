<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOAuthClientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'redirect' => ['required', 'url', 'max:500'],
            'icon' => ['nullable', 'image', 'mimes:png,jpg,jpeg,gif,svg', 'max:2048'], // 最大 2MB
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => '客户端名称不能为空',
            'name.max' => '客户端名称不能超过 255 个字符',
            'redirect.required' => '回调地址不能为空',
            'redirect.url' => '回调地址必须是有效的 URL',
            'redirect.max' => '回调地址不能超过 500 个字符',
            'icon.image' => '图标必须是图片文件',
            'icon.mimes' => '图标格式必须是 png、jpg、jpeg、gif 或 svg',
            'icon.max' => '图标文件大小不能超过 2MB',
        ];
    }
}
