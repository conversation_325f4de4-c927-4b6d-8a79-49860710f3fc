<?php

namespace App\Http\Requests;

trait BaseWorkloadValidation
{
    /**
     * 获取通用的工作负载验证规则
     */
    protected function getBaseRules(): array
    {
        return [
            'replicas' => 'nullable|integer|min:1|max:100',
            'labels' => ['nullable', 'array', new \App\Rules\CustomLabelRule],
            'containers' => 'required|array|min:1',
            'containers.*.name' => ['required', 'string', 'max:63', new \App\Rules\NoNumbersOrSymbols],
            'containers.*.image' => 'required|string',

            // Command 和 Args
            'containers.*.command' => 'nullable|array',
            'containers.*.command.*' => 'string',
            'containers.*.args' => 'nullable|array',
            'containers.*.args.*' => 'string',

            // 工作目录
            'containers.*.working_dir' => 'nullable|string',

            // 端口配置
            'containers.*.ports' => 'nullable|array',
            'containers.*.ports.*.name' => ['nullable', 'string', 'max:15', new \App\Rules\NoNumbersOrSymbols],
            'containers.*.ports.*.container_port' => 'required|integer|min:1|max:65535',
            'containers.*.ports.*.protocol' => 'nullable|string|in:TCP,UDP',

            // 环境变量（直接设置）
            'containers.*.env' => 'nullable|array',
            'containers.*.env.*.name' => 'required|string',
            'containers.*.env.*.value' => 'required|string',

            // 从 ConfigMap 引用的环境变量
            'containers.*.env_from_configmap' => 'nullable|array',
            'containers.*.env_from_configmap.*.configmap_name' => ['required', 'string', new \App\Rules\KubernetesNameRule],
            'containers.*.env_from_configmap.*.key' => 'nullable|string', // 如果为空，则引用整个 ConfigMap
            'containers.*.env_from_configmap.*.env_name' => 'nullable|string', // 环境变量名，默认使用 key

            // 从 Secret 引用的环境变量
            'containers.*.env_from_secret' => 'nullable|array',
            'containers.*.env_from_secret.*.secret_name' => ['required', 'string', new \App\Rules\KubernetesNameRule],
            'containers.*.env_from_secret.*.key' => 'nullable|string', // 如果为空，则引用整个 Secret
            'containers.*.env_from_secret.*.env_name' => 'nullable|string', // 环境变量名，默认使用 key

            // 资源限制
            'containers.*.resources' => 'required|array',
            'containers.*.resources.memory' => 'required|integer|min:512',
            'containers.*.resources.cpu' => 'required|integer|min:500',

            // 健康检查探针
            'containers.*.liveness_probe' => 'nullable|array',
            'containers.*.liveness_probe.type' => 'required_with:containers.*.liveness_probe|string|in:http,tcp,exec',
            'containers.*.liveness_probe.initial_delay_seconds' => 'nullable|integer|min:0|max:3600',
            'containers.*.liveness_probe.period_seconds' => 'nullable|integer|min:1|max:3600',
            'containers.*.liveness_probe.timeout_seconds' => 'nullable|integer|min:1|max:3600',
            'containers.*.liveness_probe.success_threshold' => 'nullable|integer|min:1|max:10',
            'containers.*.liveness_probe.failure_threshold' => 'nullable|integer|min:1|max:10',
            // HTTP 探针
            'containers.*.liveness_probe.http_path' => 'required_if:containers.*.liveness_probe.type,http|string',
            'containers.*.liveness_probe.http_port' => 'required_if:containers.*.liveness_probe.type,http|integer|min:1|max:65535',
            'containers.*.liveness_probe.http_scheme' => 'nullable|string|in:HTTP,HTTPS',
            'containers.*.liveness_probe.http_headers' => 'nullable|array',
            'containers.*.liveness_probe.http_headers.*.name' => 'required|string',
            'containers.*.liveness_probe.http_headers.*.value' => 'required|string',
            // TCP 探针
            'containers.*.liveness_probe.tcp_port' => 'required_if:containers.*.liveness_probe.type,tcp|integer|min:1|max:65535',
            // Exec 探针
            'containers.*.liveness_probe.exec_command' => 'required_if:containers.*.liveness_probe.type,exec|array',
            'containers.*.liveness_probe.exec_command.*' => 'string',

            'containers.*.readiness_probe' => 'nullable|array',
            'containers.*.readiness_probe.type' => 'required_with:containers.*.readiness_probe|string|in:http,tcp,exec',
            'containers.*.readiness_probe.initial_delay_seconds' => 'nullable|integer|min:0|max:3600',
            'containers.*.readiness_probe.period_seconds' => 'nullable|integer|min:1|max:3600',
            'containers.*.readiness_probe.timeout_seconds' => 'nullable|integer|min:1|max:3600',
            'containers.*.readiness_probe.success_threshold' => 'nullable|integer|min:1|max:10',
            'containers.*.readiness_probe.failure_threshold' => 'nullable|integer|min:1|max:10',
            // HTTP 探针
            'containers.*.readiness_probe.http_path' => 'required_if:containers.*.readiness_probe.type,http|string',
            'containers.*.readiness_probe.http_port' => 'required_if:containers.*.readiness_probe.type,http|integer|min:1|max:65535',
            'containers.*.readiness_probe.http_scheme' => 'nullable|string|in:HTTP,HTTPS',
            'containers.*.readiness_probe.http_headers' => 'nullable|array',
            'containers.*.readiness_probe.http_headers.*.name' => 'required|string',
            'containers.*.readiness_probe.http_headers.*.value' => 'required|string',
            // TCP 探针
            'containers.*.readiness_probe.tcp_port' => 'required_if:containers.*.readiness_probe.type,tcp|integer|min:1|max:65535',
            // Exec 探针
            'containers.*.readiness_probe.exec_command' => 'required_if:containers.*.readiness_probe.type,exec|array',
            'containers.*.readiness_probe.exec_command.*' => 'string',

            'containers.*.startup_probe' => 'nullable|array',
            'containers.*.startup_probe.type' => 'required_with:containers.*.startup_probe|string|in:http,tcp,exec',
            'containers.*.startup_probe.initial_delay_seconds' => 'nullable|integer|min:0|max:3600',
            'containers.*.startup_probe.period_seconds' => 'nullable|integer|min:1|max:3600',
            'containers.*.startup_probe.timeout_seconds' => 'nullable|integer|min:1|max:3600',
            'containers.*.startup_probe.success_threshold' => 'nullable|integer|min:1|max:10',
            'containers.*.startup_probe.failure_threshold' => 'nullable|integer|min:1|max:30',
            // HTTP 探针
            'containers.*.startup_probe.http_path' => 'required_if:containers.*.startup_probe.type,http|string',
            'containers.*.startup_probe.http_port' => 'required_if:containers.*.startup_probe.type,http|integer|min:1|max:65535',
            'containers.*.startup_probe.http_scheme' => 'nullable|string|in:HTTP,HTTPS',
            'containers.*.startup_probe.http_headers' => 'nullable|array',
            'containers.*.startup_probe.http_headers.*.name' => 'required|string',
            'containers.*.startup_probe.http_headers.*.value' => 'required|string',
            // TCP 探针
            'containers.*.startup_probe.tcp_port' => 'required_if:containers.*.startup_probe.type,tcp|integer|min:1|max:65535',
            // Exec 探针
            'containers.*.startup_probe.exec_command' => 'required_if:containers.*.startup_probe.type,exec|array',
            'containers.*.startup_probe.exec_command.*' => 'string',

            // 存储卷挂载
            'containers.*.volume_mounts' => 'nullable|array',
            'containers.*.volume_mounts.*.mount_path' => [
                'required',
                'string',
                'regex:/^\/([^\/\0]+\/?)*$/',  // 强化路径格式校验
            ],
            'containers.*.volume_mounts.*.mount_path.regex' => '挂载路径必须以 / 开头',

            'containers.*.volume_mounts.*.storage_name' => [
                'required',
                'string',
                new \App\Rules\KubernetesNameRule,
            ],
            'containers.*.volume_mounts.*.storage_name.regex' => 'Storage 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',

            'containers.*.volume_mounts.*.sub_path' => [
                'nullable',
                'string',
                'regex:/^[^\/\0]([^\/\0]*[^\/\0])?$/',  // 禁止绝对路径和空路径
                'not_regex:/(\/|^)\.\.(\/|$)/',          // 禁止路径回溯(..)
            ],
            'containers.*.volume_mounts.*.read_only' => 'nullable|boolean',

            // ConfigMap 文件挂载
            'containers.*.configmap_mounts' => 'nullable|array',
            'containers.*.configmap_mounts.*.configmap_name' => ['required', 'string', new \App\Rules\KubernetesNameRule],
            'containers.*.configmap_mounts.*.mount_path' => ['required', 'string', 'regex:/^\/.*$/'],
            'containers.*.configmap_mounts.*.items' => 'nullable|array', // 如果为空，挂载整个 ConfigMap
            'containers.*.configmap_mounts.*.items.*.key' => 'required|string',
            'containers.*.configmap_mounts.*.items.*.path' => 'required|string',
            'containers.*.configmap_mounts.*.default_mode' => 'nullable|string|regex:/^0?[0-7]{1,3}$/', // 八进制权限字符串

            // Secret 文件挂载
            'containers.*.secret_mounts' => 'nullable|array',
            'containers.*.secret_mounts.*.secret_name' => ['required', 'string', new \App\Rules\KubernetesNameRule],
            'containers.*.secret_mounts.*.mount_path' => ['required', 'string', 'regex:/^\/.*$/'],
            'containers.*.secret_mounts.*.items' => 'nullable|array', // 如果为空，挂载整个 Secret
            'containers.*.secret_mounts.*.items.*.key' => 'required|string',
            'containers.*.secret_mounts.*.items.*.path' => 'required|string',
            'containers.*.secret_mounts.*.default_mode' => 'nullable|string|regex:/^0?[0-7]{1,3}$/', // 八进制权限字符串

            // 镜像拉取密钥
            'image_pull_secrets' => 'nullable|array',
            'image_pull_secrets.*' => ['required', new \App\Rules\ImagePullSecretRule],
            'image_pull_policy' => 'required_with:image_pull_secrets|string|in:Always,Never,IfNotPresent',
        ];
    }

    /**
     * 获取通用的错误消息
     */
    protected function getBaseMessages(): array
    {
        return [
            'containers.*.resources.memory.min' => '内存不能小于 512Mi',
            'containers.*.resources.cpu.min' => 'CPU 不能小于 500m',
            'containers.*.command.*.string' => 'Command 必须是字符串',
            'containers.*.args.*.string' => 'Args 必须是字符串',
            'containers.*.name.*.validate' => '容器名称必须以字母开头，只能包含字母和数字',
            'containers.*.ports.*.name.*.validate' => '端口名称必须以字母开头，只能包含字母和数字',
            'containers.*.configmap_mounts.*.mount_path.regex' => '挂载路径必须以 / 开头',
            'containers.*.secret_mounts.*.mount_path.regex' => '挂载路径必须以 / 开头',
            'containers.*.env_from_configmap.*.configmap_name.required' => 'ConfigMap 名称不能为空',
            'containers.*.env_from_configmap.*.configmap_name.regex' => 'ConfigMap 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'containers.*.env_from_secret.*.secret_name.required' => 'Secret 名称不能为空',
            'containers.*.env_from_secret.*.secret_name.regex' => 'Secret 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'containers.*.configmap_mounts.*.configmap_name.required' => 'ConfigMap 名称不能为空',
            'containers.*.configmap_mounts.*.configmap_name.regex' => 'ConfigMap 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'containers.*.configmap_mounts.*.mount_path.required' => '挂载路径不能为空',
            'containers.*.secret_mounts.*.secret_name.required' => 'Secret 名称不能为空',
            'containers.*.secret_mounts.*.secret_name.regex' => 'Secret 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'containers.*.secret_mounts.*.mount_path.required' => '挂载路径不能为空',
            'containers.*.configmap_mounts.*.default_mode.max' => '文件权限不能超过 777 (511)',
            'containers.*.secret_mounts.*.default_mode.max' => '文件权限不能超过 777 (511)',

            // 健康检查探针错误消息
            'containers.*.liveness_probe.type.required_with' => '存活探针类型不能为空',
            'containers.*.liveness_probe.type.in' => '存活探针类型必须是 http、tcp 或 exec',
            'containers.*.liveness_probe.http_path.required_if' => 'HTTP 探针必须指定路径',
            'containers.*.liveness_probe.http_port.required_if' => 'HTTP 探针必须指定端口',
            'containers.*.liveness_probe.tcp_port.required_if' => 'TCP 探针必须指定端口',
            'containers.*.liveness_probe.exec_command.required_if' => 'Exec 探针必须指定命令',
            'containers.*.liveness_probe.initial_delay_seconds.max' => '初始延迟不能超过 3600 秒',
            'containers.*.liveness_probe.period_seconds.max' => '探测周期不能超过 3600 秒',
            'containers.*.liveness_probe.timeout_seconds.max' => '超时时间不能超过 3600 秒',
            'containers.*.liveness_probe.failure_threshold.max' => '失败阈值不能超过 10',

            'containers.*.readiness_probe.type.required_with' => '就绪探针类型不能为空',
            'containers.*.readiness_probe.type.in' => '就绪探针类型必须是 http、tcp 或 exec',
            'containers.*.readiness_probe.http_path.required_if' => 'HTTP 探针必须指定路径',
            'containers.*.readiness_probe.http_port.required_if' => 'HTTP 探针必须指定端口',
            'containers.*.readiness_probe.tcp_port.required_if' => 'TCP 探针必须指定端口',
            'containers.*.readiness_probe.exec_command.required_if' => 'Exec 探针必须指定命令',
            'containers.*.readiness_probe.initial_delay_seconds.max' => '初始延迟不能超过 3600 秒',
            'containers.*.readiness_probe.period_seconds.max' => '探测周期不能超过 3600 秒',
            'containers.*.readiness_probe.timeout_seconds.max' => '超时时间不能超过 3600 秒',
            'containers.*.readiness_probe.failure_threshold.max' => '失败阈值不能超过 10',

            'containers.*.startup_probe.type.required_with' => '启动探针类型不能为空',
            'containers.*.startup_probe.type.in' => '启动探针类型必须是 http、tcp 或 exec',
            'containers.*.startup_probe.http_path.required_if' => 'HTTP 探针必须指定路径',
            'containers.*.startup_probe.http_port.required_if' => 'HTTP 探针必须指定端口',
            'containers.*.startup_probe.tcp_port.required_if' => 'TCP 探针必须指定端口',
            'containers.*.startup_probe.exec_command.required_if' => 'Exec 探针必须指定命令',
            'containers.*.startup_probe.initial_delay_seconds.max' => '初始延迟不能超过 3600 秒',
            'containers.*.startup_probe.period_seconds.max' => '探测周期不能超过 3600 秒',
            'containers.*.startup_probe.timeout_seconds.max' => '超时时间不能超过 3600 秒',
            'containers.*.startup_probe.failure_threshold.max' => '失败阈值不能超过 30',

            // 镜像拉取密钥错误消息
            'image_pull_secrets.*.required' => '镜像拉取密钥名称不能为空',
            'image_pull_secrets.*.min' => '镜像拉取密钥名称不能为空',
            'image_pull_secrets.*.regex' => '镜像拉取密钥名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'image_pull_policy.required_with' => '使用镜像拉取密钥时必须指定拉取策略',
            'image_pull_policy.in' => '镜像拉取策略必须是 Always、Never 或 IfNotPresent',

        ];
    }

    /**
     * 获取创建时的名称验证规则
     */
    protected function getNameRulesForCreate(string $entityType): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:63',
                new \App\Rules\KubernetesNameRule,
            ],
        ];
    }

    /**
     * 获取名称验证的错误消息
     */
    protected function getNameMessages(string $entityType): array
    {
        return [
            'name.regex' => $entityType.' 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
        ];
    }

    /**
     * 验证资源限制 - 添加到验证器的 after 钩子
     */
    protected function validateResources($validator): void
    {
        $validator->after(function ($validator) {
            $containers = $this->input('containers', []);
            $imagePullSecrets = $this->input('image_pull_secrets', []);

            // 验证镜像拉取密钥
            $this->validateImagePullSecrets($imagePullSecrets, $validator);

            foreach ($containers as $index => $container) {
                if (isset($container['resources'])) {
                    $this->validateContainerResources($container['resources'], $index, $validator);
                }

                // 验证镜像
                if (isset($container['image'])) {
                    // 过滤掉空的镜像拉取密钥
                    $validImagePullSecrets = array_filter($imagePullSecrets, function ($secret) {
                        return ! empty($secret) && is_string($secret);
                    });

                    $this->validateContainerImage($container['image'], $index, $validImagePullSecrets, $validator);
                }
            }
        });
    }

    /**
     * 验证镜像拉取密钥
     */
    protected function validateImagePullSecrets(array $imagePullSecrets, $validator): void
    {
        // 由于使用了ImagePullSecretRule，大部分验证已经在规则级别处理了
        // 这里主要处理业务逻辑相关的验证，例如密钥是否真实存在等

        // 过滤掉空的和无效的镜像拉取密钥，只保留有效的进行后续处理
        $validSecrets = array_filter($imagePullSecrets, function ($secret) {
            return ! empty($secret) && is_string($secret) && preg_match('/^[a-z]([-a-z0-9]*[a-z0-9])?$/', $secret);
        });

        // 如果原数组中有无效项，Rule验证会自动处理错误消息
        // 这里我们只需要确保后续处理使用的是有效的密钥列表
    }

    /**
     * 验证单个容器的资源限制
     */
    protected function validateContainerResources(array $resources, int $containerIndex, $validator): void
    {
        if (isset($resources['memory'])) {
            $memory = (int) $resources['memory'];
            if ($memory % 512 !== 0) {
                $validator->errors()->add(
                    "containers.{$containerIndex}.resources.memory",
                    '内存必须是 512Mi 的倍数'
                );
            }
        }

        if (isset($resources['cpu'])) {
            $cpu = (int) $resources['cpu'];
            if ($cpu % 500 !== 0) {
                $validator->errors()->add(
                    "containers.{$containerIndex}.resources.cpu",
                    'CPU 必须是 500m 的倍数'
                );
            }
        }
    }

    /**
     * 验证容器镜像
     */
    protected function validateContainerImage(string $image, int $containerIndex, array $imagePullSecrets, $validator): void
    {
        // 检查是否启用镜像验证
        if (! config('k8s.imageValidation.enabled', true)) {
            return;
        }

        try {
            // 获取用户的工作空间
            $user = request()->user();
            if (! $user) {
                // 在测试环境中可能没有用户，跳过镜像验证
                return;
            }
            $workspace = $user->getWorkspace();

            // 创建镜像验证服务
            $imageValidationService = new \App\Service\ImageValidationService($workspace);

            // 验证镜像
            $result = $imageValidationService->validateImageSize($image, $imagePullSecrets);

            if (! $result['valid']) {
                // 检查是否启用严格验证模式
                if (config('k8s.imageValidation.strict_mode', false)) {
                    $validator->errors()->add(
                        "containers.{$containerIndex}.image",
                        "镜像验证失败: {$result['error']}"
                    );
                } else {
                    // 非严格模式下只记录警告
                    \Illuminate\Support\Facades\Log::warning('镜像验证失败（非严格模式）', [
                        'image' => $image,
                        'error' => $result['error'],
                    ]);
                }
            } else {
                // 检查镜像大小限制
                $maxSizeMB = config('k8s.imageValidation.max_size_mb', 10240);

                if ($result['size_mb'] > $maxSizeMB) {
                    if (config('k8s.imageValidation.strict_mode', false)) {
                        $validator->errors()->add(
                            "containers.{$containerIndex}.image",
                            "镜像大小 {$result['size_mb']} MB 超过限制 {$maxSizeMB} MB"
                        );
                    } else {
                        // 非严格模式下只记录警告
                        \Illuminate\Support\Facades\Log::warning('镜像大小超限（非严格模式）', [
                            'image' => $image,
                            'size_mb' => $result['size_mb'],
                            'limit_mb' => $maxSizeMB,
                        ]);
                    }
                }

                // 记录镜像信息到日志
                \Illuminate\Support\Facades\Log::info('镜像验证通过', [
                    'image' => $image,
                    'size_mb' => $result['size_mb'],
                    'layers_count' => $result['layers_count'],
                ]);
            }
        } catch (\Exception $e) {
            // 如果验证服务出错，记录错误
            \Illuminate\Support\Facades\Log::warning('镜像验证服务异常', [
                'image' => $image,
                'error' => $e->getMessage(),
            ]);

            // 检查是否启用严格验证模式
            if (config('k8s.imageValidation.strict_mode', false)) {
                $validator->errors()->add(
                    "containers.{$containerIndex}.image",
                    "镜像验证服务异常: {$e->getMessage()}"
                );
            }
        }
    }
}
