<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDockerRegistrySecretRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $workspace = $this->user()->getWorkspace();

        return $this->user()->can('update', $workspace);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:63|regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/',
            'labels' => ['nullable', 'array', new \App\Rules\CustomLabelRule],
            'server' => 'required|string',
            'username' => 'required|string',
            'password' => 'required|string',
            'email' => 'nullable|email',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'Secret 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
        ];
    }
}
