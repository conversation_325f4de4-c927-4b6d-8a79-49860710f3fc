<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PodExecRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // 权限验证在控制器中处理
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'pod_name' => 'required|string|max:255',
            'container_name' => 'nullable|string|max:255',
            'command' => 'required|array|min:1',
            'command.*' => 'required|string|max:1000',
            'working_dir' => 'nullable|string|max:500',
            'env' => 'nullable|array',
            'env.*' => 'string|max:1000',
            'timeout' => 'nullable|integer|min:1|max:300', // 最大5分钟
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'pod_name.required' => 'Pod 名称是必需的',
            'pod_name.string' => 'Pod 名称必须是字符串',
            'pod_name.max' => 'Pod 名称不能超过255个字符',
            'container_name.string' => '容器名称必须是字符串',
            'container_name.max' => '容器名称不能超过255个字符',
            'command.required' => '命令是必需的',
            'command.array' => '命令必须是数组格式',
            'command.min' => '命令至少需要一个参数',
            'command.*.required' => '命令参数不能为空',
            'command.*.string' => '命令参数必须是字符串',
            'command.*.max' => '命令参数不能超过1000个字符',
            'working_dir.string' => '工作目录必须是字符串',
            'working_dir.max' => '工作目录不能超过500个字符',
            'env.array' => '环境变量必须是数组格式',
            'env.*.string' => '环境变量值必须是字符串',
            'env.*.max' => '环境变量值不能超过1000个字符',
            'timeout.integer' => '超时时间必须是整数',
            'timeout.min' => '超时时间至少为1秒',
            'timeout.max' => '超时时间不能超过300秒',
        ];
    }
}
