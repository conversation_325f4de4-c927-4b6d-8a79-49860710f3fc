<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CalculatePriceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'memory_mi' => 'required|integer|min:0|max:1048576', // 最大 1TB
            'cpu_m' => 'required|integer|min:0|max:1000000', // 最大 1000 cores
            'storage_gi' => 'required|integer|min:0|max:10240', // 最大 10TB
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'memory_mi' => '内存',
            'cpu_m' => 'CPU',
            'storage_gi' => '存储',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'memory_mi.required' => '内存值不能为空',
            'memory_mi.integer' => '内存值必须是整数',
            'memory_mi.min' => '内存值不能小于 0',
            'memory_mi.max' => '内存值不能超过 1048576 Mi (1TB)',
            'cpu_m.required' => 'CPU 值不能为空',
            'cpu_m.integer' => 'CPU 值必须是整数',
            'cpu_m.min' => 'CPU 值不能小于 0',
            'cpu_m.max' => 'CPU 值不能超过 1000000 m (1000 cores)',
            'storage_gi.required' => '存储值不能为空',
            'storage_gi.integer' => '存储值必须是整数',
            'storage_gi.min' => '存储值不能小于 0',
            'storage_gi.max' => '存储值不能超过 10240 Gi (10TB)',
        ];
    }
}
