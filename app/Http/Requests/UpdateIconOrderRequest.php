<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateIconOrderRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'order' => ['required', 'array'],
            'order.*' => ['string'],
        ];
    }

    public function messages(): array
    {
        return [
            'order.required' => '排序数据不能为空',
            'order.array' => '排序数据格式错误',
        ];
    }
}
