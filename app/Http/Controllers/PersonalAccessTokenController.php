<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreatePersonalAccessTokenRequest;
use App\Http\Resources\AccessTokenResource;
use App\Http\Resources\AuthorizedAppResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PersonalAccessTokenController extends Controller
{
    /**
     * 显示 Personal Access Token 列表页面
     */
    public function index(): Response
    {
        $tokens = auth()->user()->tokens()
            ->where('revoked', false)
            ->latest()
            ->get();

        // 获取用户授权的应用列表
        $authorizedApps = auth()->user()->tokens()
            ->with('client')
            ->where('revoked', false)
            ->get()
            ->groupBy('client_id')
            ->map(function ($tokens) {
                $client = $tokens->first()->client;

                return (object) [
                    'client_id' => $client->id,
                    'client_name' => $client->name,
                    'tokens_count' => $tokens->count(),
                    'last_used_at' => $tokens->max('last_used_at')?->format('Y-m-d H:i:s'),
                    'created_at' => $tokens->min('created_at')?->format('Y-m-d H:i:s'),
                ];
            })
            ->values();

        return Inertia::render('PersonalAccessTokens/Index', [
            'tokens' => AccessTokenResource::collection($tokens),
            'authorizedApps' => AuthorizedAppResource::collection($authorizedApps),
        ]);
    }

    /**
     * 创建新的 Personal Access Token
     */
    public function store(CreatePersonalAccessTokenRequest $request): JsonResponse
    {
        $token = auth()->user()->createToken(
            $request->name,
            $request->scopes ?? ['*']
        );

        return $this->success([
            'token' => new AccessTokenResource($token->token),
            'access_token' => $token->accessToken,
        ]);
    }

    /**
     * 删除 Personal Access Token
     */
    public function destroy(Request $request, string $tokenId): JsonResponse
    {
        $token = $request->user()->tokens()->find($tokenId);

        if (! $token) {
            return $this->notFound();
        }

        $token->revoke();

        return $this->deleted();
    }

    /**
     * 撤销对某个客户端的所有授权
     */
    public function revokeClientAuthorization(Request $request, string $clientId): JsonResponse
    {
        auth()->user()->tokens()
            ->where('client_id', $clientId)
            ->where('revoked', false)
            ->update(['revoked' => true]);

        return $this->success();
    }
}
