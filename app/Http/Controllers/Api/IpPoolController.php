<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\IpPoolResource;
use App\Models\IpPool;
use App\Service\IpPoolService;
use Illuminate\Http\Request;

class IpPoolController extends Controller
{
    protected IpPoolService $ipPoolService;

    public function __construct(IpPoolService $ipPoolService)
    {
        $this->ipPoolService = $ipPoolService;
    }

    /**
     * 获取集群的可用IP池列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        if (! $workspace) {
            return $this->error('未找到工作空间', 404);
        }

        $ipPools = IpPool::where('cluster_id', $workspace->cluster_id)
            ->where('is_active', true)
            ->with(['poolIps' => function ($query) {
                $query->where('is_active', true);
            }])
            ->get();

        return $this->success(IpPoolResource::collection($ipPools));
    }
}
