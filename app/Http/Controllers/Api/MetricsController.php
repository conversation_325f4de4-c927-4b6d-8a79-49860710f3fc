<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\K8s\K8sConnectionException;
use App\Http\Controllers\Controller;
use App\Service\MetricsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MetricsController extends Controller
{
    /**
     * 获取命名空间下的所有Pod指标
     */
    public function index(Request $request): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $metricsService = new MetricsService($workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'metrics');

            // 使用缓存和并发锁获取数据，指标缓存时间较短
            $metrics = $this->getCachedK8sData($cacheKey, function () use ($metricsService) {
                return $metricsService->getNamespaceMetrics();
            }, 60); // 指标缓存 1 分钟

            return response()->json($metrics->toArray());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取命名空间指标失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取特定Pod的指标
     */
    public function podMetrics(Request $request, string $name): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $metricsService = new MetricsService($workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'pod_metrics', $name);

            // 使用缓存和并发锁获取数据，指标缓存时间较短
            $metrics = $this->getCachedK8sData($cacheKey, function () use ($metricsService, $name) {
                return $metricsService->getPodMetrics($name);
            }, 60); // 指标缓存 1 分钟

            if (! $metrics) {
                return response()->json([]);
            }

            return response()->json($metrics->toArray());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取Pod指标失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
