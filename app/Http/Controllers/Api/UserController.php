<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\WorkspaceResource;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        $scopes = $this->getScopes($user);
        $claims = $this->getScopeClaims($user, $scopes);

        $claims['sub'] = (string) $user->id;

        $workspace = $user->getWorkspace();
        if ($workspace) {
            $claims['workspace'] = new WorkspaceResource($workspace);
        }

        return $this->success($claims);
    }

    private function getScopes(User $user): array
    {
        $all_scopes = config('openid.passport.tokens_can');

        $scopes = [];

        foreach ($all_scopes as $scope_name => $scope_description) {
            if ($user->tokenCan($scope_name)) {
                $scopes[] = $scope_name;
            }
        }

        return $scopes;
    }

    private function getScopeClaims(User $user, array $scopes): array
    {
        return $user->getClaims($scopes);
    }
}
