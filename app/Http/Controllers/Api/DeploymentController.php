<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\Deployment\DeploymentNotFoundException;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\K8s\ResourceConflictException;
use App\Exceptions\K8s\ResourceValidationException;
use App\Exceptions\Secret\SecretNotFoundException;
use App\Exceptions\Storage\StorageNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\ScaleWorkloadRequest;
use App\Http\Requests\StoreDeploymentRequest;
use App\Http\Requests\UpdateDeploymentRequest;
use App\Service\DeploymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DeploymentController extends Controller
{
    /**
     * 获取 Deployment 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'deployments');

            // 使用缓存和并发锁获取数据
            $deployments = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $deploymentService = new DeploymentService($workspace);

                return $deploymentService->getDeployments();
            });

            return $this->dto($deployments);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Deployment 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取单个 Deployment 详情
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'deployments', $name);

            // 使用缓存和并发锁获取数据
            $deployment = $this->getCachedK8sData($cacheKey, function () use ($workspace, $name) {
                $deploymentService = new DeploymentService($workspace);

                return $deploymentService->getDeployment($name);
            });

            return $this->dto($deployment);
        } catch (DeploymentNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Deployment 详情失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 Deployment
     */
    public function store(StoreDeploymentRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $deploymentService = new DeploymentService($workspace);
            $deployment = $deploymentService->createDeployment($request->validated());

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'deployments');
            $this->clearK8sCache($workspace->id, 'pods'); // Deployment 会创建 Pod

            return $this->dto($deployment, 201);
        } catch (ResourceConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (ResourceValidationException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'errors' => $e->getValidationErrors(),
            ], 422);
        } catch (SecretNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (StorageNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 Deployment 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 更新 Deployment
     */
    public function update(UpdateDeploymentRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $deploymentService = new DeploymentService($workspace);
            $data = $request->validated();
            $data['name'] = $name; // 确保名称正确

            $deployment = $deploymentService->updateDeployment($name, $data);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'deployments');
            $this->clearK8sCache($workspace->id, 'pods'); // Deployment 更新会影响 Pod

            return $this->dto($deployment);
        } catch (DeploymentNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (ResourceValidationException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'errors' => $e->getValidationErrors(),
            ], 422);
        } catch (SecretNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (StorageNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('更新 Deployment 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 删除 Deployment
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $deploymentService = new DeploymentService($workspace);
            $deploymentService->deleteDeployment($name);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'deployments');
            $this->clearK8sCache($workspace->id, 'pods'); // Deployment 删除会删除 Pod

            return $this->deleted();
        } catch (DeploymentNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('删除 Deployment 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 扩容/缩容 Deployment
     */
    public function scale(ScaleWorkloadRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $deploymentService = new DeploymentService($workspace);
            $deployment = $deploymentService->scaleDeployment($name, $request->input('replicas'));

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'deployments');
            $this->clearK8sCache($workspace->id, 'pods'); // 扩缩容会影响 Pod

            return $this->dto($deployment);
        } catch (DeploymentNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('扩容/缩容 Deployment 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
