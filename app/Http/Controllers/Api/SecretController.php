<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\Secret\SecretConflictException;
use App\Exceptions\Secret\SecretNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreBasicAuthSecretRequest;
use App\Http\Requests\StoreDockerRegistrySecretRequest;
use App\Http\Requests\StoreGenericSecretRequest;
use App\Http\Requests\StoreSshAuthSecretRequest;
use App\Http\Requests\StoreTlsSecretRequest;
use App\Service\SecretService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SecretController extends Controller
{
    /**
     * 获取 Secret 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'secrets');

            // 使用缓存和并发锁获取数据
            $secrets = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $secretService = new SecretService($workspace);

                return $secretService->getSecrets();
            });

            return $this->dto($secrets);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Secret 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取单个 Secret
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'secrets', $name);

            // 使用缓存和并发锁获取数据
            $secret = $this->getCachedK8sData($cacheKey, function () use ($workspace, $name) {
                $secretService = new SecretService($workspace);

                return $secretService->getSecret($name);
            });

            return $this->dto($secret);
        } catch (SecretNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 Generic Secret
     */
    public function storeGeneric(StoreGenericSecretRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->createGenericSecret(
                $request->input('name'),
                $request->input('data'),
                null, // workloadType - Secret 通常独立存在
                null, // workloadName
                $request->input('labels', [])
            );

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'secrets');

            return $this->dto($secret, 201);
        } catch (SecretConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 Generic Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 Docker Registry Secret
     */
    public function storeDockerRegistry(StoreDockerRegistrySecretRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->createDockerRegistrySecret(
                $request->input('name'),
                $request->input('server'),
                $request->input('username'),
                $request->input('password'),
                $request->input('email'),
                $request->input('labels', [])
            );

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'secrets');

            return $this->dto($secret, 201);
        } catch (SecretConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 Docker Registry Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 TLS Secret
     */
    public function storeTls(StoreTlsSecretRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->createTlsSecret(
                $request->input('name'),
                $request->input('cert'),
                $request->input('key'),
                $request->input('labels', [])
            );

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'secrets');

            return $this->dto($secret, 201);
        } catch (SecretConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 TLS Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 Basic Auth Secret
     */
    public function storeBasicAuth(StoreBasicAuthSecretRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->createBasicAuthSecret(
                $request->input('name'),
                $request->input('username'),
                $request->input('password'),
                $request->input('labels', [])
            );

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'secrets');

            return $this->dto($secret, 201);
        } catch (SecretConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 Basic Auth Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 SSH Auth Secret
     */
    public function storeSshAuth(StoreSshAuthSecretRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->createSshAuthSecret(
                $request->input('name'),
                $request->input('ssh_private_key'),
                $request->input('labels', [])
            );

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'secrets');

            return $this->dto($secret, 201);
        } catch (SecretConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 SSH Auth Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 删除 Secret
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $secretService = new SecretService($workspace);
            $secretService->deleteSecret($name);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'secrets');

            return $this->deleted();
        } catch (SecretNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('删除 Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取 Secret 数据
     */
    public function data(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键，包含 edit 参数
            $params = ['edit' => $request->query('edit', 'false')];
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'secret_data', $name, $params);

            // 使用缓存和并发锁获取数据
            $result = $this->getCachedK8sData($cacheKey, function () use ($workspace, $name, $request) {
                $secretService = new SecretService($workspace);
                $data = $secretService->getSecretData($name);

                // 如果请求包含 edit=true 参数，返回实际数据用于编辑
                if ($request->query('edit') === 'true') {
                    return $data;
                }

                // 默认只返回键名，不返回实际值（安全考虑）
                return [
                    'keys' => array_keys($data),
                    'count' => count($data),
                ];
            }, 180); // Secret 数据缓存 3 分钟

            return $this->success($result);
        } catch (SecretNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Secret 数据失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 更新 Secret（通用方法，支持所有类型）
     */
    public function update(Request $request, string $name)
    {
        $request->validate([
            'data' => 'required|array',
            'data.*' => 'required|string',
        ]);

        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->updateSecretData($name, $request->input('data'));

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'secrets');

            return $this->dto($secret);
        } catch (SecretNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('更新 Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取支持的 Secret 类型
     */
    public function types(Request $request)
    {
        return $this->success([
            'types' => [
                'generic',
                'docker-registry',
                'tls',
                'basic-auth',
                'ssh-auth',
            ],
        ]);
    }
}
