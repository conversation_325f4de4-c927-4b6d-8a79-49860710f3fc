<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\Pod\PodNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\GeneratePodTerminalTokenRequest;
use App\Service\JwtService;
use App\Service\PodService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PodTerminalController extends Controller
{
    protected JwtService $jwtService;

    public function __construct(JwtService $jwtService)
    {
        $this->jwtService = $jwtService;
    }

    /**
     * 生成 Pod 终端访问 token
     */
    public function generateToken(GeneratePodTerminalTokenRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            // 验证 Pod 是否存在
            $pod = $podService->getPod($validated['pod_name']);

            // 如果没有指定容器，使用第一个容器
            $containerName = $validated['container_name'] ?? null;
            if (! $containerName && ! empty($pod->containers)) {
                $containerName = $pod->containers[0]['name'];
            }

            if (! $containerName) {
                return $this->badRequest('No containers found in pod');
            }

            // 生成 JWT token
            $token = $this->jwtService->generatePodTerminalToken(
                $request->user()->id,
                $workspace->id,
                $validated['pod_name'],
                $containerName,
                $validated['mode'] ?? 'shell'
            );

            return $this->success([
                'token' => $token,
                'expires_in' => config('websocket.jwt.ttl'),
                'websocket_url' => config('websocket.websocket_url'),
                'pod_name' => $validated['pod_name'],
                'container_name' => $containerName,
                'mode' => $validated['mode'] ?? 'shell',
            ]);

        } catch (PodNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('生成 Pod 终端访问 token 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取 Pod 的容器列表
     */
    public function getContainers(Request $request, string $podName): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $pod = $podService->getPod($podName);

            $containers = collect($pod->containers)->map(function ($container) {
                return [
                    'name' => $container['name'],
                    'image' => $container['image'] ?? 'unknown',
                    'ready' => $container['ready'] ?? false,
                    'restart_count' => $container['restart_count'] ?? 0,
                ];
            });

            return $this->success($containers);

        } catch (PodNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Pod 的容器列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
