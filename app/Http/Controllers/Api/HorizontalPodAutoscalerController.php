<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\HorizontalPodAutoscaler\HorizontalPodAutoscalerNotFoundException;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\K8s\ResourceConflictException;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreHorizontalPodAutoscalerRequest;
use App\Http\Requests\UpdateHorizontalPodAutoscalerRequest;
use App\Service\HorizontalPodAutoscalerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class HorizontalPodAutoscalerController extends Controller
{
    /**
     * 获取 HPA 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'hpas');

            // 使用缓存和并发锁获取数据
            $hpas = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $hpaService = new HorizontalPodAutoscalerService($workspace);

                return $hpaService->getHPAs();
            });

            return $this->dto($hpas);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 HPA 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取单个 HPA
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'hpas', $name);

            // 使用缓存和并发锁获取数据
            $hpa = $this->getCachedK8sData($cacheKey, function () use ($workspace, $name) {
                $hpaService = new HorizontalPodAutoscalerService($workspace);

                return $hpaService->getHPA($name);
            });

            return $this->dto($hpa);
        } catch (HorizontalPodAutoscalerNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 HPA 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 HPA
     */
    public function store(StoreHorizontalPodAutoscalerRequest $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $hpaService = new HorizontalPodAutoscalerService($workspace);

            // 验证目标工作负载是否存在
            if (! $hpaService->validateTargetWorkload(
                $request->input('target_type'),
                $request->input('target_name')
            )) {
                return $this->badRequest('目标工作负载不存在');
            }

            $hpa = $hpaService->createHPA($request->validated());

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'hpas');

            return $this->dto($hpa, 201);
        } catch (ResourceConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 HPA 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 更新 HPA
     */
    public function update(UpdateHorizontalPodAutoscalerRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $hpaService = new HorizontalPodAutoscalerService($workspace);
            $data = $request->validated();
            $data['name'] = $name; // 确保名称正确

            // 如果更新了目标工作负载，验证是否存在
            if (isset($data['target_type']) && isset($data['target_name'])) {
                if (! $hpaService->validateTargetWorkload($data['target_type'], $data['target_name'])) {
                    return $this->badRequest('目标工作负载不存在');
                }
            }

            $hpa = $hpaService->updateHPA($name, $data);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'hpas');

            return $this->dto($hpa);
        } catch (HorizontalPodAutoscalerNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('更新 HPA 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 删除 HPA
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $hpaService = new HorizontalPodAutoscalerService($workspace);
            $hpaService->deleteHPA($name);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'hpas');

            return $this->deleted();
        } catch (HorizontalPodAutoscalerNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('删除 HPA 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取可扩缩的工作负载列表
     */
    public function scalableWorkloads(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'scalable_workloads');

            // 使用缓存和并发锁获取数据
            $workloads = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $hpaService = new HorizontalPodAutoscalerService($workspace);

                return $hpaService->getScalableWorkloads();
            });

            return $this->success($workloads);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取可扩缩工作负载失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
