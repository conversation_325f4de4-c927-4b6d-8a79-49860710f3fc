<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\K8s\K8sConnectionException;
use App\Http\Controllers\Controller;
use App\Http\Requests\CalculatePriceRequest;
use App\Models\Cluster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ClusterController extends Controller
{
    /**
     * API: 获取可用的集群列表
     */
    public function index()
    {
        return Cluster::all()
            ->map(function (Cluster $cluster) {
                $billingEnabled = $cluster->isBillingEnabled();
                $pricingSummary = $billingEnabled ? $cluster->getPricingSummary() : null;

                return [
                    'id' => $cluster->id,
                    'name' => $cluster->name,
                    'description' => $pricingSummary['description'] ?? null,
                    'billing_enabled' => $billingEnabled,
                    'pricing_summary' => $pricingSummary,
                ];
            });
    }

    /**
     * API: 获取集群节点信息（包含外部 IP）
     */
    public function getNodes(Request $request, Cluster $cluster)
    {
        try {
            $response = $cluster->http()->get('/api/v1/nodes');

            if (! $response->successful()) {
                return response()->json(['message' => '获取节点信息失败'], 500);
            }

            $nodes = collect($response->json('items', []))->map(function ($node) {
                $addresses = collect($node['status']['addresses'] ?? []);

                return [
                    'name' => $node['metadata']['name'],
                    'internal_ip' => $addresses->where('type', 'InternalIP')->first()['address'] ?? null,
                    'external_ip' => $node['metadata']['labels']['external-ip'] ?? null,
                    'hostname' => $addresses->where('type', 'Hostname')->first()['address'] ?? null,
                ];
            })->filter(function ($node) {
                return $node['external_ip'] !== null;
            });

            return $this->success($nodes->values());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取节点信息失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * API: 获取集群 Ingress 外部 IP 地址
     */
    public function getIngressExternalIps(Request $request, Cluster $cluster)
    {
        try {
            // 从集群设置中获取 Ingress 外部 IP
            $ingressExternalIps = $cluster->getSetting('ingress_external_ips', []);

            if (empty($ingressExternalIps)) {
                return $this->notFound('未配置 Ingress 外部 IP');
            }

            return $this->success($ingressExternalIps);
        } catch (\Exception $e) {
            Log::error('获取 Ingress 外部 IP 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * API: 获取集群定价信息
     */
    public function getPricing(Cluster $cluster)
    {
        if (! $cluster->isBillingEnabled()) {
            return response()->json(['message' => '该集群未配置定价策略或计费已禁用'], 422);
        }

        $pricingSummary = $cluster->getPricingSummary();

        return $this->success([
            'cluster' => [
                'id' => $cluster->id,
                'name' => $cluster->name,
            ],
            'pricing' => $pricingSummary,
        ]);
    }

    /**
     * API: 计算资源价格
     */
    public function calculatePrice(CalculatePriceRequest $request, Cluster $cluster)
    {
        if (! $cluster->isBillingEnabled()) {
            return response()->json(['message' => '该集群未配置定价策略或计费已禁用'], 422);
        }

        $resources = [
            'memory_mi' => $request->input('memory_mi', 0),
            'cpu_m' => $request->input('cpu_m', 0),
            'storage_gi' => $request->input('storage_gi', 0),
        ];

        // 获取集群的当前定价
        $currentPricing = \App\Models\ResourcePricing::getAllCurrentPricingForResource($cluster);

        if (empty($currentPricing)) {
            return response()->json(['message' => '该集群未配置定价策略'], 422);
        }

        $breakdown = [];
        $totalPricePerMinute = '0';

        // 计算内存费用 (转换 Mi 到 GB: 1 Mi = 1/1024 GB)
        if ($resources['memory_mi'] > 0 && isset($currentPricing['memory_gb'])) {
            $memoryGb = bcdiv((string) $resources['memory_mi'], '1024', 8);
            $memoryPricePerMinute = $currentPricing['memory_gb']->calculatePricePerMinute();
            $memoryTotalCost = bcmul($memoryGb, $memoryPricePerMinute, 8);
            $breakdown['memory_price_per_minute'] = $memoryTotalCost;
            $totalPricePerMinute = bcadd($totalPricePerMinute, $memoryTotalCost, 8);
        }

        // 计算CPU费用 (转换 m 到 core: 1000m = 1 core)
        if ($resources['cpu_m'] > 0 && isset($currentPricing['cpu_core'])) {
            $cpuCores = bcdiv((string) $resources['cpu_m'], '1000', 8);
            $cpuPricePerMinute = $currentPricing['cpu_core']->calculatePricePerMinute();
            $cpuTotalCost = bcmul($cpuCores, $cpuPricePerMinute, 8);
            $breakdown['cpu_price_per_minute'] = $cpuTotalCost;
            $totalPricePerMinute = bcadd($totalPricePerMinute, $cpuTotalCost, 8);
        }

        // 计算存储费用 (Gi 转换为 GB: 1 Gi = 1.073741824 GB)
        if ($resources['storage_gi'] > 0 && isset($currentPricing['storage_gb'])) {
            $storageGb = bcmul((string) $resources['storage_gi'], '1.073741824', 8);
            $storagePricePerMinute = $currentPricing['storage_gb']->calculatePricePerMinute();
            $storageTotalCost = bcmul($storageGb, $storagePricePerMinute, 8);
            $breakdown['storage_price_per_minute'] = $storageTotalCost;
            $totalPricePerMinute = bcadd($totalPricePerMinute, $storageTotalCost, 8);
        }

        // 计算不同时间周期的价格
        $hourlyPrice = bcmul($totalPricePerMinute, '60', 8);
        $dailyPrice = bcmul($hourlyPrice, '24', 8);
        $monthlyPrice = bcmul($dailyPrice, '30', 8);

        return $this->success([
            'resources' => $resources,
            'breakdown' => $breakdown,
            'total' => [
                'per_minute' => $totalPricePerMinute,
                'per_hour' => $hourlyPrice,
                'per_day' => $dailyPrice,
                'per_month' => $monthlyPrice,
            ],
            'formatted' => [
                'per_minute' => formatAmount($totalPricePerMinute, true, 4),
                'per_hour' => formatAmount($hourlyPrice),
                'per_day' => formatAmount($dailyPrice),
                'per_month' => formatAmount($monthlyPrice),
            ],
        ]);
    }
}
