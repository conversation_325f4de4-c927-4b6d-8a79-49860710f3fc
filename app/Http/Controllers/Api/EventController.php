<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\K8s\K8sConnectionException;
use App\Http\Controllers\Controller;
use App\Service\EventService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EventController extends Controller
{
    /**
     * 获取命名空间下的所有事件
     */
    public function index(Request $request): JsonResponse
    {
        // 验证请求参数
        $validated = $request->validate([
            'since_minutes' => 'nullable|integer|min:1|max:1440', // 最多24小时
        ]);

        $workspace = $request->user()->getWorkspace();
        $eventService = new EventService($workspace);

        try {
            $sinceMinutes = $validated['since_minutes'] ?? null;

            // 生成缓存键，包含时间参数
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'events', null, ['since_minutes' => $sinceMinutes]);

            // 使用缓存和并发锁获取数据，事件缓存时间较短
            $events = $this->getCachedK8sData($cacheKey, function () use ($eventService, $sinceMinutes) {
                return $eventService->getNamespaceEvents($sinceMinutes);
            }, 30); // 事件缓存 30 秒

            return response()->json($events->toArray());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取命名空间事件失败', [
                'error' => $e->getMessage(),
                'since_minutes' => $validated['since_minutes'] ?? null,
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取特定资源的事件
     */
    public function resourceEvents(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'kind' => 'required|string',
            'name' => 'required|string',
            'since_minutes' => 'nullable|integer|min:1|max:1440', // 最多24小时
        ]);

        $workspace = $request->user()->getWorkspace();
        $eventService = new EventService($workspace);

        try {
            // 生成缓存键，包含资源信息和时间参数
            $params = [
                'kind' => $validated['kind'],
                'since_minutes' => $validated['since_minutes'] ?? null,
            ];
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'resource_events', $validated['name'], $params);

            // 使用缓存和并发锁获取数据，事件缓存时间较短
            $events = $this->getCachedK8sData($cacheKey, function () use ($eventService, $validated) {
                return $eventService->getResourceEvents(
                    $validated['kind'],
                    $validated['name'],
                    $validated['since_minutes'] ?? null
                );
            }, 30); // 事件缓存 30 秒

            return response()->json($events);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取资源事件失败', [
                'error' => $e->getMessage(),
                'kind' => $validated['kind'],
                'name' => $validated['name'],
                'since_minutes' => $validated['since_minutes'] ?? null,
            ]);

            return $this->serverError();
        }
    }
}
