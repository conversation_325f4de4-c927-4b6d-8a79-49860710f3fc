<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreWorkspaceRequest;
use App\Http\Requests\UpdateWorkspaceRequest;
use App\Http\Resources\WorkspaceResource;
use App\Models\Workspace;
use App\Service\K8sResourceCacheService;
use App\Service\WorkspaceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WorkspaceController extends Controller
{
    public function __construct(
        protected WorkspaceService $workspaceService,
        protected K8sResourceCacheService $cacheService
    ) {}

    /**
     * API: 获取用户的工作空间列表
     */
    public function index(Request $request)
    {
        $workspaces = $request->user()->workspaces()->with('cluster')->get();

        $currentWorkspace = $request->user()->getWorkspace();

        return [
            'data' => WorkspaceResource::collection($workspaces),
            'currentWorkspace' => new WorkspaceResource($currentWorkspace),
        ];
    }

    /**
     * API: 创建工作空间
     */
    public function store(StoreWorkspaceRequest $request)
    {
        $user = $request->user();

        // 如果用户没有余额，则不允许创建
        if (! $user->hasEnoughBalance('1')) {
            return $this->error('你的账户余额不足 1，无法创建工作空间');
        }

        try {
            $workspace = $this->workspaceService->createWorkspace([
                'user_id' => auth()->id(),
                'cluster_id' => $request->cluster_id,
                'name' => $request->name,
            ]);

            return new WorkspaceResource($workspace->load('cluster'));
        } catch (\Exception $e) {
            Log::error('创建工作空间失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建工作空间失败');
        }
    }

    /**
     * API: 获取工作空间详情
     */
    public function show(Workspace $workspace)
    {
        $this->authorize('view', $workspace);

        return new WorkspaceResource($workspace->load('cluster'));
    }

    /**
     * API: 更新工作空间
     */
    public function update(UpdateWorkspaceRequest $request, Workspace $workspace)
    {
        $this->authorize('update', $workspace);

        try {
            $workspace->update($request->validated());

            return new WorkspaceResource($workspace->load('cluster'));
        } catch (\Exception $e) {
            Log::error('更新工作空间失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('更新工作空间失败');
        }
    }

    /**
     * API: 删除工作空间
     */
    public function destroy(Workspace $workspace)
    {
        $this->authorize('delete', $workspace);

        try {
            // 如果是当前工作空间，清除用户的当前工作空间
            if (auth()->user()->current_workspace_id === $workspace->id) {
                auth()->user()->update(['current_workspace_id' => null]);
            }

            // 启动删除任务，不立即删除记录
            $this->workspaceService->startWorkspaceDeletion($workspace);

            return $this->success('工作空间删除已启动，正在清理 Kubernetes 资源');
        } catch (\Exception $e) {
            Log::error('启动工作空间删除失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('启动工作空间删除失败');
        }
    }

    /**
     * API: 设置当前工作空间
     */
    public function setCurrent(Workspace $workspace)
    {
        $this->authorize('view', $workspace);

        try {
            auth()->user()->setCurrentWorkspace($workspace);

            return $this->success('已切换到工作空间：'.$workspace->name);
        } catch (\Exception $e) {
            Log::error('切换工作空间失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('切换工作空间失败');
        }
    }

    /**
     * API: 重试创建 namespace
     */
    public function retryNamespace(Workspace $workspace)
    {
        $this->authorize('update', $workspace);

        try {
            if ($workspace->status !== Workspace::STATUS_FAILED) {
                return $this->error('只能重试创建失败的工作空间');
            }

            $this->workspaceService->retryCreateNamespace($workspace);

            return $this->success('已重新开始创建 Namespace，请稍后刷新查看状态');
        } catch (\Exception $e) {
            Log::error('重试创建失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('重试创建失败');
        }
    }

    /**
     * API: 获取当前工作空间中的应用资源（Deployment/StatefulSet）和其端口信息
     */
    public function getCurrentWorkloadResources(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return $this->getWorkloadResources($workspace);
    }

    /**
     * 获取工作空间中的应用资源（Deployment/StatefulSet）和其端口信息
     */
    public function getWorkloadResources(Workspace $workspace)
    {
        $this->authorize('view', $workspace);
        $workloadResources = [];

        try {
            // 获取有端口的 Deployment 资源
            $deployments = $this->cacheService->getCachedResources(
                $workspace->cluster_id,
                $workspace->namespace,
                'deployments'
            ) ?? [];

            // 获取有端口的 StatefulSet 资源
            $statefulSets = $this->cacheService->getCachedResources(
                $workspace->cluster_id,
                $workspace->namespace,
                'statefulsets'
            ) ?? [];

            array_map(function ($deployment) use (&$workloadResources) {
                $workloadResources[] = $deployment;
            }, $deployments);

            array_map(function ($statefulSet) use (&$workloadResources) {
                $workloadResources[] = $statefulSet;

            }, $statefulSets);

            return $this->success($workloadResources);

        } catch (\Exception $e) {
            Log::error('获取工作负载资源失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取工作负载资源失败');
        }
    }

    /**
     * API: 获取当前工作空间的所有事件
     */
    public function getWorkspaceEvents(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'since_minutes' => 'nullable|integer|min:1|max:1440', // 最多24小时
        ]);

        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $sinceMinutes = $validated['since_minutes'] ?? null;

            // 生成缓存键，包含时间参数
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'workspace_events', null, ['since_minutes' => $sinceMinutes]);

            // 使用缓存和并发锁获取数据，事件缓存时间较短
            $events = $this->getCachedK8sData($cacheKey, function () use ($workspace, $sinceMinutes) {
                $eventService = new \App\Service\EventService($workspace);

                return $eventService->getNamespaceEvents($sinceMinutes);
            }, 30); // 事件缓存 30 秒

            return response()->json($events->toArray());
        } catch (\Exception $e) {
            Log::error('获取工作空间事件失败', [
                'error' => $e->getMessage(),
                'since_minutes' => $validated['since_minutes'] ?? null,
            ]);

            return $this->error('获取工作空间事件失败');
        }
    }

    /**
     * API: 获取当前工作空间的所有Pod指标
     */
    public function getWorkspaceMetrics(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'workspace_metrics');

            // 使用缓存和并发锁获取数据，指标缓存时间较短
            $metrics = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $metricsService = new \App\Service\MetricsService($workspace);

                return $metricsService->getNamespaceMetrics();
            }, 60); // 指标缓存 1 分钟

            return response()->json($metrics->toArray());
        } catch (\Exception $e) {
            Log::error('获取工作空间指标失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取工作空间指标失败');
        }
    }

    /**
     * 获取工作空间的所有资源
     */
    public function all(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        $allResources = [];

        // 定义要获取的资源类型
        $resourceTypes = [
            'deployments' => 'deployments',
            'statefulsets' => 'statefulsets',
            'services' => 'services',
            'pods' => 'pods',
            'ingresses' => 'ingresses',
            'storages' => 'storages',
            'secrets' => 'secrets',
            'configmaps' => 'configmaps',
            'horizontalpodautoscalers' => 'hpas',
            'events' => 'events',
            'metrics' => 'pod_metrics',
        ];

        try {
            foreach ($resourceTypes as $cacheType => $displayType) {
                // 直接从缓存中获取资源
                $resources = $this->cacheService->getCachedResources(
                    $workspace->cluster_id,
                    $workspace->namespace,
                    $cacheType
                ) ?? [];

                $allResources[$displayType] = $resources;
            }

            return $this->success($allResources);
        } catch (\Exception $e) {
            Log::error('获取工作空间所有资源失败', [
                'workspace_id' => $workspace->id,
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取工作空间资源失败');
        }
    }
}
