<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\ConfigMap\ConfigMapNotFoundException;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\K8s\ResourceConflictException;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreConfigMapFromFilesRequest;
use App\Http\Requests\StoreConfigMapRequest;
use App\Http\Requests\UpdateConfigMapRequest;
use App\Service\ConfigMapService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ConfigMapController extends Controller
{
    /**
     * 获取 ConfigMap 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'configmaps');

            // 使用缓存和并发锁获取数据
            $configMaps = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $configMapService = new ConfigMapService($workspace);

                return $configMapService->getConfigMaps();
            });

            return $this->dto($configMaps);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 ConfigMap 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取单个 ConfigMap
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        $this->authorize('view', $workspace);

        // 如果 name 为 kube-root-ca.crt 则返回 404
        if ($name === 'kube-root-ca.crt') {
            return $this->notFound('ConfigMap not found');
        }

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'configmaps', $name);

            // 使用缓存和并发锁获取数据
            $configMap = $this->getCachedK8sData($cacheKey, function () use ($workspace, $name) {
                $configMapService = new ConfigMapService($workspace);

                return $configMapService->getConfigMap($name);
            });

            return $this->dto($configMap);
        } catch (ConfigMapNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 ConfigMap 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 ConfigMap
     */
    public function store(StoreConfigMapRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMap = $configMapService->createConfigMap(
                $request->input('name'),
                $request->input('data', []),
                $request->input('binary_data', []),
                null, // workloadType - ConfigMap 通常独立存在
                null, // workloadName
                $request->input('labels', [])
            );

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'configmaps');

            return $this->dto($configMap, 201);
        } catch (ResourceConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 ConfigMap 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 更新 ConfigMap
     */
    public function update(UpdateConfigMapRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMap = $configMapService->updateConfigMap(
                $name,
                $request->input('data', []),
                $request->input('binary_data', [])
            );

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'configmaps');

            return $this->dto($configMap);
        } catch (ConfigMapNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('更新 ConfigMap 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 删除 ConfigMap
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMapService->deleteConfigMap($name);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'configmaps');

            return $this->deleted();
        } catch (ConfigMapNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('删除 ConfigMap 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 从文件创建 ConfigMap
     */
    public function storeFromFiles(StoreConfigMapFromFilesRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMap = $configMapService->createConfigMapFromFiles(
                $request->input('name'),
                $request->input('files'),
                null, // workloadType - ConfigMap 通常独立存在
                null, // workloadName
                $request->input('labels', [])
            );

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'configmaps');

            return $this->dto($configMap, 201);
        } catch (ResourceConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('从文件创建 ConfigMap 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
