<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Cluster;
use App\Service\PricingHistoryService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ClusterPricingHistoryController extends Controller
{
    public function __construct(
        private PricingHistoryService $pricingHistoryService
    ) {}

    /**
     * 获取集群价格历史记录（K线图数据）
     */
    public function index(Request $request, Cluster $cluster): JsonResponse
    {
        $request->validate([
            'period' => 'in:1h,6h,1d,7d,30d',
            'limit' => 'integer|min:1|max:1000',
        ]);

        $period = $request->get('period', '7d');
        $limit = $request->get('limit', 100);

        $historyData = $this->pricingHistoryService->getPricingHistory($cluster, $period, $limit);

        return $this->success($historyData);
    }

    /**
     * 获取所有集群的最新价格
     */
    public function latest(): JsonResponse
    {
        $result = Cluster::whereHas('pricings', function ($query) {
            $query->where('effective_date', '<=', now());
        })
            ->get()
            ->map(function (Cluster $cluster) {
                return $this->pricingHistoryService->getLatestPricing($cluster);
            });

        return $this->success($result);
    }
}
