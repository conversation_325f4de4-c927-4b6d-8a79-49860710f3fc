<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\Domain\DomainConflictException;
use App\Exceptions\Ingress\IngressNotFoundException;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\K8s\ResourceConflictException;
use App\Exceptions\K8s\ResourceValidationException;
use App\Http\Controllers\Controller;
use App\Http\Requests\CheckDomainsRequest;
use App\Http\Requests\StoreIngressRequest;
use App\Http\Requests\UpdateIngressRequest;
use App\Service\IngressService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class IngressController extends Controller
{
    /**
     * Display a listing of the ingresses.
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'ingresses');

            // 使用缓存和并发锁获取数据
            $ingresses = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $ingressService = new IngressService($workspace);

                return $ingressService->getIngresses();
            });

            return $this->dto($ingresses);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Ingress 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * Store a newly created ingress.
     */
    public function store(StoreIngressRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $ingressService = new IngressService($workspace);
            $ingress = $ingressService->createIngress($request->validated());

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'ingresses');

            return $this->dto($ingress, 201);
        } catch (DomainConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (ResourceConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (ResourceValidationException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'errors' => $e->getValidationErrors(),
            ], 422);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 Ingress 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * Display the specified ingress.
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'ingresses', $name);

            // 使用缓存和并发锁获取数据
            $ingress = $this->getCachedK8sData($cacheKey, function () use ($workspace, $name) {
                $ingressService = new IngressService($workspace);

                return $ingressService->getIngress($name);
            });

            return $this->dto($ingress);
        } catch (IngressNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Ingress 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * Update the specified ingress.
     */
    public function update(UpdateIngressRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $ingressService = new IngressService($workspace);
            $ingress = $ingressService->updateIngress($name, $request->validated());

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'ingresses');

            return $this->dto($ingress);
        } catch (IngressNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (DomainConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (ResourceValidationException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'errors' => $e->getValidationErrors(),
            ], 422);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('更新 Ingress 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * Remove the specified ingress.
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $ingressService = new IngressService($workspace);
            $ingressService->deleteIngress($name);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'ingresses');

            return $this->deleted();
        } catch (IngressNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('删除 Ingress 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * Get available ingress classes.
     */
    public function ingressClasses(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'ingress_classes');

            // 使用缓存和并发锁获取数据
            $classes = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $ingressService = new IngressService($workspace);

                return $ingressService->getAvailableIngressClasses();
            }, 600); // Ingress 类信息缓存 10 分钟

            return $this->success($classes);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Ingress 类失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * Check domain availability.
     */
    public function checkDomains(CheckDomainsRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $ingressService = new IngressService($workspace);
            $conflicts = $ingressService->checkDomainAvailability($request->input('domains'));

            return $this->success([
                'available' => empty($conflicts),
                'conflicts' => $conflicts,
            ]);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('检查域名可用性失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
