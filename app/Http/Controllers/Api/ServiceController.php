<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\Network\IpPoolExhaustedException;
use App\Exceptions\Network\PortAllocationException;
use App\Exceptions\Service\ServiceConflictException;
use App\Exceptions\Service\ServiceNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreServiceRequest;
use App\Http\Requests\UpdateServiceRequest;
use App\Models\IpPool;
use App\Service\IpPoolService;
use App\Service\ServiceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ServiceController extends Controller
{
    /**
     * 获取 Service 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'services');

            // 使用缓存和并发锁获取数据
            $services = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $serviceService = new ServiceService($workspace, app(IpPoolService::class));

                return $serviceService->getServices();
            });

            return $this->dto($services);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Service 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取单个 Service
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'services', $name);

            // 使用缓存和并发锁获取数据
            $service = $this->getCachedK8sData($cacheKey, function () use ($workspace, $name) {
                $serviceService = new ServiceService($workspace, app(IpPoolService::class));

                return $serviceService->getService($name);
            });

            return $this->dto($service);
        } catch (ServiceNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Service 详情失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 Service
     */
    public function store(StoreServiceRequest $request)
    {
        $workspace = $request->user()->getWorkspace();
        $data = $request->validated();

        // 基于选择的IP池进行智能分配
        if ($data['type'] === 'LoadBalancer' && isset($data['ip_pool_id'])) {
            $ipPool = IpPool::find($data['ip_pool_id']);
            if ($ipPool && $ipPool->cluster_id === $workspace->cluster_id) {
                // 根据IP池的共享策略设置allow_shared_ip
                $data['allow_shared_ip'] = $ipPool->isShared();
            }
        }

        try {
            $serviceService = new ServiceService($workspace, app(IpPoolService::class));
            $service = $serviceService->createService($data);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'services');

            return $this->dto($service, 201);
        } catch (ServiceConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (IpPoolExhaustedException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (PortAllocationException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 Service 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 更新 Service
     */
    public function update(UpdateServiceRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $data = $request->validated();

        // 基于选择的IP池进行智能分配（如果提供了 ip_pool_id）
        if (isset($data['ip_pool_id'])) {
            $ipPool = IpPool::find($data['ip_pool_id']);
            if ($ipPool && $ipPool->cluster_id === $workspace->cluster_id) {
                // 根据IP池的共享策略设置allow_shared_ip
                $data['allow_shared_ip'] = $ipPool->isShared();
            }
        }

        try {
            $serviceService = new ServiceService($workspace, app(IpPoolService::class));
            $service = $serviceService->updateService($name, $data);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'services');

            return $this->dto($service);
        } catch (ServiceNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (IpPoolExhaustedException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (PortAllocationException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('更新 Service 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 删除 Service
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $serviceService = new ServiceService($workspace, app(IpPoolService::class));
            $serviceService->deleteService($name);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'services');

            return $this->deleted();
        } catch (ServiceNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('删除 Service 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
