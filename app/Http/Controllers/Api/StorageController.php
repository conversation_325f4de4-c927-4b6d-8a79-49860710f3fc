<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\K8s\ResourceConflictException;
use App\Exceptions\Storage\InvalidStorageSizeException;
use App\Exceptions\Storage\StorageNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\ExpandStorageRequest;
use App\Http\Requests\StoreStorageRequest;
use App\Service\StorageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class StorageController extends Controller
{
    /**
     * 获取工作空间的所有 Storage
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'storages');

            // 使用缓存和并发锁获取数据
            $storages = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $storageService = new StorageService($workspace);

                return $storageService->getStorages();
            });

            return $this->dto($storages);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Storage 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取单个 Storage 详情
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'storages', $name);

            // 使用缓存和并发锁获取数据
            $storage = $this->getCachedK8sData($cacheKey, function () use ($workspace, $name) {
                $storageService = new StorageService($workspace);

                return $storageService->getStorage($name);
            });

            return $this->dto($storage);
        } catch (StorageNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Storage 详情失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 Storage
     */
    public function store(StoreStorageRequest $request)
    {
        $workspace = $request->user()->getWorkspace();
        $validated = $request->validated();

        try {
            $storageService = new StorageService($workspace);
            $storage = $storageService->createStorage(
                $validated['name'],
                $validated['size'],
                null, // workloadType - Storage 通常独立存在
                null, // workloadName
                $validated['labels'] ?? []
            );

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'storages');

            return $this->dto($storage, 201);
        } catch (InvalidStorageSizeException $e) {
            return $this->badRequest($e->getMessage());
        } catch (ResourceConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 Storage 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 扩容 Storage
     */
    public function expand(ExpandStorageRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $validated = $request->validated();

        try {
            $storageService = new StorageService($workspace);
            $storage = $storageService->expandStorage($name, $validated['size']);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'storages');

            return $this->dto($storage);
        } catch (StorageNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (InvalidStorageSizeException $e) {
            return $this->badRequest($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('扩容 Storage 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 删除 Storage
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $storageService = new StorageService($workspace);
            $storageService->deleteStorage($name);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'storages');

            return $this->deleted();
        } catch (StorageNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('删除 Storage 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
