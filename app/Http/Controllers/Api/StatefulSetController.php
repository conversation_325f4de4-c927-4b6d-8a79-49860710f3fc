<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\K8s\ResourceConflictException;
use App\Exceptions\K8s\ResourceValidationException;
use App\Exceptions\Secret\SecretNotFoundException;
use App\Exceptions\StatefulSet\StatefulSetConflictException;
use App\Exceptions\StatefulSet\StatefulSetNotFoundException;
use App\Exceptions\Storage\StorageNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\ScaleWorkloadRequest;
use App\Http\Requests\StoreStatefulSetRequest;
use App\Http\Requests\UpdateStatefulSetRequest;
use App\Service\StatefulSetService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class StatefulSetController extends Controller
{
    /**
     * 获取 StatefulSet 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'statefulsets');

            // 使用缓存和并发锁获取数据
            $statefulSets = $this->getCachedK8sData($cacheKey, function () use ($workspace) {
                $statefulSetService = new StatefulSetService($workspace);

                return $statefulSetService->getStatefulSets();
            });

            return $this->dto($statefulSets);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 StatefulSet 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取单个 StatefulSet 详情
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'statefulsets', $name);

            // 使用缓存和并发锁获取数据
            $statefulSet = $this->getCachedK8sData($cacheKey, function () use ($workspace, $name) {
                $statefulSetService = new StatefulSetService($workspace);

                return $statefulSetService->getStatefulSet($name);
            });

            return $this->dto($statefulSet);
        } catch (StatefulSetNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 StatefulSet 详情失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 创建 StatefulSet
     */
    public function store(StoreStatefulSetRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $statefulSetService = new StatefulSetService($workspace);
            $statefulSet = $statefulSetService->createStatefulSet($request->validated());

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'statefulsets');
            $this->clearK8sCache($workspace->id, 'pods'); // StatefulSet 会创建 Pod

            return $this->dto($statefulSet, 201);
        } catch (ResourceConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (StatefulSetConflictException $e) {
            return $this->conflict($e->getMessage());
        } catch (ResourceValidationException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'errors' => $e->getValidationErrors(),
            ], 422);
        } catch (StorageNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (SecretNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('创建 StatefulSet 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 更新 StatefulSet
     */
    public function update(UpdateStatefulSetRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $statefulSetService = new StatefulSetService($workspace);
            $data = $request->validated();
            $data['name'] = $name; // 确保名称正确

            $statefulSet = $statefulSetService->updateStatefulSet($name, $data);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'statefulsets');
            $this->clearK8sCache($workspace->id, 'pods'); // StatefulSet 更新会影响 Pod

            return $this->dto($statefulSet);
        } catch (StatefulSetNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (ResourceValidationException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'errors' => $e->getValidationErrors(),
            ], 422);
        } catch (StorageNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (SecretNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('更新 StatefulSet 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 删除 StatefulSet
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $statefulSetService = new StatefulSetService($workspace);
            $statefulSetService->deleteStatefulSet($name);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'statefulsets');
            $this->clearK8sCache($workspace->id, 'pods'); // StatefulSet 删除会删除 Pod

            return $this->deleted();
        } catch (StatefulSetNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('删除 StatefulSet 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 扩容/缩容 StatefulSet
     */
    public function scale(ScaleWorkloadRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $statefulSetService = new StatefulSetService($workspace);
            $statefulSet = $statefulSetService->scaleStatefulSet($name, $request->input('replicas'));

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'statefulsets');
            $this->clearK8sCache($workspace->id, 'pods'); // 扩缩容会影响 Pod

            return $this->dto($statefulSet);
        } catch (StatefulSetNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('扩容/缩容 StatefulSet 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
