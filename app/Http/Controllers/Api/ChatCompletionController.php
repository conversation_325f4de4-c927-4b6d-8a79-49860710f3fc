<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ChatCompletionController extends Controller
{
    protected string $model = 'gpt-4o-mini';

    public function __construct()
    {
        $this->model = config('assistant.default_model', $this->model);
    }

    public function __invoke(Request $request)
    {
        $request->validate([
            'messages' => 'required|array',
            'tools' => 'sometimes|array',
            'tool_choice' => 'sometimes|string',
            'max_tokens' => 'sometimes|integer',
            'temperature' => 'sometimes|numeric',
            'top_p' => 'sometimes|numeric',
            'n' => 'sometimes|integer',
            'stream' => 'sometimes|boolean',
            'stop' => 'sometimes|array|string',
            'presence_penalty' => 'sometimes|numeric',
            'frequency_penalty' => 'sometimes|numeric',
            'logit_bias' => 'sometimes|array',
            'user' => 'sometimes|string',
            'mode' => 'sometimes|string',
        ]);

        $isStream = $request->boolean('stream', false);
        set_time_limit(config('openai.request_timeout', 600));

        // 如果 mode 是 agent，则自动在 messages 中加入 system 消息
        if ($request->input('mode') === 'agent') {
            $request->merge([
                'messages' => array_merge([['role' => 'system', 'content' => $this->systemPrompt($request)]], $request->input('messages', [])),
                'model' => $this->model,
            ]);
        } else {
            // model is required
            $request->validate([
                'model' => 'required|string',
            ]);
        }

        try {
            $response = $this->sendToOpenAI($request->all(), $isStream);

            if ($isStream) {
                return $this->handleStreamResponse($response);
            } else {
                return $this->handleNormalResponse($response);
            }
        } catch (\Exception $e) {
            // 从异常中提取完整的cURL响应信息
            $httpCode = $e->getCode();
            $responseBody = $e->getMessage();
            $fullResponse = null;

            // 如果异常中包含完整的响应数据
            if (isset($e->fullResponse)) {
                $fullResponse = $e->fullResponse;
                $httpCode = $fullResponse->http_code ?? $httpCode;
                $responseBody = $fullResponse->body ?? $responseBody;
            }

            Log::error('OpenAI API 请求失败', [
                'error' => $e->getMessage(),
                'http_code' => $httpCode,
                'response_body' => $responseBody,
                'request_data' => $request->all(),
            ]);

            // 确保使用有效的HTTP状态码
            if ($httpCode < 100 || $httpCode > 599) {
                $httpCode = 500;
            }

            return $this->apiResponse([
                'message' => 'OpenAI API 请求失败: '.$e->getMessage(),
                'type' => 'api_error',
                'code' => $httpCode,
                'full_response' => $fullResponse ? [
                    'http_code' => $fullResponse->http_code,
                    'body' => $fullResponse->body,
                    'curl_error' => $fullResponse->error ?? null,
                ] : null,
            ], $httpCode);
        }
    }

    private function sendToOpenAI(array $data, bool $isStream = false)
    {
        $baseUrl = config('openai.base_url');
        $apiKey = config('openai.api_key');

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/chat/completions');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer '.$apiKey,
            'Content-Type: '.'application/json',
            'Accept: '.($isStream ? 'text/event-stream' : 'application/json'),
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, config('openai.request_timeout', 600));
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, ! $isStream);
        curl_setopt($ch, CURLOPT_HEADER, false);

        $response = new \stdClass;
        $response->http_code = 0;
        $response->error = '';
        $response->is_stream = $isStream;

        if ($isStream) {
            $response->curl_handle = $ch; // Pass the cURL handle for streaming
        } else {
            $response->body = curl_exec($ch);
            $response->http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $response->error = curl_error($ch);
            $curlErrno = curl_errno($ch);
            curl_close($ch);

            // 处理cURL错误
            if ($curlErrno) {
                $exception = new \Exception("cURL error ($curlErrno): ".$response->error, 500);
                $exception->fullResponse = $response; // 附加完整响应
                throw $exception;
            }

            // 处理HTTP错误 (状态码 >= 400)
            if ($response->http_code >= 400) {
                $exception = new \Exception("HTTP Error ({$response->http_code}): ".$response->body, $response->http_code);
                $exception->fullResponse = $response; // 附加完整响应
                throw $exception;
            }
        }

        return $response;
    }

    private function handleStreamResponse($response): StreamedResponse
    {
        return new StreamedResponse(function () use ($response) {
            $ch = $response->curl_handle;
            $totalTokens = 0;
            $promptTokens = 0;
            $completionTokens = 0;
            $buffer = '';

            // Disable buffering
            while (ob_get_level()) {
                ob_end_flush();
            }
            flush();

            // Use curl_multi for streaming
            $mh = curl_multi_init();
            curl_multi_add_handle($mh, $ch);

            curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $chunk) use (&$totalTokens, &$promptTokens, &$completionTokens, &$buffer) {
                $buffer .= $chunk;

                // Process complete SSE events (delimited by \n\n)
                while (($pos = strpos($buffer, "\n\n")) !== false) {
                    $completeChunk = substr($buffer, 0, $pos);
                    $buffer = substr($buffer, $pos + 2);

                    // Split into lines
                    $lines = explode("\n", trim($completeChunk));

                    foreach ($lines as $line) {
                        $line = trim($line);
                        if (empty($line) || ! str_starts_with($line, 'data: ')) {
                            continue;
                        }

                        $jsonData = substr($line, 6);

                        // Output to client
                        echo $line."\n\n";
                        flush();

                        if ($jsonData === '[DONE]') {
                            return 0; // Signal cURL to stop
                        }

                        $data = json_decode($jsonData, true);
                        if (json_last_error() === JSON_ERROR_NONE && isset($data['usage'])) {
                            $totalTokens = $data['usage']['total_tokens'] ?? 0;
                            $promptTokens = $data['usage']['prompt_tokens'] ?? 0;
                            $completionTokens = $data['usage']['completion_tokens'] ?? 0;
                        }
                    }
                }

                return strlen($chunk);
            });

            // Execute cURL with multi-handle
            $running = null;
            do {
                curl_multi_exec($mh, $running);
                curl_multi_select($mh);
            } while ($running > 0);

            // Handle any remaining buffer
            if (! empty($buffer)) {
                $lines = explode("\n", trim($buffer));
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (empty($line) || ! str_starts_with($line, 'data: ')) {
                        continue;
                    }
                    $jsonData = substr($line, 6);
                    echo $line."\n\n";
                    flush();
                    if ($jsonData === '[DONE]') {
                        break;
                    }
                }
            }

            // Log token usage
            if ($totalTokens > 0) {
                $this->logTokenUsage($totalTokens, $promptTokens, $completionTokens, true);
            }

            // Clean up
            curl_multi_remove_handle($mh, $ch);
            curl_multi_close($mh);
            curl_close($ch);
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no',
        ]);
    }

    private function handleNormalResponse($response): JsonResponse
    {
        $data = json_decode($response->body, true);

        if (isset($data['usage'])) {
            $this->logTokenUsage(
                $data['usage']['total_tokens'] ?? 0,
                $data['usage']['prompt_tokens'] ?? 0,
                $data['usage']['completion_tokens'] ?? 0,
                false
            );
        }

        return response()->json($data);
    }

    private function logTokenUsage(int $totalTokens, int $promptTokens, int $completionTokens, bool $isStream): void
    {
        Log::info('OpenAI API Token 消耗记录', [
            'total_tokens' => $totalTokens,
            'prompt_tokens' => $promptTokens,
            'completion_tokens' => $completionTokens,
            'is_stream' => $isStream,
            'timestamp' => now()->toISOString(),
            'user_id' => auth()->id(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    private function systemPrompt(Request $request): string
    {
        $workspace = $request->user()->getWorkspace();
        $workspaceName = $workspace->name;

        $workspacePrompt = '';
        if ($workspace) {
            $workspacePrompt = "用户当前选择的工作空间：{$workspaceName}，你无须操作工作空间";
        } else {
            $workspacePrompt = '用户当前未选择工作空间，请到一个工作空间中，设置当前的工作空间。';
        }

        // 获取系统提示
        return <<<EOF
你是一个专业的 Kubernetes PaaS 平台助手，能够帮助用户管理和操作基于 K8s 的应用部署平台。{$workspacePrompt}

## 平台概述

这是一个基于 Kubernetes 的 PaaS 平台，让用户无需关心底层 K8s 集群的复杂性，只需要简单配置就能部署和管理应用。

### 核心概念

**工作空间 (Workspace)**
- 每个团队都有独立的工作空间
- 工作空间是资源隔离的基本单位
- 所有的应用、存储、网络等资源都属于特定的工作空间

**应用工作负载类型**
1. **Deployment** - 无状态应用
   - 适用于 Web 应用、API 服务等
   - 支持水平扩展和滚动更新
   - 实例之间无状态依赖

2. **StatefulSet** - 有状态应用
   - 适用于数据库、消息队列等
   - 提供稳定的网络标识和存储
   - 有序部署和扩展

**存储 (Storage)**
- 平台提供持久化存储卷
- 底层使用 Longhorn 存储类
- 支持动态扩容和快照备份
- 读写模式固定为 RWX（多节点读写）

**网络服务 (Service)**
- 为应用提供稳定的网络访问入口
- 支持 ClusterIP、NodePort 等类型
- 自动负载均衡到后端 Pod

**域名访问 (Ingress)**
- 提供 HTTP/HTTPS 外部访问
- 支持自定义域名和 SSL 证书
- 路径路由和虚拟主机

**配置管理**
- **ConfigMap** - 存储配置文件和环境变量
- **Secret** - 存储敏感信息如密码、证书

**自动扩展 (HPA)**
- 基于 CPU、内存使用率自动扩缩容
- 设置最小/最大实例数
- 智能的扩缩容策略

## 操作指南

### 工具使用原则
1. **仔细观察页面** - 在操作前，先分析当前页面的结构和可用元素
2. **验证元素存在** - 只操作确实存在的元素，使用精确的选择器
3. **循序渐进** - 复杂操作分解为多个简单步骤
4. **重复尝试** - 如果操作失败，可以重复调用工具直到成功
5. **等待加载** - 页面变化后等待新内容加载完成
6. **Dialog** - 如果有 Dialog，一般会有操作按钮，你需要确保点击没有问题

### 常见操作流程

**创建应用**
1. 导航到对应的工作负载页面（Deployments 或 StatefulSets）
2. 点击"创建"或"新建"按钮
3. 填写基本信息：名称、镜像、端口等
4. 配置资源限制：内存（512Mi 的倍数）、CPU（500m 的倍数）
5. 设置环境变量和挂载存储（如需要）
6. 提交创建

**配置网络访问**
1. 创建 Service 为应用提供内部访问
2. 创建 Ingress 配置外部域名访问
3. 设置合适的端口映射和路径规则

**存储管理**
1. 在 Storages 页面创建存储卷
2. 指定大小和访问模式
3. 在应用中挂载到指定路径

**配置管理**
1. 配置管理对应 ConfigMap
2. 密钥管理对应 Secret
3. 在应用中引用这些配置

### 资源规格要求
- **内存**：最小 512Mi，必须是 512 的倍数
- **CPU**：最小 500m，必须是 500 的倍数
- **存储**：最小 512Mi，必须是 512 的倍数
- **网络**：自动分配 IP 和端口，支持负载均衡

### 注意事项
1. 所有资源都会自动添加平台标签用于管理
2. 你和用户无法直接操作 K8s 原生 API
3. 资源限制会自动设置 request 为 limit 的一半
4. 删除应用会自动清理相关资源
5. 如果没有你想要的信息，你可以翻阅菜单然后逐步寻找，不要总是问用户，一切你自己都可以操作。

## 操作建议

当用户询问如何操作时：
1. 首先确认用户的目标和需求
2. 分析当前页面状态，确定需要的操作步骤
3. 使用工具逐步执行操作
4. 验证操作结果，确保达到预期效果
5. 如果遇到错误，分析原因并重试
6. 用户可能是经验丰富的开发者，所以请使用简洁明了的语言进行交流
7. 用户也可能是刚入门的新手，你必须尽可能帮他做更多的事情。

记住：你可以无限次调用工具来完成复杂的操作流程，不要害怕多次尝试。始终基于实际的页面内容进行操作，不要假设元素的存在。`;

EOF;

    }
}
