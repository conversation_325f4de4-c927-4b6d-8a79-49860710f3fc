<?php

namespace App\Http\Controllers;

use App\Http\Requests\UploadWallpaperRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class DesktopController extends Controller
{
    /**
     * 系统内置应用列表
     *
     * @return array<int, array<string, mixed>>
     */
    protected function getSystemApps(): array
    {
        return [
            [
                'id' => 'dashboard',
                'name' => '总览',
                'icon' => 'Activity',
                'type' => 'browser',
                'url' => route('dashboard'),
            ],
            [
                'id' => 'applications',
                'name' => '应用管理',
                'icon' => 'Layout',
                'type' => 'browser',
                'url' => route('applications.index'),
            ],
            [
                'id' => 'tokens',
                'name' => '访问令牌',
                'icon' => 'Key',
                'type' => 'browser',
                'url' => route('oauth.tokens.index'),
            ],
            [
                'id' => 'oauth-clients',
                'name' => 'OAuth 客户端',
                'icon' => 'KeyRound',
                'type' => 'browser',
                'url' => route('oauth.clients.index'),
            ],
            [
                'id' => 'test-publisher',
                'name' => 'Test Publisher',
                'icon' => 'Send',
                'type' => 'browser',
                'url' => route('tests.publisher'),
            ],
            [
                'id' => 'test-subscriber',
                'name' => 'Test Subscriber',
                'icon' => 'Inbox',
                'type' => 'browser',
                'url' => route('tests.subscriber'),
            ],
            [
                'id' => 'test-external',
                'name' => 'External Test',
                'icon' => 'Globe',
                'type' => 'browser',
                'url' => route('tests.external'),
            ],
        ];
    }

    /**
     * 桌面首页
     */
    public function index(): Response
    {
        $systemApps = $this->getSystemApps();

        // 获取用户授权（安装）的 OAuth 应用
        $authorizedApps = auth()->user()->tokens()
            ->with('client')
            ->where('revoked', false)
            ->get()
            ->groupBy('client_id')
            ->map(function ($tokens) {
                $client = $tokens->first()->client;

                return [
                    'id' => 'oauth-app-'.$client->id,
                    'name' => $client->name,
                    'icon' => $client->icon_url ?? 'Globe',
                    'type' => 'browser',
                    // 启动地址使用客户端回调地址；如需统一代理，可在此处调整
                    'url' => $client->redirect,
                ];
            })
            ->values()
            ->toArray();

        $wallpaperPath = auth()->user()->getSetting('desktop_wallpaper');
        $wallpaperUrl = $wallpaperPath ? Storage::disk('public')->url($wallpaperPath) : null;

        // 获取用户自定义排序
        $iconOrder = auth()->user()->getSetting('desktop_icon_order', []);

        // 合并应用后根据排序调整
        $apps = array_merge($systemApps, $authorizedApps);
        if (is_array($iconOrder) && ! empty($iconOrder)) {
            usort($apps, function ($a, $b) use ($iconOrder) {
                $posA = array_search($a['id'], $iconOrder);
                $posB = array_search($b['id'], $iconOrder);

                // 未找到的排在最后
                $posA = $posA === false ? PHP_INT_MAX : $posA;
                $posB = $posB === false ? PHP_INT_MAX : $posB;

                return $posA <=> $posB;
            });
        }

        return Inertia::render('Desktop/Index', [
            'apps' => $apps,
            'wallpaper' => $wallpaperUrl,
        ]);
    }

    /**
     * 上传/更换桌面壁纸
     */
    public function uploadWallpaper(UploadWallpaperRequest $request): JsonResponse
    {
        $file = $request->file('wallpaper');

        // 保存文件到 public 储存分区
        $path = $file->store('wallpapers', 'public');

        // 保存到用户设置
        auth()->user()->setSetting('desktop_wallpaper', $path);

        return $this->success([
            'wallpaper_url' => Storage::disk('public')->url($path),
        ]);
    }

    /**
     * 移除壁纸
     */
    public function removeWallpaper(): JsonResponse
    {
        $path = auth()->user()->getSetting('desktop_wallpaper');
        if ($path) {
            // 尝试删除文件（忽略错误）
            try {
                Storage::disk('public')->delete($path);
            } catch (\Throwable $e) {
                // ignore
            }
            auth()->user()->deleteSetting('desktop_wallpaper');
        }

        return $this->success();
    }

    /**
     * 保存图标排序
     */
    public function saveIconOrder(\App\Http\Requests\UpdateIconOrderRequest $request): JsonResponse
    {
        $order = $request->validated('order');

        auth()->user()->setSetting('desktop_icon_order', $order);

        return $this->success();
    }
}
