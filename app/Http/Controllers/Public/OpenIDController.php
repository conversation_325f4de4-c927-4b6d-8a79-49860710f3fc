<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Laravel\Passport\Passport;

class OpenIDController extends Controller
{
    public function discovery(Request $request)
    {
        $response = [
            'issuer' => url('/'),
            'authorization_endpoint' => route('passport.authorizations.authorize'),
            'token_endpoint' => route('passport.token'),
        ];

        $jwksRoute = 'public.openid.jwks';
        if (Route::has($jwksRoute)) {
            $response['jwks_uri'] = route($jwksRoute);
        }

        $userinfoRoute = 'api.user.index';
        if (Route::has($userinfoRoute)) {
            $response['userinfo_endpoint'] = route($userinfoRoute);
        }

        $response['response_types_supported'] = [
            'code',
            'token',
            'id_token',
            'code token',
            'code id_token',
            'token id_token',
            'code token id_token',
            'none',
        ];

        // grant_types_supported
        $response['grant_types_supported'] = [
            'authorization_code',
            'refresh_token',
            'urn:ietf:params:oauth:grant-type:device_code',
        ];

        $response['subject_types_supported'] = [
            'public',
        ];

        $response['id_token_signing_alg_values_supported'] = [
            'RS256',
        ];

        $response['scopes_supported'] = config('openid.passport.tokens_can');

        $response['token_endpoint_auth_methods_supported'] = [
            'client_secret_basic',
            'client_secret_post',
        ];

        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function jwks()
    {
        $publicKey = $this->getPublicKey();

        // Source: https://www.tuxed.net/fkooman/blog/json_web_key_set.html
        $keyInfo = openssl_pkey_get_details(openssl_pkey_get_public($publicKey));

        //        $bits = $keyInfo['bits'];

        $jsonData = [
            'keys' => [
                [
                    'kid' => config('openid.kid'),
                    'alg' => app(config('openid.signer'))->algorithmId(),
                    'kty' => 'RSA',
                    'use' => 'sig',
                    'n' => rtrim(str_replace(['+', '/'], ['-', '_'], base64_encode($keyInfo['rsa']['n'])), '='),
                    'e' => rtrim(str_replace(['+', '/'], ['-', '_'], base64_encode($keyInfo['rsa']['e'])), '='),
                ],
            ],
        ];

        return response()->json($jsonData, 200, [], JSON_PRETTY_PRINT);
    }

    private function getPublicKey(): string
    {
        $publicKey = str_replace('\\n', "\n", config('passport.public_key', ''));

        if (! $publicKey) {
            $publicKey = 'file://'.Passport::keyPath('oauth-public.key');
        }

        return $publicKey;
    }
}
