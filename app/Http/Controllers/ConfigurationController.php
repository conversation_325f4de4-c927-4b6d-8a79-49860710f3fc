<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;

class ConfigurationController extends Controller
{
    /**
     * 获取标签配置
     */
    public function getLabelsConfig(): JsonResponse
    {
        return response()->json([
            'max_labels' => config('k8s.max_labels', 16),
            'allow_label_prefix' => config('k8s.allow_label_prefix', ['x-']),
        ]);
    }
}
