<?php

namespace App\Http\Controllers;

use App\Http\Requests\GetManfiestRequest;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;
use Symfony\Component\Yaml\Exception\ParseException;
use Symfony\Component\Yaml\Yaml;

class ApplyController extends Controller
{
    public function index()
    {
        return Inertia::render('Apply/Index');
    }

    /**
     * 从 URL 中获取清单文件 (API 异步)
     */
    public function getManifest(GetManfiestRequest $request)
    {
        $url = $request->validated('url');
        $maxSize = 1048576; // 1MB

        try {
            // 先发 HEAD 请求检测 Content-Length
            $headResponse = Http::timeout(5)
                ->accept('application/yaml')
                ->withHeaders(['Range' => 'bytes=0-0']) // 某些服务器需要 Range 才返回 Content-Length
                ->head($url);
            $contentLength = $headResponse->header('Content-Length');
            if ($contentLength !== null && (int) $contentLength > $maxSize) {
                return $this->error('清单文件过大，不能超过 1MB。', 413);
            }

            // 再发 GET 请求获取内容
            $response = Http::timeout(10)
                ->accept('application/yaml')
                ->get($url);

            if ($response->failed()) {
                return $this->apiResponse([
                    'error' => '无法获取清单文件，服务器返回错误: '.$response->status(),
                ], $response->status());
            }

            $body = $response->body();
            if (strlen($body) > $maxSize) {
                return $this->error('清单文件过大，不能超过 1MB。', 413);
            }

            // explode ---
            $parts = explode('---', $body);

            // 检查是否是 YAML 文件
            $isYaml = false;

            try {
                foreach ($parts as $part) {
                    Yaml::parse($part);
                    $isYaml = true;
                    break;
                }
            } catch (ParseException $e) {
                $isYaml = false;
            }

            if (! $isYaml) {
                return $this->error('清单文件不是合法的 YAML 文件。', 400);
            }

            return $this->success([
                'content' => $body,
            ]);
        } catch (ConnectionException) {
            return $this->error('无法连接到指定的 URL，请检查网络连接或 URL 是否正确。', 504);
        }
    }
}
