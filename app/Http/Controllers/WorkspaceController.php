<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreWorkspaceRequest;
use App\Http\Requests\UpdateWorkspaceRequest;
use App\Http\Resources\WorkspaceResource;
use App\Models\Cluster;
use App\Models\Workspace;
use App\Service\WorkspaceService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class WorkspaceController extends Controller
{
    public function __construct(
        protected WorkspaceService $workspaceService
    ) {}

    /**
     * 显示工作空间列表页面
     */
    public function index(Request $request)
    {
        // dd($request->user()->workspaces()->with('cluster')->get()->toArray());
        $workspaces = $request->user()->workspaces()->with('cluster')->get();

        return Inertia::render('Workspaces/Index', [
            'workspaces' => WorkspaceResource::collection($workspaces)->resolve(),
        ]);
    }

    /**
     * 显示创建工作空间页面
     */
    public function create(Request $request)
    {
        $user = $request->user();

        $clusters = Cluster::select('id', 'name')->get()->map(function ($cluster) {
            return [
                'id' => $cluster->id,
                'name' => $cluster->name,
                'billing_enabled' => $cluster->isBillingEnabled(),
            ];
        });

        return Inertia::render('Workspaces/Create', [
            'clusters' => $clusters,
            'balance_not_enough' => ! $user->hasEnoughBalance('1'),
        ]);
    }

    /**
     * 存储新的工作空间
     */
    public function store(StoreWorkspaceRequest $request)
    {
        $user = $request->user();

        // 如果用户没有余额，则不允许创建
        if (! $user->hasEnoughBalance('1')) {
            return redirect()->route('balance.index')->with('error', '你的账户余额不足 1，无法创建工作空间');
        }

        try {
            $workspace = $this->workspaceService->createWorkspace([
                'user_id' => auth()->id(),
                'cluster_id' => $request->cluster_id,
                'name' => $request->name,
            ]);

            return redirect()->route('workspaces.show', $workspace)
                ->with('success', '工作空间创建成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['general' => '创建工作空间失败：'.$e->getMessage()]);
        }
    }

    /**
     * 显示工作空间详情
     */
    public function show(Workspace $workspace)
    {
        $this->authorize('view', $workspace);

        $workspace->load('cluster');

        return Inertia::render('Workspaces/Show', [
            'workspace' => new WorkspaceResource($workspace),
        ]);
    }

    /**
     * 显示编辑工作空间页面
     */
    public function edit(Workspace $workspace)
    {
        $this->authorize('update', $workspace);

        return Inertia::render('Workspaces/Edit', [
            'workspace' => new WorkspaceResource($workspace->load('cluster')),
        ]);
    }

    /**
     * 更新工作空间
     */
    public function update(UpdateWorkspaceRequest $request, Workspace $workspace)
    {
        $this->authorize('update', $workspace);

        try {
            $workspace->update($request->validated());

            return redirect()->route('workspaces.show', $workspace)
                ->with('success', '工作空间更新成功');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['general' => '更新工作空间失败：'.$e->getMessage()]);
        }
    }

    /**
     * 删除工作空间
     */
    public function destroy(Workspace $workspace)
    {
        $this->authorize('delete', $workspace);

        try {
            // 如果是当前工作空间，清除用户的当前工作空间
            if (auth()->user()->current_workspace_id === $workspace->id) {
                auth()->user()->update(['current_workspace_id' => null]);
            }

            // 使用服务删除工作空间（包括 Kubernetes namespace）
            $success = $this->workspaceService->deleteWorkspace($workspace);

            if ($success) {
                return redirect()->route('workspaces.index')
                    ->with('success', '工作空间删除成功，相关资源正在后台清理中');
            } else {
                return redirect()->back()
                    ->withErrors(['general' => '删除工作空间失败']);
            }
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => '删除工作空间失败：'.$e->getMessage()]);
        }
    }

    /**
     * 设置当前工作空间
     */
    public function setCurrent(Workspace $workspace)
    {
        $this->authorize('view', $workspace);

        try {
            auth()->user()->setCurrentWorkspace($workspace);

            return redirect()->back()
                ->with('success', '已切换到工作空间：'.$workspace->name);
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => '切换工作空间失败：'.$e->getMessage()]);
        }
    }
}
