<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateOAuthClientRequest;
use App\Http\Requests\UpdateOAuthClientRequest;
use App\Http\Resources\OAuthClientResource;
use App\Models\OAuthClient;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class OAuthController extends Controller
{
    /**
     * 显示 OAuth 客户端列表页面
     */
    public function clients(): Response
    {
        $clients = OAuthClient::where('owner_type', User::class)
            ->where('owner_id', auth()->id())
            ->where('revoked', false)
            ->latest()
            ->get();

        return Inertia::render('OAuthClients/Index', [
            'clients' => OAuthClientResource::collection($clients),
        ]);
    }

    /**
     * 创建新的 OAuth 客户端
     */
    public function store(CreateOAuthClientRequest $request): JsonResponse
    {
        $iconPath = null;

        // 处理图标上传
        if ($request->hasFile('icon')) {
            $iconPath = $this->storeIcon($request->file('icon'));
        }

        $grantTypes = ['authorization_code', 'refresh_token'];

        $secret = Str::random(40);
        // 如果是 PKCE 客户端，则不生成 secret
        if ($request->pkce_client) {
            $secret = null;
        }

        // 如果是 device 客户端，则 grantTypes 增加一个 "urn:ietf:params:oauth:grant-type:device_code"
        if ($request->device_flow) {
            $grantTypes[] = 'urn:ietf:params:oauth:grant-type:device_code';
        }

        $client = OAuthClient::create([
            'id' => (string) Str::orderedUuid(),
            'owner_type' => User::class,
            'owner_id' => auth()->id(),
            'name' => $request->name,
            'secret' => $secret,
            'redirect_uris' => [$request->redirect],
            'grant_types' => $grantTypes,
            'revoked' => false,
            'icon_path' => $iconPath,
        ]);

        return $this->success([
            'client' => new OAuthClientResource($client),
            'secret' => $secret,
        ]);
    }

    /**
     * 更新 OAuth 客户端
     */
    public function update(UpdateOAuthClientRequest $request, OAuthClient $client): JsonResponse
    {
        // 确保用户只能操作自己的客户端
        if ($client->owner_type !== User::class || $client->owner_id !== auth()->id()) {
            abort(403, '无权限操作此客户端');
        }

        $updateData = [
            'name' => $request->name,
            'redirect_uris' => [$request->redirect],
        ];

        // 处理图标上传
        if ($request->hasFile('icon')) {
            // 删除旧图标
            $client->deleteIcon();

            // 上传新图标
            $updateData['icon_path'] = $this->storeIcon($request->file('icon'));
        }

        $client->update($updateData);

        return $this->success([
            'client' => new OAuthClientResource($client),
        ]);
    }

    /**
     * 删除 OAuth 客户端
     */
    public function destroy(OAuthClient $client): JsonResponse
    {
        // 确保用户只能操作自己的客户端
        if ($client->owner_type !== User::class || $client->owner_id !== auth()->id()) {
            abort(403, '无权限操作此客户端');
        }

        // 删除图标文件
        $client->deleteIcon();

        // 软删除，将 revoked 设为 true
        $client->update(['revoked' => true]);

        return $this->deleted();
    }

    /**
     * 存储客户端图标
     */
    private function storeIcon($file): string
    {
        $userId = auth()->id();
        $extension = $file->getClientOriginalExtension();
        $filename = Str::uuid().'.'.$extension;

        // 存储路径格式: oauth-clients/user_{user_id}/{filename}
        $path = "oauth-clients/user_{$userId}/{$filename}";

        Storage::disk('public')->putFileAs(
            "oauth-clients/user_{$userId}",
            $file,
            $filename
        );

        return $path;
    }
}
