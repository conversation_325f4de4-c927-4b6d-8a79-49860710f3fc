<?php

namespace App\Http\Controllers;

use App\Http\Requests\RedeemCodeRequest;
use App\Http\Requests\TopUpRequest;
use App\Service\BalanceService;
use App\Service\PaymentManagerService;
use App\Service\PaymentService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class BalanceController extends Controller
{
    /**
     * 显示余额和充值记录页面
     */
    public function index(Request $request): Response
    {
        /** @var \App\Models\User $user */
        $user = $request->user();

        $records = $user->topUpRecords()
            ->latest('created_at')
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('Balance/Index', [
            'balance' => formatAmount($user->current_balance),
            'records' => $records,
            // 'paymentMethods' => $paymentManager->getGatewayList(),
        ]);
    }

    /**
     * 处理充值请求
     */
    public function topUp(TopUpRequest $request, PaymentService $paymentService, PaymentManagerService $paymentManager)
    {
        /** @var \App\Models\User $user */
        $user = $request->user();
        $amount = (float) $request->validated('amount');
        $paymentMethod = $request->validated('payment_method', '');

        // 验证支付方式是否可用
        $gateway = $paymentManager->getGateway($paymentMethod);
        if (! $gateway) {
            return back()->with('error', '不支持的支付方式');
        }

        // 检查支付方式是否启用（从配置中检查）
        $config = config("payments.gateways.{$paymentMethod}", []);
        if (! ($config['enabled'] ?? false)) {
            return back()->with('error', '该支付方式暂时不可用');
        }

        // 2. 根据支付方式处理
        try {
            if ($paymentMethod === 'manual') {
                // 1. 创建待支付记录
                $record = $paymentService->createPendingTopUpRecord(
                    $user,
                    $amount,
                    $paymentMethod,
                    ['remark' => '在线充值']
                );

                // 手动充值直接成功（模拟）
                $paymentService->markPaymentSuccess($record, ['gateway_response' => 'manual_success_'.now()]);

                return back()->with('success', '充值成功！');
            } else {
                // 其他支付方式先创建支付订单获取订单ID
                $paymentData = $paymentService->createPayment($user, $amount, $paymentMethod, [
                    'subject' => '账户充值',
                    'body' => "用户 {$user->name} 充值 ¥{$amount}",
                ]);

                // 使用订单ID创建待支付记录
                $record = $paymentService->createPendingTopUpRecord(
                    $user,
                    $amount,
                    $paymentMethod,
                    [
                        'remark' => '在线充值',
                        'order_id' => $paymentData['order_id'] ?? null,
                    ]
                );

                if ($paymentData['url']) {
                    // 重定向到支付页面
                    return redirect($paymentData['url']);
                } else {
                    // 如果没有支付URL，标记为成功（如兑换码等即时支付）
                    $paymentService->markPaymentSuccess($record, $paymentData);

                    return back()->with('success', '充值成功！');
                }
            }
        } catch (\Exception $e) {
            // 如果已经创建了记录，标记为失败
            if (isset($record)) {
                $paymentService->markPaymentFailed($record, ['error' => $e->getMessage()]);
            }

            return back()->with('error', '充值失败：'.$e->getMessage());
        }
    }

    /**
     * 处理兑换码兑换
     */
    public function redeem(RedeemCodeRequest $request, BalanceService $balanceService)
    {
        /** @var \App\Models\User $user */
        $user = $request->user();
        $code = strtoupper(trim($request->validated('code')));

        try {
            $result = $balanceService->redeemCode(
                $user,
                $code,
                $request->ip(),
                $request->userAgent()
            );

            return back()->with('success', "兑换成功！获得 ¥{$result['amount']} 余额");
        } catch (\Exception $e) {
            return back()->withErrors(['code' => $e->getMessage()]);
        }
    }
}
