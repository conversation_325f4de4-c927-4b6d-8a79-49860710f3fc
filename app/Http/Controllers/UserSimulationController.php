<?php

namespace App\Http\Controllers;

use App\Service\UserSimulationService;
use Illuminate\Support\Facades\Auth;

class UserSimulationController extends Controller
{
    protected UserSimulationService $userSimulationService;

    public function __construct(UserSimulationService $userSimulationService)
    {
        $this->userSimulationService = $userSimulationService;
    }

    /**
     * 验证Token并模拟用户登录
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function login(string $token)
    {
        $userId = $this->userSimulationService->validateSimulationToken($token);

        if (! $userId) {
            abort(404, 'Token无效或已过期');
        }

        // 销毁Token
        $this->userSimulationService->destroySimulationToken($token);

        // 模拟用户登录
        Auth::guard('web')->loginUsingId($userId, true);

        return redirect()->route('dashboard');
    }
}
