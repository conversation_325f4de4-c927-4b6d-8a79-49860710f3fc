<?php

namespace App\Http\Controllers;

use App\DTOs\KubernetesResourceDTO;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

abstract class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;

    /**
     * 带缓存和并发锁的 K8s 数据获取方法
     *
     * @param  string  $cacheKey  缓存键
     * @param  callable  $dataCallback  获取数据的回调函数
     * @param  int  $ttl  缓存时间（秒），默认 5 秒
     * @param  int  $lockTimeout  锁超时时间（秒），默认 30 秒
     * @return mixed
     */
    protected function getCachedK8sData(string $cacheKey, callable $dataCallback, int $ttl = 5, int $lockTimeout = 30)
    {
        // 先尝试从缓存获取数据
        $cachedData = Cache::get($cacheKey);
        if ($cachedData !== null) {
            return $cachedData;
        }

        // 生成锁键，避免并发请求重复获取数据
        $lockKey = "lock:{$cacheKey}";

        // 尝试获取锁
        $lock = Cache::lock($lockKey, $lockTimeout);

        try {
            // 获取锁成功
            if ($lock->get()) {
                // 再次检查缓存，可能在等待锁的过程中其他请求已经缓存了数据
                $cachedData = Cache::get($cacheKey);
                if ($cachedData !== null) {
                    return $cachedData;
                }

                // 执行数据获取回调
                $data = $dataCallback();

                // 将数据存入缓存
                Cache::put($cacheKey, $data, $ttl);

                return $data;
            } else {
                // 获取锁失败，等待一段时间后再次尝试从缓存获取
                // 这里使用短暂等待，避免大量请求同时重试
                usleep(100000); // 等待 100ms

                $cachedData = Cache::get($cacheKey);
                if ($cachedData !== null) {
                    return $cachedData;
                }

                // 如果仍然没有缓存数据，直接执行回调（降级处理）
                Log::warning("Failed to acquire lock for cache key: {$cacheKey}, executing callback directly");

                return $dataCallback();
            }
        } catch (\Exception $e) {
            Log::error("Error in getCachedK8sData for key {$cacheKey}: ".$e->getMessage(), [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 发生异常时直接执行回调（降级处理）
            return $dataCallback();
        } finally {
            // 确保释放锁
            if (isset($lock)) {
                $lock->release();
            }
        }
    }

    /**
     * 生成 K8s 资源的缓存键
     *
     * @param  string  $workspaceId  工作空间ID
     * @param  string  $resourceType  资源类型（如 deployments, pods 等）
     * @param  string|null  $resourceName  资源名称（可选）
     * @param  array  $params  额外参数（可选）
     */
    protected function generateK8sCacheKey(string $workspaceId, string $resourceType, ?string $resourceName = null, array $params = []): string
    {
        $keyParts = [
            'k8s',
            $workspaceId,
            $resourceType,
        ];

        if ($resourceName) {
            $keyParts[] = $resourceName;
        }

        if (! empty($params)) {
            ksort($params); // 确保参数顺序一致
            $keyParts[] = md5(serialize($params));
        }

        return implode(':', $keyParts);
    }

    /**
     * 清除指定工作空间的 K8s 缓存
     *
     * @param  string  $workspaceId  工作空间ID
     * @param  string|null  $resourceType  资源类型（可选，不指定则清除该工作空间所有缓存）
     */
    protected function clearK8sCache(string $workspaceId, ?string $resourceType = null): void
    {
        try {
            // 构建要清除的缓存键模式
            if ($resourceType) {
                // 清除特定资源类型的缓存
                $patterns = [
                    "k8s:{$workspaceId}:{$resourceType}",
                    "k8s:{$workspaceId}:{$resourceType}:*",
                ];
            } else {
                // 清除整个工作空间的缓存
                $patterns = ["k8s:{$workspaceId}:*"];
            }

            $cacheDriver = config('cache.default');
            $clearedCount = 0;

            if ($cacheDriver === 'redis') {
                // Redis 驱动使用 keys 命令
                foreach ($patterns as $pattern) {
                    try {
                        $keys = Cache::getRedis()->keys($pattern);
                        if (! empty($keys)) {
                            Cache::getRedis()->del($keys);
                            $clearedCount += count($keys);
                        }
                    } catch (\Exception $e) {
                        Log::warning("Failed to clear cache pattern: {$pattern}", [
                            'error' => $e->getMessage(),
                        ]);
                    }
                }
            } else {
                // 其他驱动，尝试删除已知的缓存键
                $resourceTypes = [
                    'deployments', 'statefulsets', 'pods', 'services', 'ingresses',
                    'configmaps', 'secrets', 'storages', 'hpas', 'events', 'metrics',
                    'pod_logs', 'pod_metrics', 'resource_events', 'secret_data',
                    'ingress_classes', 'scalable_workloads', 'workload_resources',
                    'workspace_events', 'workspace_metrics',
                ];

                if ($resourceType) {
                    // 只清除指定的资源类型
                    $typesToClear = [$resourceType];
                } else {
                    // 清除所有资源类型
                    $typesToClear = $resourceTypes;
                }

                foreach ($typesToClear as $type) {
                    // 清除列表缓存
                    $listKey = "k8s:{$workspaceId}:{$type}";
                    if (Cache::has($listKey)) {
                        Cache::forget($listKey);
                        $clearedCount++;
                    }

                    // 这里无法枚举所有可能的具体资源名称，
                    // 只能清除已知的缓存键
                }
            }

            if ($clearedCount > 0) {
                Log::info('Cleared K8s cache', [
                    'workspace_id' => $workspaceId,
                    'resource_type' => $resourceType,
                    'keys_count' => $clearedCount,
                    'cache_driver' => $cacheDriver,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to clear K8s cache', [
                'workspace_id' => $workspaceId,
                'resource_type' => $resourceType,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function notFound($message = 'Not found'): JsonResponse
    {
        return $this->error($message, 404);
    }

    public function error($message = '', $code = 400): JsonResponse
    {
        return $this->apiResponse(['message' => $message], $code);
    }

    public function apiResponse($data, $status = 200): JsonResponse
    {
        if (is_string($data)) {
            $data = ['message' => $data];
        }

        return response()->json($data, $status);
    }

    public function forbidden($message = 'Forbidden'): JsonResponse
    {
        return $this->error($message, 403);
    }

    public function unauthorized($message = 'Unauthorized'): JsonResponse
    {
        return $this->error($message, 401);
    }

    public function badRequest($message = 'Bad request'): JsonResponse
    {
        return $this->error($message);
    }

    public function created($message = 'Created'): JsonResponse
    {
        return $this->apiResponse($message, 201);
    }

    public function success($data = [], $status = 200): JsonResponse
    {
        return $this->apiResponse($data, $status);
    }

    public function failed($message = 'Failed'): JsonResponse
    {
        return $this->error($message);
    }

    public function accepted($message = 'Accepted'): JsonResponse
    {
        return $this->apiResponse([
            'message' => $message,
        ], 202);
    }

    public function noContent(): JsonResponse
    {
        return $this->apiResponse(null, 204);
    }

    public function updated(mixed $message = 'Updated'): JsonResponse
    {
        if ($message instanceof Model) {
            $message = $message->getChanges();
        }

        return $this->success($message);
    }

    public function deleted(): JsonResponse
    {
        return $this->noContent();
    }

    public function notAllowed($message = 'Not allowed'): JsonResponse
    {
        return $this->error($message, 405);
    }

    public function conflict($message = 'Conflict'): JsonResponse
    {
        return $this->error($message, 409);
    }

    public function tooManyRequests($message = 'Too many requests'): JsonResponse
    {
        return $this->error($message, 429);
    }

    public function serverError($message = 'Server error'): JsonResponse
    {
        return $this->error($message, 500);
    }

    public function serviceUnavailable($message = 'Service unavailable'): JsonResponse
    {
        return $this->error($message, 503);
    }

    public function methodNotAllowed($message = 'Method not allowed'): JsonResponse
    {
        return $this->error($message, 405);
    }

    public function notAcceptable($message = 'Not acceptable'): JsonResponse
    {
        return $this->error($message, 406);
    }

    public function preconditionFailed($message = 'Precondition failed'): JsonResponse
    {
        return $this->error($message, 412);
    }

    /**
     * 分页
     * 分页会自动调用 Model 或者 Collection 的 paginate 方法自动分页，并返回包含数据和分页信息的响应
     *
     * @param  mixed  $data
     * @param  int|null  $perPage
     */
    public function paginate($data, $perPage = null): JsonResponse
    {
        // 如果 perPage 为空，则读取 request 的 per_page 参数，如果没有，则默认为 10
        if ($perPage === null) {
            $perPage = request()->input('per_page', 10);
        }

        // parPage 最大 50
        $perPage = min(max($perPage, 1), 50); // 限制在 1-50 之间

        try {
            // 尝试调用分页方法
            $paginatedData = $data->paginate($perPage);
        } catch (\BadMethodCallException $e) {
            // 如果方法不存在，返回错误
            return $this->serverError('Unable to paginate resource: paginate method not available.');
        } catch (\Throwable $e) {
            // 处理其他可能的异常
            return $this->serverError('Unable to paginate resource: '.$e->getMessage());
        }

        // 构建分页信息
        $pagination = [
            'total' => $paginatedData->total(),
            'count' => $paginatedData->count(),
            'per_page' => $paginatedData->perPage(),
            'current_page' => $paginatedData->currentPage(),
            'total_pages' => $paginatedData->lastPage(),
        ];

        // 返回包含数据和分页信息的响应
        return $this->success([
            'data' => $paginatedData->getCollection(),
            'pagination' => $pagination,
        ]);
    }

    /**
     * DTO 转换
     */
    public function dto(KubernetesResourceDTO|array $data, $status = 200): JsonResponse
    {
        if ($data instanceof KubernetesResourceDTO) {
            return $this->success($data->toArray(), $status);
        }

        return $this->success(array_map(fn ($item) => $item->toArray(), $data), $status);
    }

    /**
     * 使用 Resource 返回成功响应
     */
    public function resource($resource, $status = 200): JsonResponse
    {
        return response()->json($resource, $status);
    }

    /**
     * 使用 Resource 返回创建成功响应
     */
    public function resourceCreated($resource): JsonResponse
    {
        return $this->resource($resource, 201);
    }

    /**
     * 获取当前认证用户
     */
    protected function user()
    {
        return auth()->user();
    }

    /**
     * 使用 Resource 进行分页响应
     */
    public function paginateResource($query, $resourceClass, $perPage = null): JsonResponse
    {
        // 如果 perPage 为空，则读取 request 的 per_page 参数，如果没有，则默认为 10
        if ($perPage === null) {
            $perPage = request()->input('per_page', 10);
        }

        // parPage 最大 50
        $perPage = min(max($perPage, 1), 50); // 限制在 1-50 之间

        try {
            // 尝试调用分页方法
            $paginatedData = $query->paginate($perPage);
        } catch (\BadMethodCallException $e) {
            // 如果方法不存在，返回错误
            return $this->serverError('Unable to paginate resource: paginate method not available.');
        } catch (\Throwable $e) {
            // 处理其他可能的异常
            return $this->serverError('Unable to paginate resource: '.$e->getMessage());
        }

        // 构建分页信息
        $pagination = [
            'total' => $paginatedData->total(),
            'count' => $paginatedData->count(),
            'per_page' => $paginatedData->perPage(),
            'current_page' => $paginatedData->currentPage(),
            'total_pages' => $paginatedData->lastPage(),
        ];

        // 将数据通过 Resource 类转换
        $transformedData = $paginatedData->getCollection()->map(function ($item) use ($resourceClass) {
            return new $resourceClass($item);
        });

        // 返回包含数据和分页信息的响应
        return $this->resource([
            'data' => $transformedData,
            'pagination' => $pagination,
        ]);
    }
}
