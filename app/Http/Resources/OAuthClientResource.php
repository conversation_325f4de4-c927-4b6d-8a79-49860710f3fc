<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OAuthClientResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'redirect' => $this->redirect,
            'revoked' => (bool) $this->revoked,
            'trusted' => (bool) $this->trusted,
            'icon_url' => $this->icon_url,
            'is_pkce_client' => $this->is_pkce_client,
            'supports_device_flow' => $this->supports_device_flow,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
