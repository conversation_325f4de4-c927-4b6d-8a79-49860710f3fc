<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AccessTokenResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'scopes' => $this->scopes,
            'revoked' => (bool) $this->revoked,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'last_used_at' => $this->last_used_at?->format('Y-m-d H:i:s'),
            'expires_at' => $this->expires_at?->format('Y-m-d H:i:s'),
            'client' => [
                'id' => $this->client->id,
                'name' => $this->client->name,
            ],
        ];
    }
}
