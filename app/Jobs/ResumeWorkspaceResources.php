<?php

namespace App\Jobs;

use App\ClusterLabel;
use App\Events\UserOverdueRecovered;
use App\Models\Workspace;
use App\Service\DeploymentService;
use App\Service\StatefulSetService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ResumeWorkspaceResources implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务重试次数
     */
    public int $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public int $timeout = 180;

    /**
     * 重试延迟时间（秒）
     */
    public int $backoff = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $workspaceId
    ) {}

    /**
     * Execute the job.
     */
    public function handle(DeploymentService $deploymentService, StatefulSetService $statefulSetService): void
    {
        try {
            $workspace = Workspace::with(['cluster', 'user'])->find($this->workspaceId);

            if (! $workspace) {
                Log::error('工作空间不存在，无法恢复资源', ['workspace_id' => $this->workspaceId]);

                return;
            }

            if ($workspace->status !== 'active') {
                Log::info('工作空间状态不是active，跳过资源恢复', [
                    'workspace_id' => $this->workspaceId,
                    'status' => $workspace->status,
                ]);

                return;
            }

            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;

            Log::info('开始恢复工作空间资源', [
                'workspace_id' => $workspace->id,
                'namespace' => $namespace,
                'cluster' => $cluster->name,
            ]);

            // 恢复所有 Deployment
            $this->resumeDeployments($cluster, $namespace, $workspace->name);

            // 恢复所有 StatefulSet
            $this->resumeStatefulSets($cluster, $namespace, $workspace->name);

            // 触发恢复事件
            UserOverdueRecovered::dispatch(
                $workspace->user,
                $workspace,
                '余额已充值，服务已恢复'
            );

            Log::info('工作空间资源恢复完成', [
                'workspace_id' => $workspace->id,
                'namespace' => $namespace,
            ]);

        } catch (\Exception $e) {
            Log::error('恢复工作空间资源失败', [
                'workspace_id' => $this->workspaceId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * 恢复所有 Deployment
     */
    protected function resumeDeployments($cluster, string $namespace, string $workspaceName): void
    {
        try {
            $response = $cluster->http()->get("/apis/apps/v1/namespaces/{$namespace}/deployments");
            $deployments = $response->json()['items'] ?? [];

            foreach ($deployments as $deployment) {
                $labels = $deployment['metadata']['labels'] ?? [];
                $annotations = $deployment['metadata']['annotations'] ?? [];

                // 只处理属于此工作空间且被暂停的资源
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') === $workspaceName &&
                    isset($annotations[ClusterLabel::SUSPENDED_REASON->value])) {

                    $deploymentName = $deployment['metadata']['name'];
                    $originalReplicas = (int) ($annotations[ClusterLabel::ORIGINAL_REPLICAS->value] ?? 1);

                    $patchData = [
                        'metadata' => [
                            'annotations' => [
                                ClusterLabel::ORIGINAL_REPLICAS->value => null,
                                ClusterLabel::SUSPENDED_REASON->value => null,
                                ClusterLabel::SUSPENDED_AT->value => null,
                                ClusterLabel::RESUMED_AT->value => now()->toISOString(),
                            ],
                        ],
                        'spec' => [
                            'replicas' => $originalReplicas,
                        ],
                    ];

                    $cluster->http()->replaceHeaders(['Content-Type' => 'application/merge-patch+json'])->patch(
                        "/apis/apps/v1/namespaces/{$namespace}/deployments/{$deploymentName}",
                        $patchData,
                    );

                    Log::info('Deployment已恢复', [
                        'deployment' => $deploymentName,
                        'namespace' => $namespace,
                        'restored_replicas' => $originalReplicas,
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('恢复Deployment失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 恢复所有 StatefulSet
     */
    protected function resumeStatefulSets($cluster, string $namespace, string $workspaceName): void
    {
        try {
            $response = $cluster->http()->get("/apis/apps/v1/namespaces/{$namespace}/statefulsets");
            $statefulSets = $response->json()['items'] ?? [];

            foreach ($statefulSets as $statefulSet) {
                $labels = $statefulSet['metadata']['labels'] ?? [];
                $annotations = $statefulSet['metadata']['annotations'] ?? [];

                // 只处理属于此工作空间且被暂停的资源
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') === $workspaceName &&
                    isset($annotations[ClusterLabel::SUSPENDED_REASON->value])) {

                    $statefulSetName = $statefulSet['metadata']['name'];
                    $originalReplicas = (int) ($annotations[ClusterLabel::ORIGINAL_REPLICAS->value] ?? 1);

                    $patchData = [
                        'metadata' => [
                            'annotations' => [
                                ClusterLabel::ORIGINAL_REPLICAS->value => null,
                                ClusterLabel::SUSPENDED_REASON->value => null,
                                ClusterLabel::SUSPENDED_AT->value => null,
                                ClusterLabel::RESUMED_AT->value => now()->toISOString(),
                            ],
                        ],
                        'spec' => [
                            'replicas' => $originalReplicas,
                        ],
                    ];

                    $cluster->http()->replaceHeaders(['Content-Type' => 'application/merge-patch+json'])->patch(
                        "/apis/apps/v1/namespaces/{$namespace}/statefulsets/{$statefulSetName}",
                        $patchData,
                    );

                    Log::info('StatefulSet已恢复', [
                        'statefulset' => $statefulSetName,
                        'namespace' => $namespace,
                        'restored_replicas' => $originalReplicas,
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('恢复StatefulSet失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('恢复工作空间资源任务最终失败', [
            'workspace_id' => $this->workspaceId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
