<?php

namespace App\Jobs;

use App\Models\Cluster;
use App\Service\Billing\OverdueManagementService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessClusterResourcesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 300;

    /**
     * 最大重试次数
     */
    public $tries = 3;

    /**
     * 创建新的任务实例
     */
    public function __construct(
        public Cluster $cluster,
        public array $resourceSnapshot,
        public $billingTime
    ) {}

    /**
     * 执行任务
     */
    public function handle(OverdueManagementService $overdueManagementService): void
    {
        try {
            Log::info('开始处理集群资源计费', [
                'cluster_id' => $this->cluster->id,
                'cluster_name' => $this->cluster->name,
                'pods_count' => count($this->resourceSnapshot['pods'] ?? []),
                'services_count' => count($this->resourceSnapshot['services'] ?? []),
                'pvcs_count' => count($this->resourceSnapshot['pvcs'] ?? []),
                'billing_time' => $this->billingTime,
            ]);

            // 第一步：检查并恢复欠费工作空间
            $this->handleOverdueRecovery($overdueManagementService);

            // 第二步：拆分资源并推送到个别计费任务
            $this->dispatchResourceBillingJobs();

            Log::info('集群资源计费任务处理完成', [
                'cluster_id' => $this->cluster->id,
            ]);

        } catch (\Exception $e) {
            Log::error('处理集群资源计费失败', [
                'cluster_id' => $this->cluster->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * 处理欠费恢复
     */
    protected function handleOverdueRecovery(OverdueManagementService $overdueManagementService): void
    {
        try {
            $results = $overdueManagementService->checkAndResumeOverdueWorkspaces();

            if (! empty($results)) {
                Log::info('欠费工作空间恢复检查完成', [
                    'cluster_id' => $this->cluster->id,
                    'recovery_results' => count($results),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('欠费恢复处理失败', [
                'cluster_id' => $this->cluster->id,
                'error' => $e->getMessage(),
            ]);
            // 不抛出异常，让计费继续进行
        }
    }

    /**
     * 拆分资源并推送到个别计费任务
     */
    protected function dispatchResourceBillingJobs(): void
    {
        $jobs = [];

        // 处理Pods
        foreach ($this->resourceSnapshot['pods'] ?? [] as $podData) {
            $workspaceName = $this->getWorkspaceNameFromResource($podData);
            if ($workspaceName) {
                $jobs[] = new ProcessResourceBillingJob(
                    $this->cluster,
                    'pod',
                    $podData,
                    $workspaceName,
                    $this->billingTime
                );
            }
        }

        // 处理Services
        foreach ($this->resourceSnapshot['services'] ?? [] as $serviceData) {
            $workspaceName = $this->getWorkspaceNameFromResource($serviceData);
            if ($workspaceName) {
                $jobs[] = new ProcessResourceBillingJob(
                    $this->cluster,
                    'service',
                    $serviceData,
                    $workspaceName,
                    $this->billingTime
                );
            }
        }

        // 处理PVCs
        foreach ($this->resourceSnapshot['pvcs'] ?? [] as $pvcData) {
            $workspaceName = $this->getWorkspaceNameFromResource($pvcData);
            if ($workspaceName) {
                $jobs[] = new ProcessResourceBillingJob(
                    $this->cluster,
                    'storage',
                    $pvcData,
                    $workspaceName,
                    $this->billingTime
                );
            }
        }

        // 分批推送任务到队列
        if (! empty($jobs)) {
            $chunks = array_chunk($jobs, 50); // 每批50个任务

            foreach ($chunks as $chunk) {
                foreach ($chunk as $job) {
                    dispatch($job);
                }
            }

            Log::info('资源计费任务已推送到队列', [
                'cluster_id' => $this->cluster->id,
                'total_jobs' => count($jobs),
                'batches' => count($chunks),
            ]);
        }
    }

    /**
     * 从资源中获取工作空间名称
     */
    protected function getWorkspaceNameFromResource(array $resourceData): ?string
    {
        $labels = $resourceData['metadata']['labels'] ?? [];

        return $labels[\App\ClusterLabel::WORKSPACE->value] ?? null;
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('集群资源处理任务最终失败', [
            'cluster_id' => $this->cluster->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
