<?php

namespace App\Jobs;

use App\ClusterLabel;
use App\Models\Cluster;
use App\Models\Workspace;
use App\Service\DeploymentService;
use App\Service\StatefulSetService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SuspendWorkspaceResources implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务重试次数
     */
    public int $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public int $timeout = 180;

    /**
     * 重试延迟时间（秒）
     */
    public int $backoff = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $workspaceId
    ) {}

    /**
     * Execute the job.
     */
    public function handle(DeploymentService $deploymentService, StatefulSetService $statefulSetService): void
    {
        try {
            $workspace = Workspace::with('cluster')->find($this->workspaceId);

            if (! $workspace) {
                Log::error('工作空间不存在，无法暂停资源', ['workspace_id' => $this->workspaceId]);

                return;
            }

            if ($workspace->status !== 'suspended') {
                Log::info('工作空间状态已变更，跳过资源暂停', [
                    'workspace_id' => $this->workspaceId,
                    'status' => $workspace->status,
                ]);

                return;
            }

            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;

            Log::info('开始暂停工作空间资源', [
                'workspace_id' => $workspace->id,
                'namespace' => $namespace,
                'cluster' => $cluster->name,
            ]);

            // 暂停所有 Deployment
            $this->suspendDeployments($cluster, $namespace, $workspace->name);

            // 暂停所有 StatefulSet
            $this->suspendStatefulSets($cluster, $namespace, $workspace->name);

            Log::info('工作空间资源暂停完成', [
                'workspace_id' => $workspace->id,
                'namespace' => $namespace,
            ]);

        } catch (\Exception $e) {
            Log::error('暂停工作空间资源失败', [
                'workspace_id' => $this->workspaceId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * 暂停所有 Deployment
     */
    protected function suspendDeployments(Cluster $cluster, string $namespace, string $workspaceName): void
    {
        try {
            $response = $cluster->http()->get("/apis/apps/v1/namespaces/{$namespace}/deployments");
            $deployments = $response->json()['items'] ?? [];

            foreach ($deployments as $deployment) {
                $labels = $deployment['metadata']['labels'] ?? [];

                // 只处理属于此工作空间的资源
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') === $workspaceName) {
                    $deploymentName = $deployment['metadata']['name'];
                    $currentReplicas = $deployment['spec']['replicas'] ?? 0;

                    // 如果当前副本数大于0，则暂停并保存原始副本数
                    if ($currentReplicas > 0) {
                        $patchData = [
                            'metadata' => [
                                'annotations' => [
                                    ClusterLabel::ORIGINAL_REPLICAS->value => (string) $currentReplicas,
                                    ClusterLabel::SUSPENDED_REASON->value => 'overdue',
                                    ClusterLabel::SUSPENDED_AT->value => now()->toISOString(),
                                ],
                            ],
                            'spec' => [
                                'replicas' => 0,
                            ],
                        ];

                        $cluster->http()->replaceHeaders(['Content-Type' => 'application/merge-patch+json'])->patch(
                            "/apis/apps/v1/namespaces/{$namespace}/deployments/{$deploymentName}",
                            $patchData,
                        );

                        Log::info('Deployment已暂停', [
                            'deployment' => $deploymentName,
                            'namespace' => $namespace,
                            'original_replicas' => $currentReplicas,
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('暂停Deployment失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 暂停所有 StatefulSet
     */
    protected function suspendStatefulSets($cluster, string $namespace, string $workspaceName): void
    {
        try {
            $response = $cluster->http()->get("/apis/apps/v1/namespaces/{$namespace}/statefulsets");
            $statefulSets = $response->json()['items'] ?? [];

            foreach ($statefulSets as $statefulSet) {
                $labels = $statefulSet['metadata']['labels'] ?? [];

                // 只处理属于此工作空间的资源
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') === $workspaceName) {
                    $statefulSetName = $statefulSet['metadata']['name'];
                    $currentReplicas = $statefulSet['spec']['replicas'] ?? 0;

                    // 如果当前副本数大于0，则暂停并保存原始副本数
                    if ($currentReplicas > 0) {
                        $patchData = [
                            'metadata' => [
                                'annotations' => [
                                    ClusterLabel::ORIGINAL_REPLICAS->value => (string) $currentReplicas,
                                    ClusterLabel::SUSPENDED_REASON->value => 'overdue',
                                    ClusterLabel::SUSPENDED_AT->value => now()->toISOString(),
                                ],
                            ],
                            'spec' => [
                                'replicas' => 0,
                            ],
                        ];

                        $cluster->http()->replaceHeaders(['Content-Type' => 'application/merge-patch+json'])->patch(
                            "/apis/apps/v1/namespaces/{$namespace}/statefulsets/{$statefulSetName}",
                            $patchData,
                        );

                        Log::info('StatefulSet已暂停', [
                            'statefulset' => $statefulSetName,
                            'namespace' => $namespace,
                            'original_replicas' => $currentReplicas,
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('暂停StatefulSet失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('暂停工作空间资源任务最终失败', [
            'workspace_id' => $this->workspaceId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
