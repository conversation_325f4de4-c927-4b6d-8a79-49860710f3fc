<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ImagePullSecretRule implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // 检查是否为null
        if ($value === null) {
            $fail('镜像拉取密钥名称不能为空');

            return;
        }

        // 检查是否为字符串
        if (! is_string($value)) {
            $fail('镜像拉取密钥名称必须是字符串');

            return;
        }

        // 检查是否为空字符串或长度小于1
        if ($value === '' || strlen($value) < 1) {
            $fail('镜像拉取密钥名称不能为空');

            return;
        }

        // 先检查长度限制（Secret名称最大253个字符，比一般的Kubernetes资源名称更长）
        if (strlen($value) > 253) {
            $fail('镜像拉取密钥名称长度不能超过253个字符');

            return;
        }

        // 检查Kubernetes命名规则 - 直接使用regex而不是KubernetesNameRule来避免63字符的限制
        if (! preg_match('/^[a-z]([-a-z0-9]*[a-z0-9])?$/', $value)) {
            $fail('镜像拉取密钥名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符和小写字母数字');

            return;
        }
    }
}
