<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class NoNumbersOrSymbols implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_string($value) || ! preg_match('/^[a-zA-Z][a-zA-Z0-9-]*[a-zA-Z0-9]$/', $value)) {
            $fail('必须以字母开头，只能包含字母、数字和连字符，且连字符不能在开头或结尾');
        }
    }
}
