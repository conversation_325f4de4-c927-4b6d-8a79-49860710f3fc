<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CustomLabelRule implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_array($value)) {
            $fail('标签必须是数组格式');

            return;
        }

        $maxLabels = config('k8s.max_labels', 16);

        // 最多 16 个标签
        if (count($value) > $maxLabels) {
            $fail('标签数量不能超过 '.$maxLabels.' 个');

            return;
        }

        $allowedPrefixes = config('k8s.allow_label_prefix', []);

        if (empty($allowedPrefixes)) {
            $fail('系统未配置允许的标签前缀');

            return;
        }

        foreach ($value as $key => $val) {
            if (! is_string($key)) {
                $fail('标签键必须是字符串');

                return;
            }

            if (! is_string($val)) {
                $fail('标签值必须是字符串');

                return;
            }

            // 检查标签键是否以允许的前缀开头
            $hasValidPrefix = false;
            foreach ($allowedPrefixes as $prefix) {
                if (str_starts_with($key, $prefix)) {
                    $hasValidPrefix = true;
                    break;
                }
            }

            if (! $hasValidPrefix) {
                $fail("标签键 '{$key}' 必须以允许的前缀开头：".implode(', ', $allowedPrefixes));

                return;
            }

            // 验证标签键格式（符合 Kubernetes 标签规范）
            if (! preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$/', str_replace($allowedPrefixes, '', $key))) {
                $fail("标签键 '{$key}' 格式不正确，必须符合 Kubernetes 标签规范");

                return;
            }

            // 验证标签值格式
            if (! preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$/', $val) && $val !== '') {
                $fail("标签值 '{$val}' 格式不正确，必须符合 Kubernetes 标签规范");

                return;
            }

            // 检查长度限制
            if (strlen($key) > 63) {
                $fail("标签键 '{$key}' 长度不能超过 63 个字符");

                return;
            }

            if (strlen($val) > 63) {
                $fail("标签值 '{$val}' 长度不能超过 63 个字符");

                return;
            }
        }
    }
}
