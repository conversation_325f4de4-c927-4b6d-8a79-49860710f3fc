<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class KubernetesNameRule implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // 检查是否为null
        if ($value === null) {
            $fail('名称不能为空');

            return;
        }

        // 检查是否为字符串
        if (! is_string($value)) {
            $fail('名称必须是字符串');

            return;
        }

        // 检查是否为空字符串或长度小于1
        if ($value === '' || strlen($value) < 1) {
            $fail('名称不能为空');

            return;
        }

        // 检查长度限制（Kubernetes资源名称通常最大63个字符）
        if (strlen($value) > 63) {
            $fail('名称长度不能超过63个字符');

            return;
        }

        // 检查Kubernetes DNS-1123 subdomain命名规则
        // 必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符、小写字母和数字
        if (! preg_match('/^[a-z]([-a-z0-9]*[a-z0-9])?$/', $value)) {
            $fail('名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符和小写字母数字');

            return;
        }
    }
}
