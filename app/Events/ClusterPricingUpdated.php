<?php

namespace App\Events;

use App\Models\Cluster;
use App\Models\ResourcePricing;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ClusterPricingUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Cluster $cluster,
        public ResourcePricing $pricing,
        public array $changes = []
    ) {}

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): Channel
    {
        return new Channel('cluster-pricing');
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        $pricingSummary = $this->cluster->getPricingSummary();

        return [
            'cluster_id' => $this->cluster->id,
            'cluster_name' => $this->cluster->name,
            'pricing' => $pricingSummary,
            'updated_unit' => $this->pricing->unit_type,
            'changes' => $this->changes,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'pricing.updated';
    }
}
