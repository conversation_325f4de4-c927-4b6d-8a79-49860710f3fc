<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ResourceChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $namespace;

    public string $clusterName;

    public int $clusterId;

    public string $resourceType;

    public array $changes;

    public array $summary;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceType,
        array $changes
    ) {
        $this->namespace = $namespace;
        $this->clusterName = $clusterName;
        $this->clusterId = $clusterId;
        $this->resourceType = $resourceType;
        $this->changes = $changes;
        $this->summary = $this->generateSummary($changes);
    }

    /**
     * Generate a summary of the changes.
     */
    private function generateSummary(array $changes): array
    {
        return [
            'created_count' => count($changes['created'] ?? []),
            'updated_count' => count($changes['updated'] ?? []),
            'deleted_count' => count($changes['deleted'] ?? []),
            'total_changes' => count($changes['created'] ?? []) +
                             count($changes['updated'] ?? []) +
                             count($changes['deleted'] ?? []),
        ];
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("workspace.{$this->namespace}.resources"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'namespace' => $this->namespace,
            'cluster' => [
                'id' => $this->clusterId,
                'name' => $this->clusterName,
            ],
            'resource_type' => $this->resourceType,
            'changes' => $this->changes,
            'summary' => $this->summary,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'resource.changed';
    }
}
