<?php

namespace App\Service;

use App\Contracts\PaymentGatewayInterface;
use App\Exceptions\Payment\PaymentAmountException;
use App\Exceptions\Payment\PaymentGatewayNotFoundException;
use App\Exceptions\Payment\PaymentLimitExceededException;
use App\Models\TopUpRecord;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class PaymentManagerService
{
    private array $gateways = [];

    public function __construct()
    {
        $this->loadGateways();
    }

    /**
     * 加载支付网关
     */
    private function loadGateways(): void
    {
        $gatewayConfigs = config('payments.gateways', []);

        foreach ($gatewayConfigs as $identifier => $config) {
            if (! isset($config['class']) || ! $config['enabled']) {
                continue;
            }

            $gatewayClass = $config['class'];

            if (class_exists($gatewayClass)) {
                $this->gateways[$identifier] = new $gatewayClass;
            }
        }
    }

    /**
     * 获取支付网关
     */
    public function getGateway(string $identifier): ?PaymentGatewayInterface
    {
        return $this->gateways[$identifier] ?? null;
    }

    /**
     * 获取所有支付网关
     */
    public function getAllGateways(): array
    {
        return $this->gateways;
    }

    /**
     * 获取启用的支付网关
     */
    public function getEnabledGateways(): Collection
    {
        return collect($this->gateways);
    }

    /**
     * 创建支付订单
     */
    public function createPayment(string $gatewayIdentifier, User $user, float $amount, array $extra = []): array
    {
        $gateway = $this->getGateway($gatewayIdentifier);

        if (! $gateway) {
            throw new PaymentGatewayNotFoundException($gatewayIdentifier);
        }

        // 验证支付金额
        $this->validatePaymentAmount($amount);

        // 验证用户支付限额
        $this->validateUserPaymentLimits($user, $amount);

        Log::info('创建支付订单', [
            'gateway' => $gatewayIdentifier,
            'user_id' => $user->id,
            'amount' => $amount,
        ]);

        return $gateway->createPayment($user, $amount, $extra);
    }

    /**
     * 验证支付回调
     */
    public function verifyCallback(string $gatewayIdentifier, array $callbackData): array
    {
        $gateway = $this->getGateway($gatewayIdentifier);

        if (! $gateway) {
            throw new PaymentGatewayNotFoundException($gatewayIdentifier);
        }

        return $gateway->verifyCallback($callbackData);
    }

    /**
     * 处理支付成功
     */
    public function handlePaymentSuccess(string $gatewayIdentifier, TopUpRecord $record, array $paymentData = []): bool
    {
        $gateway = $this->getGateway($gatewayIdentifier);

        if (! $gateway) {
            Log::error('支付成功处理失败：网关不存在', [
                'gateway' => $gatewayIdentifier,
                'record_id' => $record->id,
            ]);

            return false;
        }

        return $gateway->handleSuccess($record, $paymentData);
    }

    /**
     * 处理支付失败
     */
    public function handlePaymentFailure(string $gatewayIdentifier, TopUpRecord $record, array $paymentData = []): bool
    {
        $gateway = $this->getGateway($gatewayIdentifier);

        if (! $gateway) {
            Log::error('支付失败处理失败：网关不存在', [
                'gateway' => $gatewayIdentifier,
                'record_id' => $record->id,
            ]);

            return false;
        }

        return $gateway->handleFailure($record, $paymentData);
    }

    /**
     * 执行退款
     */
    public function refund(string $gatewayIdentifier, TopUpRecord $record, ?float $amount = null, string $reason = ''): array
    {
        $gateway = $this->getGateway($gatewayIdentifier);

        if (! $gateway) {
            return [
                'success' => false,
                'refund_id' => null,
                'message' => "支付网关 {$gatewayIdentifier} 不存在",
            ];
        }

        return $gateway->refund($record, $amount, $reason);
    }

    /**
     * 获取支付状态
     */
    public function getPaymentStatus(string $gatewayIdentifier, string $transactionId): array
    {
        $gateway = $this->getGateway($gatewayIdentifier);

        if (! $gateway) {
            return [
                'status' => 'error',
                'amount' => 0,
                'paid_at' => null,
                'message' => "支付网关 {$gatewayIdentifier} 不存在",
            ];
        }

        return $gateway->getPaymentStatus($transactionId);
    }

    /**
     * 获取支付网关列表（用于前端显示）
     */
    public function getGatewayList(): array
    {
        $gatewayConfigs = config('payments.gateways', []);
        $result = [];

        foreach ($this->gateways as $identifier => $gateway) {
            $config = $gatewayConfigs[$identifier] ?? [];
            $result[] = [
                'identifier' => $identifier,
                'name' => $config['name'] ?? $gateway->getName(),
                'description' => $config['description'] ?? $gateway->getDescription(),
                'enabled' => $config['enabled'] ?? false,
                'supports_refund' => ! in_array($identifier, ['redeem_code']),
                'requires_callback' => ! in_array($identifier, ['manual', 'redeem_code']),
            ];
        }

        return $result;
    }

    /**
     * 验证支付金额
     */
    private function validatePaymentAmount(float $amount): void
    {
        $minAmount = config('payments.limits.min_amount', 1);
        $maxAmount = config('payments.limits.max_amount', 10000);

        if ($amount < $minAmount) {
            throw new PaymentAmountException("支付金额不能少于 ¥{$minAmount}");
        }

        if ($amount > $maxAmount) {
            throw new PaymentAmountException("支付金额不能超过 ¥{$maxAmount}");
        }
    }

    /**
     * 验证用户支付限额
     */
    private function validateUserPaymentLimits(User $user, float $amount): void
    {
        $dailyLimit = config('payments.limits.daily_limit', 50000);
        $monthlyLimit = config('payments.limits.monthly_limit', 100000);

        // 检查每日限额
        $todayAmount = $user->topUpRecords()
            ->where('status', TopUpRecord::STATUS_COMPLETED)
            ->whereDate('completed_at', today())
            ->sum('amount');

        if ($todayAmount + $amount > $dailyLimit) {
            throw new PaymentLimitExceededException("超出每日支付限额 ¥{$dailyLimit}");
        }

        // 检查每月限额
        $monthAmount = $user->topUpRecords()
            ->where('status', TopUpRecord::STATUS_COMPLETED)
            ->whereYear('completed_at', now()->year)
            ->whereMonth('completed_at', now()->month)
            ->sum('amount');

        if ($monthAmount + $amount > $monthlyLimit) {
            throw new PaymentLimitExceededException("超出每月支付限额 ¥{$monthlyLimit}");
        }
    }
}
