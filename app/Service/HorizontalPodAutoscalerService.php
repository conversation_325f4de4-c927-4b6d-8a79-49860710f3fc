<?php

namespace App\Service;

use App\ClusterLabel;
use App\DTOs\HorizontalPodAutoscalerDTO;
use App\Exceptions\HorizontalPodAutoscaler\HorizontalPodAutoscalerNotFoundException;
use App\Exceptions\K8s\K8sConnectionException;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class HorizontalPodAutoscalerService
{
    public function __construct(
        protected Workspace $workspace
    ) {}

    /**
     * 获取 HPA 列表
     */
    public function getHPAs(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/autoscaling/v2/namespaces/{$this->workspace->namespace}/horizontalpodautoscalers");

            if (! $response->successful()) {
                throw new \Exception('Failed to get HPAs: '.$response->body());
            }

            $items = $response->json('items', []);

            return array_map(function ($item) {
                return HorizontalPodAutoscalerDTO::fromK8sResource($item);
            }, $items);
        } catch (\Exception $e) {
            Log::error('获取 HPA 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 HPA 列表失败：'.$e->getMessage());
        }
    }

    /**
     * 获取单个 HPA
     */
    public function getHPA(string $name): HorizontalPodAutoscalerDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/autoscaling/v2/namespaces/{$this->workspace->namespace}/horizontalpodautoscalers/{$name}");

            if (! $response->successful()) {
                throw new \Exception('Failed to get HPA: '.$response->body());
            }

            return HorizontalPodAutoscalerDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('获取 HPA 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'hpa_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 HPA 失败：'.$e->getMessage());
        }
    }

    /**
     * 创建 HPA
     */
    public function createHPA(array $data): HorizontalPodAutoscalerDTO
    {
        $payload = $this->buildHPAPayload($data);

        try {
            $response = $this->workspace->cluster->http()
                ->post("/apis/autoscaling/v2/namespaces/{$this->workspace->namespace}/horizontalpodautoscalers", $payload);

            if (! $response->successful()) {
                throw new \Exception('Failed to create HPA: '.$response->body());
            }

            Log::info('HPA 创建成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'hpa_name' => $data['name'],
            ]);

            return HorizontalPodAutoscalerDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('创建 HPA 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'hpa_name' => $data['name'],
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('创建 HPA 失败：'.$e->getMessage());
        }
    }

    /**
     * 更新 HPA
     */
    public function updateHPA(string $name, array $data): HorizontalPodAutoscalerDTO
    {
        try {
            // 先获取现有的 HPA
            $existing = $this->workspace->cluster->http()
                ->get("/apis/autoscaling/v2/namespaces/{$this->workspace->namespace}/horizontalpodautoscalers/{$name}")
                ->json();

            $payload = $this->buildHPAPayload($data, $existing);

            $response = $this->workspace->cluster->http()
                ->put("/apis/autoscaling/v2/namespaces/{$this->workspace->namespace}/horizontalpodautoscalers/{$name}", $payload);

            if (! $response->successful()) {
                throw new \Exception('Failed to update HPA: '.$response->body());
            }

            Log::info('HPA 更新成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'hpa_name' => $name,
            ]);

            return HorizontalPodAutoscalerDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('更新 HPA 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'hpa_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('更新 HPA 失败：'.$e->getMessage());
        }
    }

    /**
     * 删除 HPA
     */
    public function deleteHPA(string $name): bool
    {
        try {
            $response = $this->workspace->cluster->http()
                ->delete("/apis/autoscaling/v2/namespaces/{$this->workspace->namespace}/horizontalpodautoscalers/{$name}");

            if (! $response->successful()) {
                if ($response->status() === 404) {
                    Log::warning('HPA 不存在', [
                        'workspace_id' => $this->workspace->id,
                        'namespace' => $this->workspace->namespace,
                        'hpa_name' => $name,
                    ]);

                    throw new HorizontalPodAutoscalerNotFoundException($name, $this->workspace->namespace);
                }

                throw new \Exception('Failed to delete HPA: '.$response->body());
            }

            Log::info('HPA 删除成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'hpa_name' => $name,
            ]);

            return true;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('HPA 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'hpa_name' => $name,
                ]);

                throw new HorizontalPodAutoscalerNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('删除 HPA 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'hpa_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法删除 HPA', $e);
        } catch (\Exception $e) {
            Log::error('删除 HPA 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'hpa_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('删除 HPA 失败：'.$e->getMessage());
        }
    }

    /**
     * 构建 HPA payload
     */
    protected function buildHPAPayload(array $data, ?array $existing = null): array
    {
        // 根据工作负载类型自动确定 API 版本
        $apiVersion = match ($data['target_type']) {
            'Deployment', 'StatefulSet', 'ReplicaSet' => 'apps/v1',
            default => 'apps/v1'
        };

        $payload = [
            'apiVersion' => 'autoscaling/v2',
            'kind' => 'HorizontalPodAutoscaler',
            'metadata' => [
                'name' => $data['name'],
                'namespace' => $this->workspace->namespace,
                'labels' => array_merge(
                    $this->workspace->buildDefaultLabels(null, $data['name']),
                    [
                        ClusterLabel::WORKLOAD_TYPE->value => strtolower($data['target_type']),
                        ClusterLabel::WORKLOAD_NAME->value => $data['target_name'],
                    ],
                    $data['labels'] ?? []
                ),
            ],
            'spec' => [
                'scaleTargetRef' => [
                    'apiVersion' => $apiVersion,
                    'kind' => $data['target_type'],
                    'name' => $data['target_name'],
                ],
                'minReplicas' => $data['min_replicas'],
                'maxReplicas' => $data['max_replicas'],
                'metrics' => $this->buildMetrics($data['metrics'] ?? []),
            ],
        ];

        // 如果有行为配置，添加到 spec 中
        if (! empty($data['behavior'])) {
            $payload['spec']['behavior'] = $this->buildBehavior($data['behavior']);
        }

        // 如果是更新操作，保留 metadata
        if ($existing) {
            $payload['metadata'] = array_merge($existing['metadata'], $payload['metadata']);
        }

        return $payload;
    }

    /**
     * 构建度量指标
     */
    protected function buildMetrics(array $metrics): array
    {
        $result = [];

        foreach ($metrics as $metric) {
            $metricSpec = [
                'type' => $metric['type'],
            ];

            switch ($metric['type']) {
                case 'Resource':
                    $metricSpec['resource'] = [
                        'name' => $metric['resource_name'],
                        'target' => [
                            'type' => $metric['target_type'],
                        ],
                    ];

                    if ($metric['target_type'] === 'Utilization') {
                        $metricSpec['resource']['target']['averageUtilization'] = (int) $metric['target_value'];
                    } elseif ($metric['target_type'] === 'AverageValue') {
                        // 前端只传数字，后端自动添加单位
                        $value = (int) $metric['target_value'];
                        if ($metric['resource_name'] === 'memory') {
                            $metricSpec['resource']['target']['averageValue'] = $value.'Mi';
                        } elseif ($metric['resource_name'] === 'cpu') {
                            $metricSpec['resource']['target']['averageValue'] = $value.'m';
                        } else {
                            $metricSpec['resource']['target']['averageValue'] = $metric['target_value'];
                        }
                    }
                    break;

                case 'Pods':
                    $metricSpec['pods'] = [
                        'metric' => [
                            'name' => $metric['metric_name'],
                        ],
                        'target' => [
                            'type' => 'AverageValue',
                            'averageValue' => $metric['target_value'],
                        ],
                    ];

                    if (! empty($metric['selector'])) {
                        $metricSpec['pods']['metric']['selector'] = $metric['selector'];
                    }
                    break;

                case 'Object':
                    $metricSpec['object'] = [
                        'metric' => [
                            'name' => $metric['metric_name'],
                        ],
                        'describedObject' => [
                            'apiVersion' => $metric['object_api_version'],
                            'kind' => $metric['object_kind'],
                            'name' => $metric['object_name'],
                        ],
                        'target' => [
                            'type' => 'Value',
                            'value' => $metric['target_value'],
                        ],
                    ];

                    if (! empty($metric['selector'])) {
                        $metricSpec['object']['metric']['selector'] = $metric['selector'];
                    }
                    break;

                case 'External':
                    $metricSpec['external'] = [
                        'metric' => [
                            'name' => $metric['metric_name'],
                        ],
                        'target' => [
                            'type' => $metric['target_type'] ?? 'AverageValue',
                            $metric['target_type'] === 'Value' ? 'value' : 'averageValue' => $metric['target_value'],
                        ],
                    ];

                    if (! empty($metric['selector'])) {
                        $metricSpec['external']['metric']['selector'] = $metric['selector'];
                    }
                    break;
            }

            $result[] = $metricSpec;
        }

        return $result;
    }

    /**
     * 构建行为配置
     */
    protected function buildBehavior(array $behavior): array
    {
        $result = [];

        if (! empty($behavior['scale_up'])) {
            $result['scaleUp'] = $this->buildScalingPolicy($behavior['scale_up']);
        }

        if (! empty($behavior['scale_down'])) {
            $result['scaleDown'] = $this->buildScalingPolicy($behavior['scale_down']);
        }

        return $result;
    }

    /**
     * 构建扩缩容策略
     */
    protected function buildScalingPolicy(array $policy): array
    {
        $result = [];

        if (isset($policy['stabilization_window_seconds'])) {
            $result['stabilizationWindowSeconds'] = (int) $policy['stabilization_window_seconds'];
        }

        if (isset($policy['select_policy'])) {
            $result['selectPolicy'] = $policy['select_policy'];
        }

        if (! empty($policy['policies'])) {
            $result['policies'] = [];
            foreach ($policy['policies'] as $p) {
                $result['policies'][] = [
                    'type' => $p['type'],
                    'value' => (int) $p['value'],
                    'periodSeconds' => (int) $p['period_seconds'],
                ];
            }
        }

        return $result;
    }

    /**
     * 获取可扩缩的工作负载
     */
    public function getScalableWorkloads(): array
    {
        $workloads = [];

        try {
            // 获取 Deployments
            $deploymentService = new DeploymentService($this->workspace);
            $deployments = $deploymentService->getDeployments();
            foreach ($deployments as $deployment) {
                $workloads[] = [
                    'kind' => 'Deployment',
                    'name' => $deployment->name,
                    'api_version' => 'apps/v1',
                    'current_replicas' => $deployment->replicas,
                ];
            }

            // 获取 StatefulSets
            $statefulSetService = new StatefulSetService($this->workspace);
            $statefulSets = $statefulSetService->getStatefulSets();
            foreach ($statefulSets as $statefulSet) {
                $workloads[] = [
                    'kind' => 'StatefulSet',
                    'name' => $statefulSet->name,
                    'api_version' => 'apps/v1',
                    'current_replicas' => $statefulSet->replicas,
                ];
            }
        } catch (\Exception $e) {
            Log::error('获取可扩缩工作负载失败', [
                'workspace_id' => $this->workspace->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $workloads;
    }

    /**
     * 验证目标工作负载是否存在
     */
    public function validateTargetWorkload(string $type, string $name): bool
    {
        try {
            $endpoint = match ($type) {
                'Deployment' => "/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments/{$name}",
                'StatefulSet' => "/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$name}",
                'ReplicaSet' => "/apis/apps/v1/namespaces/{$this->workspace->namespace}/replicasets/{$name}",
                default => null,
            };

            if (! $endpoint) {
                return false;
            }

            $response = $this->workspace->cluster->http()->get($endpoint);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('验证目标工作负载失败', [
                'workspace_id' => $this->workspace->id,
                'type' => $type,
                'name' => $name,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
