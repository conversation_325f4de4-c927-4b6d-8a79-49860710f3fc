<?php

namespace App\Service;

use App\DTOs\EventDTO;
use App\DTOs\NamespaceEventsDTO;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class EventService
{
    public function __construct(
        protected Workspace $workspace
    ) {}

    /**
     * 获取命名空间下的所有事件
     *
     * @param  int|null  $sinceMinutes  获取指定分钟数内的事件，null 表示获取所有事件
     */
    public function getNamespaceEvents(?int $sinceMinutes = null): NamespaceEventsDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/events");

            $items = $response->json('items', []);
            $allEvents = NamespaceEventsDTO::fromK8sResource($items);

            // 如果指定了时间范围，进行时间过滤
            if ($sinceMinutes !== null) {
                $filteredEvents = $allEvents->getRecentEvents($sinceMinutes);

                return new NamespaceEventsDTO($filteredEvents);
            }

            return $allEvents;
        } catch (\Exception $e) {
            Log::error('获取命名空间事件失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'since_minutes' => $sinceMinutes,
                'error' => $e->getMessage(),
            ]);

            return new NamespaceEventsDTO;
        }
    }

    /**
     * 获取特定资源的事件
     *
     * @param  string  $kind  资源类型
     * @param  string  $name  资源名称
     * @param  int|null  $sinceMinutes  获取指定分钟数内的事件，null 表示获取所有事件
     */
    public function getResourceEvents(string $kind, string $name, ?int $sinceMinutes = null): array
    {
        try {
            $fieldSelectors = ["involvedObject.name={$name}", "involvedObject.kind={$kind}"];

            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/events", [
                    'fieldSelector' => implode(',', $fieldSelectors),
                ]);

            $items = $response->json('items', []);
            $events = [];

            foreach ($items as $item) {
                $event = EventDTO::fromK8sResource($item);

                // 如果指定了时间范围，进行时间过滤
                if ($sinceMinutes !== null && ! $event->isRecent($sinceMinutes)) {
                    continue;
                }

                $events[] = $event;
            }

            return array_map(function (EventDTO $event) {
                return $event->toArray();
            }, $events);
        } catch (\Exception $e) {
            Log::error('获取资源事件失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'kind' => $kind,
                'name' => $name,
                'since_minutes' => $sinceMinutes,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * 获取最近的事件（默认30分钟内）
     */
    public function getRecentEvents(int $minutes = 30): NamespaceEventsDTO
    {
        return $this->getNamespaceEvents($minutes);
    }

    /**
     * 获取特定资源的最近事件（默认30分钟内）
     */
    public function getRecentResourceEvents(string $kind, string $name, int $minutes = 30): array
    {
        return $this->getResourceEvents($kind, $name, $minutes);
    }
}
