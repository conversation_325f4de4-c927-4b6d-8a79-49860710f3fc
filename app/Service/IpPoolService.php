<?php

namespace App\Service;

use App\Exceptions\Network\PortAllocationException;
use App\Models\DisabledIp;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\PortAllocation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IpPoolService
{
    protected LoadBalancerManager $loadBalancerManager;

    public function __construct()
    {
        $this->loadBalancerManager = new LoadBalancerManager;
    }

    /**
     * 根据策略分配 IP
     */
    public function allocateIp(IpPool $ipPool, bool $allowSharedIp = true): ?PoolIp
    {
        // 优先使用IP池的共享策略设置
        $actualAllowSharedIp = $ipPool->isShared() && $allowSharedIp;

        // 获取所有全局禁用的IP
        $disabledIps = DisabledIp::getDisabledIps();

        if ($actualAllowSharedIp) {
            // IP 共享逻辑：尝试寻找一个可重用的 IP（已使用，但有空闲端口，且未被禁用）
            $reusableIp = $ipPool->activePoolIps()
                ->where('usage_count', '>', 0)
                ->whereNotIn('ip_address', $disabledIps)
                ->orderBy('usage_count', 'desc')
                ->get()
                ->first(function (PoolIp $ip) {
                    return $ip->available_ports_count > 0;
                });

            if ($reusableIp) {
                return $reusableIp;
            }

            // 如果没有可重用的 IP，尝试从现有的未使用IP中分配
            $unusedIp = $ipPool->activePoolIps()
                ->where('usage_count', 0)
                ->whereNotIn('ip_address', $disabledIps)
                ->orderBy('id', 'asc')
                ->first();

            if ($unusedIp) {
                return $unusedIp;
            }

            // 如果没有现有的IP可用，动态生成新的IP
            return $this->generateNewPoolIp($ipPool, true);
        } else {
            // 独享模式：只选择一个从未使用过的 IP，且未被禁用
            $unusedIp = $ipPool->activePoolIps()
                ->where('usage_count', 0)
                ->whereNotIn('ip_address', $disabledIps)
                ->orderBy('id', 'asc')
                ->first();

            if ($unusedIp) {
                return $unusedIp;
            }

            // 如果没有现有的IP可用，动态生成新的IP
            return $this->generateNewPoolIp($ipPool, false);
        }
    }

    /**
     * 自动分配 IP 池和 IP（顺序选择）
     */
    public function autoAllocateIp(int $clusterId, bool $allowSharedIp = true): ?PoolIp
    {
        // 获取集群的活跃 IP 池，按 ID 顺序排序
        $ipPools = IpPool::where('cluster_id', $clusterId)
            ->where('is_active', true)
            ->orderBy('id', 'asc')
            ->get();

        if ($ipPools->isEmpty()) {
            throw new PortAllocationException('集群中没有可用的 IP 池');
        }

        // 顺序尝试每个 IP 池
        foreach ($ipPools as $ipPool) {
            $poolIp = $this->allocateIp($ipPool, $allowSharedIp);
            if ($poolIp) {
                return $poolIp;
            }
        }

        $message = $allowSharedIp
            ? '集群中没有可用的共享 IP 地址'
            : '集群中没有可用的独占 IP 地址';
        throw new PortAllocationException($message);
    }

    /**
     * 从指定的IP池分配IP
     */
    public function allocateFromSpecificPool(int $ipPoolId, bool $allowSharedIp = true): ?PoolIp
    {
        $ipPool = IpPool::where('id', $ipPoolId)
            ->where('is_active', true)
            ->first();

        if (! $ipPool) {
            throw new PortAllocationException('指定的 IP 池不存在或不可用');
        }

        $poolIp = $this->allocateIp($ipPool, $allowSharedIp);

        if (! $poolIp) {
            $strategyLabel = $ipPool->isShared() ? '共享' : '独享';
            throw new PortAllocationException("IP 池 '{$ipPool->name}' ({$strategyLabel}模式) 中没有可用的 IP 地址");
        }

        return $poolIp;
    }

    /**
     * 根据 IP 家族自动分配 IP 池和 IP
     */
    public function autoAllocateIpByFamily(int $clusterId, string $ipFamily, bool $allowSharedIp = true): ?PoolIp
    {
        // 根据 IP 家族过滤 IP 池
        $ipPools = IpPool::where('cluster_id', $clusterId)
            ->where('is_active', true)
            ->where(function ($query) use ($ipFamily) {
                if ($ipFamily === 'ipv4') {
                    $query->where('ip_version', IpPool::IP_VERSION_IPV4)
                        ->orWhere('ip_version', IpPool::IP_VERSION_DUAL);
                } elseif ($ipFamily === 'ipv6') {
                    $query->where('ip_version', IpPool::IP_VERSION_IPV6)
                        ->orWhere('ip_version', IpPool::IP_VERSION_DUAL);
                }
            })
            ->orderBy('id', 'asc')
            ->get();

        if ($ipPools->isEmpty()) {
            throw new PortAllocationException("集群中没有可用的 {$ipFamily} IP 池");
        }

        // 顺序尝试每个 IP 池
        foreach ($ipPools as $ipPool) {
            $poolIp = $this->allocateIpByFamily($ipPool, $ipFamily, $allowSharedIp);
            if ($poolIp) {
                return $poolIp;
            }
        }

        $message = $allowSharedIp
            ? "集群中没有可用的 {$ipFamily} 共享 IP 地址"
            : "集群中没有可用的 {$ipFamily} 独占 IP 地址";
        throw new PortAllocationException($message);
    }

    /**
     * 根据 IP 家族分配 IP
     */
    public function allocateIpByFamily(IpPool $ipPool, string $ipFamily, bool $allowSharedIp = true): ?PoolIp
    {
        $disabledIps = DisabledIp::getDisabledIps();
        // 获取指定 IP 家族的 IP 地址，排除已禁用的
        $poolIps = $ipPool->activePoolIps()
            ->whereNotIn('ip_address', $disabledIps)
            ->get()
            ->filter(function (PoolIp $poolIp) use ($ipFamily) {
                $ipType = $poolIp->getIpType();

                return ($ipFamily === 'ipv4' && $ipType === 'IPv4') ||
                       ($ipFamily === 'ipv6' && $ipType === 'IPv6');
            });

        if ($poolIps->isEmpty()) {
            return null;
        }

        if ($allowSharedIp) {
            // IP 共享逻辑
            // 1. 尝试寻找一个可重用的 IP（已使用，但有空闲端口）
            $reusableIp = $poolIps->filter(function (PoolIp $ip) {
                return $ip->usage_count > 0 && $ip->available_ports_count > 0;
            })->sortByDesc('usage_count')->first();

            if ($reusableIp) {
                return $reusableIp;
            }

            // 2. 如果没有可重用的 IP，则分配一个全新的、未使用的 IP
            return $poolIps->where('usage_count', 0)->sortBy('id')->first();
        } else {
            // 不共享 IP 的逻辑
            // 只选择一个从未使用过的 IP
            return $poolIps->where('usage_count', 0)->sortBy('id')->first();
        }
    }

    /**
     * 分配端口 - 高性能算法
     */
    public function allocatePort(PoolIp $poolIp, string $serviceName, string $namespace): ?int
    {
        // 首先检查是否已经为这个服务分配了端口
        $existingAllocation = PortAllocation::where('service_name', $serviceName)
            ->where('namespace', $namespace)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->first();

        if ($existingAllocation) {
            return $existingAllocation->port;
        }

        // 策略1: 优先复用已释放的端口（排除禁用的端口）
        $releasedAllocation = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('status', PortAllocation::STATUS_RELEASED)
            ->whereBetween('port', [$poolIp->port_range_start, $poolIp->port_range_end])
            ->orderBy('released_at', 'desc')
            ->first();

        if ($releasedAllocation) {
            $updated = PortAllocation::where('id', $releasedAllocation->id)
                ->where('status', PortAllocation::STATUS_RELEASED)
                ->update([
                    'service_name' => $serviceName,
                    'namespace' => $namespace,
                    'status' => PortAllocation::STATUS_ALLOCATED,
                    'allocated_at' => now(),
                    'released_at' => null,
                ]);

            if ($updated) {
                $poolIp->incrementUsage();

                return (int) $releasedAllocation->port;
            }
        }

        // 策略2: 分配新端口
        // 获取最大已分配端口号来优化起始位置
        $maxPort = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->max('port');

        $startPort = $maxPort ? max($maxPort + 1, $poolIp->port_range_start) : $poolIp->port_range_start;

        // 从起始端口开始分配
        for ($port = $startPort; $port <= $poolIp->port_range_end; $port++) {
            // 检查端口是否已被占用或禁用
            $exists = PortAllocation::where('pool_ip_id', $poolIp->id)
                ->where('port', $port)
                ->where(function ($query) {
                    $query->where('status', PortAllocation::STATUS_ALLOCATED);
                })
                ->exists();

            if (! $exists) {
                try {
                    // 尝试创建新的端口分配
                    $allocation = PortAllocation::create([
                        'pool_ip_id' => $poolIp->id,
                        'port' => $port,
                        'service_name' => $serviceName,
                        'namespace' => $namespace,
                        'status' => PortAllocation::STATUS_ALLOCATED,
                        'allocated_at' => now(),
                    ]);

                    if ($allocation) {
                        // 更新使用计数
                        $poolIp->incrementUsage();

                        return $port;
                    }
                } catch (\Illuminate\Database\QueryException $e) {
                    // 如果是唯一约束冲突，继续尝试下一个端口
                    if (str_contains($e->getMessage(), 'duplicate key value violates unique constraint') ||
                        str_contains($e->getMessage(), 'Duplicate entry')) {
                        continue;
                    }
                    throw $e;
                }
            }
        }

        // 如果从优化起始点开始没有找到，从头开始搜索
        if ($startPort > $poolIp->port_range_start) {
            for ($port = $poolIp->port_range_start; $port < $startPort; $port++) {
                $exists = PortAllocation::where('pool_ip_id', $poolIp->id)
                    ->where('port', $port)
                    ->where(function ($query) {
                        $query->where('status', PortAllocation::STATUS_ALLOCATED);
                    })
                    ->exists();

                if (! $exists) {
                    try {
                        $allocation = PortAllocation::create([
                            'pool_ip_id' => $poolIp->id,
                            'port' => $port,
                            'service_name' => $serviceName,
                            'namespace' => $namespace,
                            'status' => PortAllocation::STATUS_ALLOCATED,
                            'allocated_at' => now(),
                        ]);

                        if ($allocation) {
                            $poolIp->incrementUsage();

                            return $port;
                        }
                    } catch (\Illuminate\Database\QueryException $e) {
                        if (str_contains($e->getMessage(), 'duplicate key value violates unique constraint') ||
                            str_contains($e->getMessage(), 'Duplicate entry')) {
                            continue;
                        }
                        throw $e;
                    }
                }
            }
        }

        // 如果所有端口都被占用
        throw new PortAllocationException('IP '.$poolIp->ip_address.' 的端口已耗尽');
    }

    /**
     * 批量分配端口 - 为一个服务分配多个连续端口
     */
    public function allocatePorts(PoolIp $poolIp, string $serviceName, string $namespace, int $portCount): array
    {
        return DB::transaction(function () use ($poolIp, $serviceName, $namespace, $portCount) {
            // 首先检查是否已经为这个服务分配了端口
            $existingAllocations = PortAllocation::where('service_name', $serviceName)
                ->where('namespace', $namespace)
                ->where('status', PortAllocation::STATUS_ALLOCATED)
                ->orderBy('port')
                ->get();

            if ($existingAllocations->count() >= $portCount) {
                return $existingAllocations->pluck('port')->toArray();
            }

            // 如果已有部分分配，需要重新分配
            if ($existingAllocations->isNotEmpty()) {
                // 在同一事务中释放现有分配
                $this->releasePortsInTransaction($serviceName, $namespace);
            }

            $allocatedPorts = [];

            // 暂时仅使用散列端口分配进行测试
            for ($i = 0; $i < $portCount; $i++) {
                $port = $this->allocateSinglePortForBatch($poolIp, $serviceName, $namespace);
                if ($port) {
                    $allocatedPorts[] = $port;
                } else {
                    // 如果分配失败，抛出异常，让事务回滚
                    throw new PortAllocationException('无法分配足够的端口');
                }
            }

            // 批量更新使用计数
            if (! empty($allocatedPorts)) {
                $poolIp->increment('usage_count', count($allocatedPorts));
            }

            return $allocatedPorts;
        });
    }

    /**
     * 查找连续的可用端口
     */
    protected function findConsecutivePorts(PoolIp $poolIp, int $count): ?array
    {
        // 获取已占用的端口列表（状态为allocated或禁用的端口）
        $occupiedPorts = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where(function ($query) {
                $query->where('status', PortAllocation::STATUS_ALLOCATED);
            })
            ->pluck('port')
            ->toArray();

        sort($occupiedPorts);

        // 查找连续的空闲端口段
        $currentPort = $poolIp->port_range_start;

        while ($currentPort <= $poolIp->port_range_end - $count + 1) {
            $isConsecutive = true;
            $tempPorts = [];

            // 检查从当前端口开始的连续端口是否都可用
            for ($i = 0; $i < $count; $i++) {
                $checkPort = $currentPort + $i;
                if ($checkPort > $poolIp->port_range_end || in_array($checkPort, $occupiedPorts)) {
                    $isConsecutive = false;
                    break;
                }
                $tempPorts[] = $checkPort;
            }

            if ($isConsecutive) {
                return $tempPorts;
            }

            $currentPort++;
        }

        return null; // 找不到连续端口
    }

    /**
     * 释放端口
     */
    public function releasePort(string $serviceName, string $namespace): void
    {
        DB::transaction(function () use ($serviceName, $namespace) {
            $this->releasePortsInTransaction($serviceName, $namespace);
        });
    }

    /**
     * 在事务中释放端口（不创建新事务）
     */
    protected function releasePortsInTransaction(string $serviceName, string $namespace): void
    {
        // 先获取要释放的端口信息，用于更新使用计数
        $allocationsToRelease = DB::select('
            SELECT pool_ip_id, COUNT(*) as count
            FROM port_allocations 
            WHERE service_name = ? 
                AND namespace = ? 
                AND status = ?
            GROUP BY pool_ip_id
        ', [$serviceName, $namespace, PortAllocation::STATUS_ALLOCATED]);

        // 使用原生SQL批量更新，性能更好
        $updatedRows = DB::update('
            UPDATE port_allocations 
            SET status = ?, 
                released_at = ?, 
                updated_at = ?
            WHERE service_name = ? 
                AND namespace = ? 
                AND status = ?
        ', [
            PortAllocation::STATUS_RELEASED,
            now(),
            now(),
            $serviceName,
            $namespace,
            PortAllocation::STATUS_ALLOCATED,
        ]);

        if ($updatedRows > 0) {
            // 批量更新 PoolIp 的使用计数
            foreach ($allocationsToRelease as $allocation) {
                DB::update('
                    UPDATE pool_ips 
                    SET usage_count = GREATEST(0, usage_count - ?), 
                        updated_at = ?
                    WHERE id = ?
                ', [$allocation->count, now(), $allocation->pool_ip_id]);
            }
        }
    }

    /**
     * 同步 IP 池到 K8s 集群
     */
    public function syncToKubernetes(IpPool $ipPool): void
    {
        try {
            $driver = $this->loadBalancerManager->driver($ipPool->driver);
            $driver->syncIpPool($ipPool);

            // 更新同步状态
            $ipPool->update([
                'synced_to_k8s' => true,
                'last_sync_at' => now(),
                'sync_error' => null,
            ]);

        } catch (\Exception $e) {
            // 获取详细错误信息
            $errorMessage = $e->getMessage();

            // 更新同步错误状态
            $ipPool->update([
                'synced_to_k8s' => false,
                'sync_error' => $errorMessage,
            ]);

            Log::error('同步 IP 池到 K8s 失败', [
                'pool_id' => $ipPool->id,
                'pool_name' => $ipPool->name,
                'driver' => $ipPool->driver,
                'error' => $errorMessage,
            ]);
            throw new \Exception('同步到 K8s 失败: '.$errorMessage);
        }
    }

    /**
     * 从 K8s 集群删除 IP 池配置
     */
    public function deleteFromKubernetes(IpPool $ipPool): void
    {
        try {
            $driver = $this->loadBalancerManager->driver($ipPool->driver);
            $driver->deleteIpPool($ipPool);

        } catch (\Exception $e) {
            Log::error('从 K8s 删除 IP 池失败', [
                'pool_id' => $ipPool->id,
                'pool_name' => $ipPool->name,
                'driver' => $ipPool->driver,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('从 K8s 删除失败: '.$e->getMessage());
        }
    }

    /**
     * 获取 IP 池统计信息
     */
    public function getPoolStats(IpPool $ipPool): array
    {
        $totalIps = $ipPool->poolIps()->count();
        $activeIps = $ipPool->activePoolIps()->count();
        $totalPorts = $ipPool->poolIps()
            ->selectRaw('SUM(port_range_end - port_range_start + 1) as total')
            ->value('total') ?? 0;

        $allocatedPorts = PortAllocation::whereHas('poolIp', function ($query) use ($ipPool) {
            $query->where('ip_pool_id', $ipPool->id);
        })->where('status', 'allocated')->count();

        $utilizationRate = $totalPorts > 0 ? round(($allocatedPorts / $totalPorts) * 100, 2) : 0;

        return [
            'total_ips' => $totalIps,
            'active_ips' => $activeIps,
            'inactive_ips' => $totalIps - $activeIps,
            'total_ports' => $totalPorts,
            'allocated_ports' => $allocatedPorts,
            'available_ports' => $totalPorts - $allocatedPorts,
            'utilization_rate' => $utilizationRate,
        ];
    }

    /**
     * 构建 IP 池范围列表
     */
    protected function buildIpPoolRanges(IpPool $ipPool): string
    {
        $ips = [];

        // 收集所有活跃的 IP 地址
        foreach ($ipPool->poolIps as $poolIp) {
            if (! $poolIp->is_active) {
                continue;
            }
            $ips[] = $poolIp->ip_address;
        }

        if (empty($ips)) {
            return '';
        }

        // 对 IP 地址排序
        usort($ips, function ($a, $b) {
            return ip2long($a) - ip2long($b);
        });

        // 查找连续的 IP 范围
        $ranges = [];
        $start = $ips[0];
        $end = $ips[0];

        for ($i = 1; $i < count($ips); $i++) {
            if (ip2long($ips[$i]) == ip2long($end) + 1) {
                // 连续的 IP
                $end = $ips[$i];
            } else {
                // 断开，保存当前范围
                if ($start === $end) {
                    $ranges[] = $start.'-'.$end;
                } else {
                    $ranges[] = $start.'-'.$end;
                }
                $start = $ips[$i];
                $end = $ips[$i];
            }
        }

        // 添加最后一个范围
        if ($start === $end) {
            $ranges[] = $start.'-'.$end;
        } else {
            $ranges[] = $start.'-'.$end;
        }

        return implode(',', $ranges);
    }

    /**
     * 为批量分配单独分配一个端口（不更新使用计数）
     */
    protected function allocateSinglePortForBatch(PoolIp $poolIp, string $serviceName, string $namespace): ?int
    {
        // 策略1: 优先复用已释放的端口（排除禁用的端口）
        $releasedAllocation = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('status', PortAllocation::STATUS_RELEASED)
            ->whereBetween('port', [$poolIp->port_range_start, $poolIp->port_range_end])
            ->orderBy('released_at', 'desc')
            ->first();

        if ($releasedAllocation) {
            $updated = PortAllocation::where('id', $releasedAllocation->id)
                ->where('status', PortAllocation::STATUS_RELEASED)
                ->update([
                    'service_name' => $serviceName,
                    'namespace' => $namespace,
                    'status' => PortAllocation::STATUS_ALLOCATED,
                    'allocated_at' => now(),
                    'released_at' => null,
                ]);

            if ($updated) {
                return (int) $releasedAllocation->port;
            }
        }

        // 策略2: 分配新端口
        $maxPort = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->max('port');

        $startPort = $maxPort ? max($maxPort + 1, $poolIp->port_range_start) : $poolIp->port_range_start;

        // 从起始端口开始分配
        for ($port = $startPort; $port <= $poolIp->port_range_end; $port++) {
            $exists = PortAllocation::where('pool_ip_id', $poolIp->id)
                ->where('port', $port)
                ->where(function ($query) {
                    $query->where('status', PortAllocation::STATUS_ALLOCATED);
                })
                ->exists();

            if (! $exists) {
                try {
                    $allocation = PortAllocation::create([
                        'pool_ip_id' => $poolIp->id,
                        'port' => $port,
                        'service_name' => $serviceName,
                        'namespace' => $namespace,
                        'status' => PortAllocation::STATUS_ALLOCATED,
                        'allocated_at' => now(),
                    ]);

                    if ($allocation) {
                        return $port;
                    }
                } catch (\Illuminate\Database\QueryException $e) {
                    if (str_contains($e->getMessage(), 'duplicate key value violates unique constraint') ||
                        str_contains($e->getMessage(), 'Duplicate entry')) {
                        continue;
                    }
                    throw $e;
                }
            }
        }

        // 如果从优化起始点开始没有找到，从头开始搜索
        if ($startPort > $poolIp->port_range_start) {
            for ($port = $poolIp->port_range_start; $port < $startPort; $port++) {
                $exists = PortAllocation::where('pool_ip_id', $poolIp->id)
                    ->where('port', $port)
                    ->where(function ($query) {
                        $query->where('status', PortAllocation::STATUS_ALLOCATED);
                    })
                    ->exists();

                if (! $exists) {
                    try {
                        $allocation = PortAllocation::create([
                            'pool_ip_id' => $poolIp->id,
                            'port' => $port,
                            'service_name' => $serviceName,
                            'namespace' => $namespace,
                            'status' => PortAllocation::STATUS_ALLOCATED,
                            'allocated_at' => now(),
                        ]);

                        if ($allocation) {
                            return $port;
                        }
                    } catch (\Illuminate\Database\QueryException $e) {
                        if (str_contains($e->getMessage(), 'duplicate key value violates unique constraint') ||
                            str_contains($e->getMessage(), 'Duplicate entry')) {
                            continue;
                        }
                        throw $e;
                    }
                }
            }
        }

        return null; // 无法分配端口
    }

    /**
     * 动态生成新的 PoolIp 实例
     */
    protected function generateNewPoolIp(IpPool $ipPool, bool $isShared): ?PoolIp
    {
        // 从IP池的IP段中生成下一个可用的IP地址
        $newIpAddress = $ipPool->generateNextIp();

        if (! $newIpAddress) {
            return null; // IP段已耗尽
        }

        // 检查是否在禁用列表中
        $disabledIps = DisabledIp::getDisabledIps();
        if (in_array($newIpAddress, $disabledIps)) {
            // 如果生成的IP被禁用，继续尝试生成下一个
            return $this->generateNewPoolIp($ipPool, $isShared);
        }

        // 检查是否已存在
        if ($ipPool->poolIps()->where('ip_address', $newIpAddress)->exists()) {
            // 如果IP已存在，继续尝试生成下一个
            return $this->generateNewPoolIp($ipPool, $isShared);
        }

        // 确定端口范围
        if ($ipPool->isShared()) {
            $portStart = 10000;
            $portEnd = 30000;
        } else {
            $portStart = 80;
            $portEnd = 80;
        }

        // 创建新的 PoolIp 实例
        $poolIp = PoolIp::create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => $newIpAddress,
            'port_range_start' => $portStart,
            'port_range_end' => $portEnd,
            'usage_count' => 0,
            'is_active' => true,
        ]);

        return $poolIp;
    }
}
