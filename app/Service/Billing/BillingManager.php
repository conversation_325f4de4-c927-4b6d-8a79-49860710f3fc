<?php

namespace App\Service\Billing;

use App\Contracts\BillingResourceHandlerInterface;
use App\Models\Cluster;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class BillingManager
{
    protected array $handlers = [];

    public function __construct()
    {
        $this->registerHandlers();
    }

    /**
     * 注册所有配置的计费处理器
     */
    protected function registerHandlers(): void
    {
        $handlers = config('pricable.handlers', []);

        foreach ($handlers as $resourceType => $handlerClass) {
            if (class_exists($handlerClass)) {
                $handler = new $handlerClass;

                if ($handler instanceof BillingResourceHandlerInterface) {
                    $this->handlers[$resourceType] = $handler;
                } else {
                    Log::warning("Handler {$handlerClass} does not implement BillingResourceHandlerInterface");
                }
            } else {
                Log::warning("Handler class {$handlerClass} not found for resource type {$resourceType}");
            }
        }
    }

    /**
     * 获取指定资源类型的处理器
     */
    public function getHandler(string $resourceType): ?BillingResourceHandlerInterface
    {
        return $this->handlers[$resourceType] ?? null;
    }

    /**
     * 计算资源价格（新方法，支持DTO）
     */
    public function calculateResourcePrice($usageDTO, $priceResource, string $resourceType, int $minutes = 1): array
    {
        $handler = $this->getHandler($resourceType);

        if (! $handler) {
            Log::warning('No handler found for resource type', [
                'resource_type' => $resourceType,
            ]);

            return [
                'resource_type' => $resourceType,
                'total_cost' => config('pricable.default_cost'),
                'details' => [],
                'error' => 'No handler found',
            ];
        }

        try {
            return $handler->calculatePrice($usageDTO, $priceResource, $minutes);
        } catch (\Exception $e) {
            Log::error('Error calculating resource price', [
                'resource_type' => $resourceType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'resource_type' => $resourceType,
                'total_cost' => config('pricable.default_cost'),
                'details' => [],
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 验证DTO数据
     */
    public function validateUsageDTO($usageDTO, string $resourceType): bool
    {
        $handler = $this->getHandler($resourceType);

        if (! $handler) {
            return false;
        }

        return $handler->validateUsage($usageDTO);
    }

    /**
     * 获取所有已注册的处理器
     */
    public function getRegisteredHandlers(): array
    {
        return $this->handlers;
    }

    /**
     * 获取支持的资源类型
     */
    public function getSupportedResourceTypes(): array
    {
        return array_keys($this->handlers);
    }

    /**
     * 检查资源类型是否支持计费
     */
    public function isResourceTypeSupported(string $resourceType): bool
    {
        return isset($this->handlers[$resourceType]);
    }

    // ==== 保持向后兼容性的旧方法 ====

    /**
     * 根据模型获取处理器（保持向后兼容）
     */
    public function getHandlerForModel(Model $model): ?BillingResourceHandlerInterface
    {
        $modelClass = get_class($model);
        $config = config('pricable.models', []);

        if (isset($config[$modelClass])) {
            $resourceType = $config[$modelClass];

            return $this->getHandler($resourceType);
        }

        return null;
    }

    /**
     * 计算单个资源的费用（旧方法，保持向后兼容）
     */
    public function calculateResourceCost(Model $resource, array $usage, int $minutes = 1): array
    {
        $handler = $this->getHandlerForModel($resource);

        if (! $handler) {
            Log::warning('No handler found for model', [
                'model_class' => get_class($resource),
                'model_id' => $resource->id,
            ]);

            return [
                'resource_type' => 'unknown',
                'resource_id' => $resource->id,
                'total_cost' => config('pricable.default_cost'),
                'details' => [],
                'error' => 'No handler found',
            ];
        }

        // 这里需要适配旧接口到新接口，但由于接口变化较大，
        // 建议逐步迁移到新方法
        Log::warning('Using deprecated calculateResourceCost method', [
            'model_class' => get_class($resource),
            'model_id' => $resource->id,
        ]);

        return [
            'resource_type' => 'deprecated',
            'resource_id' => $resource->id,
            'total_cost' => config('pricable.default_cost'),
            'details' => [],
            'error' => 'Deprecated method used',
        ];
    }

    /**
     * 计算工作空间的总费用（旧方法，保持向后兼容）
     */
    public function calculateWorkspaceCosts(Cluster $cluster, array $usage, int $minutes = 1): array
    {
        Log::warning('Using deprecated calculateWorkspaceCosts method', [
            'cluster_id' => $cluster->id,
        ]);

        return [
            'cluster_id' => $cluster->id,
            'cluster_name' => $cluster->name,
            'total_cost' => config('pricable.default_cost'),
            'resources' => [],
            'error' => 'Deprecated method used',
        ];
    }

    /**
     * 验证使用量数据（旧方法，保持向后兼容）
     */
    public function validateUsage(Model $resource, array $usage): bool
    {
        Log::warning('Using deprecated validateUsage method');

        return false;
    }

    /**
     * 检查资源是否支持计费（旧方法，保持向后兼容）
     */
    public function isResourceBillable(Model $resource): bool
    {
        return ! is_null($this->getHandlerForModel($resource));
    }
}
