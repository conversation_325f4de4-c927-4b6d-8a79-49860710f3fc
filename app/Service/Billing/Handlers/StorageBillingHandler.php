<?php

namespace App\Service\Billing\Handlers;

use App\Contracts\BillingResourceHandlerInterface;
use App\DTOs\Billing\StorageUsageDTO;
use App\Models\Cluster;

class StorageBillingHandler implements BillingResourceHandlerInterface
{
    /**
     * 基于DTO计算价格
     */
    public function calculatePrice($usageDTO, $priceResource, int $minutes = 1): array
    {
        if (! $usageDTO instanceof StorageUsageDTO) {
            throw new \InvalidArgumentException('Expected StorageUsageDTO');
        }

        if (! $priceResource instanceof Cluster) {
            throw new \InvalidArgumentException('Expected Cluster model');
        }

        $usage = $usageDTO->toUsageArray();

        $result = [
            'resource_type' => $this->getResourceType(),
            'resource_name' => $usageDTO->name,
            'workspace_name' => $usageDTO->workspaceName,
            'namespace' => $usageDTO->namespace,
            'storage_class' => $usageDTO->storageClass,
            'total_cost' => config('pricable.default_cost'),
            'details' => [],
        ];

        // 计算存储费用
        if ($usage['storage_gi'] > 0) {
            $storageGb = $usage['storage_gi']; // Gi ≈ GB
            $storagePrice = $priceResource->calculatePricePerMinute('storage_gb', (float) $storageGb);
            $storageCost = bcmul($storagePrice, (string) $minutes, 8);

            $result['details']['storage'] = [
                'usage_gi' => $usage['storage_gi'],
                'usage_gb' => $storageGb,
                'storage_class' => $usageDTO->storageClass,
                'price_per_minute' => $storagePrice,
                'minutes' => $minutes,
                'total_cost' => $storageCost,
            ];

            $result['total_cost'] = bcadd($result['total_cost'], $storageCost, 8);
        }

        return $result;
    }

    /**
     * 验证DTO数据是否有效
     */
    public function validateUsage($usageDTO): bool
    {
        if (! $usageDTO instanceof StorageUsageDTO) {
            return false;
        }

        return $usageDTO->sizeGi >= 0;
    }

    /**
     * 获取支持的DTO类型
     */
    public function getSupportedDTOTypes(): array
    {
        return [StorageUsageDTO::class];
    }

    /**
     * 获取资源类型名称
     */
    public function getResourceType(): string
    {
        return 'storage';
    }
}
