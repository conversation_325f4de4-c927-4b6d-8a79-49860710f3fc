<?php

namespace App\Service\Billing\Handlers;

use App\Contracts\BillingResourceHandlerInterface;
use App\DTOs\Billing\ServiceUsageDTO;
use App\Models\IpPool;
use App\Models\PoolIp;

class ServiceBillingHandler implements BillingResourceHandlerInterface
{
    /**
     * 基于DTO计算价格
     */
    public function calculatePrice($usageDTO, $priceResource, int $minutes = 1): array
    {
        if (! $usageDTO instanceof ServiceUsageDTO) {
            throw new \InvalidArgumentException('Expected ServiceUsageDTO');
        }

        $result = [
            'resource_type' => $this->getResourceType(),
            'resource_name' => $usageDTO->name,
            'workspace_name' => $usageDTO->workspaceName,
            'namespace' => $usageDTO->namespace,
            'service_type' => $usageDTO->serviceType,
            'total_cost' => config('pricable.default_cost'),
            'details' => [],
        ];

        // 只有LoadBalancer类型的Service才计费
        if (! $usageDTO->isLoadBalancer() || empty($usageDTO->externalIps)) {
            return $result;
        }

        // 如果提供了poolIpId，直接使用对应的IP Pool
        if ($usageDTO->poolIpId) {
            $poolIp = PoolIp::find($usageDTO->poolIpId);
            if ($poolIp && $poolIp->ipPool) {
                return $this->calculateIpPoolCost($usageDTO, $poolIp->ipPool, $minutes, $result);
            }
        }

        // 如果没有poolIpId，尝试通过IP地址查找对应的IP Pool
        if ($priceResource instanceof IpPool) {
            return $this->calculateIpPoolCost($usageDTO, $priceResource, $minutes, $result);
        }

        // 如果无法确定IP Pool，返回0费用
        return $result;
    }

    /**
     * 计算IP Pool的费用
     */
    private function calculateIpPoolCost(ServiceUsageDTO $usageDTO, IpPool $ipPool, int $minutes, array $result): array
    {
        $ipCount = count($usageDTO->externalIps);

        if ($ipCount > 0) {
            $ipPrice = $ipPool->calculatePricePerMinute('ip_address', (float) $ipCount);
            $ipCost = bcmul($ipPrice, (string) $minutes, 8);

            $result['details']['ip_address'] = [
                'ip_pool_id' => $ipPool->id,
                'ip_pool_name' => $ipPool->name,
                'ip_count' => $ipCount,
                'external_ips' => $usageDTO->externalIps,
                'price_per_minute' => $ipPrice,
                'minutes' => $minutes,
                'total_cost' => $ipCost,
            ];

            $result['total_cost'] = bcadd($result['total_cost'], $ipCost, 8);
        }

        return $result;
    }

    /**
     * 验证DTO数据是否有效
     */
    public function validateUsage($usageDTO): bool
    {
        if (! $usageDTO instanceof ServiceUsageDTO) {
            return false;
        }

        // 验证服务类型
        $validTypes = ['ClusterIP', 'NodePort', 'LoadBalancer', 'ExternalName'];
        if (! in_array($usageDTO->serviceType, $validTypes)) {
            return false;
        }

        // 验证IP地址格式
        foreach ($usageDTO->externalIps as $ip) {
            if (! filter_var($ip, FILTER_VALIDATE_IP)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取支持的DTO类型
     */
    public function getSupportedDTOTypes(): array
    {
        return [ServiceUsageDTO::class];
    }

    /**
     * 获取资源类型名称
     */
    public function getResourceType(): string
    {
        return 'service';
    }
}
