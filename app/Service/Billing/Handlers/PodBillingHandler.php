<?php

namespace App\Service\Billing\Handlers;

use App\Contracts\BillingResourceHandlerInterface;
use App\DTOs\Billing\PodUsageDTO;
use App\Models\Cluster;

class PodBillingHandler implements BillingResourceHandlerInterface
{
    /**
     * 基于DTO计算价格
     */
    public function calculatePrice($usageDTO, $priceResource, int $minutes = 1): array
    {
        if (! $usageDTO instanceof PodUsageDTO) {
            throw new \InvalidArgumentException('Expected PodUsageDTO');
        }

        if (! $priceResource instanceof Cluster) {
            throw new \InvalidArgumentException('Expected Cluster model');
        }

        $usage = $usageDTO->toUsageArray();

        $result = [
            'resource_type' => $this->getResourceType(),
            'resource_name' => $usageDTO->name,
            'workspace_name' => $usageDTO->workspaceName,
            'namespace' => $usageDTO->namespace,
            'total_cost' => config('pricable.default_cost'),
            'details' => [],
        ];

        // 计算CPU费用
        if ($usage['cpu_m'] > 0) {
            $cpuCores = bcdiv((string) $usage['cpu_m'], '1000', 8);
            $cpuPrice = $priceResource->calculatePricePerMinute('cpu_core', (float) $cpuCores);
            $cpuCost = bcmul($cpuPrice, (string) $minutes, 8);

            $result['details']['cpu'] = [
                'usage_millicores' => $usage['cpu_m'],
                'usage_cores' => $cpuCores,
                'price_per_minute' => $cpuPrice,
                'minutes' => $minutes,
                'total_cost' => $cpuCost,
            ];

            $result['total_cost'] = bcadd($result['total_cost'], $cpuCost, 8);
        }

        // 计算内存费用
        if ($usage['memory_mi'] > 0) {
            $memoryGb = bcdiv((string) $usage['memory_mi'], '1024', 8);
            $memoryPrice = $priceResource->calculatePricePerMinute('memory_gb', (float) $memoryGb);
            $memoryCost = bcmul($memoryPrice, (string) $minutes, 8);

            $result['details']['memory'] = [
                'usage_mi' => $usage['memory_mi'],
                'usage_gb' => $memoryGb,
                'price_per_minute' => $memoryPrice,
                'minutes' => $minutes,
                'total_cost' => $memoryCost,
            ];

            $result['total_cost'] = bcadd($result['total_cost'], $memoryCost, 8);
        }

        return $result;
    }

    /**
     * 验证DTO数据是否有效
     */
    public function validateUsage($usageDTO): bool
    {
        if (! $usageDTO instanceof PodUsageDTO) {
            return false;
        }

        return $usageDTO->cpuRequestMillicores >= 0 && $usageDTO->memoryRequestMi >= 0;
    }

    /**
     * 获取支持的DTO类型
     */
    public function getSupportedDTOTypes(): array
    {
        return [PodUsageDTO::class];
    }

    /**
     * 获取资源类型名称
     */
    public function getResourceType(): string
    {
        return 'pod';
    }
}
