<?php

namespace App\Service\Billing;

use App\Events\UserBalanceChanged;
use App\Models\BillingRecord;
use App\Models\Workspace;
use App\Service\BalanceService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 计费记录服务
 * 负责计费记录的创建、扣费和状态管理
 */
class BillingRecordService
{
    public function __construct(
        protected BalanceService $balanceService,
    ) {}

    /**
     * 创建计费记录
     */
    public function createBillingRecord(
        Workspace $workspace,
        array $usage,
        array $costs,
        string $billingStartAt,
        string $billingEndAt
    ): BillingRecord {
        return BillingRecord::create([
            'workspace_id' => $workspace->id,
            'cluster_id' => $workspace->cluster_id,
            'user_id' => $workspace->user_id,
            'billing_start_at' => $billingStartAt,
            'billing_end_at' => $billingEndAt,
            'resource_usage' => $usage,
            'memory_cost' => $costs['memory_cost'],
            'cpu_cost' => $costs['cpu_cost'],
            'storage_cost' => $costs['storage_cost'],
            'loadbalancer_cost' => $costs['loadbalancer_cost'],
            'total_cost' => $costs['total_cost'],
            'status' => BillingRecord::STATUS_PENDING,
        ]);
    }

    /**
     * 执行计费记录的扣费
     */
    public function chargeBillingRecord(BillingRecord $record): array
    {
        if (! $record->canCharge()) {
            return ['success' => false, 'reason' => 'cannot_charge'];
        }

        try {
            return DB::transaction(function () use ($record) {
                /** @var \App\Models\User $user */
                $user = $record->user;
                /* @var \App\Models\Workspace $workspace */
                $workspace = $record->workspace;
                $oldBalance = $user->current_balance;

                // 尝试扣费
                $this->balanceService->deductBalance(
                    $user,
                    (string) $record->total_cost,
                    "工作空间 {$workspace->name} 资源使用费"
                );

                // 标记为已扣费
                $record->markAsCharged();

                // 触发余额变化事件
                UserBalanceChanged::dispatch(
                    $user,
                    (string) $oldBalance,
                    (string) $user->fresh()->current_balance,
                    (string) $record->total_cost,
                    'charge',
                    '工作空间资源使用计费'
                );

                Log::info('计费成功', [
                    'record_id' => $record->id,
                    'user_id' => $user->id,
                    'amount' => $record->total_cost,
                    'new_balance' => $user->fresh()->current_balance,
                ]);

                return ['success' => true, 'charged_amount' => $record->total_cost];
            });
        } catch (\Exception $e) {
            // 扣费失败，记录原因（欠费处理由Job中的具体业务逻辑处理）

            $record->markAsFailed();

            Log::error('计费失败', [
                'record_id' => $record->id,
                'user_id' => $record->user_id,
                'error' => $e->getMessage(),
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 处理计费记录
     * 综合创建和扣费的完整流程
     */
    public function processBillingRecord(
        Workspace $workspace,
        array $usage,
        array $costs,
        string $billingStartAt,
        string $billingEndAt
    ): array {
        try {
            // 创建计费记录
            $record = $this->createBillingRecord(
                $workspace,
                $usage,
                $costs,
                $billingStartAt,
                $billingEndAt
            );

            Log::info('计费记录已创建', [
                'record_id' => $record->id,
                'workspace_id' => $workspace->id,
                'total_cost' => $record->total_cost,
            ]);

            // 执行扣费
            $chargeResult = $this->chargeBillingRecord($record);

            return array_merge($chargeResult, [
                'record_id' => $record->id,
                'workspace_id' => $workspace->id,
            ]);

        } catch (\Exception $e) {
            Log::error('处理计费记录失败', [
                'workspace_id' => $workspace->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'workspace_id' => $workspace->id,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 获取用户的计费统计
     */
    public function getUserBillingStats(int $userId, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $records = BillingRecord::where('user_id', $userId)
            ->where('created_at', '>=', $startDate)
            ->where('status', BillingRecord::STATUS_CHARGED)
            ->get();

        $totalCost = $records->sum('total_cost');
        $memoryCost = $records->sum('memory_cost');
        $cpuCost = $records->sum('cpu_cost');
        $storageCost = $records->sum('storage_cost');
        $loadBalancerCost = $records->sum('loadbalancer_cost');

        return [
            'period_days' => $days,
            'total_records' => $records->count(),
            'total_cost' => (string) $totalCost,
            'breakdown' => [
                'memory_cost' => (string) $memoryCost,
                'cpu_cost' => (string) $cpuCost,
                'storage_cost' => (string) $storageCost,
                'loadbalancer_cost' => (string) $loadBalancerCost,
            ],
            'average_daily_cost' => $days > 0 ? bcdiv((string) $totalCost, (string) $days, 4) : '0',
        ];
    }

    /**
     * 获取工作空间的计费统计
     */
    public function getWorkspaceBillingStats(int $workspaceId, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $records = BillingRecord::where('workspace_id', $workspaceId)
            ->where('created_at', '>=', $startDate)
            ->where('status', BillingRecord::STATUS_CHARGED)
            ->get();

        $totalCost = $records->sum('total_cost');

        return [
            'workspace_id' => $workspaceId,
            'period_days' => $days,
            'total_records' => $records->count(),
            'total_cost' => (string) $totalCost,
            'average_daily_cost' => $days > 0 ? bcdiv((string) $totalCost, (string) $days, 4) : '0',
        ];
    }

    /**
     * 重试失败的计费记录
     */
    public function retryFailedBillingRecords(int $limit = 100): array
    {
        $failedRecords = BillingRecord::where('status', BillingRecord::STATUS_FAILED)
            ->where('created_at', '>=', now()->subDays(1)) // 只重试1天内的记录
            ->limit($limit)
            ->get();

        $results = [];

        foreach ($failedRecords as $record) {
            try {
                // 重置状态为待处理
                $record->update(['status' => BillingRecord::STATUS_PENDING]);

                // 重新尝试扣费
                $result = $this->chargeBillingRecord($record);
                $results[] = array_merge($result, ['record_id' => $record->id]);

            } catch (\Exception $e) {
                $results[] = [
                    'record_id' => $record->id,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }
}
