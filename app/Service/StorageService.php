<?php

namespace App\Service;

use App\ClusterLabel;
use App\DTOs\StorageDTO;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\K8s\ResourceConflictException;
use App\Exceptions\Storage\InvalidStorageSizeException;
use App\Exceptions\Storage\StorageNotFoundException;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class StorageService
{
    public function __construct(
        protected Workspace $workspace,
    ) {}

    /**
     * 获取工作空间的所有 Storage（PVC）
     */
    public function getStorages(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/persistentvolumeclaims");

            $items = $response->json('items', []);

            return array_map(function ($item) {
                return StorageDTO::fromK8sResource($item);
            }, $items);
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('获取 Storage 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取存储列表', $e);
        } catch (\Exception $e) {
            Log::error('获取 Storage 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Storage 列表失败：'.$e->getMessage());
        }
    }

    /**
     * 获取单个 Storage 详情
     */
    public function getStorage(string $name): StorageDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/persistentvolumeclaims/{$name}");

            return StorageDTO::fromK8sResource($response->json());
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('Storage 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'storage_name' => $name,
                ]);

                throw new StorageNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('获取 Storage 详情失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'storage_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取存储信息', $e);
        } catch (\Exception $e) {
            Log::error('获取 Storage 详情失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'storage_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Storage 详情失败：'.$e->getMessage());
        }
    }

    /**
     * 创建 Storage（PVC）
     *
     * @param  string  $name  PVC 名称
     * @param  int  $sizeInMi  容量大小（Mi 单位）
     */
    public function createStorage(string $name, int $sizeInMi, ?string $workloadType = null, ?string $workloadName = null, array $customLabels = []): StorageDTO
    {
        $this->validateStorageSize($sizeInMi);

        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'PersistentVolumeClaim',
            'metadata' => [
                'name' => $name,
                'namespace' => $this->workspace->namespace,
                'labels' => array_merge(
                    $this->workspace->buildDefaultLabels(null, $name),
                    $this->buildWorkloadLabels($workloadType, $workloadName),
                    $customLabels
                ),
            ],
            'spec' => [
                'accessModes' => ['ReadWriteMany'], // 固定为 RWX
                'storageClassName' => 'longhorn', // 固定为 longhorn
                'resources' => [
                    'requests' => [
                        'storage' => $sizeInMi.'Mi',
                    ],
                ],
            ],
        ];

        try {
            $response = $this->workspace->cluster->http()
                ->post("/api/v1/namespaces/{$this->workspace->namespace}/persistentvolumeclaims", $payload);

            Log::info('Storage 创建成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'storage_name' => $name,
                'size' => $sizeInMi.'Mi',
            ]);

            return StorageDTO::fromK8sResource($response->json());
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 409) {
                Log::warning('Storage 名称冲突', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'storage_name' => $name,
                ]);

                throw new ResourceConflictException("存储 '{$name}' 已存在");
            }

            Log::error('创建 Storage 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'storage_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法创建存储', $e);
        } catch (\Exception $e) {
            Log::error('创建 Storage 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'storage_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('创建 Storage 失败：'.$e->getMessage());
        }
    }

    /**
     * 扩容 Storage（PVC）
     *
     * @param  string  $name  PVC 名称
     * @param  int  $newSizeInMi  新的容量大小（Mi 单位）
     */
    public function expandStorage(string $name, int $newSizeInMi): StorageDTO
    {
        // 验证容量大小
        $this->validateStorageSize($newSizeInMi);

        try {
            // 先获取现有的 PVC
            $existing = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/persistentvolumeclaims/{$name}")
                ->json();

            // 获取当前容量
            $currentSizeStr = $existing['spec']['resources']['requests']['storage'] ?? '';
            $currentSizeInMi = $this->parseSizeToMi($currentSizeStr);

            // 验证不能缩减容量
            if ($newSizeInMi <= $currentSizeInMi) {
                throw new \Exception("新容量 ({$newSizeInMi}Mi) 必须大于当前容量 ({$currentSizeInMi}Mi)，不支持缩减容量");
            }

            // 更新 PVC 规格
            $existing['spec']['resources']['requests']['storage'] = $newSizeInMi.'Mi';

            $response = $this->workspace->cluster->http()
                ->put("/api/v1/namespaces/{$this->workspace->namespace}/persistentvolumeclaims/{$name}", $existing);

            Log::info('Storage 扩容成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'storage_name' => $name,
                'old_size' => $currentSizeInMi.'Mi',
                'new_size' => $newSizeInMi.'Mi',
            ]);

            return StorageDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('扩容 Storage 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'storage_name' => $name,
                'new_size' => $newSizeInMi.'Mi',
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('扩容 Storage 失败：'.$e->getMessage());
        }
    }

    /**
     * 删除 Storage（PVC）
     */
    public function deleteStorage(string $name): bool
    {
        try {
            $this->workspace->cluster->http()
                ->delete("/api/v1/namespaces/{$this->workspace->namespace}/persistentvolumeclaims/{$name}");

            Log::info('Storage 删除成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'storage_name' => $name,
            ]);

            return true;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('Storage 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'storage_name' => $name,
                ]);

                throw new StorageNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('删除 Storage 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'storage_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法删除 Storage', $e);
        } catch (\Exception $e) {
            Log::error('删除 Storage 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'storage_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('删除 Storage 失败：'.$e->getMessage());
        }
    }

    /**
     * 验证存储容量大小
     */
    protected function validateStorageSize(int $sizeInMi): void
    {
        if ($sizeInMi < 512) {
            throw new InvalidStorageSizeException('存储大小不能小于 512Mi (1Gi)');
        }

        if ($sizeInMi > 1024 * 1024) { // 1Ti
            throw new InvalidStorageSizeException('存储大小不能大于 1Ti');
        }

        if ($sizeInMi % 512 !== 0) {
            throw new InvalidStorageSizeException('存储大小必须是 512Mi 的倍数');
        }
    }

    /**
     * 解析存储大小字符串为 Mi 单位的数值
     */
    protected function parseSizeToMi(string $sizeStr): int
    {
        if (empty($sizeStr)) {
            return 0;
        }

        // 移除空格
        $sizeStr = trim($sizeStr);

        if (str_ends_with($sizeStr, 'Mi')) {
            return (int) str_replace('Mi', '', $sizeStr);
        }

        if (str_ends_with($sizeStr, 'Gi')) {
            return (int) ((float) str_replace('Gi', '', $sizeStr) * 1024);
        }

        if (str_ends_with($sizeStr, 'Ki')) {
            return (int) ((float) str_replace('Ki', '', $sizeStr) / 1024);
        }

        if (str_ends_with($sizeStr, 'M')) {
            return (int) str_replace('M', '', $sizeStr);
        }

        if (str_ends_with($sizeStr, 'G')) {
            return (int) ((float) str_replace('G', '', $sizeStr) * 1024);
        }

        // 假设是字节，转换为 Mi
        return (int) ((int) $sizeStr / 1024 / 1024);
    }

    /**
     * 构建工作负载关联标签
     */
    protected function buildWorkloadLabels(?string $workloadType, ?string $workloadName): array
    {
        $labels = [];

        if ($workloadType && $workloadName) {
            $labels[ClusterLabel::WORKLOAD_TYPE->value] = strtolower($workloadType);
            $labels[ClusterLabel::WORKLOAD_NAME->value] = $workloadName;
        }

        return $labels;
    }
}
