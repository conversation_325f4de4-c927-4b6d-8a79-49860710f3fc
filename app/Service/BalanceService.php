<?php

namespace App\Service;

use App\Events\UserBalanceChanged;
use App\Exceptions\Balance\InsufficientBalanceException;
use App\Exceptions\Balance\InvalidAmountException;
use App\Models\TopUpRecord;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BalanceService
{
    /**
     * 添加用户余额（充值）
     *
     * @param  User  $user  用户
     * @param  float  $amount  充值金额
     * @param  string  $paymentMethod  支付方式
     * @param  string|null  $transactionNumber  流水号
     * @param  array  $extra  额外信息
     * @return TopUpRecord 充值记录
     */
    public function addBalance(
        User $user,
        float $amount,
        string $paymentMethod,
        ?string $transactionNumber = null,
        array $extra = []
    ): TopUpRecord {
        return DB::transaction(function () use ($user, $amount, $paymentMethod, $transactionNumber, $extra) {
            // 生成流水号
            if (! $transactionNumber) {
                $transactionNumber = $this->generateTransactionNumber();
            }

            // 创建充值记录
            $record = TopUpRecord::create([
                'user_id' => $user->id,
                'transaction_number' => $transactionNumber,
                'amount' => $amount,
                'remaining_amount' => $amount,
                'status' => TopUpRecord::STATUS_COMPLETED,
                'payment_method' => $paymentMethod,
                'payment_gateway_response' => $extra['gateway_response'] ?? null,
                'completed_at' => now(),
                'remark' => $extra['remark'] ?? null,
            ]);

            // 更新用户当前余额
            $oldBalance = $user->current_balance;
            $currentBalance = number_format($user->current_balance, 8, '.', '');
            $addAmount = number_format($amount, 8, '.', '');
            $newBalance = bcadd($currentBalance, $addAmount, 8);
            $user->update(['current_balance' => $newBalance]);
            $user->refresh();

            // 触发余额变化事件
            UserBalanceChanged::dispatch(
                $user,
                (string) $oldBalance,
                (string) $newBalance,
                (string) $amount,
                'topup',
                '账户充值'
            );

            Log::info('用户余额充值成功', [
                'user_id' => $user->id,
                'amount' => $amount,
                'transaction_number' => $transactionNumber,
                'new_balance' => $newBalance,
            ]);

            return $record;
        });
    }

    /**
     * 扣除用户余额
     *
     * @param  User  $user  用户
     * @param  string  $amount  扣除金额（BCMath 字符串）
     * @param  string  $reason  扣除原因
     * @return bool 是否成功
     *
     * @throws InvalidAmountException
     * @throws InsufficientBalanceException
     */
    public function deductBalance(User $user, string $amount, string $reason = ''): bool
    {
        // 验证金额格式
        if (! is_numeric($amount) || bccomp($amount, '0', 8) <= 0) {
            throw new InvalidAmountException('扣除金额必须是正数');
        }

        if (! $user->hasEnoughBalance($amount)) {
            throw new InsufficientBalanceException("余额不足，当前余额: {$user->current_balance}，需要扣除: {$amount}");
        }

        return DB::transaction(function () use ($user, $amount, $reason) {
            // 使用传入的 BCMath 字符串
            $remainingAmount = $amount;

            // 获取有余额的充值记录，按时间排序（先进先出）
            $availableRecords = $user->availableTopUpRecords()->get();

            foreach ($availableRecords as $record) {
                if (bccomp($remainingAmount, config('pricable.default_cost'), 8) <= 0) {
                    break;
                }

                // 格式化记录余额
                $recordRemainingAmount = number_format($record->remaining_amount, 8, '.', '');

                // 使用BCMath计算需要从当前记录扣除的金额
                $deductFromRecord = bccomp($remainingAmount, $recordRemainingAmount, 8) <= 0
                    ? $remainingAmount
                    : $recordRemainingAmount;

                // 从充值记录中扣除余额
                $record->useBalance((float) $deductFromRecord);
                $remainingAmount = bcsub($remainingAmount, $deductFromRecord, 8);

                Log::info('从充值记录扣除余额', [
                    'user_id' => $user->id,
                    'record_id' => $record->id,
                    'deduct_amount' => $deductFromRecord,
                    'record_remaining' => $record->remaining_amount,
                ]);
            }

            if (bccomp($remainingAmount, config('pricable.default_cost'), 8) > 0) {
                throw new \Exception('余额扣除失败，剩余未扣除金额：'.$remainingAmount);
            }

            // 更新用户当前余额，使用 BCMath 精确计算
            $currentBalance = number_format($user->current_balance, 8, '.', '');
            $newBalance = bcsub($currentBalance, $amount, 8);
            $user->update(['current_balance' => $newBalance]);

            Log::info('用户余额扣除成功', [
                'user_id' => $user->id,
                'amount' => $amount,
                'reason' => $reason,
                'new_balance' => $newBalance,
            ]);

            return true;
        });
    }

    /**
     * 计算用户实际余额（从充值记录计算）
     *
     * @param  User  $user  用户
     * @return float 实际余额
     */
    public function calculateActualBalance(User $user): float
    {
        return $user->completedTopUpRecords()->sum('remaining_amount');
    }

    /**
     * 同步用户余额（修正余额不一致问题）
     *
     * @param  User  $user  用户
     * @return bool 是否有更新
     */
    public function syncUserBalance(User $user): bool
    {
        $actualBalance = $this->calculateActualBalance($user);

        if ($user->current_balance != $actualBalance) {
            $oldBalance = $user->current_balance;
            $user->update(['current_balance' => $actualBalance]);

            Log::warning('用户余额同步', [
                'user_id' => $user->id,
                'old_balance' => $oldBalance,
                'actual_balance' => $actualBalance,
                'difference' => $actualBalance - $oldBalance,
            ]);

            return true;
        }

        return false;
    }

    /**
     * 退款
     *
     * @param  TopUpRecord  $record  充值记录
     * @param  float|null  $amount  退款金额，null表示全额退款
     * @param  string  $reason  退款原因
     * @return bool 是否成功
     *
     * @throws \Exception
     */
    public function refund(TopUpRecord $record, ?float $amount = null, string $reason = ''): bool
    {
        if (! $record->canRefund()) {
            throw new \Exception('该记录不可退款');
        }

        return DB::transaction(function () use ($record, $amount, $reason) {
            $refundAmount = $amount ?? $record->remaining_amount;

            if ($refundAmount > $record->remaining_amount) {
                throw new \Exception('退款金额不能超过剩余金额');
            }

            // 退款
            $record->refund($refundAmount);

            // 更新用户当前余额
            $currentBalance = number_format($record->user->current_balance, 8, '.', '');
            $refundAmountStr = number_format($refundAmount, 8, '.', '');
            $newBalance = bcsub($currentBalance, $refundAmountStr, 8);
            $record->user->update(['current_balance' => $newBalance]);

            Log::info('退款成功', [
                'user_id' => $record->user_id,
                'record_id' => $record->id,
                'refund_amount' => $refundAmount,
                'reason' => $reason,
                'new_user_balance' => $newBalance,
            ]);

            return true;
        });
    }

    /**
     * 使用兑换码添加余额
     *
     * @param  User  $user  用户
     * @param  string  $code  兑换码
     * @param  string|null  $ipAddress  IP地址
     * @param  string|null  $userAgent  用户代理
     * @return array 返回兑换结果
     *
     * @throws \Exception
     */
    public function redeemCode(User $user, string $code, ?string $ipAddress = null, ?string $userAgent = null): array
    {
        return DB::transaction(function () use ($user, $code, $ipAddress, $userAgent) {
            // 先查找兑换码是否存在
            $redeemCode = \App\Models\RedeemCode::findByCode($code);

            if (! $redeemCode) {
                throw new \Exception('兑换码不存在');
            }

            // 检查用户是否可以使用此兑换码
            if (! $redeemCode->canUse($user)) {
                if ($redeemCode->max_uses == 1 && $redeemCode->usages()->where('user_id', $user->id)->exists()) {
                    throw new \Exception('该兑换码你已经使用过了');
                } elseif ($redeemCode->isExpired()) {
                    throw new \Exception('兑换码已过期');
                } elseif ($redeemCode->isExhausted()) {
                    throw new \Exception('兑换码使用次数已用完');
                } elseif (! $redeemCode->is_active) {
                    throw new \Exception('兑换码已被禁用');
                } else {
                    throw new \Exception('兑换码不可用');
                }
            }

            // 使用兑换码
            $usage = $redeemCode->use($user, $ipAddress, $userAgent);

            // 添加余额
            $record = $this->addBalance(
                $user,
                (float) $redeemCode->amount,
                'redeem_code', // 支付方式设为兑换码
                null,
                [
                    'remark' => "兑换码充值：{$code}",
                    'redeem_code_id' => $redeemCode->id,
                    'redeem_usage_id' => $usage->id,
                ]
            );

            Log::info('兑换码使用成功', [
                'user_id' => $user->id,
                'code' => $code,
                'amount' => $redeemCode->amount,
                'usage_id' => $usage->id,
                'record_id' => $record->id,
                'ip_address' => $ipAddress,
            ]);

            return [
                'success' => true,
                'amount' => $redeemCode->amount,
                'new_balance' => $user->fresh()->current_balance,
                'record' => $record,
                'usage' => $usage,
            ];
        });
    }

    /**
     * 生成流水号
     *
     * @return string 流水号
     */
    private function generateTransactionNumber(): string
    {
        return 'TXN'.date('YmdHis').str_pad(random_int(0, 9999), 4, '0', STR_PAD_LEFT);
    }
}
