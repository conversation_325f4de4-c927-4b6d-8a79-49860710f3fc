<?php

namespace App\Service;

use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class UserSimulationService
{
    /**
     * 生成模拟用户登录的Token并存储到缓存
     */
    public function generateSimulationToken(User $user, int $expires = 60): string
    {
        $token = Str::random(32);
        Cache::put("user_simulate_token:{$token}", $user->id, $expires * 60);

        return $token;
    }

    /**
     * 验证Token并返回用户ID
     */
    public function validateSimulationToken(string $token): ?int
    {
        return Cache::get("user_simulate_token:{$token}");
    }

    /**
     * 销毁Token
     */
    public function destroySimulationToken(string $token): bool
    {
        return Cache::forget("user_simulate_token:{$token}");
    }
}
