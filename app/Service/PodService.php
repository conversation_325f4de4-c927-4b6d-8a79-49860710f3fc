<?php

namespace App\Service;

use App\DTOs\PodDTO;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\Pod\PodNotFoundException;
use App\Exceptions\Pod\PodNotReadyException;
use App\Models\Workspace;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Log;

class PodService
{
    public function __construct(
        protected Workspace $workspace
    ) {}

    /**
     * 获取 Pod 列表
     */
    public function getPods(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/pods");

            $items = $response->json('items', []);

            // 不再为每个 Pod 获取事件和指标，避免 N+1 查询
            return array_map(function ($item) {
                return PodDTO::fromK8sResource($item);
            }, $items);
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('获取 Pod 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取 Pod 列表', $e);
        } catch (\Exception $e) {
            Log::error('获取 Pod 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Pod 列表失败：'.$e->getMessage());
        }
    }

    /**
     * 获取单个 Pod
     */
    public function getPod(string $name): PodDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/pods/{$name}");

            return PodDTO::fromK8sResource($response->json());
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('Pod 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'pod_name' => $name,
                ]);

                throw new PodNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('获取 Pod 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'pod_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取 Pod 信息', $e);
        } catch (\Exception $e) {
            Log::error('获取 Pod 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'pod_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Pod 失败：'.$e->getMessage());
        }
    }

    /**
     * 获取 Pod 日志
     */
    public function getPodLogs(string $podName, ?string $containerName = null, array $options = []): string
    {
        try {
            $params = array_filter([
                'container' => $containerName,
                'tailLines' => $options['tail_lines'] ?? 100,
                'follow' => $options['follow'] ?? false,
                'previous' => $options['previous'] ?? false,
                'sinceSeconds' => $options['since_seconds'] ?? null,
                'timestamps' => $options['timestamps'] ?? true,
            ]);

            $url = "/api/v1/namespaces/{$this->workspace->namespace}/pods/{$podName}/log";

            $response = $this->workspace->cluster->http()
                ->get($url, $params);

            return $response->body();
        } catch (RequestException $e) {
            if ($e->response && $e->response->status() === 400) {
                throw new PodNotReadyException($podName, $containerName);
            }

            Log::error('获取 Pod 日志请求失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('获取 Pod 日志请求失败', $e);
        } catch (\Exception $e) {
            Log::error('获取 Pod 日志失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Pod 日志失败：'.$e->getMessage());
        }
    }

    /**
     * 删除 Pod
     */
    public function deletePod(string $name): bool
    {
        try {
            $this->workspace->cluster->http()
                ->delete("/api/v1/namespaces/{$this->workspace->namespace}/pods/{$name}");

            Log::info('Pod 删除成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'pod_name' => $name,
            ]);

            return true;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('Pod 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'pod_name' => $name,
                ]);

                throw new PodNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('删除 Pod 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'pod_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法删除 Pod', $e);
        } catch (\Exception $e) {
            Log::error('删除 Pod 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'pod_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('删除 Pod 失败：'.$e->getMessage());
        }
    }

    /**
     * 获取 Pod 事件
     */
    public function getPodEvents(string $podName): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/events", [
                    'fieldSelector' => "involvedObject.name={$podName},involvedObject.kind=Pod",
                ]);

            $items = $response->json('items', []);

            return array_map(function ($event) {
                return [
                    'type' => $event['type'] ?? 'Normal',
                    'reason' => $event['reason'] ?? '',
                    'message' => $event['message'] ?? '',
                    'timestamp' => $event['lastTimestamp'] ?? $event['firstTimestamp'] ?? null,
                    'first_timestamp' => $event['firstTimestamp'] ?? null,
                    'last_timestamp' => $event['lastTimestamp'] ?? null,
                    'count' => $event['count'] ?? 1,
                    'source' => $event['source']['component'] ?? '',
                ];
            }, $items);
        } catch (\Exception $e) {
            Log::error('获取 Pod 事件失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'pod_name' => $podName,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * 重启 Pod（删除后由控制器重新创建）
     */
    public function restartPod(string $name): bool
    {
        return $this->deletePod($name);
    }

    /**
     * 获取 Pod 指标（如果有 metrics-server）
     */
    public function getPodMetrics(string $podName): ?array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/metrics.k8s.io/v1beta1/namespaces/{$this->workspace->namespace}/pods/{$podName}");

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'timestamp' => $data['timestamp'] ?? null,
                    'window' => $data['window'] ?? null,
                    'containers' => array_map(function ($container) {
                        return [
                            'name' => $container['name'] ?? '',
                            'usage' => [
                                'cpu' => $container['usage']['cpu'] ?? '0',
                                'memory' => $container['usage']['memory'] ?? '0',
                            ],
                        ];
                    }, $data['containers'] ?? []),
                ];
            }

            return null;
        } catch (\Exception $e) {
            Log::warning('获取 Pod 指标失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'pod_name' => $podName,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }
}
