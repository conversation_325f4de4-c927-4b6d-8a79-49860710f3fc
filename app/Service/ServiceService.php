<?php

namespace App\Service;

use App\ClusterLabel;
use App\Contracts\LoadBalancerDriverInterface;
use App\DTOs\ServiceDTO;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\Network\IpPoolExhaustedException;
use App\Exceptions\Service\ServiceConflictException;
use App\Exceptions\Service\ServiceNotFoundException;
use App\Models\PortAllocation;
use App\Models\Service;
use App\Models\Workspace;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ServiceService
{
    public function __construct(
        protected Workspace $workspace,
        protected IpPoolService $ipPoolService
    ) {}

    /**
     * 获取 Service 列表
     */
    public function getServices(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/services");

            $items = $response->json('items', []);

            // 获取数据库中的 Service 记录以补充 workload 信息，使用预加载避免 N+1 查询
            $dbServices = Service::where('workspace_id', $this->workspace->id)
                ->with(['poolIp.ipPool']) // 预加载 IP 池信息
                ->get()
                ->keyBy('name');

            return array_map(function ($item) use ($dbServices) {
                $serviceName = $item['metadata']['name'] ?? '';
                $dbService = $dbServices->get($serviceName);

                $serviceDTO = ServiceDTO::fromK8sResource($item);

                if ($dbService) {
                    // 从数据库补充 workload 信息
                    return new ServiceDTO(
                        name: $serviceDTO->name,
                        namespace: $serviceDTO->namespace,
                        serviceType: $serviceDTO->serviceType,
                        clusterIp: $serviceDTO->clusterIp,
                        externalIps: $serviceDTO->externalIps,
                        loadBalancerIp: $serviceDTO->loadBalancerIp,
                        ports: $serviceDTO->ports,
                        selector: $serviceDTO->selector,
                        externalTrafficPolicy: $serviceDTO->externalTrafficPolicy,
                        internalTrafficPolicy: $serviceDTO->internalTrafficPolicy,
                        sessionAffinity: $serviceDTO->sessionAffinity,
                        loadBalancerClass: $serviceDTO->loadBalancerClass,
                        status: $serviceDTO->status,
                        labels: $serviceDTO->labels,
                        annotations: $serviceDTO->annotations,
                        createdAt: $serviceDTO->createdAt,
                        uid: $serviceDTO->uid,
                        resourceVersion: $serviceDTO->resourceVersion,
                        workloadType: $dbService->target_workload_type,
                        workloadName: $dbService->target_workload_name,
                    );
                }

                return $serviceDTO;
            }, $items);
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('获取 Service 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取 Service 列表', $e);
        } catch (\Exception $e) {
            Log::error('获取 Service 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Service 列表失败：'.$e->getMessage());
        }
    }

    /**
     * 获取单个 Service
     */
    public function getService(string $name): ServiceDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/services/{$name}");

            $serviceResource = $response->json();
            $serviceDTO = ServiceDTO::fromK8sResource($serviceResource);

            // 从数据库获取 workload 信息，使用预加载避免 N+1 查询
            $dbService = Service::where('workspace_id', $this->workspace->id)
                ->where('name', $name)
                ->with(['poolIp.ipPool']) // 预加载 IP 池信息
                ->first();

            if ($dbService) {
                // 返回包含工作负载信息的新 DTO
                return new ServiceDTO(
                    name: $serviceDTO->name,
                    namespace: $serviceDTO->namespace,
                    serviceType: $serviceDTO->serviceType,
                    clusterIp: $serviceDTO->clusterIp,
                    externalIps: $serviceDTO->externalIps,
                    loadBalancerIp: $serviceDTO->loadBalancerIp,
                    ports: $serviceDTO->ports,
                    selector: $serviceDTO->selector,
                    sessionAffinity: $serviceDTO->sessionAffinity,
                    externalTrafficPolicy: $serviceDTO->externalTrafficPolicy,
                    internalTrafficPolicy: $serviceDTO->internalTrafficPolicy,
                    loadBalancerClass: $serviceDTO->loadBalancerClass,
                    status: $serviceDTO->status,
                    labels: $serviceDTO->labels,
                    annotations: $serviceDTO->annotations,
                    createdAt: $serviceDTO->createdAt,
                    uid: $serviceDTO->uid,
                    resourceVersion: $serviceDTO->resourceVersion,
                    workloadType: $dbService->target_workload_type,
                    workloadName: $dbService->target_workload_name,
                );
            }

            return $serviceDTO;

        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('Service 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                ]);

                throw new ServiceNotFoundException("Service '{$name}' 不存在");
            }

            Log::error('获取 Service 详情失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'service_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取 Service 详情', $e);
        } catch (\Exception $e) {
            Log::error('获取 Service 详情失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'service_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Service 详情失败：'.$e->getMessage());
        }
    }

    /**
     * 创建 Service
     */
    public function createService(array $data): ServiceDTO
    {
        return DB::transaction(function () use ($data) {
            // 检查同工作空间下是否已存在同名Service
            $existingService = Service::where('workspace_id', $this->workspace->id)
                ->where('name', $data['name'])
                ->first();

            if ($existingService) {
                throw new ServiceConflictException($data['name'], "Service '{$data['name']}' 已存在于当前工作空间");
            }

            // 验证目标工作负载是否存在并生成选择器
            $selector = $this->generateSelectorFromWorkload($data['target_workload_type'], $data['target_workload_name']);

            // 创建数据库记录
            $serviceData = [
                'workspace_id' => $this->workspace->id,
                'name' => $data['name'],
                'type' => $data['type'],
                'ports' => $data['ports'],
                'selector' => $data['selector'] ?? ['app' => $data['target_workload_name']],
                'target_workload_type' => $data['target_workload_type'],
                'target_workload_name' => $data['target_workload_name'],
                'session_affinity' => $data['session_affinity'] ?? 'None',
                'external_traffic_policy' => $data['external_traffic_policy'] ?? 'Cluster',
                'load_balancer_class' => $data['load_balancer_class'] ?? null,
                'allow_shared_ip' => $data['allow_shared_ip'] ?? false,
                'status' => 'pending',
            ];

            $service = Service::create($serviceData);

            try {
                // 处理 LoadBalancer 类型的特殊逻辑
                if ($service->isLoadBalancer()) {
                    $this->handleLoadBalancerService($service, $data);
                }

                // 构建 K8s Service 配置
                $payload = $this->buildServicePayload($service, $data);

                Log::debug('Creating K8s Service with payload:', $payload);

                // 创建 K8s Service
                $response = $this->workspace->cluster->http()
                    ->post("/api/v1/namespaces/{$this->workspace->namespace}/services", $payload);

                // 更新状态为成功
                $service->update([
                    'status' => 'active',
                    'status_message' => null,
                ]);

                Log::info('Service 创建成功', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $service->name,
                    'service_type' => $service->type,
                    'target_workload' => $data['target_workload_type'].'/'.$data['target_workload_name'],
                ]);

                $k8sServiceData = $response->json();
                $serviceDTO = ServiceDTO::fromK8sResource($k8sServiceData);

                // 返回包含 workload 信息的 DTO
                return new ServiceDTO(
                    name: $serviceDTO->name,
                    namespace: $serviceDTO->namespace,
                    serviceType: $serviceDTO->serviceType,
                    clusterIp: $serviceDTO->clusterIp,
                    externalIps: $serviceDTO->externalIps,
                    loadBalancerIp: $serviceDTO->loadBalancerIp,
                    ports: $serviceDTO->ports,
                    selector: $serviceDTO->selector,
                    sessionAffinity: $serviceDTO->sessionAffinity,
                    externalTrafficPolicy: $serviceDTO->externalTrafficPolicy,
                    internalTrafficPolicy: $serviceDTO->internalTrafficPolicy,
                    loadBalancerClass: $serviceDTO->loadBalancerClass,
                    status: $serviceDTO->status,
                    labels: $serviceDTO->labels,
                    annotations: $serviceDTO->annotations,
                    createdAt: $serviceDTO->createdAt,
                    uid: $serviceDTO->uid,
                    resourceVersion: $serviceDTO->resourceVersion,
                    workloadType: $data['target_workload_type'],
                    workloadName: $data['target_workload_name'],
                );

            } catch (\Illuminate\Http\Client\RequestException $e) {
                // 更新状态为失败
                $service->update([
                    'status' => 'failed',
                    'status_message' => $e->getMessage(),
                ]);

                // 如果分配了资源，需要清理
                if ($service->isLoadBalancer() && $service->allocated_ip) {
                    $this->ipPoolService->releasePort($service->name, $this->workspace->namespace);
                }

                if ($e->response && $e->response->status() === 409) {
                    Log::warning('Service 名称冲突', [
                        'workspace_id' => $this->workspace->id,
                        'namespace' => $this->workspace->namespace,
                        'service_name' => $service->name,
                    ]);

                    throw new ServiceConflictException($service->name);
                }

                Log::error('创建 Service 失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $service->name,
                    'error' => $e->getMessage(),
                    'response' => $e->response->body(),
                ]);

                throw new K8sConnectionException('无法创建 Service', $e);
            }
        });
    }

    /**
     * 更新 Service
     */
    public function updateService(string $name, array $data): ServiceDTO
    {
        return DB::transaction(function () use ($name, $data) {
            // 获取数据库中的 Service 记录
            $service = Service::where('workspace_id', $this->workspace->id)
                ->where('name', $name)
                ->first();

            if (! $service) {
                throw new ServiceNotFoundException($name, $this->workspace->namespace);
            }

            // 验证目标工作负载是否存在并生成选择器
            $selector = $this->generateSelectorFromWorkload($data['target_workload_type'], $data['target_workload_name']);

            try {
                // 先获取现有的 K8s Service
                $existing = $this->workspace->cluster->http()
                    ->get("/api/v1/namespaces/{$this->workspace->namespace}/services/{$name}")
                    ->json();

                // 更新数据库记录
                $service->update([
                    'ports' => $data['ports'],
                    'selector' => $selector,
                    'target_workload_type' => $data['target_workload_type'],
                    'target_workload_name' => $data['target_workload_name'],
                    'session_affinity' => $data['session_affinity'] ?? $service->session_affinity,
                    'external_traffic_policy' => $data['external_traffic_policy'] ?? $service->external_traffic_policy,
                    'load_balancer_class' => $data['load_balancer_class'] ?? $service->load_balancer_class,
                    'allow_shared_ip' => $data['allow_shared_ip'] ?? $service->allow_shared_ip,
                    'status' => 'pending',
                ]);

                // 如果是 LoadBalancer 且修改了共享设置，需要重新处理
                if ($service->isLoadBalancer()) {
                    $this->handleLoadBalancerService($service, $data, true);
                }

                // 构建更新的 payload
                $payload = $this->buildServicePayload($service, $data, $existing);

                $response = $this->workspace->cluster->http()
                    ->put("/api/v1/namespaces/{$this->workspace->namespace}/services/{$name}", $payload);

                // 更新状态为成功
                $service->update([
                    'status' => 'active',
                    'status_message' => null,
                ]);

                Log::info('Service 更新成功', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                    'target_workload' => $data['target_workload_type'].'/'.$data['target_workload_name'],
                ]);

                $k8sServiceData = $response->json();
                $serviceDTO = ServiceDTO::fromK8sResource($k8sServiceData);

                // 返回包含 workload 信息的 DTO
                return new ServiceDTO(
                    name: $serviceDTO->name,
                    namespace: $serviceDTO->namespace,
                    serviceType: $serviceDTO->serviceType,
                    clusterIp: $serviceDTO->clusterIp,
                    externalIps: $serviceDTO->externalIps,
                    loadBalancerIp: $serviceDTO->loadBalancerIp,
                    ports: $serviceDTO->ports,
                    selector: $serviceDTO->selector,
                    sessionAffinity: $serviceDTO->sessionAffinity,
                    externalTrafficPolicy: $serviceDTO->externalTrafficPolicy,
                    internalTrafficPolicy: $serviceDTO->internalTrafficPolicy,
                    loadBalancerClass: $serviceDTO->loadBalancerClass,
                    status: $serviceDTO->status,
                    labels: $serviceDTO->labels,
                    annotations: $serviceDTO->annotations,
                    createdAt: $serviceDTO->createdAt,
                    uid: $serviceDTO->uid,
                    resourceVersion: $serviceDTO->resourceVersion,
                    workloadType: $data['target_workload_type'],
                    workloadName: $data['target_workload_name'],
                );

            } catch (\Illuminate\Http\Client\RequestException $e) {
                // 更新状态为失败
                $service->update([
                    'status' => 'failed',
                    'status_message' => $e->getMessage(),
                ]);

                if ($e->response && $e->response->status() === 404) {
                    Log::warning('Service 不存在', [
                        'workspace_id' => $this->workspace->id,
                        'namespace' => $this->workspace->namespace,
                        'service_name' => $name,
                    ]);

                    throw new ServiceNotFoundException($name, $this->workspace->namespace);
                }

                Log::error('更新 Service 失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                    'error' => $e->getMessage(),
                ]);

                throw new K8sConnectionException('无法更新 Service', $e);
            } catch (\Exception $e) {
                // 更新状态为失败
                $service->update([
                    'status' => 'failed',
                    'status_message' => $e->getMessage(),
                ]);

                Log::error('更新 Service 失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                    'error' => $e->getMessage(),
                ]);

                throw new \Exception('更新 Service 失败：'.$e->getMessage());
            }
        });
    }

    /**
     * 删除 Service
     */
    public function deleteService(string $name): bool
    {
        return DB::transaction(function () use ($name) {
            try {
                // 获取数据库中的 Service 记录
                $service = Service::where('workspace_id', $this->workspace->id)
                    ->where('name', $name)
                    ->first();

                // 删除 K8s Service
                $this->workspace->cluster->http()
                    ->delete("/api/v1/namespaces/{$this->workspace->namespace}/services/{$name}");

                // 如果是 LoadBalancer，释放分配的资源
                if ($service && $service->isLoadBalancer()) {
                    if ($service->poolIp) {
                        $ipType = $service->poolIp->getIpType();
                        if ($ipType === 'IPv6' || ! $service->allow_shared_ip) {
                            // IPv6 或独享模式：直接减少使用计数
                            $service->poolIp->decrementUsage();
                        } else {
                            // IPv4 共享模式：释放端口分配
                            $this->ipPoolService->releasePort($name, $this->workspace->namespace);
                        }
                    }
                }

                // 删除数据库记录
                if ($service) {
                    $service->delete();
                }

                Log::info('Service 删除成功', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                ]);

                return true;
            } catch (\Illuminate\Http\Client\RequestException $e) {
                if ($e->response && $e->response->status() === 404) {
                    Log::warning('Service 不存在', [
                        'workspace_id' => $this->workspace->id,
                        'namespace' => $this->workspace->namespace,
                        'service_name' => $name,
                    ]);

                    // 删除数据库记录
                    if ($service) {
                        $service->delete();
                    }

                    throw new ServiceNotFoundException($name, $this->workspace->namespace);
                }

                Log::error('删除 Service 失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                    'error' => $e->getMessage(),
                ]);

                throw new K8sConnectionException('无法删除 Service', $e);
            } catch (\Exception $e) {
                Log::error('删除 Service 失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                    'error' => $e->getMessage(),
                ]);

                throw new K8sConnectionException('删除 Service 失败：'.$e->getMessage());
            }
        });
    }

    /**
     * 处理 LoadBalancer 类型 Service 的特殊逻辑
     */
    protected function handleLoadBalancerService(Service $service, array $data, bool $isUpdate = false): void
    {
        // 如果是更新且已有分配，根据情况决定是否重新分配
        if ($isUpdate && $service->allocated_ip) {
            $needReallocation = false;

            // 如果修改了共享 IP 设置，需要重新分配
            if (isset($data['allow_shared_ip']) && $data['allow_shared_ip'] !== $service->allow_shared_ip) {
                $needReallocation = true;
            }

            // 如果指定了不同的IP池，需要重新分配
            if (isset($data['ip_pool_id']) && $data['ip_pool_id'] !== $service->pool_ip_id) {
                $needReallocation = true;
            }

            // 如果端口数量发生变化，需要重新分配
            $currentPortCount = count($this->getServiceAllocatedPorts($service));
            $newPortCount = count($data['ports']);
            if ($currentPortCount !== $newPortCount) {
                $needReallocation = true;
            }

            if (! $needReallocation) {
                return; // 不需要重新分配
            }

            // 释放当前分配
            $this->ipPoolService->releasePort($service->name, $this->workspace->namespace);
        }

        // 确定 IP 分配策略
        $allowSharedIp = $data['allow_shared_ip'] ?? true;
        $ipPoolId = $data['ip_pool_id'] ?? null;

        // 分配 IP
        $poolIp = null;
        if ($ipPoolId) {
            // 指定了 IP 池，从指定池分配
            $poolIp = $this->ipPoolService->allocateFromSpecificPool($ipPoolId, $allowSharedIp);
        } else {
            // 未指定 IP 池，自动选择（优先 IPv4）
            $poolIp = $this->ipPoolService->autoAllocateIp(
                $this->workspace->cluster_id,
                $allowSharedIp
            );
        }

        if (! $poolIp) {
            $message = $allowSharedIp
                ? '集群中没有可用的共享 IP 地址'
                : '集群中没有可用的独占 IP 地址';
            throw new IpPoolExhaustedException($message);
        }

        // 根据IP池的共享策略决定端口分配策略
        $ipPool = $poolIp->ipPool;
        $ipType = $poolIp->getIpType(); // 添加IP类型定义

        if ($ipPool->isDedicated()) {
            // 独享模式：不需要端口分配，每个服务独享整个 IP
            $allocatedPorts = [];

            // 更新使用计数（标记 IP 被使用）
            $poolIp->incrementUsage();
        } else {
            // 共享模式：需要分配端口
            $portCount = count($data['ports']);
            $allocatedPorts = $this->ipPoolService->allocatePorts(
                $poolIp,
                $service->name,
                $this->workspace->namespace,
                $portCount
            );

            if (empty($allocatedPorts)) {
                throw new IpPoolExhaustedException('无法为服务分配足够的端口');
            }
        }

        // 更新 Service 记录
        $service->update([
            'pool_ip_id' => $poolIp->id,
            'allocated_ip' => $poolIp->ip_address,
            'load_balancer_class' => $poolIp->ipPool->driver,
            'allow_shared_ip' => $allowSharedIp,
        ]);

        Log::info('LoadBalancer IP 分配成功', [
            'workspace_id' => $this->workspace->id,
            'service_name' => $service->name,
            'allocated_ip' => $poolIp->ip_address,
            'ip_type' => $ipType,
            'allow_shared_ip' => $allowSharedIp,
            'allocated_ports' => $allocatedPorts,
        ]);
    }

    /**
     * 构建 Service 载荷
     */
    protected function buildServicePayload(Service $service, array $data, ?array $existing = null): array
    {
        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Service',
            'metadata' => [
                'name' => $service->name,
                'namespace' => $this->workspace->namespace,
                'labels' => array_merge(
                    $this->workspace->buildDefaultLabels(null, $service->name),
                    [
                        ClusterLabel::WORKLOAD_TYPE->value => ClusterLabel::WORKLOAD_TYPE_SERVICE->value,
                        ClusterLabel::WORKLOAD_NAME->value => $service->name,
                        ClusterLabel::WORKLOAD_TYPE->value.'-target' => $service->target_workload_type,
                        ClusterLabel::WORKLOAD_NAME->value.'-target' => $service->target_workload_name,
                    ],
                    $data['labels'] ?? []
                ),
                'annotations' => [],
            ],
            'spec' => [
                'type' => $service->type,
                'ports' => $this->buildPorts($data['ports'], $service),
                'selector' => $service->selector,
                'sessionAffinity' => $service->session_affinity,
            ],
        ];

        // 添加 LoadBalancer 特定配置
        if ($service->isLoadBalancer()) {
            $payload['metadata']['annotations'] = array_merge(
                $payload['metadata']['annotations'],
                $this->buildLoadBalancerAnnotations($service)
            );

            if ($service->external_traffic_policy) {
                $payload['spec']['externalTrafficPolicy'] = $service->external_traffic_policy;
            }

            if ($loadBalancerClass = $this->driver($service)->getLoadBalancerClassName($service)) {
                $payload['spec']['loadBalancerClass'] = $loadBalancerClass;
            }

            $payload['spec']['allocateLoadBalancerNodePorts'] = false;

            // IP 版本
            $ipFamily = $service->poolIp->ipPool->ip_version;
            if ($ipFamily === 'ipv4') {
                $ipFamily = 'IPv4';
            } elseif ($ipFamily === 'ipv6') {
                $ipFamily = 'IPv6';
            }

            $payload['spec']['ipFamilies'] = [$ipFamily];
        }

        // 添加 NodePort 特定配置
        if ($service->isNodePort()) {
            if ($service->external_traffic_policy) {
                $payload['spec']['externalTrafficPolicy'] = $service->external_traffic_policy;
            }
        }

        if (empty($payload['metadata']['annotations'])) {
            $payload['metadata']['annotations'] = new \stdClass;
        }

        return $payload;
    }

    /**
     * 构建端口配置
     */
    protected function buildPorts(array $ports, Service $service): array
    {
        return array_map(function ($port, $index) use ($service) {
            $portConfig = [
                'name' => $port['name'] ?? 'port-'.($index + 1),
                'protocol' => $port['protocol'] ?? 'TCP',
                'targetPort' => is_numeric($port['target_port']) ? (int) $port['target_port'] : $port['target_port'],
            ];

            // 根据服务类型设置端口
            if ($service->isLoadBalancer()) {
                // 检查是否为 IPv6 或独享模式
                $isIpv6OrExclusive = false;
                if ($service->poolIp) {
                    $ipType = $service->poolIp->getIpType();
                    $isIpv6OrExclusive = ($ipType === 'IPv6') || ! $service->allow_shared_ip;
                }

                if ($isIpv6OrExclusive) {
                    // IPv6 或独享模式：使用用户指定的端口
                    $portConfig['port'] = (int) $port['port'];
                } else {
                    // IPv4 共享模式：使用分配的端口
                    $allocatedPorts = $this->getServiceAllocatedPorts($service);
                    if (! empty($allocatedPorts) && isset($allocatedPorts[$index])) {
                        $portConfig['port'] = $allocatedPorts[$index];
                    } else {
                        // 如果没有分配端口（创建时），使用用户指定的端口作为占位符
                        $portConfig['port'] = (int) $port['port'];
                    }
                }
            } elseif ($service->isNodePort()) {
                // NodePort 使用用户指定的端口，但不设置 nodePort（让 K8s 自动分配）
                $portConfig['port'] = (int) $port['port'];
                // nodePort 会由 Kubernetes 自动分配，范围通常是 30000-32767
            } else {
                // ClusterIP 使用用户指定的端口
                $portConfig['port'] = (int) $port['port'];
            }

            return $portConfig;
        }, $ports, array_keys($ports));
    }

    /**
     * 获取服务的分配端口列表
     */
    protected function getServiceAllocatedPorts(Service $service): array
    {
        return PortAllocation::where('service_name', $service->name)
            ->where('namespace', $this->workspace->namespace)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->orderBy('port')
            ->pluck('port')
            ->toArray();
    }

    /**
     * 构建 LoadBalancer 注解
     */
    protected function buildLoadBalancerAnnotations(Service $service): array
    {
        if (! $service->isLoadBalancer() || ! $service->poolIp || ! $service->poolIp->ipPool) {
            return [];
        }

        return $this->driver($service)->getServiceAnnotations($service);
    }

    public function driver(Service $service): LoadBalancerDriverInterface
    {
        $manager = new \App\Service\LoadBalancerManager;
        $driver = $manager->driver($service->poolIp->ipPool->driver);

        return $driver;
    }

    /**
     * 根据目标工作负载生成标签选择器
     */
    protected function generateSelectorFromWorkload(string $workloadType, string $workloadName): array
    {
        try {
            if ($workloadType === 'Deployment') {
                // 验证 Deployment 是否存在
                $this->workspace->cluster->http()
                    ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments/{$workloadName}");
            } elseif ($workloadType === 'StatefulSet') {
                // 验证 StatefulSet 是否存在
                $this->workspace->cluster->http()
                    ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$workloadName}");
            } else {
                throw new \Exception("不支持的工作负载类型: {$workloadType}");
            }

            // 根据标准规范生成选择器
            return ['app' => $workloadName];

        } catch (\Exception $e) {
            if (str_contains($e->getMessage(), '404')) {
                throw new \Exception("{$workloadType} '{$workloadName}' 不存在");
            }
            throw $e;
        }
    }

    /**
     * 验证选择器对应的 Pod 是否存在（保留方法但修改实现）
     */
    protected function validateSelector(array $selector): void
    {
        if (empty($selector)) {
            return; // 允许空选择器
        }

        try {
            // 构建标签选择器查询字符串
            $labelSelector = [];
            foreach ($selector as $key => $value) {
                $labelSelector[] = "{$key}={$value}";
            }
            $labelSelectorStr = implode(',', $labelSelector);

            // 查询匹配的 Pod
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/pods", [
                    'labelSelector' => $labelSelectorStr,
                ]);

            $pods = $response->json('items', []);

            if (empty($pods)) {
                Log::warning('Service 选择器未匹配到任何 Pod', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'selector' => $selector,
                ]);
            }

        } catch (\Exception $e) {
            Log::warning('验证 Service 选择器失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'selector' => $selector,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
