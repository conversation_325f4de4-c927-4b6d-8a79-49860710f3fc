<?php

namespace App\Service;

use App\ClusterLabel;
use App\DTOs\ConfigMapDTO;
use App\Exceptions\ConfigMap\ConfigMapNotFoundException;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\K8s\ResourceConflictException;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class ConfigMapService
{
    public function __construct(
        protected Workspace $workspace
    ) {}

    /**
     * 获取 ConfigMap 列表
     */
    public function getConfigMaps(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/configmaps");

            $items = $response->json('items', []);

            $items = array_filter($items, function ($item) {
                // 如果为 kube-root-ca.crt 则跳过
                return $item['metadata']['name'] !== 'kube-root-ca.crt';
            });

            return array_map(function ($item) {
                return ConfigMapDTO::fromK8sResource($item);
            }, $items);
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('获取 ConfigMap 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取 ConfigMap 列表', $e);
        } catch (\Exception $e) {
            Log::error('获取 ConfigMap 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 ConfigMap 列表失败：'.$e->getMessage());
        }
    }

    /**
     * 获取单个 ConfigMap
     */
    public function getConfigMap(string $name): ConfigMapDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/configmaps/{$name}");

            return ConfigMapDTO::fromK8sResource($response->json());
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('ConfigMap 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'configmap_name' => $name,
                ]);

                throw new ConfigMapNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('获取 ConfigMap 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'configmap_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取 ConfigMap 信息', $e);
        } catch (\Exception $e) {
            Log::error('获取 ConfigMap 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'configmap_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 ConfigMap 失败：'.$e->getMessage());
        }
    }

    /**
     * 创建 ConfigMap
     */
    public function createConfigMap(string $name, array $data = [], array $binaryData = [], ?string $workloadType = null, ?string $workloadName = null, array $customLabels = []): ConfigMapDTO
    {
        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'ConfigMap',
            'metadata' => [
                'name' => $name,
                'namespace' => $this->workspace->namespace,
                'labels' => array_merge(
                    $this->workspace->buildDefaultLabels(null, $name),
                    $this->buildWorkloadLabels($workloadType, $workloadName),
                    $customLabels
                ),
            ],
            'data' => $data,
        ];

        // 如果有二进制数据，添加到 payload 中
        if (! empty($binaryData)) {
            $encodedBinaryData = [];
            foreach ($binaryData as $key => $value) {
                $encodedBinaryData[$key] = base64_encode($value);
            }
            $payload['binaryData'] = $encodedBinaryData;
        }

        try {
            $response = $this->workspace->cluster->http()
                ->post("/api/v1/namespaces/{$this->workspace->namespace}/configmaps", $payload);

            Log::info('ConfigMap 创建成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'configmap_name' => $name,
            ]);

            return ConfigMapDTO::fromK8sResource($response->json());
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 409) {
                Log::warning('ConfigMap 名称冲突', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'configmap_name' => $name,
                ]);

                throw new ResourceConflictException("ConfigMap '{$name}' 已存在");
            }

            Log::error('创建 ConfigMap 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'configmap_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法创建 ConfigMap', $e);
        } catch (\Exception $e) {
            Log::error('创建 ConfigMap 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'configmap_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('创建 ConfigMap 失败：'.$e->getMessage());
        }
    }

    /**
     * 更新 ConfigMap
     */
    public function updateConfigMap(string $name, array $data = [], array $binaryData = []): ConfigMapDTO
    {
        try {
            // 先获取现有的 ConfigMap
            $existing = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/configmaps/{$name}")
                ->json();

            $payload = [
                'apiVersion' => 'v1',
                'kind' => 'ConfigMap',
                'metadata' => $existing['metadata'],
                'data' => $data,
            ];

            // 如果有二进制数据，添加到 payload 中
            if (! empty($binaryData)) {
                $encodedBinaryData = [];
                foreach ($binaryData as $key => $value) {
                    $encodedBinaryData[$key] = base64_encode($value);
                }
                $payload['binaryData'] = $encodedBinaryData;
            }

            $response = $this->workspace->cluster->http()
                ->put("/api/v1/namespaces/{$this->workspace->namespace}/configmaps/{$name}", $payload);

            Log::info('ConfigMap 更新成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'configmap_name' => $name,
            ]);

            return ConfigMapDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('更新 ConfigMap 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'configmap_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('更新 ConfigMap 失败：'.$e->getMessage());
        }
    }

    /**
     * 删除 ConfigMap
     */
    public function deleteConfigMap(string $name): bool
    {
        try {
            $this->workspace->cluster->http()
                ->delete("/api/v1/namespaces/{$this->workspace->namespace}/configmaps/{$name}");

            Log::info('ConfigMap 删除成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'configmap_name' => $name,
            ]);

            return true;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('ConfigMap 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'configmap_name' => $name,
                ]);

                throw new ConfigMapNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('删除 ConfigMap 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'configmap_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法删除 ConfigMap', $e);
        } catch (\Exception $e) {
            Log::error('删除 ConfigMap 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'configmap_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('删除 ConfigMap 失败：'.$e->getMessage());
        }
    }

    /**
     * 从文件创建 ConfigMap
     */
    public function createConfigMapFromFiles(string $name, array $files, ?string $workloadType = null, ?string $workloadName = null, array $customLabels = []): ConfigMapDTO
    {
        $data = [];
        $binaryData = [];

        foreach ($files as $filename => $content) {
            // 检查是否为二进制文件
            if ($this->isBinaryContent($content)) {
                $binaryData[$filename] = $content;
            } else {
                $data[$filename] = $content;
            }
        }

        return $this->createConfigMap($name, $data, $binaryData, $workloadType, $workloadName, $customLabels);
    }

    /**
     * 检查内容是否为二进制
     */
    protected function isBinaryContent(string $content): bool
    {
        // 简单的二进制检测：如果包含空字节，认为是二进制
        return strpos($content, "\0") !== false;
    }

    /**
     * 构建工作负载关联标签
     */
    protected function buildWorkloadLabels(?string $workloadType, ?string $workloadName): array
    {
        $labels = [];

        if ($workloadType && $workloadName) {
            $labels[ClusterLabel::WORKLOAD_TYPE->value] = strtolower($workloadType);
            $labels[ClusterLabel::WORKLOAD_NAME->value] = $workloadName;
        }

        return $labels;
    }
}
