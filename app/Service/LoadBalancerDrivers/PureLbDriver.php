<?php

namespace App\Service\LoadBalancerDrivers;

use App\Contracts\LoadBalancerDriverInterface;
use App\Models\IpPool;
use App\Models\Service;
use Illuminate\Support\Facades\Log;

class PureLbDriver implements LoadBalancerDriverInterface
{
    protected const PURELB_NAMESPACE = 'purelb';

    public function syncIpPool(IpPool $ipPool): void
    {
        $this->createOrUpdateServiceGroup($ipPool);
    }

    public function deleteIpPool(IpPool $ipPool): void
    {
        $this->deleteServiceGroup($ipPool);
    }

    public function getServiceAnnotations(Service $service): array
    {
        $annotations = [];

        if ($service->allocated_ip) {
            // 设置手动指定的 IP 地址
            $annotations['purelb.io/addresses'] = $service->allocated_ip;

            // 设置 service group（IP 池名称）
            if ($service->poolIp && $service->poolIp->ipPool) {
                $annotations['purelb.io/service-group'] = $service->poolIp->ipPool->name;
            }

            // 设置 IP 共享（基于 IP 池的共享策略和服务的共享设置）
            if ($service->poolIp && $service->poolIp->ipPool) {
                $ipPool = $service->poolIp->ipPool;
                $ipType = $service->poolIp->getIpType();

                // 只有在 IP 池为共享模式且服务允许共享且为 IPv4 时才设置共享
                if ($ipPool->isShared() && $service->allow_shared_ip && $ipType === 'IPv4') {
                    $sharingKey = 'share-ip-'.str_replace('.', '-', $service->allocated_ip);
                    $annotations['purelb.io/allow-shared-ip'] = $sharingKey;
                }
            }
        }

        return $annotations;
    }

    public function getLoadBalancerClassName(Service $service): ?string
    {
        return 'purelb.io/purelb';
    }

    protected function createOrUpdateServiceGroup(IpPool $ipPool): void
    {
        $cluster = $ipPool->cluster;
        $poolName = $ipPool->name;

        // 构建 ServiceGroup 规格
        $serviceGroupSpec = [
            'local' => [],
        ];

        // 根据 IP 版本添加对应的池配置
        if ($ipPool->supportsIpv4()) {
            $ipv4Addresses = $this->buildIpPoolAddresses($ipPool, 'ipv4');
            if (! empty($ipv4Addresses)) {
                $serviceGroupSpec['local']['v4pool'] = [
                    'subnet' => $this->buildIpPoolSubnet($ipPool, 'ipv4'),
                    'pool' => $ipv4Addresses,
                    'aggregation' => 'default',
                ];
            }
        }

        if ($ipPool->supportsIpv6()) {
            $ipv6Addresses = $this->buildIpPoolAddresses($ipPool, 'ipv6');
            if (! empty($ipv6Addresses)) {
                $serviceGroupSpec['local']['v6pool'] = [
                    'subnet' => $this->buildIpPoolSubnet($ipPool, 'ipv6'),
                    'pool' => $ipv6Addresses,
                    'aggregation' => 'default',
                ];
            }
        }

        $serviceGroupResource = [
            'apiVersion' => 'purelb.io/v1',
            'kind' => 'ServiceGroup',
            'metadata' => [
                'name' => $poolName,
                'namespace' => self::PURELB_NAMESPACE,
            ],
            'spec' => $serviceGroupSpec,
        ];

        try {
            $existing = $cluster->http()->get('/apis/purelb.io/v1/namespaces/'.self::PURELB_NAMESPACE."/servicegroups/{$poolName}");
            if ($existing->successful()) {
                $cluster->http()->put('/apis/purelb.io/v1/namespaces/'.self::PURELB_NAMESPACE."/servicegroups/{$poolName}", $serviceGroupResource);
                Log::info('PureLB ServiceGroup updated successfully', ['pool_name' => $poolName]);
            } else {
                throw new \Exception('Failed to get existing ServiceGroup, status: '.$existing->status());
            }
        } catch (\Exception $e) {
            if ($e instanceof \Illuminate\Http\Client\RequestException && $e->response->status() === 404) {
                // Not found, so create it
                $cluster->http()->post('/apis/purelb.io/v1/namespaces/'.self::PURELB_NAMESPACE.'/servicegroups', $serviceGroupResource);
                Log::info('PureLB ServiceGroup created successfully', ['pool_name' => $poolName]);
            } else {
                Log::error('Failed to sync PureLB ServiceGroup', ['pool_name' => $poolName, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to sync PureLB ServiceGroup: '.$e->getMessage());
            }
        }
    }

    protected function deleteServiceGroup(IpPool $ipPool): void
    {
        try {
            $ipPool->cluster->http()->delete('/apis/purelb.io/v1/namespaces/'.self::PURELB_NAMESPACE."/servicegroups/{$ipPool->name}");
            Log::info('PureLB ServiceGroup deleted successfully', ['pool_name' => $ipPool->name]);
        } catch (\Exception $e) {
            // Ignore if not found, log other errors
            if ($e->getCode() !== 404) {
                Log::error('Failed to delete PureLB ServiceGroup', ['pool_name' => $ipPool->name, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to delete PureLB ServiceGroup: '.$e->getMessage());
            }
        }
    }

    protected function buildIpPoolSubnet(IpPool $ipPool, string $ipFamily = 'ipv4'): string
    {
        if ($ipFamily === 'ipv6') {
            return $ipPool->subnet_v6 ?? '2001:db8::/64'; // 默认 IPv6 子网
        } else {
            // IPv4 逻辑保持不变
            $firstIp = $ipPool->poolIps()->where('is_active', true)->get()->first(function ($poolIp) {
                return $poolIp->getIpType() === 'IPv4';
            });

            if (! $firstIp) {
                return $ipPool->subnet_v4 ?? '***********/24'; // 默认 IPv4 子网
            }

            // 简单地假设是/24子网，实际应用中可能需要更复杂的逻辑
            $ipParts = explode('.', $firstIp->ip_address);

            return $ipParts[0].'.'.$ipParts[1].'.'.$ipParts[2].'.0/24';
        }
    }

    protected function buildIpPoolAddresses(IpPool $ipPool, ?string $ipFamily = null): string
    {
        // 获取指定 IP 版本的地址
        $ips = $ipPool->poolIps()->where('is_active', true)->get();

        if ($ipFamily) {
            $ips = $ips->filter(function ($poolIp) use ($ipFamily) {
                $ipType = $poolIp->getIpType();

                return ($ipFamily === 'ipv4' && $ipType === 'IPv4') ||
                       ($ipFamily === 'ipv6' && $ipType === 'IPv6');
            });
        }

        $ipAddresses = $ips->pluck('ip_address')->toArray();

        if (empty($ipAddresses)) {
            return '';
        }

        // 对于 IPv6，不能使用 ip2long，需要特殊处理
        if ($ipFamily === 'ipv6') {
            // IPv6 地址直接返回，不进行范围合并
            return implode(',', $ipAddresses);
        }

        // IPv4 地址排序和范围合并
        usort($ipAddresses, fn ($a, $b) => ip2long($a) <=> ip2long($b));

        // PureLB 支持范围格式 "start-end" 或单个IP
        $ranges = [];
        $start = $ipAddresses[0];
        $end = $ipAddresses[0];

        for ($i = 1; $i < count($ipAddresses); $i++) {
            if (ip2long($ipAddresses[$i]) == ip2long($end) + 1) {
                $end = $ipAddresses[$i];
            } else {
                if ($start === $end) {
                    $ranges[] = $start;
                } else {
                    $ranges[] = "$start-$end";
                }
                $start = $ipAddresses[$i];
                $end = $ipAddresses[$i];
            }
        }

        if ($start === $end) {
            $ranges[] = $start;
        } else {
            $ranges[] = "$start-$end";
        }

        return implode(',', $ranges);
    }
}
