<?php

namespace App\Service\LoadBalancerDrivers;

use App\Contracts\LoadBalancerDriverInterface;
use App\Models\IpPool;
use App\Models\Service;
use Illuminate\Support\Facades\Log;

class MetallbDriver implements LoadBalancerDriverInterface
{
    protected const METALLB_NAMESPACE = 'metallb-system';

    public function syncIpPool(IpPool $ipPool): void
    {
        $this->createOrUpdateIpAddressPool($ipPool);
        $this->createOrUpdateL2Advertisement($ipPool);
    }

    public function deleteIpPool(IpPool $ipPool): void
    {
        $this->deleteL2Advertisement($ipPool);
        $this->deleteIpAddressPool($ipPool);
    }

    public function getServiceAnnotations(Service $service): array
    {
        $annotations = [];

        if ($service->allocated_ip) {
            $annotations['metallb.io/loadBalancerIPs'] = $service->allocated_ip;

            if ($service->poolIp && $service->poolIp->ipPool) {
                $annotations['metallb.io/ip-address-pool'] = $service->poolIp->ipPool->name;
            }

            // 设置 IP 共享（基于 IP 池的共享策略和服务的共享设置）
            if ($service->poolIp && $service->poolIp->ipPool) {
                $ipPool = $service->poolIp->ipPool;
                $ipType = $service->poolIp->getIpType();

                // 只有在 IP 池为共享模式且服务允许共享且为 IPv4 时才设置共享
                if ($ipPool->isShared() && $service->allow_shared_ip && $ipType === 'IPv4') {
                    $sharingKey = 'share-ip-'.str_replace(['.', ':'], '-', $service->allocated_ip);
                    $annotations['metallb.io/allow-shared-ip'] = $sharingKey;
                }
            }
        }

        return $annotations;
    }

    public function getLoadBalancerClassName(Service $service): ?string
    {
        return null;
    }

    protected function createOrUpdateIpAddressPool(IpPool $ipPool): void
    {
        $cluster = $ipPool->cluster;
        $poolName = $ipPool->name;
        $ipAddressPoolResource = [
            'apiVersion' => 'metallb.io/v1beta1',
            'kind' => 'IPAddressPool',
            'metadata' => [
                'name' => $poolName,
                'namespace' => self::METALLB_NAMESPACE,
            ],
            'spec' => [
                'addresses' => $this->buildIpPoolRanges($ipPool),
            ],
        ];

        try {
            $existing = $cluster->http()->get('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/ipaddresspools/{$poolName}");
            if ($existing->successful()) {
                $cluster->http()->put('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/ipaddresspools/{$poolName}", $ipAddressPoolResource);
                Log::info('Metallb IPAddressPool updated successfully', ['pool_name' => $poolName]);
            } else {
                throw new \Exception('Failed to get existing IPAddressPool, status: '.$existing->status());
            }
        } catch (\Exception $e) {
            if ($e instanceof \Illuminate\Http\Client\RequestException && $e->response->status() === 404) {
                // Not found, so create it
                $cluster->http()->post('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE.'/ipaddresspools', $ipAddressPoolResource);
                Log::info('Metallb IPAddressPool created successfully', ['pool_name' => $poolName]);
            } else {
                Log::error('Failed to sync Metallb IPAddressPool', ['pool_name' => $poolName, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to sync Metallb IPAddressPool: '.$e->getMessage());
            }
        }
    }

    protected function createOrUpdateL2Advertisement(IpPool $ipPool): void
    {
        $cluster = $ipPool->cluster;
        $poolName = $ipPool->name;
        $adName = "{$poolName}-l2-advertisement";

        $l2AdvertisementResource = [
            'apiVersion' => 'metallb.io/v1beta1',
            'kind' => 'L2Advertisement',
            'metadata' => [
                'name' => $adName,
                'namespace' => self::METALLB_NAMESPACE,
            ],
            'spec' => [
                'ipAddressPools' => [$poolName],
            ],
        ];

        try {
            $existing = $cluster->http()->get('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/l2advertisements/{$adName}");
            if ($existing->successful()) {
                $cluster->http()->put('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/l2advertisements/{$adName}", $l2AdvertisementResource);
                Log::info('Metallb L2Advertisement updated successfully', ['pool_name' => $poolName]);
            } else {
                throw new \Exception('Failed to get existing L2Advertisement, status: '.$existing->status());
            }
        } catch (\Exception $e) {
            if ($e instanceof \Illuminate\Http\Client\RequestException && $e->response->status() === 404) {
                // Not found, so create it
                $cluster->http()->post('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE.'/l2advertisements', $l2AdvertisementResource);
                Log::info('Metallb L2Advertisement created successfully', ['pool_name' => $poolName]);
            } else {
                Log::error('Failed to sync Metallb L2Advertisement', ['pool_name' => $poolName, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to sync Metallb L2Advertisement: '.$e->getMessage());
            }
        }
    }

    protected function deleteIpAddressPool(IpPool $ipPool): void
    {
        try {
            $ipPool->cluster->http()->delete('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/ipaddresspools/{$ipPool->name}");
            Log::info('Metallb IPAddressPool deleted successfully', ['pool_name' => $ipPool->name]);
        } catch (\Exception $e) {
            // Ignore if not found, log other errors
            if ($e->getCode() !== 404) {
                Log::error('Failed to delete Metallb IPAddressPool', ['pool_name' => $ipPool->name, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to delete Metallb IPAddressPool: '.$e->getMessage());
            }
        }
    }

    protected function deleteL2Advertisement(IpPool $ipPool): void
    {
        $adName = "{$ipPool->name}-l2-advertisement";
        try {
            $ipPool->cluster->http()->delete('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/l2advertisements/{$adName}");
            Log::info('Metallb L2Advertisement deleted successfully', ['pool_name' => $ipPool->name]);
        } catch (\Exception $e) {
            if ($e->getCode() !== 404) {
                Log::error('Failed to delete Metallb L2Advertisement', ['pool_name' => $ipPool->name, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to delete Metallb L2Advertisement: '.$e->getMessage());
            }
        }
    }

    protected function buildIpPoolRanges(IpPool $ipPool): array
    {
        $ips = $ipPool->poolIps()->where('is_active', true)->get();
        if ($ips->isEmpty()) {
            return [];
        }

        // 分别处理 IPv4 和 IPv6 地址
        $ipv4Addresses = [];
        $ipv6Addresses = [];

        foreach ($ips as $poolIp) {
            $ipType = $poolIp->getIpType();
            if ($ipType === 'IPv4') {
                $ipv4Addresses[] = $poolIp->ip_address;
            } elseif ($ipType === 'IPv6') {
                $ipv6Addresses[] = $poolIp->ip_address;
            }
        }

        $ranges = [];

        // 处理 IPv4 地址
        if (! empty($ipv4Addresses)) {
            usort($ipv4Addresses, fn ($a, $b) => ip2long($a) <=> ip2long($b));
            $ranges = array_merge($ranges, $this->buildIpv4Ranges($ipv4Addresses));
        }

        // 处理 IPv6 地址
        if (! empty($ipv6Addresses)) {
            $ranges = array_merge($ranges, $this->buildIpv6Ranges($ipv6Addresses));
        }

        return $ranges;
    }

    protected function buildIpv4Ranges(array $ips): array
    {
        $ranges = [];
        if (empty($ips)) {
            return $ranges;
        }

        $start = $ips[0];
        $end = $ips[0];

        for ($i = 1; $i < count($ips); $i++) {
            if (ip2long($ips[$i]) == ip2long($end) + 1) {
                $end = $ips[$i];
            } else {
                if ($start === $end) {
                    $ranges[] = $start.'/32';
                } else {
                    $ranges[] = "$start-$end";
                }
                $start = $ips[$i];
                $end = $ips[$i];
            }
        }

        if ($start === $end) {
            $ranges[] = $start.'/32';
        } else {
            $ranges[] = "$start-$end";
        }

        return $ranges;
    }

    protected function buildIpv6Ranges(array $ips): array
    {
        $ranges = [];

        // 对于 IPv6，暂时不进行范围合并，直接作为单个地址处理
        foreach ($ips as $ip) {
            $ranges[] = $ip.'/128';
        }

        return $ranges;
    }
}
