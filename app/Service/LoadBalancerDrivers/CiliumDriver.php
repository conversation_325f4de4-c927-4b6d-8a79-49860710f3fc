<?php

namespace App\Service\LoadBalancerDrivers;

use App\Contracts\LoadBalancerDriverInterface;
use App\Models\IpPool;
use App\Models\Service;
use Illuminate\Support\Facades\Log;

class CiliumDriver implements LoadBalancerDriverInterface
{
    public function syncIpPool(IpPool $ipPool): void
    {
        $this->createOrUpdateCiliumLoadBalancerIPPool($ipPool);
    }

    public function deleteIpPool(IpPool $ipPool): void
    {
        $this->deleteCiliumLoadBalancerIPPool($ipPool);
    }

    public function getServiceAnnotations(Service $service): array
    {
        $annotations = [];

        if ($service->allocated_ip) {
            // 使用 Cilium 的 IP 请求注解
            $annotations['lbipam.cilium.io/ips'] = $service->allocated_ip;

            // 设置 IP 共享（基于 IP 池的共享策略和服务的共享设置）
            if ($service->poolIp && $service->poolIp->ipPool) {
                $ipPool = $service->poolIp->ipPool;
                $ipType = $service->poolIp->getIpType();

                // 只有在 IP 池为共享模式且服务允许共享且为 IPv4 时才设置共享
                if ($ipPool->isShared() && $service->allow_shared_ip && $ipType === 'IPv4') {
                    $sharingKey = 'share-ip-'.str_replace(['.', ':'], '-', $service->allocated_ip);
                    $annotations['lbipam.cilium.io/sharing-key'] = $sharingKey;

                    // 允许跨命名空间共享 IP，设置为 * 允许所有命名空间
                    $annotations['lbipam.cilium.io/sharing-cross-namespace'] = '*';
                }
            }
        }

        return $annotations;
    }

    public function getLoadBalancerClassName(Service $service): ?string
    {
        // 返回 Cilium BGP Control Plane 的 LoadBalancer 类名
        // return 'io.cilium/io.cilium/l2-announcer';
        return null;
    }

    protected function createOrUpdateCiliumLoadBalancerIPPool(IpPool $ipPool): void
    {
        $cluster = $ipPool->cluster;
        $poolName = $ipPool->name;

        $ciliumIPPoolResource = [
            'apiVersion' => 'cilium.io/v2alpha1',
            'kind' => 'CiliumLoadBalancerIPPool',
            'metadata' => [
                'name' => $poolName,
            ],
            'spec' => [
                'blocks' => $this->buildIpPoolBlocks($ipPool),
            ],
        ];

        try {
            $existing = $cluster->http()->get("/apis/cilium.io/v2alpha1/ciliumloadbalancerippools/{$poolName}");
            if ($existing->successful()) {
                $cluster->http()->put("/apis/cilium.io/v2alpha1/ciliumloadbalancerippools/{$poolName}", $ciliumIPPoolResource);
                Log::info('Cilium LoadBalancer IP Pool updated successfully', ['pool_name' => $poolName]);
            } else {
                throw new \Exception('Failed to get existing CiliumLoadBalancerIPPool, status: '.$existing->status());
            }
        } catch (\Exception $e) {
            if ($e instanceof \Illuminate\Http\Client\RequestException && $e->response->status() === 404) {
                // Not found, so create it
                $cluster->http()->post('/apis/cilium.io/v2alpha1/ciliumloadbalancerippools', $ciliumIPPoolResource);
                Log::info('Cilium LoadBalancer IP Pool created successfully', ['pool_name' => $poolName]);
            } else {
                Log::error('Failed to sync Cilium LoadBalancer IP Pool', ['pool_name' => $poolName, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to sync Cilium LoadBalancer IP Pool: '.$e->getMessage());
            }
        }
    }

    protected function deleteCiliumLoadBalancerIPPool(IpPool $ipPool): void
    {
        try {
            $ipPool->cluster->http()->delete("/apis/cilium.io/v2alpha1/ciliumloadbalancerippools/{$ipPool->name}");
            Log::info('Cilium LoadBalancer IP Pool deleted successfully', ['pool_name' => $ipPool->name]);
        } catch (\Exception $e) {
            // Ignore if not found, log other errors
            if ($e->getCode() !== 404) {
                Log::error('Failed to delete Cilium LoadBalancer IP Pool', ['pool_name' => $ipPool->name, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to delete Cilium LoadBalancer IP Pool: '.$e->getMessage());
            }
        }
    }

    protected function buildIpPoolBlocks(IpPool $ipPool): array
    {
        $ips = $ipPool->poolIps()->where('is_active', true)->get();
        if ($ips->isEmpty()) {
            return [];
        }

        // 分别处理 IPv4 和 IPv6 地址
        $ipv4Addresses = [];
        $ipv6Addresses = [];

        foreach ($ips as $poolIp) {
            $ipType = $poolIp->getIpType();
            if ($ipType === 'IPv4') {
                $ipv4Addresses[] = $poolIp->ip_address;
            } elseif ($ipType === 'IPv6') {
                $ipv6Addresses[] = $poolIp->ip_address;
            }
        }

        $blocks = [];

        // 处理 IPv4 地址
        if (! empty($ipv4Addresses)) {
            usort($ipv4Addresses, fn ($a, $b) => ip2long($a) <=> ip2long($b));
            $blocks = array_merge($blocks, $this->buildIpv4Blocks($ipv4Addresses));
        }

        // 处理 IPv6 地址
        if (! empty($ipv6Addresses)) {
            $blocks = array_merge($blocks, $this->buildIpv6Blocks($ipv6Addresses));
        }

        return $blocks;
    }

    protected function buildIpv4Blocks(array $ips): array
    {
        $blocks = [];
        if (empty($ips)) {
            return $blocks;
        }

        $start = $ips[0];
        $end = $ips[0];

        for ($i = 1; $i < count($ips); $i++) {
            if (ip2long($ips[$i]) == ip2long($end) + 1) {
                $end = $ips[$i];
            } else {
                // 添加当前范围
                if ($start === $end) {
                    $blocks[] = ['start' => $start, 'stop' => $start];
                } else {
                    $blocks[] = ['start' => $start, 'stop' => $end];
                }
                $start = $ips[$i];
                $end = $ips[$i];
            }
        }

        // 添加最后一个范围
        if ($start === $end) {
            $blocks[] = ['start' => $start, 'stop' => $start];
        } else {
            $blocks[] = ['start' => $start, 'stop' => $end];
        }

        return $blocks;
    }

    protected function buildIpv6Blocks(array $ips): array
    {
        $blocks = [];

        // 对于 IPv6，暂时不进行范围合并，直接作为单个地址处理
        foreach ($ips as $ip) {
            $blocks[] = ['start' => $ip, 'stop' => $ip];
        }

        return $blocks;
    }
}
