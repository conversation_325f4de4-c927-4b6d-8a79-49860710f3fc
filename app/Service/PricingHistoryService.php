<?php

namespace App\Service;

use App\Models\ResourcePricing;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class PricingHistoryService
{
    /**
     * 获取资源的价格历史数据
     */
    public function getPricingHistory(Model $resource, string $period, int $limit): array
    {
        // 计算时间范围
        $endTime = Carbon::now();
        $startTime = $this->calculateStartTime($endTime, $period);

        // 查询历史记录
        $histories = ResourcePricing::forResource($resource)
            ->where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime)
            ->orderBy('created_at', 'asc')
            ->limit($limit)
            ->get();

        // 如果没有历史记录，返回空数据
        if ($histories->isEmpty()) {
            return $this->createEmptyResult($resource, $period);
        }

        // 获取资源类型的单位类型配置
        $unitTypes = $this->getResourceUnitTypes($resource);

        // 按时间戳分组价格数据
        $pricesByTimestamp = $this->groupPricesByTimestamp($histories, $unitTypes);

        // 转换为数组并排序
        $rawData = array_values($pricesByTimestamp);
        usort($rawData, fn ($a, $b) => $a['timestamp'] <=> $b['timestamp']);

        // 转换为K线图数据格式
        $klineData = $this->convertToKLineData($rawData, $unitTypes);

        // 获取当前价格信息
        $currentPrices = $this->calculateCurrentPrices($rawData, $unitTypes);

        return [
            'resource_id' => $resource->id,
            'resource_name' => $resource->name ?? 'N/A',
            'resource_type' => $this->getResourceType($resource),
            'period' => $period,
            'kline_data' => $klineData,
            'current_prices' => $currentPrices,
            'unit_types' => $unitTypes,
            'raw_data' => $rawData, // 保留原始数据用于调试
        ];
    }

    /**
     * 获取最新价格信息
     */
    public function getLatestPricing(Model $resource): array
    {
        $pricingSummary = $resource->getPricingSummary();
        $unitTypes = $this->getResourceUnitTypes($resource);

        $pricing = [];
        foreach ($unitTypes as $unitType => $config) {
            $key = $unitType.'_price';
            $pricing[$key] = (float) ($pricingSummary[$unitType]['price_per_hour'] ?? 0);
        }

        return [
            'id' => $resource->id,
            'name' => $resource->name ?? 'N/A',
            'resource_type' => $this->getResourceType($resource),
            'pricing' => $pricing,
        ];
    }

    /**
     * 计算开始时间
     */
    private function calculateStartTime(Carbon $endTime, string $period): Carbon
    {
        return match ($period) {
            '1h' => $endTime->copy()->subHour(),
            '6h' => $endTime->copy()->subHours(6),
            '1d' => $endTime->copy()->subDay(),
            '7d' => $endTime->copy()->subDays(7),
            '30d' => $endTime->copy()->subDays(30),
            default => $endTime->copy()->subDays(7),
        };
    }

    /**
     * 获取资源类型
     */
    private function getResourceType(Model $resource): string
    {
        $modelClass = get_class($resource);
        $models = config('pricable.models', []);

        return $models[$modelClass] ?? 'unknown';
    }

    /**
     * 获取资源的单位类型配置
     */
    private function getResourceUnitTypes(Model $resource): array
    {
        $resourceType = $this->getResourceType($resource);

        return config("pricable.unit_types.{$resourceType}", []);
    }

    /**
     * 创建空结果
     */
    private function createEmptyResult(Model $resource, string $period): array
    {
        $unitTypes = $this->getResourceUnitTypes($resource);
        $emptyKlineData = [];

        foreach ($unitTypes as $unitType => $config) {
            $emptyKlineData[$unitType] = [];
        }
        $emptyKlineData['all'] = [];

        return [
            'resource_id' => $resource->id,
            'resource_name' => $resource->name ?? 'N/A',
            'resource_type' => $this->getResourceType($resource),
            'period' => $period,
            'kline_data' => $emptyKlineData,
            'current_prices' => null,
            'unit_types' => $unitTypes,
        ];
    }

    /**
     * 按时间戳分组价格数据
     */
    private function groupPricesByTimestamp($histories, array $unitTypes): array
    {
        $pricesByTimestamp = [];

        foreach ($histories as $history) {
            $timestamp = $history->created_at->timestamp * 1000; // KLineChart需要毫秒时间戳

            if (! isset($pricesByTimestamp[$timestamp])) {
                $baseData = [
                    'timestamp' => $timestamp,
                    'time' => $history->created_at->format('Y-m-d H:i:s'),
                ];

                // 动态初始化所有单位类型的价格为0
                foreach ($unitTypes as $unitType => $config) {
                    $baseData[$this->getPriceKeyForUnitType($unitType)] = 0;
                }

                $pricesByTimestamp[$timestamp] = $baseData;
            }

            // 动态设置价格
            $priceKey = $this->getPriceKeyForUnitType($history->unit_type);
            if (isset($pricesByTimestamp[$timestamp][$priceKey])) {
                $pricesByTimestamp[$timestamp][$priceKey] = (float) $history->price_per_unit_per_hour;
            }
        }

        return $pricesByTimestamp;
    }

    /**
     * 转换为K线图数据格式
     */
    private function convertToKLineData(array $rawData, array $unitTypes): array
    {
        $klineData = [];

        // 为每个单位类型生成K线数据
        foreach ($unitTypes as $unitType => $config) {
            $priceKey = $this->getPriceKeyForUnitType($unitType);
            $klineData[$unitType] = $this->generateKLineForPriceType($rawData, $priceKey);
        }

        // 生成总价格K线数据
        $klineData['all'] = $this->generateKLineForPriceType($rawData, 'all');

        return $klineData;
    }

    /**
     * 生成特定价格类型的K线数据
     */
    private function generateKLineForPriceType(array $rawData, string $priceType): array
    {
        $klineData = [];
        $previousPrice = null;

        foreach ($rawData as $data) {
            $timestamp = $data['timestamp'];

            // 获取当前价格
            $currentPrice = $priceType === 'all'
                ? $this->calculateTotalPrice($data)
                : $data[$priceType];

            // 设置开盘价（使用前一个价格作为开盘价）
            $open = $previousPrice ?? $currentPrice;
            $close = $currentPrice;

            // 模拟价格波动来创建高低价
            $volatility = 0.02; // 2% 的波动率
            $priceRange = $currentPrice * $volatility;

            $high = max($open, $close) + ($priceRange * mt_rand(0, 100) / 100);
            $low = min($open, $close) - ($priceRange * mt_rand(0, 100) / 100);

            // 确保价格不为负数
            $low = max($low, 0);

            $klineData[] = [
                'timestamp' => $timestamp,
                'open' => round($open, 6),
                'high' => round($high, 6),
                'low' => round($low, 6),
                'close' => round($close, 6),
            ];

            $previousPrice = $currentPrice;
        }

        return $klineData;
    }

    /**
     * 计算总价格
     */
    private function calculateTotalPrice(array $data): float
    {
        $total = 0;
        foreach ($data as $key => $value) {
            if (str_ends_with($key, '_price') && is_numeric($value)) {
                $total += $value;
            }
        }

        return $total;
    }

    /**
     * 计算当前价格信息
     */
    private function calculateCurrentPrices(array $rawData, array $unitTypes): ?array
    {
        if (empty($rawData)) {
            return null;
        }

        $latestData = end($rawData);
        $previousData = count($rawData) > 1 ? $rawData[count($rawData) - 2] : $latestData;

        $currentPrices = [];

        // 动态添加每个单位类型的当前价格和变化趋势
        foreach ($unitTypes as $unitType => $config) {
            $priceKey = $this->getPriceKeyForUnitType($unitType);
            $changeKey = $this->getChangeKeyForUnitType($unitType);

            $currentPrices[$priceKey] = $latestData[$priceKey] ?? 0;
            $currentPrices[$changeKey] = $this->calculateChange(
                $latestData[$priceKey] ?? 0,
                $previousData[$priceKey] ?? 0
            );
        }

        return $currentPrices;
    }

    /**
     * 获取单位类型对应的价格键名
     */
    private function getPriceKeyForUnitType(string $unitType): string
    {
        return $unitType.'_price';
    }

    /**
     * 获取单位类型对应的变化键名
     */
    private function getChangeKeyForUnitType(string $unitType): string
    {
        return $unitType.'_change';
    }

    /**
     * 计算价格变化趋势
     */
    private function calculateChange(float $current, float $previous): string
    {
        if ($current > $previous) {
            return 'up';
        } elseif ($current < $previous) {
            return 'down';
        } else {
            return 'same';
        }
    }
}
