<?php

namespace App\Service;

use App\Contracts\LoadBalancerDriverInterface;
use InvalidArgumentException;

class LoadBalancerManager
{
    private array $drivers = [];

    public function __construct()
    {
        $this->registerConfiguredDrivers();
    }

    private function registerConfiguredDrivers(): void
    {
        $drivers = config('loadbalancers.drivers', []);

        foreach ($drivers as $name => $config) {
            $driverClass = $config['class'] ?? null;

            if (! $driverClass || ! class_exists($driverClass)) {
                throw new InvalidArgumentException("LoadBalancer driver class [{$driverClass}] not found for driver [{$name}].");
            }

            $driver = new $driverClass;

            if (! $driver instanceof LoadBalancerDriverInterface) {
                throw new InvalidArgumentException("LoadBalancer driver [{$name}] must implement LoadBalancerDriverInterface.");
            }

            $this->register($name, $driver);
        }
    }

    public function register(string $name, LoadBalancerDriverInterface $driver): void
    {
        $this->drivers[$name] = $driver;
    }

    public function driver(?string $name = null): LoadBalancerDriverInterface
    {
        $name = $name ?: $this->getDefaultDriverName();

        if (! isset($this->drivers[$name])) {
            throw new InvalidArgumentException("LoadBalancer driver [{$name}] not found.");
        }

        return $this->drivers[$name];
    }

    public function getDefaultDriverName(): string
    {
        return config('loadbalancers.default', 'metallb');
    }

    /**
     * 获取所有可用的驱动名称
     */
    public function getAvailableDrivers(): array
    {
        return array_keys($this->drivers);
    }

    /**
     * 检查驱动是否存在
     */
    public function hasDriver(string $name): bool
    {
        return isset($this->drivers[$name]);
    }

    /**
     * 获取驱动的显示名称
     */
    public function getDriverDisplayName(string $name): string
    {
        $drivers = config('loadbalancers.drivers', []);

        return $drivers[$name]['name'] ?? ucfirst($name);
    }

    /**
     * 获取驱动的描述信息
     */
    public function getDriverDescription(string $name): ?string
    {
        $drivers = config('loadbalancers.drivers', []);

        return $drivers[$name]['description'] ?? null;
    }

    /**
     * 获取所有驱动的详细信息
     */
    public function getDriversInfo(): array
    {
        $drivers = config('loadbalancers.drivers', []);
        $result = [];

        foreach ($drivers as $name => $config) {
            $result[$name] = [
                'name' => $config['name'] ?? ucfirst($name),
                'description' => $config['description'] ?? null,
                'available' => $this->hasDriver($name),
            ];
        }

        return $result;
    }
}
