<?php

namespace App\Service;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class K8sResourceCacheService
{
    protected $redis;

    public function __construct()
    {
        $this->redis = Redis::connection('cache');
    }

    /**
     * 获取缓存的资源数据
     *
     * @param  int  $clusterId  集群ID
     * @param  string  $namespace  命名空间
     * @param  string  $resourceType  资源类型
     */
    public function getCachedResources(int $clusterId, string $namespace, string $resourceType): ?array
    {
        $cacheKey = $this->generateCacheKey($clusterId, $namespace, $resourceType);
        $cachedData = Cache::get($cacheKey);

        if (! $cachedData) {
            return null;
        }

        try {
            $data = json_decode($cachedData, true);

            // 检查缓存是否过期（超过1小时认为过期）
            $cachedAt = $data['cached_at'] ?? 0;
            $maxAge = 3600; // 1小时

            if ((time() - $cachedAt) > $maxAge) {
                Log::debug('缓存已过期', [
                    'cache_key' => $cacheKey,
                    'cached_at' => $cachedAt,
                    'age' => time() - $cachedAt,
                ]);

                return null;
            }

            return $data['resources'] ?? [];

        } catch (\Exception $e) {
            Log::error('解析缓存数据失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * 获取特定资源
     *
     * @param  int  $clusterId  集群ID
     * @param  string  $namespace  命名空间
     * @param  string  $resourceType  资源类型
     * @param  string  $resourceName  资源名称
     */
    public function getCachedResource(int $clusterId, string $namespace, string $resourceType, string $resourceName): ?array
    {
        $resources = $this->getCachedResources($clusterId, $namespace, $resourceType);

        if (! $resources) {
            return null;
        }

        foreach ($resources as $resource) {
            if (($resource['name'] ?? '') === $resourceName) {
                return $resource;
            }
        }

        return null;
    }

    /**
     * 设置缓存的资源数据
     *
     * @param  int  $clusterId  集群ID
     * @param  string  $namespace  命名空间
     * @param  string  $resourceType  资源类型
     * @param  array  $data  要缓存的数据
     */
    public function setCachedResources(int $clusterId, string $namespace, string $resourceType, array $data): bool
    {
        $cacheKey = $this->generateCacheKey($clusterId, $namespace, $resourceType);

        try {
            $jsonData = json_encode($data);
            if ($jsonData === false) {
                throw new \Exception('JSON 编码失败: '.json_last_error_msg());
            }

            // 使用 Laravel Cache 存储，设置过期时间为 1 小时
            Cache::put($cacheKey, $jsonData, 3600);

            // Log::debug('资源已缓存', [
            //     'cache_key' => $cacheKey,
            //     'resource_type' => $resourceType,
            //     'resource_count' => count($data['resources'] ?? []),
            //     'data_length' => strlen($jsonData),
            // ]);

            return true;
        } catch (\Exception $e) {
            Log::error('资源缓存失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 生成缓存键
     */
    protected function generateCacheKey(int $clusterId, string $namespace, string $resourceType): string
    {
        return "k8s:{$clusterId}:{$namespace}:{$resourceType}";
    }

    /**
     * 原子性地添加或更新缓存中的单个资源
     *
     * @param  array  $resourceData  要添加或更新的资源数据 (DTO->toArray())
     */
    public function addOrUpdateResourceInCache(int $clusterId, string $namespace, string $resourceType, array $resourceData): bool
    {
        $cacheKey = $this->generateCacheKey($clusterId, $namespace, $resourceType);
        $lock = Cache::lock($cacheKey.'.lock', 10); // 10秒锁

        if ($lock->get()) {
            try {
                $rawCachedData = $this->getRawCachedData($clusterId, $namespace, $resourceType) ?? [
                    'resources' => [],
                    'cached_at' => 0,
                ];

                $resources = $rawCachedData['resources'] ?? [];
                $resourceUid = $resourceData['uid'] ?? null;

                if (! $resourceUid) {
                    Log::warning('尝试缓存缺少 UID 的资源', ['resource' => $resourceData]);

                    return false;
                }

                $found = false;
                foreach ($resources as $index => $existingResource) {
                    if (($existingResource['uid'] ?? null) === $resourceUid) {
                        $resources[$index] = $resourceData;
                        $found = true;
                        break;
                    }
                }

                if (! $found) {
                    $resources[] = $resourceData;
                }

                $rawCachedData['resources'] = $resources;
                $rawCachedData['cached_at'] = time();

                return $this->setCachedResources($clusterId, $namespace, $resourceType, $rawCachedData);
            } finally {
                $lock->release();
            }
        } else {
            Log::warning('无法获取缓存锁', ['cache_key' => $cacheKey]);

            // Could retry, but for now just fail.
            return false;
        }
    }

    /**
     * 原子性地从缓存中删除单个资源
     *
     * @param  string  $resourceUid  要删除的资源的 UID
     */
    public function deleteResourceFromCache(int $clusterId, string $namespace, string $resourceType, string $resourceUid): bool
    {
        $cacheKey = $this->generateCacheKey($clusterId, $namespace, $resourceType);
        $lock = Cache::lock($cacheKey.'.lock', 10);

        if ($lock->get()) {
            try {
                $rawCachedData = $this->getRawCachedData($clusterId, $namespace, $resourceType);
                if (! $rawCachedData) {
                    return true; // Nothing to delete
                }

                $resources = $rawCachedData['resources'] ?? [];

                $initialCount = count($resources);
                $filteredResources = array_filter($resources, function ($resource) use ($resourceUid) {
                    return ($resource['uid'] ?? null) !== $resourceUid;
                });

                if (count($filteredResources) === $initialCount) {
                    return true; // Resource not found, no change needed
                }

                // Re-index array
                $rawCachedData['resources'] = array_values($filteredResources);
                $rawCachedData['cached_at'] = time();

                return $this->setCachedResources($clusterId, $namespace, $resourceType, $rawCachedData);
            } finally {
                $lock->release();
            }
        } else {
            Log::warning('无法获取缓存锁', ['cache_key' => $cacheKey]);

            return false;
        }
    }

    /**
     * 获取缓存的命名空间列表
     */
    public function getCachedNamespaces(int $clusterId, string $resourceType): array
    {
        $cachePrefix = config('cache.prefix', '');
        $pattern = "{$cachePrefix}k8s:{$clusterId}:*:{$resourceType}";
        $keys = $this->redis->keys($pattern);

        $namespaces = [];
        foreach ($keys as $key) {
            // 移除缓存前缀
            $keyWithoutPrefix = str_replace($cachePrefix, '', $key);
            // 解析 key 格式：k8s:{clusterId}:{namespace}:{resourceType}
            $parts = explode(':', $keyWithoutPrefix);
            if (count($parts) >= 4) {
                $namespace = $parts[2];
                if (! in_array($namespace, $namespaces)) {
                    $namespaces[] = $namespace;
                }
            }
        }

        return $namespaces;
    }

    /**
     * 获取原始缓存数据（包含元数据）
     */
    public function getRawCachedData(int $clusterId, string $namespace, string $resourceType): ?array
    {
        $cacheKey = $this->generateCacheKey($clusterId, $namespace, $resourceType);
        $cachedData = Cache::get($cacheKey);

        if (! $cachedData) {
            return null;
        }

        try {
            return json_decode($cachedData, true);
        } catch (\Exception $e) {
            Log::error('解析缓存数据失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * 格式化缓存年龄为人类可读格式
     */
    public function formatCacheAge(int $seconds): string
    {
        if ($seconds < 60) {
            return "{$seconds}秒";
        }

        $minutes = floor($seconds / 60);
        if ($minutes < 60) {
            return "{$minutes}分钟";
        }

        $hours = floor($minutes / 60);
        if ($hours < 24) {
            return "{$hours}小时";
        }

        $days = floor($hours / 24);

        return "{$days}天";
    }

    /**
     * 获取 Redis 连接实例
     */
    public function getRedis()
    {
        return $this->redis;
    }
}
