<?php

namespace App\Console\Commands;

use App\Models\AIConversation;
use App\Models\AIMessage;
use App\Models\Team;
use App\Models\User;
use App\Neuron\ChatAgent;
use Exception;
use Illuminate\Console\Command;
use NeuronAI\Chat\Enums\MessageRole;
use NeuronAI\Chat\Messages\Message;
use NeuronAI\Chat\Messages\UserMessage;

class ChatCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chat 
                            {--conversation= : 对话ID}
                            {--user= : 用户ID (默认为第一个用户)}
                            {--workspace= : 工作区ID (默认为用户的第一个工作区)}
                            {--message= : 直接发送消息}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '与 AI 助手进行命令行对话';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // 获取用户和团队
            $user = $this->getUser();
            $workspace = $this->getWorkspace($user);

            $this->info('欢迎使用 AI 助手命令行工具！');
            $this->info("用户: {$user->name} ({$user->email})");
            $this->info("工作区: {$workspace->name}");
            $this->line('');

            // 获取或创建对话
            $conversation = $this->getOrCreateConversation($user, $team);
            $this->info("对话ID: {$conversation->conversation_id}");
            $this->info('对话标题: '.($conversation->title ?: '新对话'));
            $this->line('');

            // 创建 ChatAgent
            $agent = new ChatAgent($user, $team);

            // 如果提供了直接消息，发送后退出
            if ($message = $this->option('message')) {
                $this->sendMessage($agent, $conversation, $message, $user);

                return 0;
            }

            // 进入交互模式
            $this->enterInteractiveMode($agent, $conversation, $user);

            return 0;
        } catch (Exception $e) {
            $this->error('错误: '.$e->getMessage());

            return 1;
        }
    }

    /**
     * 获取用户
     */
    protected function getUser(): User
    {
        $userId = $this->option('user');

        if ($userId) {
            $user = User::find($userId);
            if (! $user) {
                throw new Exception("用户ID {$userId} 不存在");
            }
        } else {
            $user = User::first();
            if (! $user) {
                throw new Exception('系统中没有用户');
            }
        }

        return $user;
    }

    /**
     * 获取团队
     */
    protected function getTeam(User $user): Team
    {
        $teamId = $this->option('team');

        if ($teamId) {
            $team = Team::find($teamId);
            if (! $team) {
                throw new Exception("团队ID {$teamId} 不存在");
            }

            // 验证用户是否在此团队中
            if (! $user->teams()->where('teams.id', $teamId)->exists()) {
                throw new Exception('用户不在指定的团队中');
            }
        } else {
            $team = $user->teams()->first();
            if (! $team) {
                throw new Exception('用户不在任何团队中');
            }
        }

        return $team;
    }

    /**
     * 获取或创建对话
     */
    protected function getOrCreateConversation(User $user, Workspace $workspace): AIConversation
    {
        $conversationId = $this->option('conversation');

        if ($conversationId) {
            // 验证UUID格式
            if (! preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $conversationId)) {
                throw new Exception('对话ID格式无效，必须是有效的UUID格式');
            }

            try {
                $conversation = AIConversation::where('conversation_id', $conversationId)
                    ->where('team_id', $team->id)
                    ->first();

                if (! $conversation) {
                    throw new Exception("对话ID {$conversationId} 不存在或不属于当前团队");
                }
            } catch (\Exception $e) {
                if (str_contains($e->getMessage(), 'invalid input syntax for type uuid')) {
                    throw new Exception('对话ID格式无效，必须是有效的UUID格式');
                }
                throw $e;
            }
        } else {
            // 创建新对话
            $conversation = AIConversation::create([
                'team_id' => $team->id,
                'user_id' => $user->id,
                'title' => null,
            ]);
        }

        return $conversation;
    }

    /**
     * 进入交互模式
     */
    protected function enterInteractiveMode(ChatAgent $agent, AIConversation $conversation, User $user): void
    {
        $this->info("进入交互模式，输入 'exit' 或 'quit' 退出，输入 'history' 查看历史消息");
        $this->line('');

        while (true) {
            $message = $this->ask('您');

            if (! $message) {
                continue;
            }

            $message = trim($message);

            if (in_array(strtolower($message), ['exit', 'quit', '退出'])) {
                $this->info('再见！');
                break;
            }

            if (in_array(strtolower($message), ['history', '历史', '历史消息'])) {
                $this->showHistory($conversation);

                continue;
            }

            $this->sendMessage($agent, $conversation, $message, $user);
        }
    }

    /**
     * 发送消息并处理流式响应
     */
    protected function sendMessage(ChatAgent $agent, AIConversation $conversation, string $messageText, User $user): void
    {
        // 保存用户消息
        $userMessage = AIMessage::create([
            'conversation_id' => $conversation->conversation_id,
            'user_id' => $user->id,
            'role' => 'user',
            'content' => $messageText,
            'images' => [],
        ]);

        // 构建对话历史
        $conversationHistory = $this->buildConversationHistory($conversation, $userMessage);

        // 创建用户消息对象
        $neuronUserMessage = new UserMessage($messageText);

        // 构建完整的消息历史
        $messages = $conversationHistory;
        $messages[] = $neuronUserMessage;

        $this->line('');
        $this->info('AI助手:');

        try {
            // 使用流式响应
            $stream = $agent->stream($messages);
            $assistantContent = '';

            foreach ($stream as $text) {
                $assistantContent .= $text;
                $this->getOutput()->write($text);
            }

            $this->line(''); // 换行
            $this->line(''); // 空行

            // 保存助手回复
            AIMessage::create([
                'conversation_id' => $conversation->conversation_id,
                'role' => 'assistant',
                'content' => $assistantContent,
                'images' => [],
            ]);

            // 如果对话没有标题，生成一个
            if (! $conversation->title) {
                $title = $this->generateConversationTitle($messageText);
                $conversation->update(['title' => $title]);
                $this->comment("对话标题已设置为: {$title}");
                $this->line('');
            }

        } catch (Exception $e) {
            $this->error('处理消息时发生错误: '.$e->getMessage());
        }
    }

    /**
     * 构建对话历史
     */
    protected function buildConversationHistory(AIConversation $conversation, AIMessage $currentMessage): array
    {
        $messages = $conversation->messages()
            ->where('id', '<', $currentMessage->id)
            ->orderBy('created_at')
            ->get();

        $history = [];
        foreach ($messages as $message) {
            $messageRole = $message->role === 'user' ? MessageRole::USER : MessageRole::ASSISTANT;
            $history[] = new Message($messageRole, $message->content);
        }

        return $history;
    }

    /**
     * 显示历史消息
     */
    protected function showHistory(AIConversation $conversation): void
    {
        $messages = $conversation->messages()
            ->orderBy('created_at')
            ->get();

        if ($messages->isEmpty()) {
            $this->info('暂无历史消息');

            return;
        }

        $this->line('');
        $this->info('=== 历史消息 ===');

        foreach ($messages as $message) {
            $role = $message->role === 'user' ? '您' : 'AI助手';
            $time = $message->created_at->format('Y-m-d H:i:s');

            $this->line('');
            $this->comment("[{$time}] {$role}:");
            $this->line($message->content);
        }

        $this->line('');
        $this->info('=== 历史消息结束 ===');
        $this->line('');
    }

    /**
     * 生成对话标题
     */
    protected function generateConversationTitle(string $firstMessage): string
    {
        $title = mb_substr($firstMessage, 0, 30);
        if (mb_strlen($firstMessage) > 30) {
            $title .= '...';
        }

        return $title ?: '新对话';
    }
}
