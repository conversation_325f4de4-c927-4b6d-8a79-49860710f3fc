<?php

namespace App\Console\Commands\Billing;

use App\Jobs\ProcessClusterResourcesJob;
use App\Models\Cluster;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CollectClusterResourcesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'billing:process
                          {--cluster-id= : 指定集群ID，留空则处理所有集群}
                          {--dry-run : 干运行模式，只显示要处理的集群}';

    /**
     * The console command description.
     */
    protected $description = '收集集群资源快照并推送到计费队列';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $clusterId = $this->option('cluster-id');
        $isDryRun = $this->option('dry-run');

        // 获取要处理的集群
        $clusters = $this->getClustersToProcess($clusterId);

        if ($clusters->isEmpty()) {
            $this->warn('没有找到要处理的集群');

            return 0;
        }

        $this->info("找到 {$clusters->count()} 个集群需要处理");

        if ($isDryRun) {
            $this->displayClusters($clusters);

            return 0;
        }

        // 记录计费开始时间
        $billingTime = now();
        $this->info("开始计费时间: {$billingTime->format('Y-m-d H:i:s')}");

        // 处理多个集群
        $results = [];
        foreach ($clusters as $cluster) {
            $results[] = $this->processCluster($cluster, $billingTime);
        }

        // 统计结果
        $this->displayResults($results, $clusters);

        return 0;
    }

    /**
     * 获取要处理的集群
     */
    protected function getClustersToProcess(?string $clusterId)
    {
        $query = Cluster::query()
            ->whereNotNull('server_url')
            ->whereNotNull('auth_type');

        if ($clusterId) {
            $query->where('id', $clusterId);
        }

        return $query->get();
    }

    /**
     * 显示集群信息
     */
    protected function displayClusters($clusters)
    {
        $this->table(
            ['ID', '名称', '服务器URL', '认证类型', '有计费配置'],
            $clusters->map(function ($cluster) {
                return [
                    $cluster->id,
                    $cluster->name,
                    $cluster->server_url,
                    $cluster->auth_type,
                    $cluster->isBillingEnabled() ? '是' : '否',
                ];
            })->toArray()
        );
    }

    /**
     * 处理单个集群
     */
    protected function processCluster(Cluster $cluster, $billingTime): array
    {
        try {
            // 检查集群是否启用计费
            if (! $cluster->isBillingEnabled()) {
                return [
                    'cluster_id' => $cluster->id,
                    'cluster_name' => $cluster->name,
                    'success' => false,
                    'reason' => 'billing_disabled',
                    'message' => '计费未启用',
                ];
            }

            // 收集集群资源快照
            $snapshot = $this->collectClusterSnapshot($cluster);

            if (empty($snapshot['pods']) && empty($snapshot['services']) && empty($snapshot['pvcs'])) {
                return [
                    'cluster_id' => $cluster->id,
                    'cluster_name' => $cluster->name,
                    'success' => false,
                    'reason' => 'no_resources',
                    'message' => '没有找到可计费资源',
                ];
            }

            // 推送到处理队列
            ProcessClusterResourcesJob::dispatch($cluster, $snapshot, $billingTime);

            Log::info('集群资源快照已推送到队列', [
                'cluster_id' => $cluster->id,
                'cluster_name' => $cluster->name,
                'pods_count' => count($snapshot['pods']),
                'services_count' => count($snapshot['services']),
                'pvcs_count' => count($snapshot['pvcs']),
                'billing_time' => $billingTime->toISOString(),
            ]);

            return [
                'cluster_id' => $cluster->id,
                'cluster_name' => $cluster->name,
                'success' => true,
                'pods_count' => count($snapshot['pods']),
                'services_count' => count($snapshot['services']),
                'pvcs_count' => count($snapshot['pvcs']),
            ];

        } catch (\Exception $e) {
            Log::error('处理集群失败', [
                'cluster_id' => $cluster->id,
                'cluster_name' => $cluster->name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'cluster_id' => $cluster->id,
                'cluster_name' => $cluster->name,
                'success' => false,
                'reason' => 'exception',
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * 收集集群资源快照
     */
    protected function collectClusterSnapshot(Cluster $cluster): array
    {
        $snapshot = [
            'pods' => [],
            'services' => [],
            'pvcs' => [],
            'timestamp' => now()->toISOString(),
        ];

        try {
            // 收集不同类型的资源
            $snapshot['pods'] = $this->collectPods($cluster);
            $snapshot['services'] = $this->collectServices($cluster);
            $snapshot['pvcs'] = $this->collectPVCs($cluster);

        } catch (\Exception $e) {
            Log::error('收集集群资源快照失败', [
                'cluster_id' => $cluster->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }

        return $snapshot;
    }

    /**
     * 收集Pods
     */
    protected function collectPods(Cluster $cluster): array
    {
        try {
            $response = $cluster->http()->get('/api/v1/pods');
            $pods = $response->json()['items'] ?? [];

            return array_filter($pods, function ($pod) {
                return $this->isPlatformManagedResource($pod) &&
                       $this->isValidNamespace($pod['metadata']['namespace'] ?? '');
            });

        } catch (\Exception $e) {
            Log::warning('收集Pods失败', [
                'cluster_id' => $cluster->id,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * 收集Services
     */
    protected function collectServices(Cluster $cluster): array
    {
        try {
            $response = $cluster->http()->get('/api/v1/services');
            $services = $response->json()['items'] ?? [];

            return array_filter($services, function ($service) {
                return $this->isPlatformManagedResource($service) &&
                       $this->isValidNamespace($service['metadata']['namespace'] ?? '') &&
                       ($service['spec']['type'] ?? '') === 'LoadBalancer';
            });

        } catch (\Exception $e) {
            Log::warning('收集Services失败', [
                'cluster_id' => $cluster->id,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * 收集PVCs
     */
    protected function collectPVCs(Cluster $cluster): array
    {
        try {
            $response = $cluster->http()->get('/api/v1/persistentvolumeclaims');
            $pvcs = $response->json()['items'] ?? [];

            return array_filter($pvcs, function ($pvc) {
                return $this->isPlatformManagedResource($pvc) &&
                       $this->isValidNamespace($pvc['metadata']['namespace'] ?? '');
            });

        } catch (\Exception $e) {
            Log::warning('收集PVCs失败', [
                'cluster_id' => $cluster->id,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * 检查是否是平台管理的资源
     */
    protected function isPlatformManagedResource(array $resource): bool
    {
        $labels = $resource['metadata']['labels'] ?? [];

        return isset($labels[\App\ClusterLabel::PLATFORM->value]);
    }

    /**
     * 检查namespace是否有效（ns-开头）
     */
    protected function isValidNamespace(string $namespace): bool
    {
        return str_starts_with($namespace, 'ns-');
    }

    /**
     * 显示处理结果
     */
    protected function displayResults(array $results, $clusters)
    {
        $successCount = 0;
        $failedCount = 0;
        $totalPods = 0;
        $totalServices = 0;
        $totalPvcs = 0;

        foreach ($results as $result) {
            if ($result['success']) {
                $successCount++;
                $totalPods += $result['pods_count'] ?? 0;
                $totalServices += $result['services_count'] ?? 0;
                $totalPvcs += $result['pvcs_count'] ?? 0;
            } else {
                $failedCount++;
            }
        }

        $this->newLine();
        $this->info('处理结果统计：');
        $this->line("  成功集群: {$successCount}");
        $this->line("  失败集群: {$failedCount}");
        $this->line("  总计 Pods: {$totalPods}");
        $this->line("  总计 Services: {$totalServices}");
        $this->line("  总计 PVCs: {$totalPvcs}");

        // 显示失败的集群
        if ($failedCount > 0) {
            $this->newLine();
            $this->warn('失败的集群：');
            $failedResults = [];
            foreach ($results as $result) {
                if (! $result['success']) {
                    $failedResults[] = $result;
                }
            }

            $failedTableData = [];
            foreach ($failedResults as $result) {
                $failedTableData[] = [
                    $result['cluster_id'],
                    $result['cluster_name'],
                    $result['message'] ?? $result['reason'],
                ];
            }

            $this->table(['集群ID', '集群名称', '失败原因'], $failedTableData);
        }
    }
}
