<?php

namespace App\Console\Commands\Workspace;

use App\Models\Workspace;
use App\Service\WorkspaceService;
use Illuminate\Console\Command;

class UpdateWorkspace extends Command
{
    protected $signature = 'workspace:update {id} {name}';

    protected $description = 'Update a workspace';

    public function __construct(
        protected WorkspaceService $workspaceService
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $id = $this->argument('id');
        $name = $this->argument('name');

        $workspace = Workspace::find($id);
        if (! $workspace) {
            $this->error('Workspace not found');

            return;
        }

        try {
            $workspace->update(['name' => $name]);
            $this->info('Workspace updated successfully: '.$workspace->id);
        } catch (\Exception $e) {
            $this->error('Failed to update workspace: '.$e->getMessage());
        }
    }
}
