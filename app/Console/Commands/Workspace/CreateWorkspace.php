<?php

namespace App\Console\Commands\Workspace;

use App\Models\Cluster;
use App\Service\WorkspaceService;
use Illuminate\Console\Command;

class CreateWorkspace extends Command
{
    protected $signature = 'workspace:create {name} {cluster_id} {user_id}';

    protected $description = 'Create a new workspace';

    public function __construct(
        protected WorkspaceService $workspaceService
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $name = $this->argument('name');
        $clusterId = $this->argument('cluster_id');

        $cluster = Cluster::find($clusterId);
        if (! $cluster) {
            $this->error('Cluster not found');

            return;
        }

        try {
            $workspace = $this->workspaceService->createWorkspace([
                'user_id' => $this->argument('user_id'),
                'cluster_id' => $clusterId,
                'name' => $name,
            ]);

            $this->info("Workspace created successfully: {$workspace->id}");
        } catch (\Exception $e) {
            $this->error('Failed to create workspace: '.$e->getMessage());
        }
    }
}
