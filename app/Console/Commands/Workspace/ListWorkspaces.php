<?php

namespace App\Console\Commands\Workspace;

use App\Models\Workspace;
use App\Service\WorkspaceService;
use Illuminate\Console\Command;

class ListWorkspaces extends Command
{
    protected $signature = 'workspace:list {cluster_id?}';

    protected $description = 'List all workspaces';

    public function __construct(
        protected WorkspaceService $workspaceService
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $clusterId = $this->argument('cluster_id');
        $workspaces = $clusterId
            ? Workspace::where('cluster_id', $clusterId)->get()
            : Workspace::all();

        $this->table(
            ['ID', '名称', '集群ID', '状态'],
            $workspaces->map(fn ($workspace) => [
                $workspace->id,
                $workspace->name,
                $workspace->cluster_id,
                $this->getStatusText($workspace->status),
            ])
        );
    }

    protected function getStatusText(string $status): string
    {
        return match ($status) {
            'pending' => '创建中',
            'active' => '运行中',
            'deleting' => '删除中',
            'suspended' => '已暂停',
            'failed' => '创建失败',
            default => $status,
        };
    }
}
