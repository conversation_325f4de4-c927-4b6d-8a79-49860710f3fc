<?php

namespace App\Console\Commands\Workspace;

use App\Models\Workspace;
use App\Service\WorkspaceService;
use Illuminate\Console\Command;

class DeleteWorkspace extends Command
{
    protected $signature = 'workspace:delete {id}';

    protected $description = 'Delete a workspace';

    public function __construct(
        protected WorkspaceService $workspaceService
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $id = $this->argument('id');

        $workspace = Workspace::find($id);
        if (! $workspace) {
            $this->error('Workspace not found');

            return;
        }

        if (! $this->confirm('Are you sure you want to delete this workspace? This action cannot be undone.')) {
            return;
        }

        try {
            $success = $this->workspaceService->deleteWorkspace($workspace);
            if ($success) {
                $this->info("Workspace deletion initiated successfully: {$workspace->id}");
            } else {
                $this->error('Failed to delete workspace');
            }
        } catch (\Exception $e) {
            $this->error('Failed to delete workspace: '.$e->getMessage());
        }
    }
}
