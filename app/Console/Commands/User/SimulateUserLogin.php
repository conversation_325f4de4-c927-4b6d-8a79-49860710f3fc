<?php

namespace App\Console\Commands\User;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class SimulateUserLogin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:simulate-login 
                            {user : 用户ID或邮箱} 
                            {--expires=60 : Token有效期（分钟）}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '模拟用户登录并生成临时访问URL';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userIdentifier = $this->argument('user');
        $expires = (int) $this->option('expires');

        // 查找用户
        $user = $this->findUser($userIdentifier);
        if (! $user) {
            $this->error('用户不存在');

            return Command::FAILURE;
        }

        // 生成随机Token并存储到缓存
        $token = Str::random(32);
        Cache::put("user_simulate_token:{$token}", $user->id, $expires * 60);

        // 生成访问URL
        $url = route('user.simulate.login', ['token' => $token]);

        $this->info('模拟用户登录URL已生成：');
        $this->line($url);

        return Command::SUCCESS;
    }

    /**
     * 查找用户
     */
    private function findUser(string $identifier): ?User
    {
        // 如果是数字，按ID查找
        if (is_numeric($identifier)) {
            return User::find((int) $identifier);
        }

        // 否则按邮箱查找
        return User::where('email', $identifier)->first();
    }
}
