<?php

namespace App\Console\Commands\User;

use App\Exceptions\Balance\InsufficientBalanceException;
use App\Exceptions\Balance\InvalidAmountException;
use App\Models\User;
use App\Service\BalanceService;
use Illuminate\Console\Command;

class DeductUserBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:deduct-balance 
                            {user : 用户ID或邮箱} 
                            {amount : 扣除金额} 
                            {--reason= : 扣除原因}
                            {--force : 强制执行，不需要确认}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '扣除用户余额';

    protected BalanceService $balanceService;

    public function __construct(BalanceService $balanceService)
    {
        parent::__construct();
        $this->balanceService = $balanceService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userIdentifier = $this->argument('user');
        $amount = (float) $this->argument('amount');
        $reason = $this->option('reason') ?: '管理员手动扣除';
        $force = $this->option('force');

        // 验证金额
        if ($amount <= 0) {
            $this->error('扣除金额必须大于0');

            return Command::FAILURE;
        }

        // 查找用户
        $user = $this->findUser($userIdentifier);
        if (! $user) {
            $this->error('用户不存在');

            return Command::FAILURE;
        }

        // 检查余额是否充足
        $amountStr = number_format($amount, 8, '.', '');
        if (! $user->hasEnoughBalance($amountStr)) {
            $this->error('用户余额不足');
            $this->table(
                ['项目', '值'],
                [
                    ['用户当前余额', formatAmountForCli($user->current_balance)],
                    ['需要扣除金额', formatAmountForCli($amount)],
                    ['余额不足', formatAmountForCli($amount - $user->current_balance)],
                ]
            );

            return Command::FAILURE;
        }

        // 显示确认信息
        $this->info('即将扣除用户余额:');
        $this->table(
            ['项目', '值'],
            [
                ['用户ID', $user->id],
                ['用户邮箱', $user->email],
                ['用户姓名', $user->name],
                ['当前余额', formatAmountForCli($user->current_balance)],
                ['扣除金额', formatAmountForCli($amount)],
                ['扣除后余额', formatAmountForCli($user->current_balance - $amount)],
                ['扣除原因', $reason],
            ]
        );

        if (! $force && ! $this->confirm('确认执行扣除操作？')) {
            $this->info('操作已取消');

            return Command::SUCCESS;
        }

        try {
            // 执行扣除
            $success = $this->balanceService->deductBalance(
                $user,
                $amountStr,
                $reason
            );

            if ($success) {
                $this->info('✅ 扣除成功！');
                $this->table(
                    ['项目', '值'],
                    [
                        ['扣除金额', formatAmountForCli($amount)],
                        ['用户新余额', formatAmountForCli($user->fresh()->current_balance)],
                        ['扣除原因', $reason],
                    ]
                );
            } else {
                $this->error('扣除失败');

                return Command::FAILURE;
            }

            return Command::SUCCESS;

        } catch (InvalidAmountException $e) {
            $this->error('金额无效：'.$e->getMessage());

            return Command::FAILURE;
        } catch (InsufficientBalanceException $e) {
            $this->error('余额不足：'.$e->getMessage());

            return Command::FAILURE;
        } catch (\Exception $e) {
            $this->error('扣除失败：'.$e->getMessage());

            return Command::FAILURE;
        }
    }

    /**
     * 查找用户
     */
    private function findUser(string $identifier): ?User
    {
        // 如果是数字，按ID查找
        if (is_numeric($identifier)) {
            return User::find((int) $identifier);
        }

        // 否则按邮箱查找
        return User::where('email', $identifier)->first();
    }
}
