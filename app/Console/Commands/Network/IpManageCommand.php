<?php

namespace App\Console\Commands\Network;

use App\Models\DisabledIp;
use Illuminate\Console\Command;
use PhpIP\IP;

class IpManageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ip:manage
                            {--disable= : IP 地址列表（逗号分隔）进行禁用}
                            {--enable= : IP 地址列表（逗号分隔）进行启用}
                            {--reason= : 禁用原因}
                            {--force : 强制操作，跳过确认}
                            {--list : 列出所有禁用 IP}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '批量禁用或启用 IP 地址，支持预禁用还未生成的 IP';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $disableIps = $this->option('disable');
        $enableIps = $this->option('enable');
        $reason = $this->option('reason');
        $force = $this->option('force');
        $list = $this->option('list');

        // 列出禁用的 IP
        if ($list) {
            $this->listDisabledIps();

            return;
        }

        if ($disableIps) {
            $this->disableIps($disableIps, $reason, $force);
        }

        if ($enableIps) {
            $this->enableIps($enableIps, $force);
        }

        if (! $disableIps && ! $enableIps && ! $list) {
            $this->error('请提供要禁用 (--disable) 或启用 (--enable) 的 IP 地址，或使用 --list 查看禁用列表');
        }
    }

    /**
     * 禁用 IP 地址
     */
    private function disableIps(string $ipList, ?string $reason, bool $force)
    {
        $ipAddresses = $this->parseIpList($ipList);

        if (empty($ipAddresses)) {
            $this->error('没有有效的 IP 地址');

            return;
        }

        $this->info('准备禁用 '.count($ipAddresses).' 个 IP 地址:');

        $toDisable = [];
        $alreadyDisabledIps = [];
        $invalidIps = [];

        foreach ($ipAddresses as $ip) {
            // 验证 IP 地址格式
            if (! $this->isValidIp($ip)) {
                $invalidIps[] = $ip;

                continue;
            }

            // 检查是否已经被禁用
            if (DisabledIp::isIpDisabled($ip)) {
                $alreadyDisabledIps[] = $ip;
            } else {
                $toDisable[] = $ip;
            }
        }

        // 显示分类结果
        if (! empty($invalidIps)) {
            $this->warn('无效的 IP 地址: '.implode(', ', $invalidIps));
        }

        if (! empty($alreadyDisabledIps)) {
            $this->warn('已禁用的 IP 地址: '.implode(', ', $alreadyDisabledIps));
        }

        if (empty($toDisable)) {
            $this->warn('没有需要禁用的 IP 地址');

            return;
        }

        $this->info('将要禁用的 IP 地址:');
        foreach ($toDisable as $ip) {
            $this->line("  - {$ip}");
        }

        // 确认操作
        if (! $force && ! $this->confirm('确定要禁用 '.count($toDisable).' 个 IP 地址吗？')) {
            $this->info('操作已取消');

            return;
        }

        try {
            $disabledCount = DisabledIp::disableIps($toDisable, $reason);
            $this->info("✅ 已成功禁用 {$disabledCount} 个 IP 地址");
        } catch (\Exception $e) {
            $this->error('❌ 禁用 IP 失败: '.$e->getMessage());
        }

        $this->newLine();
    }

    /**
     * 启用 IP 地址
     */
    private function enableIps(string $ipList, bool $force)
    {
        $ipAddresses = $this->parseIpList($ipList);

        if (empty($ipAddresses)) {
            $this->error('没有有效的 IP 地址');

            return;
        }

        $this->info('准备启用 '.count($ipAddresses).' 个 IP 地址:');

        $toEnable = [];
        $notDisabledIps = [];
        $invalidIps = [];

        foreach ($ipAddresses as $ip) {
            // 验证 IP 地址格式
            if (! $this->isValidIp($ip)) {
                $invalidIps[] = $ip;

                continue;
            }

            if (DisabledIp::isIpDisabled($ip)) {
                $toEnable[] = $ip;
            } else {
                $notDisabledIps[] = $ip;
            }
        }

        // 显示分类结果
        if (! empty($invalidIps)) {
            $this->warn('无效的 IP 地址: '.implode(', ', $invalidIps));
        }

        if (! empty($notDisabledIps)) {
            $this->warn('未被禁用的 IP 地址: '.implode(', ', $notDisabledIps));
        }

        if (empty($toEnable)) {
            $this->warn('没有需要启用的 IP 地址');

            return;
        }

        $this->info('将要启用的 IP 地址:');
        foreach ($toEnable as $ip) {
            $this->line("  - {$ip}");
        }

        // 确认操作
        if (! $force && ! $this->confirm('确定要启用 '.count($toEnable).' 个 IP 地址吗？')) {
            $this->info('操作已取消');

            return;
        }

        try {
            $enabledCount = DisabledIp::enableIps($toEnable);
            $this->info("✅ 已成功启用 {$enabledCount} 个 IP 地址");
        } catch (\Exception $e) {
            $this->error('❌ 启用 IP 失败: '.$e->getMessage());
        }

        $this->newLine();
    }

    /**
     * 列出禁用的 IP 地址
     */
    private function listDisabledIps()
    {
        $this->info('全局禁用 IP 列表:');

        $preDisabledIps = DisabledIp::all();

        if ($preDisabledIps->isEmpty()) {
            $this->warn('没有禁用的 IP 地址');

            return;
        }

        $headers = ['IP 地址', 'IP 类型', '原因', '禁用时间'];
        $rows = [];

        foreach ($preDisabledIps as $disabledIp) {
            $rows[] = [
                $disabledIp->ip_address,
                $disabledIp->getIpType(),
                $disabledIp->reason ?: '无',
                $disabledIp->disabled_at->format('Y-m-d H:i:s'),
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * 解析 IP 列表（支持逗号分隔）
     */
    private function parseIpList(string $ipList): array
    {
        return array_filter(
            array_map('trim', explode(',', $ipList)),
            function ($ip) {
                return ! empty($ip);
            }
        );
    }

    /**
     * 验证 IP 地址格式
     */
    private function isValidIp(string $ip): bool
    {
        try {
            IP::create($ip);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
