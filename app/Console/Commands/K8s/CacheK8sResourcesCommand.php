<?php

namespace App\Console\Commands\K8s;

use App\DTOs\ConfigMapDTO;
use App\DTOs\DeploymentDTO;
use App\DTOs\EventDTO;
use App\DTOs\HorizontalPodAutoscalerDTO;
use App\DTOs\IngressDTO;
use App\DTOs\PodDTO;
use App\DTOs\PodMetricsDTO;
use App\DTOs\SecretDTO;
use App\DTOs\ServiceDTO;
use App\DTOs\StatefulSetDTO;
use App\DTOs\StorageDTO;
use App\Events\ResourceChanged;
use App\Models\Cluster;
use App\Service\K8sResourceCacheService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;

class CacheK8sResourcesCommand extends Command
{
    protected $signature = 'k8s:cache-resources 
                            {--polling : 缓存后持续轮询变化} 
                            {--cluster-id= : 处理指定集群ID}
                            {--resource-type= : 指定资源类型，多个用逗号分隔}
                            {--namespace= : 指定命名空间}
                            {--interval=1 : 轮询间隔(秒)，默认1秒}';

    protected $description = '轮询缓存多种 K8s 资源到 Redis，支持手动内存管理和增量更新';

    protected K8sResourceCacheService $cacheService;

    protected bool $shouldStop = false;

    /**
     * 支持的资源类型配置
     */
    protected array $resourceConfigs = [
        'deployments' => [
            'endpoint' => '/apis/apps/v1/deployments',
            'dto_class' => DeploymentDTO::class,
            'name' => 'Deployment',
        ],
        'statefulsets' => [
            'endpoint' => '/apis/apps/v1/statefulsets',
            'dto_class' => StatefulSetDTO::class,
            'name' => 'StatefulSet',
        ],
        'services' => [
            'endpoint' => '/api/v1/services',
            'dto_class' => ServiceDTO::class,
            'name' => 'Service',
        ],
        'pods' => [
            'endpoint' => '/api/v1/pods',
            'dto_class' => PodDTO::class,
            'name' => 'Pod',
        ],
        'ingresses' => [
            'endpoint' => '/apis/networking.k8s.io/v1/ingresses',
            'dto_class' => IngressDTO::class,
            'name' => 'Ingress',
        ],
        'storages' => [
            'endpoint' => '/api/v1/persistentvolumeclaims',
            'dto_class' => StorageDTO::class,
            'name' => 'PersistentVolumeClaim',
        ],
        'secrets' => [
            'endpoint' => '/api/v1/secrets',
            'dto_class' => SecretDTO::class,
            'name' => 'Secret',
        ],
        'configmaps' => [
            'endpoint' => '/api/v1/configmaps',
            'dto_class' => ConfigMapDTO::class,
            'name' => 'ConfigMap',
        ],
        'horizontalpodautoscalers' => [
            'endpoint' => '/apis/autoscaling/v2/horizontalpodautoscalers',
            'dto_class' => HorizontalPodAutoscalerDTO::class,
            'name' => 'HorizontalPodAutoscaler',
        ],
        'events' => [
            'endpoint' => '/api/v1/events',
            'dto_class' => EventDTO::class,
            'name' => 'Event',
        ],
        'metrics' => [
            'endpoint' => '/apis/metrics.k8s.io/v1beta1/pods',
            'dto_class' => PodMetricsDTO::class,
            'name' => 'Metrics',
        ],
    ];

    public function __construct(K8sResourceCacheService $cacheService)
    {
        parent::__construct();
        $this->cacheService = $cacheService;
    }

    /**
     * 判断命名空间是否应该被监听和缓存
     */
    protected function shouldWatchNamespace(string $namespace): bool
    {
        // 在测试环境中，监听所有命名空间
        if (app()->environment('testing')) {
            return true;
        }

        // 生产环境只监听以 "ns-" 开头的命名空间
        return str_starts_with($namespace, 'ns-');
    }

    /**
     * 判断资源是否应该被广播
     */
    protected function shouldBroadcastResource(string $resourceType, array $resource): bool
    {
        // 检查命名空间
        $namespace = $resource['metadata']['namespace'] ?? '';
        if (! empty($namespace) && ! $this->shouldWatchNamespace($namespace)) {
            return false;
        }

        // 检查特定资源的过滤规则 - 但在测试环境中不应用这些过滤
        if ($resourceType === 'configmaps') {
            $name = $resource['metadata']['name'] ?? '';
            // 忽略 kube-root-ca.crt 这个 configmap
            if ($name === 'kube-root-ca.crt') {
                return false;
            }
        }

        return true;
    }

    public function handle()
    {
        // 设置一个较高的内存限制，因为我们现在是手动管理
        ini_set('memory_limit', '512M');

        // 注册信号处理
        if (! function_exists('pcntl_signal') || ! function_exists('pcntl_signal_dispatch')) {
            $this->error('pcntl_signal/pcntl_signal_dispatch 函数不存在，请检查是否安装了 pcntl 扩展');

            return self::FAILURE;
        }

        pcntl_signal(SIGINT, [$this, 'handleSignal']);
        pcntl_signal(SIGTERM, [$this, 'handleSignal']);

        $clusterId = $this->option('cluster-id');
        $resourceTypes = $this->parseResourceTypes();
        $namespace = $this->option('namespace');

        if ($clusterId) {
            // 子进程模式：处理单个集群的所有资源
            return $this->handleSingleCluster($clusterId, $resourceTypes, $namespace);
        } else {
            // 主进程模式：为每个集群启动一个进程
            return $this->handleAllClusters($resourceTypes, $namespace);
        }
    }

    /**
     * 解析资源类型参数
     */
    protected function parseResourceTypes(): array
    {
        $resourceTypeOption = $this->option('resource-type');

        if (! $resourceTypeOption) {
            // 默认缓存所有资源类型
            return array_keys($this->resourceConfigs);
        }

        $requestedTypes = array_map('trim', explode(',', $resourceTypeOption));
        $validTypes = [];

        foreach ($requestedTypes as $type) {
            if (isset($this->resourceConfigs[$type])) {
                $validTypes[] = $type;
            } else {
                $this->warn("未知的资源类型: {$type}");
                $this->info('支持的资源类型: '.implode(', ', array_keys($this->resourceConfigs)));
            }
        }

        return $validTypes ?: array_keys($this->resourceConfigs);
    }

    /**
     * 处理所有集群（主进程）
     */
    protected function handleAllClusters(array $resourceTypes, ?string $namespace): int
    {
        $this->info('开始轮询缓存 K8s 资源...');
        $this->info('资源类型: '.implode(', ', $resourceTypes));
        if ($namespace) {
            $this->info('指定命名空间: '.$namespace);
        }

        $processes = [];
        $isPollingMode = $this->option('polling');
        $allProcessesStarted = false;

        while (! $this->shouldStop) {
            $this->checkAndManageClusterProcesses($processes, $resourceTypes, $namespace, $isPollingMode);

            // 检查是否所有进程都已启动
            if (! $allProcessesStarted && ! empty($processes)) {
                $currentClusters = Cluster::all();
                $expectedProcessCount = $currentClusters->count();

                if (count($processes) >= $expectedProcessCount) {
                    $this->info("✅ 所有 {$expectedProcessCount} 个集群进程已启动完成");
                    if ($isPollingMode) {
                        $interval = $this->option('interval');
                        $this->info("🔄 轮询模式已激活，每 {$interval} 秒检查一次资源变化");
                        $this->info('💡 提示：系统将持续监控集群变化，包括集群的新增和删除');
                    }
                    $allProcessesStarted = true;
                }
            }

            // 如果不是轮询模式，并且所有进程都已完成，则退出
            if (! $isPollingMode && empty($processes)) {
                $this->info('所有一次性缓存任务已完成。');
                break;
            }

            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch();
            }
            sleep(5);
        }

        if ($this->shouldStop) {
            $this->info('收到停止信号，终止所有子进程...');
            foreach ($processes as $processInfo) {
                if ($processInfo['process']->running()) {
                    $processInfo['process']->signal(SIGTERM);
                }
            }
        }

        $this->info('主进程退出。');

        return self::SUCCESS;
    }

    /**
     * 检查和管理集群进程
     */
    protected function checkAndManageClusterProcesses(array &$processes, array $resourceTypes, ?string $namespace, bool $isPollingMode): void
    {
        $currentClusters = Cluster::all()->keyBy('id');
        $activeClusterIds = [];

        // 启动新的集群进程
        foreach ($currentClusters as $cluster) {
            $processKey = (string) $cluster->id;
            $activeClusterIds[$processKey] = true;

            if (! isset($processes[$processKey])) {
                $this->startClusterProcess($processes, $cluster, $resourceTypes, $namespace, $isPollingMode);
            }
        }

        // 清理和重启进程
        foreach ($processes as $key => &$processInfo) {
            // 清理已删除集群的进程
            if (! isset($activeClusterIds[$key])) {
                $this->warn("检测到集群已删除，终止进程: 集群ID {$key}");
                if ($processInfo['process']->running()) {
                    $processInfo['process']->signal(SIGTERM);
                }
                unset($processes[$key]);

                continue;
            }

            // 检查运行中进程的输出
            if ($processInfo['process']->running()) {
                // 获取并显示子进程的新输出
                $latestOutput = $processInfo['process']->latestOutput();
                $latestErrorOutput = $processInfo['process']->latestErrorOutput();

                if (! empty($latestOutput)) {
                    // 将子进程输出添加前缀以便识别
                    $lines = explode("\n", trim($latestOutput));
                    foreach ($lines as $line) {
                        if (! empty(trim($line))) {
                            $this->line("[子进程-{$processInfo['cluster']->name}] ".$line);
                        }
                    }
                }

                if (! empty($latestErrorOutput)) {
                    $lines = explode("\n", trim($latestErrorOutput));
                    foreach ($lines as $line) {
                        if (! empty(trim($line))) {
                            $this->error("[子进程错误-{$processInfo['cluster']->name}] ".$line);
                        }
                    }
                }

                continue;
            }

            // 检查已结束的进程
            if (! $processInfo['process']->running()) {
                $runTime = time() - $processInfo['started_at'];
                $this->warn("集群 {$processInfo['cluster']->name} 进程已停止，运行时间: {$runTime} 秒");
                if (! empty(trim($processInfo['process']->errorOutput()))) {
                    $this->error("错误输出 (STDERR):\n".$processInfo['process']->errorOutput());
                }
                if (! empty(trim($processInfo['process']->output()))) {
                    $this->info("标准输出 (STDOUT):\n".$processInfo['process']->output());
                }

                $this->handleCompletedClusterProcess($processes, $key, $resourceTypes, $namespace, $isPollingMode);
            }
        }

        // 输出当前运行的进程状态（仅在详细模式下）
        if ($this->getOutput() && $this->getOutput()->isVerbose() && ! empty($processes)) {
            $runningCount = count(array_filter($processes, fn ($p) => $p['process']->running()));
            $totalCount = count($processes);
            if ($runningCount != $totalCount) {
                $this->line("当前运行中的集群进程: {$runningCount}/{$totalCount}");
            }
        }
    }

    /**
     * 启动一个集群进程
     */
    protected function startClusterProcess(array &$processes, Cluster $cluster, array $resourceTypes, ?string $namespace, bool $isPollingMode): void
    {
        $processKey = (string) $cluster->id;
        $this->info("启动集群进程: [{$cluster->name}] (ID: {$cluster->id})");

        try {
            $command = $this->buildClusterProcessCommand($cluster->id, $resourceTypes, $namespace, $isPollingMode);

            // 调试：显示完整的命令
            if ($this->getOutput()->isVerbose()) {
                $this->line('命令: '.implode(' ', $command));
            }

            // 使用 Process::start() 并设置工作目录
            $process = Process::path(base_path())->start($command);

            if (! $process->running()) {
                $result = $process->wait();
                $this->error("集群进程 {$processKey} 启动后立即退出");
                $this->error('退出码: '.$result->exitCode());
                $this->error('错误输出: '.$result->errorOutput());
                $this->error('标准输出: '.$result->output());

                return;
            }

            $processes[$processKey] = [
                'process' => $process,
                'cluster' => $cluster,
                'resource_types' => $resourceTypes,
                'started_at' => time(),
            ];

            $this->info("集群进程 {$processKey} 启动成功，PID: ".$process->id());

        } catch (\Exception $e) {
            $this->error("启动集群进程 {$processKey} 失败: ".$e->getMessage());
        }
    }

    /**
     * 处理已完成的集群进程
     */
    protected function handleCompletedClusterProcess(array &$processes, string $key, array $resourceTypes, ?string $namespace, bool $isPollingMode): void
    {
        $processInfo = $processes[$key];
        $result = $processInfo['process']->wait();
        $clusterName = $processInfo['cluster']->name;

        if (! $result->successful()) {
            $this->error("[{$clusterName}] 集群进程异常退出 (退出码: {$result->exitCode()})");
            $errorOutput = $result->errorOutput();
            if (! empty(trim($errorOutput))) {
                $this->error("错误输出 (STDERR):\n".$errorOutput);
            }
            $output = $result->output();
            if (! empty(trim($output))) {
                $this->error("标准输出 (STDOUT):\n".$output);
            }
        } else {
            $this->info("[{$clusterName}] 集群进程正常完成");
        }

        // 无论如何都先移除旧的进程信息
        unset($processes[$key]);

        // 如果是轮询模式，并且没有收到停止信号，则重启进程
        if ($isPollingMode && ! $this->shouldStop) {
            $this->warn("[{$clusterName}] 集群进程已停止，将在 5 秒后重启...");
            sleep(5);
            $this->startClusterProcess($processes, $processInfo['cluster'], $resourceTypes, $namespace, $isPollingMode);
        }
    }

    /**
     * 构建集群进程命令
     */
    protected function buildClusterProcessCommand(int $clusterId, array $resourceTypes, ?string $namespace, bool $isPollingMode): array
    {
        $commands = [
            PHP_BINARY,
            '-d',
            'opcache.enable=0',
            '-d',
            'opcache.enable_cli=0',
            'artisan',
            'k8s:cache-resources',
            '--cluster-id='.$clusterId,
            '--resource-type='.implode(',', $resourceTypes),
        ];

        if ($namespace) {
            $commands[] = "--namespace={$namespace}";
        }

        if ($isPollingMode) {
            $commands[] = '--polling';
            $commands[] = '--interval='.$this->option('interval');
        }

        return $commands;
    }

    /**
     * 处理单个集群的所有资源类型（子进程）
     */
    protected function handleSingleCluster(int $clusterId, array $resourceTypes, ?string $namespace): int
    {
        $this->info("集群进程启动 - 集群ID: {$clusterId}, 资源类型: ".implode(', ', $resourceTypes));

        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGINT, [$this, 'handleSignal']);
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
        }

        // 初始集群检查和连接验证
        $cluster = Cluster::find($clusterId);
        if (! $cluster) {
            $this->error("集群 ID {$clusterId} 不存在");

            return self::FAILURE;
        }

        $this->info("成功获取集群: {$cluster->name}");
        try {
            $this->line('正在验证集群连接...');
            $curl = $cluster->curl('GET', '/version');
            $curl->exec();
            if ($curl->error) {
                throw new \Exception($curl->error_message ?? json_encode($curl->error));
            }
            $this->info('集群连接验证成功');
        } catch (\Exception $e) {
            $this->error("[{$cluster->name}] 集群连接失败: ".$e->getMessage());

            return self::FAILURE;
        } finally {
            if (isset($curl)) {
                $curl->close();
                unset($curl);
            }
            unset($cluster);
        }

        // 验证资源类型
        foreach ($resourceTypes as $resourceType) {
            if (! isset($this->resourceConfigs[$resourceType])) {
                $this->error("未知的资源类型: {$resourceType}");

                return self::FAILURE;
            }
        }

        try {
            if ($this->option('polling')) {
                $interval = (int) $this->option('interval');
                $this->info("[集群 {$clusterId}] 进入轮询模式，间隔: {$interval} 秒");
                $this->startPollingForCluster($clusterId, $resourceTypes, $namespace, $interval);
            } else {
                $this->info("[集群 {$clusterId}] 开始一次性缓存...");
                $resourceVersions = [];
                $this->performCacheRun($clusterId, $resourceTypes, $namespace, $resourceVersions);
                $this->info("[集群 {$clusterId}] 一次性缓存完成。");
            }

            return self::SUCCESS;
        } catch (\Throwable $e) {
            $this->error("[集群 {$clusterId}] 处理失败: ".$e->getMessage());
            Log::error('K8s 集群处理失败', [
                'cluster_id' => $clusterId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return self::FAILURE;
        }
    }

    /**
     * 执行一次完整的缓存和变更检测
     */
    protected function performCacheRun(int $clusterId, array $resourceTypes, ?string $namespace, array &$resourceVersions): void
    {
        if (! Cluster::query()->where('id', $clusterId)->exists()) {
            $this->error("[集群 {$clusterId}] 在执行缓存时未找到，可能已被删除。");
            $this->shouldStop = true; // 停止轮询

            return;
        }

        try {
            // 在每次运行开始时获取一次可监听的命名空间
            $watchableNamespaces = $namespace ? null : $this->getWatchableNamespaces($clusterId);

            foreach ($resourceTypes as $resourceType) {
                if ($this->shouldStop) {
                    break;
                }
                $this->processResourceTypeForChanges($clusterId, $resourceType, $resourceVersions, $namespace, $watchableNamespaces);
            }
        } catch (\Throwable $e) {
            $this->error("[集群 {$clusterId}] 缓存运行时出错: ".$e->getMessage());
            Log::error('K8s 缓存运行时错误', [
                'cluster_id' => $clusterId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 开始集群的轮询模式
     */
    protected function startPollingForCluster(int $clusterId, array $resourceTypes, ?string $namespace, int $interval): void
    {
        $resourceVersions = [];
        $requestCounter = 0;

        // 获取初始的 resource versions
        $this->line("[集群 {$clusterId}] 正在获取初始资源版本...");
        if (Cluster::find($clusterId)) { // 确认集群存在
            foreach ($resourceTypes as $resourceType) {
                $resourceVersions[$resourceType] = $this->getLatestResourceVersion($clusterId, $resourceType, $namespace);
            }
        }
        $this->line("[集群 {$clusterId}] 初始资源版本获取完成。");

        while (! $this->shouldStop) {
            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch();
            }
            if ($this->shouldStop) {
                break;
            }

            // 每30次请求检查一次集群是否存在
            $requestCounter++;
            if ($requestCounter > 0 && $requestCounter % 30 === 0) {
                if (! Cluster::find($clusterId)) {
                    $this->warn("集群 {$clusterId} 已被删除，退出轮询。");
                    exit(0); // 正常退出子进程
                }
                $requestCounter = 0; // 重置计数器
            }

            $this->performCacheRun($clusterId, $resourceTypes, $namespace, $resourceVersions);

            sleep($interval);
        }

        $this->info("[集群 {$clusterId}] 轮询已停止");
    }

    /**
     * 处理单个资源类型的变更检测和缓存
     */
    protected function processResourceTypeForChanges(int $clusterId, string $resourceType, array &$resourceVersions, ?string $namespace, ?array $watchableNamespaces): void
    {
        $config = $this->resourceConfigs[$resourceType];
        $currentResourceVersion = $resourceVersions[$resourceType] ?? '';

        $result = $this->fetchResourcesWithVersion($clusterId, $config['endpoint'], $namespace);
        $newResourceVersion = $result['resourceVersion'];

        // 如果 resourceVersion 没有变化，跳过处理
        if ($currentResourceVersion && $currentResourceVersion === $newResourceVersion) {
            return;
        }

        $changes = ['hasChanges' => false];

        // 如果这不是第一次轮询（有 currentResourceVersion），则进行变化检测
        if ($currentResourceVersion) {
            $changes = $this->calculateResourceChanges($clusterId, $resourceType, $result['items'], $namespace);
            if ($changes['hasChanges']) {
                $this->displayAndBroadcastChanges($clusterId, $resourceType, $config, $changes);
            }
        }

        // 总是更新缓存
        $this->updateResourceCache($clusterId, $resourceType, $result['items'], $namespace, $watchableNamespaces);

        // 更新 resourceVersion
        $resourceVersions[$resourceType] = $newResourceVersion;

        if ($this->getOutput()->isVerbose()) {
            $cluster = Cluster::find($clusterId);
            if ($cluster) {
                $changeInfo = $changes['hasChanges'] ?
                    sprintf('变化: +%d ±%d -%d', count($changes['created']), count($changes['updated']), count($changes['deleted'])) :
                    '无变化';
                $this->line("[{$cluster->name}] {$config['name']} {$changeInfo} (rv: {$currentResourceVersion} → {$newResourceVersion})");
                unset($cluster);
            }
        }
    }

    /**
     * 获取集群中所有可监听的命名空间
     */
    protected function getWatchableNamespaces(int $clusterId): array
    {
        $cluster = Cluster::find($clusterId);
        if (! $cluster) {
            $this->warn("获取命名空间列表时未找到集群 {$clusterId}");

            return [];
        }

        try {
            $curl = $cluster->curl('GET', '/api/v1/namespaces');
            $curl->exec();
            if ($curl->error) {
                throw new \Exception(is_string($curl->error_message ?? null) ? $curl->error_message : json_encode($curl->error));
            }

            $response = $curl->response;
            $data = is_string($response) ? (json_decode($response, true) ?? []) : (json_decode(json_encode($response), true) ?? []);
            $namespaces = [];

            foreach ($data['items'] ?? [] as $namespace) {
                $name = $namespace['metadata']['name'] ?? '';
                if ($name && $this->shouldWatchNamespace($name)) {
                    $namespaces[] = $name;
                }
            }

            return $namespaces;
        } catch (\Exception $e) {
            $this->warn("获取集群 {$cluster->name} 的命名空间列表失败: ".$e->getMessage());

            return [];
        } finally {
            if (isset($curl)) {
                $curl->close();
                unset($curl);
            }
            unset($cluster);
        }
    }

    /**
     * 从 K8s 获取资源
     */
    protected function fetchResourcesWithVersion(int $clusterId, string $endpoint, ?string $namespace): array
    {
        $cluster = Cluster::find($clusterId);
        if (! $cluster) {
            throw new \Exception("在 fetchResourcesWithVersion 中未找到集群 {$clusterId}");
        }

        try {
            if ($namespace && strpos($endpoint, '/namespaces/') === false) {
                $parts = explode('/', $endpoint);
                $lastPart = array_pop($parts);
                $endpoint = implode('/', $parts)."/namespaces/{$namespace}/".$lastPart;
            }

            $curl = $cluster->curl('GET', $endpoint);
            $curl->exec();
            if ($curl->error) {
                throw new \Exception("请求失败 [{$endpoint}]: ".(is_string($curl->error_message ?? null) ? $curl->error_message : json_encode($curl->error)));
            }

            $response = $curl->response;
            $data = is_string($response) ? (json_decode($response, true) ?? []) : (json_decode(json_encode($response), true) ?? []);

            return [
                'items' => $data['items'] ?? [],
                'resourceVersion' => $data['metadata']['resourceVersion'] ?? '',
            ];
        } finally {
            if (isset($curl)) {
                $curl->close();
                unset($curl);
            }
            unset($cluster);
        }
    }

    /**
     * 获取最新的 resourceVersion
     */
    protected function getLatestResourceVersion(int $clusterId, string $resourceType, ?string $namespace): string
    {
        try {
            $config = $this->resourceConfigs[$resourceType];
            $result = $this->fetchResourcesWithVersion($clusterId, $config['endpoint'], $namespace);

            return $result['resourceVersion'];
        } catch (\Throwable $e) {
            $cluster = Cluster::find($clusterId);
            $clusterName = $cluster ? $cluster->name : "ID {$clusterId}";
            if ($cluster) {
                unset($cluster);
            }
            $this->warn("[{$clusterName}] 获取 {$resourceType} resourceVersion 失败: ".$e->getMessage());

            return '';
        }
    }

    /**
     * 计算资源变化
     */
    protected function calculateResourceChanges(int $clusterId, string $resourceType, array $newItems, ?string $namespace): array
    {
        $cluster = Cluster::find($clusterId);
        if (! $cluster) {
            return ['created' => [], 'updated' => [], 'deleted' => [], 'hasChanges' => false];
        }

        try {
            $cachedResources = $this->getCachedResourcesByType($clusterId, $resourceType, $namespace);

            $cachedByUid = [];
            foreach ($cachedResources as $resource) {
                $uid = $resource['uid'] ?? $resource['metadata']['uid'] ?? '';
                if ($uid) {
                    $cachedByUid[$uid] = $resource;
                }
            }

            $config = $this->resourceConfigs[$resourceType];
            $dtoClass = $config['dto_class'];
            $newByUid = [];
            $newDtosByUid = [];

            foreach ($newItems as $item) {
                if ($this->shouldBroadcastResource($resourceType, $item)) {
                    $uid = $item['metadata']['uid'] ?? '';
                    if ($uid) {
                        $newByUid[$uid] = $item;
                        try {
                            $dto = $dtoClass::fromK8sResource($item);
                            $newDtosByUid[$uid] = $dto->toArray();
                        } catch (\Exception $e) {
                            $this->warn("[{$cluster->name}] {$config['name']} 转换新资源DTO失败: ".$e->getMessage());
                            unset($newByUid[$uid]);
                        }
                    }
                }
            }

            $created = [];
            $updated = [];
            $deleted = [];

            foreach ($newByUid as $uid => $newItem) {
                $newDto = $newDtosByUid[$uid];
                if (! isset($cachedByUid[$uid])) {
                    $created[] = $newDto;
                } else {
                    $cachedItem = $cachedByUid[$uid];
                    $cachedVersion = $cachedItem['resource_version'] ?? $cachedItem['resourceVersion'] ?? $cachedItem['metadata']['resourceVersion'] ?? '';
                    $newVersion = $newItem['metadata']['resourceVersion'] ?? '';

                    if ($cachedVersion !== $newVersion) {
                        $updatedDto = $newDto;
                        $updatedDto['previousResourceVersion'] = $cachedVersion;
                        $updated[] = $updatedDto;
                    }
                }
            }

            foreach ($cachedByUid as $uid => $cachedItem) {
                if (! isset($newByUid[$uid])) {
                    $deleted[] = $cachedItem;
                }
            }

            return [
                'created' => $created,
                'updated' => $updated,
                'deleted' => $deleted,
                'hasChanges' => ! empty($created) || ! empty($updated) || ! empty($deleted),
            ];
        } finally {
            unset($cluster);
        }
    }

    /**
     * 获取缓存中的资源（按类型）
     */
    protected function getCachedResourcesByType(int $clusterId, string $resourceType, ?string $namespace): array
    {
        try {
            if ($namespace) {
                $cacheData = $this->cacheService->getRawCachedData($clusterId, $namespace, $resourceType);

                return $cacheData['resources'] ?? [];
            } else {
                $allResources = [];
                $namespaces = $this->getWatchableNamespaces($clusterId);

                foreach ($namespaces as $ns) {
                    $cacheData = $this->cacheService->getRawCachedData($clusterId, $ns, $resourceType);
                    $resources = $cacheData['resources'] ?? [];
                    $allResources = array_merge($allResources, $resources);
                }

                return $allResources;
            }
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 更新资源缓存
     */
    protected function updateResourceCache(int $clusterId, string $resourceType, array $newItems, ?string $namespace, ?array $watchableNamespaces = null): void
    {
        $cluster = Cluster::find($clusterId);
        if (! $cluster) {
            return;
        }

        try {
            $config = $this->resourceConfigs[$resourceType];

            $filteredResources = array_filter(
                $newItems,
                fn ($resource) => $this->shouldBroadcastResource($resourceType, $resource)
            );

            $dtoClass = $config['dto_class'];
            $dtos = [];
            foreach ($filteredResources as $resource) {
                try {
                    $dtos[] = $dtoClass::fromK8sResource($resource);
                } catch (\Exception $e) {
                    $this->warn("[{$cluster->name}] {$config['name']} 转换 DTO 失败: ".$e->getMessage());
                }
            }

            $groupedByNamespace = [];
            foreach ($dtos as $dto) {
                $ns = $dto->namespace;
                $groupedByNamespace[$ns][] = $dto;
            }

            if ($namespace && $this->shouldWatchNamespace($namespace)) {
                $namespaceResources = $groupedByNamespace[$namespace] ?? [];
                $this->cacheResources($cluster->id, $namespace, $resourceType, $namespaceResources);
            } elseif (! $namespace) {
                if ($watchableNamespaces === null) {
                    $watchableNamespaces = $this->getWatchableNamespaces($clusterId);
                }
                foreach ($watchableNamespaces as $ns) {
                    $namespaceResources = $groupedByNamespace[$ns] ?? [];
                    $this->cacheResources($cluster->id, $ns, $resourceType, $namespaceResources);
                }
            }
        } finally {
            unset($cluster);
        }
    }

    /**
     * 显示和广播变化
     */
    protected function displayAndBroadcastChanges(int $clusterId, string $resourceType, array $config, array $changes): void
    {
        $cluster = Cluster::find($clusterId);
        if (! $cluster) {
            return;
        }

        try {
            if (! empty($changes['created'])) {
                foreach ($changes['created'] as $resourceDto) {
                    $name = $resourceDto['name'] ?? 'unknown';
                    $ns = $resourceDto['namespace'] ?? 'default';
                    $this->line("[{$cluster->name}] [{$ns}] {$config['name']}: [CREATED] {$name}");
                    $this->broadcastSingleResourceChange($cluster->id, $resourceType, 'created', $resourceDto);
                }
            }

            if (! empty($changes['updated'])) {
                foreach ($changes['updated'] as $resourceDto) {
                    $name = $resourceDto['name'] ?? 'unknown';
                    $ns = $resourceDto['namespace'] ?? 'default';
                    $currentVersion = $resourceDto['resource_version'] ?? $resourceDto['resourceVersion'] ?? '';
                    $previousVersion = $resourceDto['previousResourceVersion'] ?? '';
                    $this->line("[{$cluster->name}] [{$ns}] {$config['name']}: [UPDATED] {$name} (rv: {$previousVersion} → {$currentVersion})");
                    $this->broadcastSingleResourceChange($cluster->id, $resourceType, 'updated', $resourceDto);
                }
            }

            if (! empty($changes['deleted'])) {
                foreach ($changes['deleted'] as $resourceDto) {
                    $name = $resourceDto['name'] ?? 'unknown';
                    $ns = $resourceDto['namespace'] ?? 'default';
                    $this->line("[{$cluster->name}] [{$ns}] {$config['name']}: [DELETED] {$name}");
                    $this->broadcastSingleResourceChange($cluster->id, $resourceType, 'deleted', $resourceDto);
                }
            }
        } finally {
            unset($cluster);
        }
    }

    /**
     * 缓存资源
     */
    protected function cacheResources(int $clusterId, string $namespace, string $resourceType, array $resources): void
    {
        $processedResources = [];
        foreach ($resources as $dto) {
            $processedResources[] = $dto->toArray();
        }

        $cacheData = [
            'resources' => $processedResources,
            'cached_at' => time(),
        ];

        $this->cacheService->setCachedResources($clusterId, $namespace, $resourceType, $cacheData);
    }

    /**
     * 广播单个资源变化事件
     */
    protected function broadcastSingleResourceChange(int $clusterId, string $resourceType, string $changeType, array $resourceInfo): void
    {
        $namespace = $resourceInfo['namespace'];
        if (! $this->shouldWatchNamespace($namespace)) {
            return;
        }

        $cluster = Cluster::find($clusterId);
        if (! $cluster) {
            return;
        }

        try {
            event(new ResourceChanged(
                $namespace,
                $cluster->name,
                $cluster->id,
                $resourceType,
                [$changeType => [$resourceInfo]]
            ));

            if ($this->getOutput()->isVerbose()) {
                $this->line("    📡 已广播 {$namespace} 的 {$changeType} 事件");
            }
        } finally {
            unset($cluster);
        }
    }

    /**
     * 信号处理
     */
    public function handleSignal(int $signal, $previousExitCode = 0): int|false
    {
        if (is_array($previousExitCode)) {
            $previousExitCode = 0;
        }

        $this->line("\n收到停止信号，正在退出...");
        $this->shouldStop = true;

        return 0;
    }
}
