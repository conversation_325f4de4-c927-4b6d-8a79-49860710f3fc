<?php

namespace App\Console\Commands\Cluster;

use App\Enums\ClusterSetting;
use App\Models\Cluster;
use App\Service\LoadBalancerManager;
use Illuminate\Console\Command;

class SetDefaultDriver extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:set-default-driver 
                            {name : 集群名称或 ID}
                            {driver : LoadBalancer 驱动名称}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '设置集群的默认 LoadBalancer 驱动';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $clusterName = $this->argument('name');
            $driverName = $this->argument('driver');

            // 查找集群
            $cluster = $this->findCluster($clusterName);
            if (! $cluster) {
                $this->error('找不到指定的集群');

                return Command::FAILURE;
            }

            // 验证驱动
            $loadBalancerManager = app(LoadBalancerManager::class);
            if (! $loadBalancerManager->hasDriver($driverName)) {
                $availableDrivers = implode(', ', $loadBalancerManager->getAvailableDrivers());
                $this->error("无效的 LoadBalancer 驱动: {$driverName}");
                $this->error("可用的驱动: {$availableDrivers}");

                return Command::FAILURE;
            }

            // 设置默认驱动
            $cluster->setSetting(ClusterSetting::LOADBALANCER_DRIVER->value, $driverName);

            $this->info("✅ 已将集群 '{$cluster->name}' 的默认 LoadBalancer 驱动设置为: ".
                       $loadBalancerManager->getDriverDisplayName($driverName));

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('设置默认驱动失败: '.$e->getMessage());

            return Command::FAILURE;
        }
    }

    /**
     * 查找集群
     */
    private function findCluster(string $identifier): ?Cluster
    {
        // 首先尝试按 ID 查找
        if (is_numeric($identifier)) {
            $cluster = Cluster::find($identifier);
            if ($cluster) {
                return $cluster;
            }
        }

        // 然后按名称查找
        return Cluster::where('name', $identifier)->first();
    }
}
