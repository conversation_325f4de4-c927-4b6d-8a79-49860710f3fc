<?php

namespace App\Console\Commands\Cluster;

use App\Models\Cluster;
use App\Service\WorkspaceService;
use Illuminate\Console\Command;

class DeleteCluster extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:delete {id} {--force : 强制删除，不显示确认提示}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除一个 Kubernetes 集群';

    public function __construct(
        protected WorkspaceService $workspaceService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $id = $this->argument('id');
        $force = $this->option('force');

        // 查找集群
        $cluster = Cluster::find($id);

        if (! $cluster) {
            $this->error("集群 {$id} 不存在");

            return 1;
        }

        // 显示集群信息
        $this->info('集群信息：');
        $this->table(
            ['字段', '值'],
            [
                ['ID', $cluster->id],
                ['名称', $cluster->name],
                ['服务器地址', $cluster->server_url],
                ['认证类型', $cluster->auth_type],
                ['创建时间', $cluster->created_at->format('Y-m-d H:i:s')],
                ['更新时间', $cluster->updated_at->format('Y-m-d H:i:s')],
            ]
        );

        // 检查是否有关联的工作空间
        $workspaces = $cluster->workspaces;
        if ($workspaces->isNotEmpty()) {
            $this->info("找到 {$workspaces->count()} 个工作空间。");
            if (! $force && ! $this->confirm('是否删除所有工作空间？')) {
                $this->info('操作已取消');

                return 0;
            }

            foreach ($workspaces as $workspace) {
                try {
                    $success = $this->workspaceService->deleteWorkspace($workspace);
                    if ($success) {
                        $this->info("工作空间删除成功: {$workspace->id}");
                    } else {
                        $this->error("删除工作空间失败: {$workspace->id}");
                    }
                } catch (\Exception $e) {
                    $this->error("删除工作空间失败: {$workspace->id} - {$e->getMessage()}");
                }
            }

            $this->info('等待所有工作空间删除...');
            sleep(5);

            while ($cluster->workspaces()->exists()) {
                $this->info('等待所有工作空间删除...');
                sleep(5);
            }
        }

        try {
            $cluster->delete();
            $this->info("集群 {$cluster->name} 删除成功!");

            return 0;
        } catch (\Exception $e) {
            $this->error("删除集群失败: {$e->getMessage()}");

            return 1;
        }
    }
}
