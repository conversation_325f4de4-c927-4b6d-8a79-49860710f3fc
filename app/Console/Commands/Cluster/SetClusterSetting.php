<?php

namespace App\Console\Commands\Cluster;

use App\Models\Cluster;
use Illuminate\Console\Command;

class SetClusterSetting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:settings:set {name} {key} {value} {--json : 将值解析为 JSON} {--bool : 将值解析为布尔值} {--format=table : 输出格式 (table|json)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '设置指定集群的某个配置值';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->argument('name');
        $key = $this->argument('key');
        $value = $this->argument('value');
        $isJson = $this->option('json');
        $isBool = $this->option('bool');
        $format = $this->option('format');

        // 查找集群
        $cluster = Cluster::where('name', $name)->first();

        if (! $cluster) {
            $this->error("集群 {$name} 不存在");

            return 1;
        }

        // 处理值类型
        $processedValue = $this->processValue($value, $isJson, $isBool);

        if ($processedValue === null) {
            return 1; // 处理失败
        }

        // 获取旧值用于显示
        $oldValue = $cluster->getSetting($key);
        $isNew = $oldValue === null;

        // 设置值
        try {
            if ($isBool) {
                $setting = $cluster->setSettingBool($key, $processedValue);
            } else {
                $setting = $cluster->setSetting($key, $processedValue);
            }
        } catch (\Exception $e) {
            $this->error("设置失败：{$e->getMessage()}");

            return 1;
        }

        // 根据格式输出
        switch ($format) {
            case 'json':
                $this->outputJson($cluster, $key, $oldValue, $processedValue, $isNew, $setting);
                break;
            case 'table':
            default:
                $this->outputTable($cluster, $key, $oldValue, $processedValue, $isNew);
                break;
        }

        return 0;
    }

    /**
     * 处理输入值
     */
    private function processValue($value, $isJson, $isBool)
    {
        if ($isJson && $isBool) {
            $this->error('不能同时指定 --json 和 --bool 选项');

            return null;
        }

        if ($isJson) {
            $decoded = json_decode($value, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error('无效的 JSON 格式：'.json_last_error_msg());

                return null;
            }

            return $decoded;
        }

        if ($isBool) {
            // 处理布尔值
            $lowerValue = strtolower(trim($value));
            if (in_array($lowerValue, ['true', '1', 'yes', 'on', 'enabled'])) {
                return true;
            } elseif (in_array($lowerValue, ['false', '0', 'no', 'off', 'disabled'])) {
                return false;
            } else {
                $this->error("无效的布尔值：{$value}。支持的值：true/false, 1/0, yes/no, on/off, enabled/disabled");

                return null; // 使用null而不是false来表示错误
            }
        }

        // 默认作为字符串处理
        return $value;
    }

    /**
     * 以表格格式输出结果
     */
    private function outputTable($cluster, $key, $oldValue, $newValue, $isNew)
    {
        if ($isNew) {
            $this->info("成功为集群 {$cluster->name} 添加新设置：");
        } else {
            $this->info("成功更新集群 {$cluster->name} 的设置：");
        }

        $rows = [
            ['集群 ID', $cluster->id],
            ['集群名称', $cluster->name],
            ['设置键名', $key],
        ];

        if (! $isNew) {
            $oldValueDisplay = is_array($oldValue) || is_object($oldValue)
                ? json_encode($oldValue, JSON_UNESCAPED_UNICODE)
                : (string) $oldValue;
            $rows[] = ['旧值', $oldValueDisplay];
        }

        $newValueDisplay = is_array($newValue) || is_object($newValue)
            ? json_encode($newValue, JSON_UNESCAPED_UNICODE)
            : (string) $newValue;
        $rows[] = ['新值', $newValueDisplay];

        $this->table(['字段', '值'], $rows);
    }

    /**
     * 以 JSON 格式输出结果
     */
    private function outputJson($cluster, $key, $oldValue, $newValue, $isNew, $setting)
    {
        $data = [
            'cluster' => [
                'id' => $cluster->id,
                'name' => $cluster->name,
            ],
            'key' => $key,
            'old_value' => $oldValue,
            'new_value' => $newValue,
            'is_new' => $isNew,
            'setting_info' => [
                'id' => $setting->id,
                'created_at' => $setting->created_at->toISOString(),
                'updated_at' => $setting->updated_at->toISOString(),
            ],
        ];

        $this->line(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}
