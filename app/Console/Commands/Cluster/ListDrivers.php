<?php

namespace App\Console\Commands\Cluster;

use App\Service\LoadBalancerManager;
use Illuminate\Console\Command;

class ListDrivers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:drivers 
                            {--format=table : 输出格式 (table, json)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '列出所有可用的 LoadBalancer 驱动';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $format = $this->option('format');
            $loadBalancerManager = app(LoadBalancerManager::class);

            $drivers = $loadBalancerManager->getAvailableDrivers();
            $defaultDriver = $loadBalancerManager->getDefaultDriverName();

            if ($format === 'json') {
                $this->info(json_encode([
                    'drivers' => $drivers,
                    'default_driver' => $defaultDriver,
                    'driver_details' => array_map(function ($driver) use ($loadBalancerManager, $defaultDriver) {
                        return [
                            'name' => $driver,
                            'display_name' => $loadBalancerManager->getDriverDisplayName($driver),
                            'is_default' => $driver === $defaultDriver,
                        ];
                    }, $drivers),
                ], JSON_PRETTY_PRINT));
            } else {
                $this->info('可用的 LoadBalancer 驱动：');
                $this->newLine();

                $rows = [];
                foreach ($drivers as $driver) {
                    $rows[] = [
                        $driver,
                        $loadBalancerManager->getDriverDisplayName($driver),
                        $driver === $defaultDriver ? '✅ 默认' : '',
                    ];
                }

                $this->table(
                    ['驱动名称', '显示名称', '状态'],
                    $rows
                );
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('获取驱动列表失败: '.$e->getMessage());

            return Command::FAILURE;
        }
    }
}
