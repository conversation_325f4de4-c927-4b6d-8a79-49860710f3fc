<?php

namespace App\Console\Commands\Cluster;

use App\Models\Cluster;
use Illuminate\Console\Command;

class GetClusterSetting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:settings:get {name} {key} {--default= : 当设置不存在时的默认值} {--format=value : 输出格式 (value|json)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '获取指定集群的某个设置值';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->argument('name');
        $key = $this->argument('key');
        $default = $this->option('default');
        $format = $this->option('format');

        // 查找集群
        $cluster = Cluster::where('name', $name)->first();

        if (! $cluster) {
            $this->error("集群 {$name} 不存在");

            return 1;
        }

        // 获取设置值
        $value = $cluster->getSetting($key, $default);

        if ($value === null && $default === null) {
            $this->error("设置 {$key} 不存在，且未提供默认值");

            return 1;
        }

        // 根据格式输出
        switch ($format) {
            case 'json':
                $this->outputJson($cluster, $key, $value, $default);
                break;
            case 'value':
            default:
                $this->outputValue($value);
                break;
        }

        return 0;
    }

    /**
     * 以值格式输出（仅输出值，便于脚本使用）
     */
    private function outputValue($value)
    {
        if (is_array($value) || is_object($value)) {
            $this->line(json_encode($value, JSON_UNESCAPED_UNICODE));
        } else {
            $this->line((string) $value);
        }
    }

    /**
     * 以 JSON 格式输出设置信息
     */
    private function outputJson($cluster, $key, $value, $default)
    {
        $setting = $cluster->settings()->where('key', $key)->first();

        $data = [
            'cluster' => [
                'id' => $cluster->id,
                'name' => $cluster->name,
            ],
            'key' => $key,
            'value' => $value,
            'default_used' => $value === $default && $setting === null,
            'exists' => $setting !== null,
        ];

        if ($setting) {
            $data['setting_info'] = [
                'created_at' => $setting->created_at->toISOString(),
                'updated_at' => $setting->updated_at->toISOString(),
            ];
        }

        $this->line(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}
