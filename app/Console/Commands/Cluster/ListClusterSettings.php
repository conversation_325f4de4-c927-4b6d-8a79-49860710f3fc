<?php

namespace App\Console\Commands\Cluster;

use App\Models\Cluster;
use Illuminate\Console\Command;

class ListClusterSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:settings:list {name} {--format=table : 输出格式 (table|json)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '列出指定集群的所有设置';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->argument('name');
        $format = $this->option('format');

        // 查找集群
        $cluster = Cluster::where('name', $name)->first();

        if (! $cluster) {
            $this->error("集群 {$name} 不存在");

            return 1;
        }

        // 获取所有设置
        $settings = $cluster->getAllSettings();

        if ($settings->isEmpty()) {
            $this->info("集群 {$name} 没有设置任何配置");

            return 0;
        }

        // 根据格式输出
        switch ($format) {
            case 'json':
                $this->outputJson($cluster, $settings);
                break;
            case 'table':
            default:
                $this->outputTable($cluster, $settings);
                break;
        }

        return 0;
    }

    /**
     * 以表格格式输出设置列表
     */
    private function outputTable($cluster, $settings)
    {
        $this->info("集群 {$cluster->name} 的设置：");

        $headers = ['键名', '值', '创建时间', '更新时间'];
        $rows = [];

        foreach ($settings as $setting) {
            $value = $setting->value;

            // 如果值是数组或对象，转换为 JSON 字符串显示
            if (is_array($value) || is_object($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }

            // 如果值太长，进行截断
            if (strlen($value) > 100) {
                $value = substr($value, 0, 100).'...';
            }

            $rows[] = [
                $setting->key,
                $value,
                $setting->created_at->format('Y-m-d H:i:s'),
                $setting->updated_at->format('Y-m-d H:i:s'),
            ];
        }

        $this->table($headers, $rows);
        $this->info("\n共找到 {$settings->count()} 个设置");
    }

    /**
     * 以 JSON 格式输出设置列表
     */
    private function outputJson($cluster, $settings)
    {
        $data = [
            'cluster' => [
                'id' => $cluster->id,
                'name' => $cluster->name,
            ],
            'settings' => $settings->map(function ($setting) {
                return [
                    'key' => $setting->key,
                    'value' => $setting->value,
                    'created_at' => $setting->created_at->toISOString(),
                    'updated_at' => $setting->updated_at->toISOString(),
                ];
            }),
            'count' => $settings->count(),
        ];

        $this->line(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}
