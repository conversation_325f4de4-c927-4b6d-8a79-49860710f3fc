<?php

namespace App\Console\Commands\Pod;

use App\Models\Workspace;
use App\Service\PodTerminalService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestPodExecCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pod:test-exec 
                            {workspace_id : The workspace ID}
                            {pod_name : The pod name}
                            {--container= : The container name (optional)}
                            {--command=* : The command to execute (can be specified multiple times)}
                            {--timeout=30 : Command timeout in seconds}
                            {--working-dir= : Working directory}
                            {--environment=* : Environment variables in KEY=VALUE format}
                            {--real-time : Enable real-time output display}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Pod command execution using PodTerminalService (supports real-time output)';

    public function __construct(
        protected PodTerminalService $podTerminalService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $workspaceId = $this->argument('workspace_id');
        $podName = $this->argument('pod_name');
        $containerName = $this->option('container');
        $commands = $this->option('command');
        $timeout = (int) $this->option('timeout');
        $workingDir = $this->option('working-dir');
        $envVars = $this->option('environment');
        $realTime = $this->option('real-time');

        // 验证工作空间
        $workspace = Workspace::find($workspaceId);
        if (! $workspace) {
            $this->error("Workspace with ID {$workspaceId} not found");

            return 1;
        }

        // 如果没有指定命令，使用默认的测试命令
        if (empty($commands)) {
            $commands = ['echo', 'Hello from Pod!'];
            $this->info('No command specified, using default: echo "Hello from Pod!"');
        }

        // 解析环境变量
        $env = [];
        if ($envVars) {
            foreach ($envVars as $envVar) {
                if (strpos($envVar, '=') !== false) {
                    [$key, $value] = explode('=', $envVar, 2);
                    $env[$key] = $value;
                } else {
                    $this->warn("Invalid environment variable format: {$envVar}. Expected KEY=VALUE");
                }
            }
        }

        $this->info('Executing command in Pod...');
        $this->info("Workspace: {$workspace->name} (ID: {$workspace->id})");
        $this->info("Pod: {$podName}");
        $this->info('Container: '.($containerName ?: 'auto-detect'));
        $this->info('Command: '.implode(' ', $commands));
        $this->info("Timeout: {$timeout} seconds");

        if ($workingDir) {
            $this->info("Working Directory: {$workingDir}");
        }

        if (! empty($env)) {
            $this->info('Environment Variables:');
            foreach ($env as $key => $value) {
                $this->info("  {$key}={$value}");
            }
        }

        $this->newLine();

        $startTime = microtime(true);

        try {
            // 根据是否启用实时输出选择不同的执行方法
            if ($realTime) {
                $this->info('🔄 Real-time output enabled');
                $this->newLine();

                // 创建实时输出回调
                $outputCallback = function (string $type, string $data) {
                    switch ($type) {
                        case 'stdout':
                            // 直接输出到控制台，不换行
                            echo $data;
                            break;
                        case 'stderr':
                            // 使用错误样式输出
                            $this->getOutput()->write("<error>{$data}</error>");
                            break;
                        case 'error':
                            $this->newLine();
                            $this->error("❌ Error: {$data}");
                            break;
                        case 'exit_code':
                            $this->newLine();
                            $this->info("🏁 Exit code: {$data}");
                            break;
                        case 'timeout':
                            $this->newLine();
                            $this->warn("⏰ {$data}");
                            break;
                        case 'completed':
                            $this->newLine();
                            $this->info("✅ {$data}");
                            break;
                    }
                };

                // 执行命令（实时输出）
                $result = $this->podTerminalService->executeCommandWithRealTimeOutput(
                    workspace: $workspace,
                    podName: $podName,
                    containerName: $containerName,
                    command: $commands,
                    outputCallback: $outputCallback,
                    workingDir: $workingDir,
                    env: $env,
                    timeout: $timeout
                );

                $this->newLine();
                $this->info('📊 Final Result Summary:');
                $this->displayResult($result);

            } else {
                // 执行命令（传统方式）
                $result = $this->podTerminalService->executeCommand(
                    workspace: $workspace,
                    podName: $podName,
                    containerName: $containerName,
                    command: $commands,
                    workingDir: $workingDir,
                    env: $env,
                    timeout: $timeout
                );

                // 显示结果
                $this->displayResult($result);
            }

            $executionTime = microtime(true) - $startTime;

            // 记录到日志
            Log::info('Pod command execution test completed', [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'command' => $commands,
                'real_time' => $realTime,
                'result' => $result->toArray(),
                'execution_time' => $executionTime,
            ]);

            return $result->isSuccess() ? 0 : 1;

        } catch (\Exception $e) {
            $this->error("Command execution failed: {$e->getMessage()}");
            $this->error('Stack trace:');
            $this->error($e->getTraceAsString());

            Log::error('Pod command execution test failed', [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'command' => $commands,
                'real_time' => $realTime,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }

    /**
     * 显示命令执行结果
     */
    protected function displayResult($result): void
    {
        $this->info('=== Execution Result ===');

        // 基本信息
        $this->info('Status: '.($result->isSuccess() ? '<info>SUCCESS</info>' : '<error>FAILED</error>'));
        $this->info('Completed: '.($result->completed ? 'Yes' : 'No'));
        $this->info('Timeout: '.($result->timeout ? 'Yes' : 'No'));
        $this->info('Execution Time: '.round($result->executionTime, 3).'s');

        if ($result->exitCode !== null) {
            $this->info("Exit Code: {$result->exitCode}");
        }

        // 错误信息
        if ($result->error) {
            $this->error("Error: {$result->error}");
        }

        // 标准输出
        if (! empty($result->stdout)) {
            $this->newLine();
            $this->info('=== STDOUT ===');
            $this->line($result->stdout);
        }

        // 标准错误输出
        if (! empty($result->stderr)) {
            $this->newLine();
            $this->info('=== STDERR ===');
            $this->line($result->stderr);
        }

        // 如果没有输出
        if (! $result->hasOutput() && ! $result->error) {
            $this->newLine();
            $this->comment('No output received from command');
        }
    }
}
