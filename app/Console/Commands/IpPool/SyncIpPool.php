<?php

namespace App\Console\Commands\IpPool;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Service\IpPoolService;
use Illuminate\Console\Command;

class SyncIpPool extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ippool:sync
                            {--pool-id= : 同步指定 ID 的 IP 池}
                            {--pool-name= : 同步指定名称的 IP 池}
                            {--cluster-id= : 同步指定集群中的所有 IP 池}
                            {--all : 同步所有 IP 池}
                            {--force : 强制重新同步，即使已同步}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步 IP 池到 K8s 集群';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $ipPoolService = app(IpPoolService::class);
            $pools = $this->getPoolsToSync();

            if ($pools->isEmpty()) {
                $this->warn('没有找到要同步的 IP 池');

                return Command::SUCCESS;
            }

            $this->info("找到 {$pools->count()} 个 IP 池需要同步");

            $progressBar = $this->output->createProgressBar($pools->count());
            $progressBar->start();

            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            foreach ($pools as $pool) {
                try {
                    // 检查是否需要强制同步
                    if (! $this->option('force') && $pool->synced_to_k8s && ! $pool->sync_error) {
                        $this->newLine();
                        $this->info("跳过已同步的 IP 池: {$pool->name} (使用 --force 强制重新同步)");
                        $progressBar->advance();

                        continue;
                    }

                    $ipPoolService->syncToKubernetes($pool);
                    $successCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = [
                        'pool' => $pool->name,
                        'error' => $e->getMessage(),
                    ];
                }

                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine(2);

            // 显示同步结果
            if ($successCount > 0) {
                $this->info("✅ 成功同步 {$successCount} 个 IP 池");
            }

            if ($errorCount > 0) {
                $this->error("❌ {$errorCount} 个 IP 池同步失败:");
                foreach ($errors as $error) {
                    $this->error("  - {$error['pool']}: {$error['error']}");
                }
            }

            return $errorCount > 0 ? Command::FAILURE : Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('同步失败: '.$e->getMessage());
            if ($this->option('verbose')) {
                $this->error('详细错误信息: '.$e->getTraceAsString());
            }

            return Command::FAILURE;
        }
    }

    /**
     * 获取要同步的 IP 池
     */
    private function getPoolsToSync()
    {
        $poolId = $this->option('pool-id');
        $poolName = $this->option('pool-name');
        $clusterId = $this->option('cluster-id');
        $syncAll = $this->option('all');

        // 验证参数
        $optionCount = collect([$poolId, $poolName, $clusterId, $syncAll])->filter()->count();
        if ($optionCount !== 1) {
            $this->error('必须指定且仅能指定以下选项之一: --pool-id, --pool-name, --cluster-id, --all');

            return collect();
        }

        $query = IpPool::where('is_active', true);

        if ($poolId) {
            $query->where('id', $poolId);
        } elseif ($poolName) {
            $query->where('name', $poolName);
        } elseif ($clusterId) {
            // 验证集群是否存在
            $cluster = Cluster::find($clusterId);
            if (! $cluster) {
                $this->error("找不到 ID 为 {$clusterId} 的集群");

                return collect();
            }
            $query->where('cluster_id', $clusterId);
        } elseif ($syncAll) {
            // 不添加额外条件，同步所有活跃的 IP 池
        }

        return $query->with('cluster')->get();
    }
}
