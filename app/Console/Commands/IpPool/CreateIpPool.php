<?php

namespace App\Console\Commands\IpPool;

use App\Enums\ClusterSetting;
use App\Models\Cluster;
use App\Models\DisabledIp;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Service\IpPoolService;
use Illuminate\Console\Command;
use PhpIP\IP;
use PhpIP\IPBlock;

class CreateIpPool extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ippool:create
                            {range : IP 范围 (例如: ***********-************ 或 ***********/24)}
                            {--name= : IP 池名称 (必需)}
                            {--cluster-id= : 集群 ID (必需)}
                            {--desc= : IP 池描述}
                            {--driver=metallb : LoadBalancer 驱动 (metallb, purelb, cilium)}
                            {--ip-version=ipv4 : IP 版本 (ipv4, ipv6, dual)}
                            {--sharing=shared : 共享策略 (shared=共享, dedicated=独享)}
                            {--allocation=least_used : 分配策略 (least_used, round_robin, random)}
                            {--port-start=10000 : 端口范围开始 (共享模式时使用)}
                            {--port-end=30000 : 端口范围结束 (共享模式时使用)}
                            {--subnet-v4= : IPv4 子网 CIDR (IPv4 池必需, 例如: ***********/24)}
                            {--gateway-v4= : IPv4 网关}
                            {--subnet-v6= : IPv6 子网 CIDR (IPv6 池必需, 例如: fd53:9ef0:8683::/120)}
                            {--gateway-v6= : IPv6 网关}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建 IP 池并可选择同步到 K8s 集群';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            // 验证集群
            $cluster = $this->getCluster($this->option('cluster-id'));
            if (! $cluster) {
                $this->error('找不到指定的集群');

                return Command::FAILURE;
            }

            $this->info("正在为集群 '{$cluster->name}' 创建 IP 池...");

            // 验证参数
            $name = $this->option('name');
            $ipVersion = $this->option('ip-version');
            $driver = $this->option('driver');
            $sharingStrategy = $this->option('sharing');
            $allocationStrategy = $this->option('allocation');
            $portStart = (int) $this->option('port-start');
            $portEnd = (int) $this->option('port-end');
            $subnetV4 = $this->option('subnet-v4');
            $gatewayV4 = $this->option('gateway-v4');
            $subnetV6 = $this->option('subnet-v6');
            $gatewayV6 = $this->option('gateway-v6');
            $description = $this->option('desc') ?? "IP 池 {$name}";

            if (! $name) {
                $this->error('必须指定 IP 池名称 (--name)');

                return Command::FAILURE;
            }

            // 验证 LoadBalancer 驱动
            $loadBalancerManager = app(\App\Service\LoadBalancerManager::class);

            // 如果没有指定驱动，尝试使用集群的默认驱动
            if (! $driver || $driver === 'metallb') {
                $clusterDefaultDriver = $cluster->getSetting(ClusterSetting::LOADBALANCER_DRIVER->value, 'metallb');
                if ($clusterDefaultDriver !== $driver) {
                    $driver = $clusterDefaultDriver;
                    $this->warn('未指定驱动或使用默认值，自动使用集群默认驱动: '.$loadBalancerManager->getDriverDisplayName($driver));
                }
            }

            if (! $loadBalancerManager->hasDriver($driver)) {
                $availableDrivers = implode(', ', $loadBalancerManager->getAvailableDrivers());
                $this->error("无效的 LoadBalancer 驱动: {$driver}");
                $this->error("可用的驱动: {$availableDrivers}");

                return Command::FAILURE;
            }

            $this->info('使用 LoadBalancer 驱动: '.$loadBalancerManager->getDriverDisplayName($driver));

            // 验证 IP 版本
            if (! in_array($ipVersion, ['ipv4', 'ipv6', 'dual'])) {
                $this->error('无效的 IP 版本，可选值: ipv4, ipv6, dual');

                return Command::FAILURE;
            }

            // 验证共享策略
            if (! in_array($sharingStrategy, ['shared', 'dedicated'])) {
                $this->error('无效的共享策略，可选值: shared, dedicated');

                return Command::FAILURE;
            }

            // 验证分配策略
            if (! in_array($allocationStrategy, ['least_used', 'round_robin', 'random'])) {
                $this->error('无效的分配策略，可选值: least_used, round_robin, random');

                return Command::FAILURE;
            }

            // 验证端口范围
            if ($sharingStrategy === 'shared') {
                if ($portStart <= 0 || $portEnd <= 0 || $portStart >= $portEnd) {
                    $this->error('共享模式下必须指定有效的端口范围 (--port-start 和 --port-end)');

                    return Command::FAILURE;
                }
                if ($portStart < 1024 || $portEnd > 65535) {
                    $this->error('端口范围必须在 1024-65535 之间');

                    return Command::FAILURE;
                }
            } else {
                // 独享模式使用固定的端口范围
                $portStart = 80;
                $portEnd = 80;
            }

            // 验证子网配置
            if ($ipVersion === 'ipv4' && ! $subnetV4) {
                $this->error('IPv4 IP 池必须提供 --subnet-v4 参数');

                return Command::FAILURE;
            }
            if ($ipVersion === 'ipv6' && ! $subnetV6) {
                $this->error('IPv6 IP 池必须提供 --subnet-v6 参数');

                return Command::FAILURE;
            }
            if ($ipVersion === 'dual' && (! $subnetV4 || ! $subnetV6)) {
                $this->error('双栈 IP 池必须同时提供 --subnet-v4 和 --subnet-v6 参数');

                return Command::FAILURE;
            }

            // 检查 IP 池名称是否已存在
            if (IpPool::where('cluster_id', $cluster->id)->where('name', $name)->exists()) {
                $this->error("IP 池名称 '{$name}' 在此集群中已存在");

                return Command::FAILURE;
            }

            // 解析 IP 范围
            $ips = $this->parseIpRange($this->argument('range'));

            // 对于IPv6 CIDR，ips可能为空（动态生成），这是正常的
            $isDynamicGeneration = empty($ips) && ($ipVersion === 'ipv6' || str_contains($this->argument('range'), ':'));

            if (empty($ips) && ! $isDynamicGeneration) {
                $this->error('无效的 IP 范围格式');

                return Command::FAILURE;
            }

            // 检测 IP 版本
            if ($isDynamicGeneration) {
                // 对于动态生成的情况，使用指定的IP版本
                $ipVersionResult = $ipVersion;
                $this->info("将使用动态生成的 IPv6 地址段 (版本: {$ipVersionResult})");
            } else {
                $ipVersionResult = $this->detectIpVersion($ips);
                $this->info('解析到 '.count($ips)." 个 IP 地址 (版本: {$ipVersionResult})");

                // 验证检测到的 IP 版本与指定版本是否匹配
                if ($ipVersion !== 'dual' && $ipVersionResult !== $ipVersion) {
                    $this->error("指定的 IP 版本 ({$ipVersion}) 与检测到的版本 ({$ipVersionResult}) 不匹配");

                    return Command::FAILURE;
                }
            }

            // 验证子网配置
            if ($ipVersionResult === IpPool::IP_VERSION_IPV4 && ! $subnetV4) {
                $this->error('IPv4 IP 池必须提供 --subnet-v4 参数');

                return Command::FAILURE;
            }
            if ($ipVersionResult === IpPool::IP_VERSION_IPV6 && ! $subnetV6) {
                $this->error('IPv6 IP 池必须提供 --subnet-v6 参数');

                return Command::FAILURE;
            }
            if ($ipVersionResult === IpPool::IP_VERSION_DUAL && (! $subnetV4 || ! $subnetV6)) {
                $this->error('双栈 IP 池必须同时提供 --subnet-v4 和 --subnet-v6 参数');

                return Command::FAILURE;
            }

            // 创建 IP 池
            $ipPool = IpPool::create([
                'cluster_id' => $cluster->id,
                'name' => $name,
                'description' => $description,
                'driver' => $driver,
                'ip_version' => $ipVersionResult,
                'sharing_strategy' => $sharingStrategy,
                'allocation_strategy' => $allocationStrategy,
                'subnet_v4' => $subnetV4,
                'gateway_v4' => $gatewayV4,
                'subnet_v6' => $subnetV6,
                'gateway_v6' => $gatewayV6,
                'is_active' => true,
            ]);

            $this->info("IP 池 '{$name}' 创建成功");

            $actualCreated = 0;
            $skippedCount = 0;

            // 只有在非动态生成模式下才预创建 IP 地址
            if (! $isDynamicGeneration && ! empty($ips)) {
                // 创建 IP 地址
                $progressBar = $this->output->createProgressBar(count($ips));
                $progressBar->start();

                // 获取预禁用的 IP 列表
                $preDisabledIps = DisabledIp::getDisabledIps();

                foreach ($ips as $ip) {
                    // 检查是否在预禁用列表中
                    if (in_array($ip, $preDisabledIps)) {
                        $this->newLine();
                        $this->warn("跳过预禁用的 IP: {$ip}");
                        $skippedCount++;
                        $progressBar->advance();

                        continue;
                    }

                    $poolIpData = [
                        'ip_pool_id' => $ipPool->id,
                        'ip_address' => $ip,
                        'usage_count' => 0,
                        'is_active' => true,
                        'port_range_start' => $portStart,
                        'port_range_end' => $portEnd,
                    ];

                    PoolIp::create($poolIpData);
                    $progressBar->advance();
                }

                $progressBar->finish();
                $this->newLine(2);

                $actualCreated = count($ips) - $skippedCount;

                if ($skippedCount > 0) {
                    $this->warn("跳过了 {$skippedCount} 个预禁用的 IP 地址");
                }
            } else {
                $this->info('使用动态生成模式，IP 地址将在需要时自动生成');
            }

            // 显示创建摘要
            $this->info('🎉 IP 池创建完成！');
            $this->table(
                ['属性', '值'],
                [
                    ['集群', $cluster->name],
                    ['名称', $ipPool->name],
                    ['驱动', $loadBalancerManager->getDriverDisplayName($ipPool->driver)],
                    ['IP 版本', $ipPool->ip_version],
                    ['共享策略', $ipPool->sharing_strategy === 'shared' ? '共享' : '独享'],
                    ['分配策略', $this->translateAllocationStrategy($ipPool->allocation_strategy)],
                    ['IP 数量', $actualCreated],
                    ['端口范围', $sharingStrategy === 'shared' ? "{$portStart}-{$portEnd}" : 'N/A (独享模式)'],
                    ['IPv4 子网', $subnetV4 ?: 'N/A'],
                    ['IPv4 网关', $gatewayV4 ?: 'N/A'],
                    ['IPv6 子网', $subnetV6 ?: 'N/A'],
                    ['IPv6 网关', $gatewayV6 ?: 'N/A'],
                    ['状态', $ipPool->is_active ? '启用' : '禁用'],
                ]
            );

            // 可选择同步到 K8s 集群
            $this->newLine();
            $this->info('正在同步到 K8s 集群...');

            try {
                $ipPoolService = app(IpPoolService::class);
                $ipPoolService->syncToKubernetes($ipPool);
                $this->info('✅ 已成功同步到 K8s 集群');
            } catch (\Exception $e) {
                $this->error('⚠️  同步到 K8s 失败: '.$e->getMessage());
                $this->warn('IP 池已创建，但同步失败。可以稍后手动同步。');
                $this->warn('同步命令: php artisan ippool:sync --pool-id='.$ipPool->id);
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('创建 IP 池失败: '.$e->getMessage());
            if ($this->option('verbose')) {
                $this->error('详细错误信息: '.$e->getTraceAsString());
            }

            return Command::FAILURE;
        }
    }

    /**
     * 获取集群
     */
    private function getCluster(string $identifier): ?Cluster
    {
        // 首先尝试按 ID 查找
        if (is_numeric($identifier)) {
            $cluster = Cluster::find($identifier);
            if ($cluster) {
                return $cluster;
            }
        }

        // 然后按名称查找
        return Cluster::where('name', $identifier)->first();
    }

    /**
     * 解析 IP 范围
     */
    private function parseIpRange(string $range): array
    {
        $ips = [];

        try {
            // 检查是否是逗号分隔的多个 IP
            if (str_contains($range, ',')) {
                $ipList = explode(',', $range);
                foreach ($ipList as $singleIp) {
                    $singleIp = trim($singleIp);
                    $parsed = $this->parseSingleIpOrRange($singleIp);
                    $ips = array_merge($ips, $parsed);
                }
            } else {
                $ips = $this->parseSingleIpOrRange($range);
            }
        } catch (\Exception $e) {
            $this->error('解析 IP 范围失败: '.$e->getMessage());

            return [];
        }

        return array_unique($ips);
    }

    /**
     * 解析单个 IP 或 IP 范围
     */
    private function parseSingleIpOrRange(string $range): array
    {
        $ips = [];

        if (str_contains($range, '-')) {
            // 范围格式: ***********-************
            [$start, $end] = explode('-', $range, 2);
            $start = trim($start);
            $end = trim($end);

            $startIp = IP::create($start);
            $endIp = IP::create($end);

            if ($startIp->getVersion() !== $endIp->getVersion()) {
                throw new \Exception('起始和结束 IP 必须是同一版本');
            }

            // 对于 IPv4，生成范围内的所有 IP
            if ($startIp->getVersion() === 4) {
                $startLong = ip2long($start);
                $endLong = ip2long($end);

                if ($startLong > $endLong) {
                    throw new \Exception('起始 IP 不能大于结束 IP');
                }

                if ($endLong - $startLong > 1000) {
                    throw new \Exception('IP 范围不能超过 1000 个地址');
                }

                for ($i = $startLong; $i <= $endLong; $i++) {
                    $ips[] = long2ip($i);
                }
            } else {
                // 对于 IPv6，只添加起始和结束地址
                $ips[] = $start;
                if ($start !== $end) {
                    $ips[] = $end;
                }
            }
        } elseif (str_contains($range, '/')) {
            // CIDR 格式: ***********/24 或 2404:8c80:82:1075::/64
            $block = IPBlock::create($range);

            if ($block->getVersion() === 4) {
                $iterator = $block->getIterator();
                $count = 0;
                foreach ($iterator as $ip) {
                    if ($count >= 1000) {
                        break; // 限制最大数量
                    }
                    $ips[] = $ip->humanReadable();
                    $count++;
                }
            } else {
                // IPv6 CIDR，不预生成IP地址，使用动态生成
                // 返回空数组，IP将在需要时动态生成
                return [];
            }
        } else {
            // 单个 IP
            $ip = IP::create($range);
            $ips[] = $ip->humanReadable();
        }

        return $ips;
    }

    /**
     * 检测 IP 版本
     */
    private function detectIpVersion(array $ips): string
    {
        $hasIpv4 = false;
        $hasIpv6 = false;

        foreach ($ips as $ip) {
            try {
                $ipObj = IP::create($ip);
                if ($ipObj->getVersion() === 4) {
                    $hasIpv4 = true;
                } elseif ($ipObj->getVersion() === 6) {
                    $hasIpv6 = true;
                }
            } catch (\Exception $e) {
                // 忽略无效 IP
                continue;
            }
        }

        if ($hasIpv4 && $hasIpv6) {
            return IpPool::IP_VERSION_DUAL;
        } elseif ($hasIpv6) {
            return IpPool::IP_VERSION_IPV6;
        } else {
            return IpPool::IP_VERSION_IPV4;
        }
    }

    /**
     * 翻译分配策略
     */
    private function translateAllocationStrategy(string $strategy): string
    {
        return match ($strategy) {
            'least_used' => '最少使用',
            'round_robin' => '轮询',
            'random' => '随机',
            default => $strategy,
        };
    }
}
