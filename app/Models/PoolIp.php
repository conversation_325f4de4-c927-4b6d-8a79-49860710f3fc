<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use PhpIP\IP;

class PoolIp extends Model
{
    use HasFactory;

    protected $fillable = [
        'ip_pool_id',
        'ip_address',
        'port_range_start',
        'port_range_end',
        'usage_count',
        'is_active',
    ];

    protected $casts = [
        'port_range_start' => 'integer',
        'port_range_end' => 'integer',
        'usage_count' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * 关联到 IP 池
     */
    public function ipPool(): BelongsTo
    {
        return $this->belongsTo(IpPool::class);
    }

    /**
     * 关联到端口分配
     */
    public function portAllocations(): HasMany
    {
        return $this->hasMany(PortAllocation::class);
    }

    /**
     * 获取活跃的端口分配
     */
    public function activePortAllocations(): HasMany
    {
        return $this->portAllocations()->where('status', 'allocated');
    }

    /**
     * 获取 IP 地址
     */
    public function getIpAttribute(): string
    {
        return $this->ip_address;
    }

    /**
     * 验证 IP 地址格式
     */
    public function isValidIpAddress(): bool
    {
        try {
            IP::create($this->ip_address);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取 IP 地址类型
     */
    public function getIpType(): string
    {
        try {
            $ip = IP::create($this->ip_address);

            return $ip->getVersion() === 4 ? 'IPv4' : 'IPv6';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * 获取可用端口数量
     */
    public function getAvailablePortsCountAttribute(): int
    {
        $totalPorts = $this->port_range_end - $this->port_range_start + 1;
        $allocatedPorts = $this->activePortAllocations()->count();

        return $totalPorts - $allocatedPorts;
    }

    /**
     * 获取已使用端口数量
     */
    public function getUsedPortsCountAttribute(): int
    {
        return $this->activePortAllocations()->count();
    }

    /**
     * 获取端口利用率
     */
    public function getPortUtilizationAttribute(): float
    {
        $totalPorts = $this->port_range_end - $this->port_range_start + 1;
        $usedPorts = $this->used_ports_count;

        return $totalPorts > 0 ? round(($usedPorts / $totalPorts) * 100, 2) : 0;
    }

    /**
     * 检查端口是否在范围内
     */
    public function isPortInRange(int $port): bool
    {
        return $port >= $this->port_range_start && $port <= $this->port_range_end;
    }

    /**
     * 检查端口是否可用
     */
    public function isPortAvailable(int $port): bool
    {
        if (! $this->isPortInRange($port)) {
            return false;
        }

        return ! $this->activePortAllocations()
            ->where('port', $port)
            ->exists();
    }

    /**
     * 获取下一个可用端口
     */
    public function getNextAvailablePort(): ?int
    {
        $allocatedPorts = $this->activePortAllocations()
            ->pluck('port')
            ->toArray();

        for ($port = $this->port_range_start; $port <= $this->port_range_end; $port++) {
            if (! in_array($port, $allocatedPorts)) {
                return $port;
            }
        }

        return null;
    }

    /**
     * 增加使用次数
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * 减少使用次数
     */
    public function decrementUsage(): void
    {
        if ($this->usage_count > 0) {
            $this->decrement('usage_count');
        }
    }

    /**
     * 范围查询：只获取活跃的 IP
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 范围查询：按使用次数排序
     */
    public function scopeOrderByUsage($query, $direction = 'asc')
    {
        return $query->orderBy('usage_count', $direction);
    }
}
