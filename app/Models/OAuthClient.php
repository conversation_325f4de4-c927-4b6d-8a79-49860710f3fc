<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Storage;
use <PERSON>vel\Passport\Client as PassportClient;

class OAuthClient extends PassportClient
{
    protected $fillable = [
        'id',
        'owner_type',
        'owner_id',
        'name',
        'secret',
        'provider',
        'redirect_uris',
        'grant_types',
        'revoked',
        'trusted',
        'icon_path',
    ];

    protected $casts = [
        'redirect_uris' => 'array',
        'grant_types' => 'array',
        'revoked' => 'boolean',
        'trusted' => 'boolean',
    ];

    protected $hidden = [
        'secret',
    ];

    /**
     * 获取拥有者关系
     */
    public function owner(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 获取第一个重定向 URI
     */
    public function getRedirectAttribute(): string
    {
        return $this->redirect_uris[0] ?? '';
    }

    /**
     * 获取图标完整 URL
     */
    public function getIconUrlAttribute(): ?string
    {
        if (! $this->icon_path) {
            return null;
        }

        return Storage::disk('public')->url($this->icon_path);
    }

    /**
     * 删除图标文件
     */
    public function deleteIcon(): bool
    {
        if ($this->icon_path && Storage::disk('public')->exists($this->icon_path)) {
            return Storage::disk('public')->delete($this->icon_path);
        }

        return true;
    }

    /**
     * 检查是否为 PKCE 客户端
     */
    public function getIsPkceClientAttribute(): bool
    {
        return empty($this->secret);
    }

    /**
     * 检查是否支持 Device Flow
     */
    public function getSupportsDeviceFlowAttribute(): bool
    {
        return in_array('urn:ietf:params:oauth:grant-type:device_code', $this->grant_types ?? []);
    }
}
