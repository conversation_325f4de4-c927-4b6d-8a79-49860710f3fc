<?php

namespace App\Models;

use App\Service\CertificateCacheService;
use App\Traits\HasSettings;
use App\Traits\Pricable;
use Curl\Curl;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Cluster extends Model
{
    use Cachable, HasFactory, HasSettings, Pricable;

    /**
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'server_url',
        'auth_type',
        'insecure_skip_tls_verify',
        'certificate_authority_data',
        'client_certificate_data',
        'client_key_data',
        'token',
        'username',
        'password',
    ];

    /**
     * @var array<string, string>
     */
    protected $casts = [
        'insecure_skip_tls_verify' => 'boolean',
        'certificate_authority_data' => 'encrypted',
        'client_certificate_data' => 'encrypted',
        'client_key_data' => 'encrypted',
        'token' => 'encrypted',
        'password' => 'encrypted',
    ];

    protected $hidden = [
        'auth_type',
        'certificate_authority_data',
        'client_certificate_data',
        'client_key_data',
        'token',
        'username',
        'password',
        'insecure_skip_tls_verify',
        'server_url',
    ];

    public function http(): PendingRequest
    {
        $client = Http::baseUrl($this->server_url)
            ->withHeaders([
                'Accept' => 'application/json',
            ])
            ->timeout(30) // 30秒超时
            ->connectTimeout(10) // 10秒连接超时
            ->throw();

        // 根据认证类型配置客户端
        try {
            switch ($this->auth_type) {
                case 'token':
                    if (empty($this->token)) {
                        throw new \Exception('Token authentication requires a token');
                    }
                    $client = $client->withHeaders([
                        'Authorization' => 'Bearer '.$this->token,
                    ]);
                    break;

                case 'certificate':
                    if (empty($this->client_certificate_data) || empty($this->client_key_data)) {
                        throw new \Exception('Certificate authentication requires certificate and key data');
                    }
                    // 使用证书缓存服务
                    $certFile = CertificateCacheService::getCachedCertificateFile('cert', $this->client_certificate_data);
                    $keyFile = CertificateCacheService::getCachedCertificateFile('key', $this->client_key_data);
                    $client = $client->withOptions([
                        'cert' => $certFile,
                        'ssl_key' => $keyFile,
                    ]);
                    break;

                case 'username_password':
                    if (empty($this->username) || empty($this->password)) {
                        throw new \Exception('Username/password authentication requires username and password');
                    }
                    $client = $client->withBasicAuth($this->username, $this->password);
                    break;

                default:
                    throw new \Exception("Unsupported authentication type: {$this->auth_type}");
            }

            // 配置 CA 证书或跳过 TLS 验证
            if ($this->insecure_skip_tls_verify) {
                $client = $client->withOptions(['verify' => false]);
            } elseif ($this->certificate_authority_data) {
                $caFile = CertificateCacheService::getCachedCertificateFile('ca', $this->certificate_authority_data);
                $client = $client->withOptions(['verify' => $caFile]);
            }
        } catch (\Exception $e) {
            // 如果加密字段解密失败，记录错误但继续使用基本配置
            Log::warning('Cluster 配置失败，使用不安全模式', [
                'cluster_id' => $this->id,
                'error' => $e->getMessage(),
            ]);

            // 使用不安全模式继续
            $client = $client->withOptions(['verify' => false]);
        }

        return $client;
    }

    public function curl(string $method, string $url): Curl
    {
        $curl = new Curl;
        $curl->setOpt(CURLOPT_CUSTOMREQUEST, $method);
        $curl->setOpt(CURLOPT_URL, $this->server_url.$url);
        $curl->setOpt(CURLOPT_RETURNTRANSFER, true);
        $curl->setOpt(CURLOPT_FOLLOWLOCATION, true);
        $curl->setOpt(CURLOPT_MAXREDIRS, 5);
        $curl->setOpt(CURLOPT_SSL_VERIFYPEER, false);
        $curl->setHeader('Accept', 'application/json');
        $curl->setTimeout(30);
        $curl->setConnectTimeout(10);

        // 根据认证类型配置客户端
        try {
            switch ($this->auth_type) {
                case 'token':
                    if (empty($this->token)) {
                        throw new \Exception('Token authentication requires a token');
                    }
                    $curl->setHeader('Authorization', 'Bearer '.$this->token);
                    break;

                case 'certificate':
                    if (empty($this->client_certificate_data) || empty($this->client_key_data)) {
                        throw new \Exception('Certificate authentication requires certificate and key data');
                    }
                    // 使用证书缓存服务
                    $certFile = CertificateCacheService::getCachedCertificateFile('cert', $this->client_certificate_data);
                    $keyFile = CertificateCacheService::getCachedCertificateFile('key', $this->client_key_data);
                    $curl->setOpt(CURLOPT_SSLCERT, $certFile);
                    $curl->setOpt(CURLOPT_SSLKEY, $keyFile);
                    break;

                case 'username_password':
                    if (empty($this->username) || empty($this->password)) {
                        throw new \Exception('Username/password authentication requires username and password');
                    }
                    $curl->setBasicAuthentication($this->username, $this->password);
                    break;

                default:
                    throw new \Exception("Unsupported authentication type: {$this->auth_type}");
            }

            // 配置 CA 证书或跳过 TLS 验证
            if ($this->insecure_skip_tls_verify) {
                $curl->setOpt(CURLOPT_SSL_VERIFYPEER, false);
                $curl->setOpt(CURLOPT_SSL_VERIFYHOST, false);
            } elseif ($this->certificate_authority_data) {
                $caFile = CertificateCacheService::getCachedCertificateFile('ca', $this->certificate_authority_data);
                $curl->setOpt(CURLOPT_CAINFO, $caFile);
                $curl->setOpt(CURLOPT_SSL_VERIFYPEER, true);
                $curl->setOpt(CURLOPT_SSL_VERIFYHOST, 2);
            }
        } catch (\Exception $e) {
            // 如果加密字段解密失败，记录错误但继续使用基本配置
            Log::warning('Cluster (curl) 配置失败，使用不安全模式', [
                'cluster_id' => $this->id,
                'error' => $e->getMessage(),
            ]);

            // 使用不安全模式继续
            $curl->setOpt(CURLOPT_SSL_VERIFYPEER, false);
            $curl->setOpt(CURLOPT_SSL_VERIFYHOST, false);
        }

        return $curl;
    }

    public function workspaces(): HasMany
    {
        return $this->hasMany(Workspace::class);
    }

    /**
     * 关联到 IP 池
     */
    public function ipPools(): HasMany
    {
        return $this->hasMany(IpPool::class);
    }

    /**
     * 获取活跃的 IP 池
     */
    public function activeIpPools(): HasMany
    {
        return $this->ipPools()->where('is_active', true);
    }

    /**
     * 检查集群是否启用了计费
     */
    public function getBillingEnabledAttribute(): bool
    {
        return $this->isBillingEnabled();
    }
}
