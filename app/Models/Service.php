<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Service extends Model
{
    use HasFactory;

    protected $fillable = [
        'workspace_id',
        'name',
        'type',
        'ports',
        'selector',
        'target_workload_type',
        'target_workload_name',
        'session_affinity',
        'pool_ip_id',
        'allocated_ip',
        'external_traffic_policy',
        'load_balancer_class',
        'allow_shared_ip',
        'status',
        'status_message',
    ];

    protected $casts = [
        'ports' => 'array',
        'selector' => 'array',
        'allow_shared_ip' => 'boolean',
    ];

    /**
     * 默认预加载关系
     */
    protected $with = ['poolIp.ipPool'];

    /**
     * Service 类型常量
     */
    public const TYPE_CLUSTER_IP = 'ClusterIP';

    public const TYPE_NODE_PORT = 'NodePort';

    public const TYPE_LOAD_BALANCER = 'LoadBalancer';

    /**
     * 状态常量
     */
    public const STATUS_PENDING = 'pending';

    public const STATUS_ACTIVE = 'active';

    public const STATUS_FAILED = 'failed';

    /**
     * 外部流量策略常量
     */
    public const EXTERNAL_TRAFFIC_POLICY_CLUSTER = 'Cluster';

    public const EXTERNAL_TRAFFIC_POLICY_LOCAL = 'Local';

    /**
     * 会话亲和性常量
     */
    public const SESSION_AFFINITY_NONE = 'None';

    public const SESSION_AFFINITY_CLIENT_IP = 'ClientIP';

    /**
     * IP 家族常量（保留用于兼容性）
     */
    public const IP_FAMILY_IPV4 = 'ipv4';

    public const IP_FAMILY_IPV6 = 'ipv6';

    /**
     * 关联到工作空间
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * 关联到分配的 IP
     */
    public function poolIp(): BelongsTo
    {
        return $this->belongsTo(PoolIp::class);
    }

    /**
     * 关联到端口分配记录
     */
    public function portAllocations(): HasMany
    {
        return $this->hasMany(PortAllocation::class, 'service_name', 'name');
    }

    /**
     * 获取已分配的端口列表（需要指定namespace）
     */
    public function getAllocatedPorts(string $namespace): array
    {
        return $this->portAllocations()
            ->where('namespace', $namespace)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->orderBy('port')
            ->pluck('port')
            ->toArray();
    }

    /**
     * 获取基础端口（第一个分配的端口）
     */
    public function getBasePort(string $namespace): ?int
    {
        $allocatedPorts = $this->getAllocatedPorts($namespace);

        return empty($allocatedPorts) ? null : min($allocatedPorts);
    }

    /**
     * 检查是否为 LoadBalancer 类型
     */
    public function isLoadBalancer(): bool
    {
        return $this->type === self::TYPE_LOAD_BALANCER;
    }

    /**
     * 检查是否为 ClusterIP 类型
     */
    public function isClusterIp(): bool
    {
        return $this->type === self::TYPE_CLUSTER_IP;
    }

    /**
     * 检查是否为 NodePort 类型
     */
    public function isNodePort(): bool
    {
        return $this->type === self::TYPE_NODE_PORT;
    }

    /**
     * 检查是否需要外部 IP
     */
    public function needsExternalIp(): bool
    {
        return $this->isLoadBalancer();
    }

    /**
     * 检查是否有外部访问能力
     */
    public function hasExternalAccess(): bool
    {
        return $this->isLoadBalancer() || $this->isNodePort();
    }

    /**
     * 检查是否支持 Local 外部流量策略
     */
    public function supportsLocalTrafficPolicy(): bool
    {
        // NodePort 和 LoadBalancer 都支持外部流量策略
        if ($this->isNodePort()) {
            return true;
        }

        // 根据 PureLB 文档，Address Sharing 不支持 Local
        // Local addresses 只支持 Cluster
        return ! $this->allow_shared_ip && $this->isLoadBalancer();
    }

    /**
     * 检查是否为 IPv4
     */
    public function isIpv4(): bool
    {
        if (! $this->poolIp) {
            return true; // 默认为 IPv4
        }

        $ipType = $this->poolIp->getIpType();

        return $ipType === 'IPv4';
    }

    /**
     * 检查是否为 IPv6
     */
    public function isIpv6(): bool
    {
        if (! $this->poolIp) {
            return false;
        }

        $ipType = $this->poolIp->getIpType();

        return $ipType === 'IPv6';
    }

    /**
     * 获取 IP 版本字符串
     */
    public function getIpFamily(): string
    {
        if (! $this->poolIp) {
            return self::IP_FAMILY_IPV4; // 默认为 IPv4
        }

        $ipType = $this->poolIp->getIpType();

        return $ipType === 'IPv6' ? self::IP_FAMILY_IPV6 : self::IP_FAMILY_IPV4;
    }

    /**
     * 获取有效的外部流量策略
     */
    public function getEffectiveExternalTrafficPolicy(): string
    {
        // 只有 LoadBalancer 和 NodePort 支持外部流量策略
        if (! $this->isLoadBalancer() && ! $this->isNodePort()) {
            return self::EXTERNAL_TRAFFIC_POLICY_CLUSTER;
        }

        // NodePort 支持任何流量策略
        if ($this->isNodePort()) {
            return $this->external_traffic_policy ?? self::EXTERNAL_TRAFFIC_POLICY_CLUSTER;
        }

        // 根据 PureLB 文档限制
        if ($this->allow_shared_ip) {
            // Address Sharing 不支持 Local
            return self::EXTERNAL_TRAFFIC_POLICY_CLUSTER;
        }

        return $this->external_traffic_policy;
    }

    /**
     * 范围查询：根据状态过滤
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 范围查询：根据类型过滤
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 范围查询：LoadBalancer 类型
     */
    public function scopeLoadBalancer($query)
    {
        return $query->where('type', self::TYPE_LOAD_BALANCER);
    }

    /**
     * 范围查询：ClusterIP 类型
     */
    public function scopeClusterIp($query)
    {
        return $query->where('type', self::TYPE_CLUSTER_IP);
    }

    /**
     * 范围查询：NodePort 类型
     */
    public function scopeNodePort($query)
    {
        return $query->where('type', self::TYPE_NODE_PORT);
    }

    protected static function booted(): void
    {
        static::creating(function (self $service) {
            $service->external_traffic_policy = $service->getEffectiveExternalTrafficPolicy();
        });

        static::updating(function ($service) {
            // 根据 PureLB 文档限制
            if ($service->allow_shared_ip) {
                $service->external_traffic_policy = 'Cluster';
            }
        });
    }
}
