<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use PhpIP\IP;

class DisabledIp extends Model
{
    use HasFactory;

    protected $fillable = [
        'ip_address',
        'reason',
        'disabled_at',
    ];

    protected $casts = [
        'disabled_at' => 'datetime',
    ];

    /**
     * 检查 IP 地址是否有效
     */
    public function isValidIpAddress(): bool
    {
        try {
            IP::create($this->ip_address);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取 IP 类型（IPv4 或 IPv6）
     */
    public function getIpType(): string
    {
        try {
            $ip = IP::create($this->ip_address);

            return $ip->getVersion() === 4 ? 'IPv4' : 'IPv6';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * 检查指定集群中的 IP 是否被预禁用
     */
    public static function isIpDisabled(string $ipAddress): bool
    {
        return self::where('ip_address', $ipAddress)
            ->exists();
    }

    /**
     * 获取指定集群中所有被预禁用的 IP
     */
    public static function getDisabledIps(): array
    {
        return self::pluck('ip_address')
            ->toArray();
    }

    /**
     * 批量禁用 IP 地址
     */
    public static function disableIps(array $ipAddresses, ?string $reason = null): int
    {
        $count = 0;
        $now = now();

        foreach ($ipAddresses as $ip) {
            try {
                // 验证 IP 地址格式
                IP::create($ip);

                // 创建或更新禁用记录
                self::updateOrCreate(
                    [
                        'ip_address' => $ip,
                    ],
                    [
                        'reason' => $reason,
                        'disabled_at' => $now,
                    ]
                );

                $count++;
            } catch (\Exception $e) {
                // 忽略无效的 IP 地址
                continue;
            }
        }

        return $count;
    }

    /**
     * 批量启用 IP 地址（删除禁用记录）
     */
    public static function enableIps(array $ipAddresses): int
    {
        return self::whereIn('ip_address', $ipAddresses)
            ->delete();
    }

    /**
     * 清理指定集群的所有预禁用 IP
     */
    public static function clearAllDisabledIps(): int
    {
        return self::query()->delete();
    }
}
