<?php

function getPermissionsWorkspaceId()
{
    return getPermissionsTeamId();
}

function setPermissionsWorkspaceId($workspaceId)
{
    setPermissionsTeamId($workspaceId);
}

/**
 * 将八进制权限格式转换为十进制格式（Kubernetes 所需）
 *
 * @param  string|int|null  $permission  八进制权限，如 644、0644、777
 * @return int|null 十进制权限，如 420（对应 0644）
 */
function convertOctalPermissionToDecimal($permission): ?int
{
    if ($permission === null || $permission === '') {
        return null;
    }

    // 转换为字符串处理
    $permissionStr = (string) $permission;

    // 移除前导的 '0' 如果存在（支持 0644 格式）
    $permissionStr = ltrim($permissionStr, '0');

    // 如果移除后为空，说明输入的是 '0' 或 '000'，应该返回 0
    if ($permissionStr === '') {
        return 0;
    }

    // 验证是否为有效的八进制数字（只包含 0-7）
    if (! preg_match('/^[0-7]{1,3}$/', $permissionStr)) {
        return null;
    }

    // 转换八进制到十进制
    return octdec($permissionStr);
}

/**
 * 将十进制权限格式转换为八进制格式（用户友好显示）
 *
 * @param  int|null  $permission  十进制权限，如 420
 * @return string|null 八进制权限，如 "644"
 */
function convertDecimalPermissionToOctal(?int $permission): ?string
{
    if ($permission === null) {
        return null;
    }

    // 转换十进制到八进制，并格式化为3位数
    return sprintf('%03o', $permission);
}

/**
 * 格式化金额显示（保留2位小数）
 *
 * @param  float|string  $amount  金额
 * @param  bool  $withSymbol  是否包含货币符号
 * @return string 格式化后的金额
 */
function formatAmount($amount, bool $withSymbol = false, int $precision = 2): string
{
    $formatted = number_format((float) $amount, $precision, '.', '');

    return $withSymbol ? '¥ '.$formatted : $formatted;
}

/**
 * 格式化金额显示（用于命令行，使用"元"单位）
 *
 * @param  float|string  $amount  金额
 * @return string 格式化后的金额
 */
function formatAmountForCli($amount): string
{
    return number_format((float) $amount, 8).' 元';
}
