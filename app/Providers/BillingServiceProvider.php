<?php

namespace App\Providers;

use App\Service\BalanceService;
use App\Service\Billing\BillingManager;
use App\Service\Billing\BillingRecordService;
use App\Service\Billing\OverdueManagementService;
use Illuminate\Support\ServiceProvider;

class BillingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // 注册核心计费管理器
        $this->app->singleton(BillingManager::class);

        // 注册计费记录服务
        $this->app->singleton(BillingRecordService::class, function ($app) {
            return new BillingRecordService(
                $app->make(BalanceService::class)
            );
        });

        // 注册欠费管理服务
        $this->app->singleton(OverdueManagementService::class, function ($app) {
            return new OverdueManagementService(
                $app->make(BalanceService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
