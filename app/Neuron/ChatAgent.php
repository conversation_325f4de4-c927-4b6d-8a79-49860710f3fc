<?php

namespace App\Neuron;

use App\Models\User;
use App\Models\Workspace;
use App\Neuron\AIProviders\OpenAICompatible;
use NeuronAI\Agent;
use NeuronAI\Providers\AIProviderInterface;

class ChatAgent extends Agent
{
    protected Workspace $workspace;

    protected User $user;

    public function __construct(User $user, Workspace $workspace)
    {
        $this->user = $user;
        $this->workspace = $workspace;
    }

    public function instructions(): string
    {
        // 构建背景信息数组
        $backgroundInfo = [];

        // 添加基础系统提示词
        $backgroundInfo[] = config('assistant.default_system_prompt');

        // 添加当前时间信息
        $now = now();
        $backgroundInfo[] = "当前时间：{$now->format('Y年m月d日 H:i:s')}，星期{$this->getChineseWeekday($now->format('w'))}。";

        // 添加团队信息
        if ($this->workspace) {
            $backgroundInfo[] = "你正在与工作区「{$this->workspace->name}」的成员对话。";

            // 添加团队自定义上下文
            $teamPromptActive = $this->workspace->getSetting('ai.system_prompt_active', true);
            if ($teamPromptActive) {
                $teamCustomPrompt = $this->workspace->getSetting('ai.system_prompt');
                if ($teamCustomPrompt) {
                    $backgroundInfo[] = "{$teamCustomPrompt}";
                }
            }
        }

        // 添加用户信息
        if ($this->user) {
            $backgroundInfo[] = "你正在与用户「{$this->user->name}」对话。";

            // 添加用户自定义上下文
            $userPromptActive = $this->user->getSetting('ai.system_prompt_active', true);
            if ($userPromptActive) {
                $userCustomPrompt = $this->user->getSetting('ai.system_prompt');
                if ($userCustomPrompt) {
                    $backgroundInfo[] = "用户上下文：{$userCustomPrompt}";
                }
            }
        }

        $prompt = '';
        foreach ($backgroundInfo as $info) {
            $prompt .= $info."\n";
        }

        return $prompt;
    }

    /**
     * 获取中文星期几
     */
    private function getChineseWeekday(string $weekday): string
    {
        $weekdays = ['日', '一', '二', '三', '四', '五', '六'];

        return $weekdays[(int) $weekday] ?? '未知';
    }

    protected function provider(): AIProviderInterface
    {
        return new OpenAICompatible;
    }

    protected function tools(): array
    {
        return [
            // new WebSearchTool($this->workspace, $this->user),
        ];
    }
}
