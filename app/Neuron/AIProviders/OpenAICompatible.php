<?php

namespace App\Neuron\AIProviders;

use NeuronAI\Providers\AIProviderInterface;
use NeuronAI\Providers\OpenAI\OpenAI as OpenAIProvider;

class OpenAICompatible extends OpenAIProvider implements AIProviderInterface
{
    public function __construct(?string $model = null)
    {
        $this->baseUri = config('openai.base_url');

        parent::__construct(
            key: config('openai.api_key'),
            model: $model ?? config('assistant.default_model')
        );
    }
}
