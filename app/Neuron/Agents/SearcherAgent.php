<?php

namespace App\Neuron\Agents;

use App\Models\Team;
use App\Models\User;
use App\Neuron\AIProviders\OpenAICompatible;
use App\Neuron\Tools\DocumentSearchTool;
use NeuronAI\Agent;
use NeuronAI\Providers\AIProviderInterface;
use NeuronAI\Tools\Toolkits\Jina\JinaUrlReader;
use NeuronAI\Tools\Toolkits\Jina\JinaWebSearch;

class SearcherAgent extends Agent
{
    protected Team $team;

    protected User $user;

    public function __construct(User $user, Team $team)
    {
        $this->user = $user;
        $this->team = $team;
    }

    protected function provider(): AIProviderInterface
    {
        return new OpenAICompatible;
    }

    public function instructions(): string
    {
        $currentDate = date('Y-m-d');

        return <<<INSTRUCTIONS
            You are a web research agent.

            Your task is to search for the most recent, credible information on the user's query, using authoritative online sources.

            Instructions:
            - The current date is {$currentDate}. Always prioritize up-to-date sources.
            - Use the provided web search tool to gather and verify information. NEVER invent information that is not in the sources.
            - For each key point, explicitly cite the source and always include the direct URL.
            - If the answer requires covering multiple aspects, organize the summary with clear section headings (e.g., Definition, History, Implications).
            - Make your summary detailed and well-structured, but concise and focused on what the user actually asked.
            - If you cannot find sufficient information, state what is missing.
            - Your output should be a self-contained, readable summary, suitable to be read as-is by a user.
            - Always write in the same language as the user's question (unless instructed otherwise).
        INSTRUCTIONS;
    }

    protected function tools(): array
    {
        // Just for testing, will be removed later
        // $key = 'jina_e36baf5a31564650840fc7e3e2dbb596FoPvg-beNVywT_p7MVgAs8_RC3tw';
        // return [
        //     JinaUrlReader::make($key),
        //     JinaWebSearch::make($key)
        // ];

        return [
            new DocumentSearchTool($this->team, $this->user),
        ];
    }
}
