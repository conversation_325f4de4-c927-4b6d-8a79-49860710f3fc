<?php

namespace App\Neuron\Tools;

use App\Models\Document;
use App\Models\User;
use App\Models\Workspace;
use Exception;
use Log;
use NeuronAI\Tools\PropertyType;
use NeuronAI\Tools\Tool;
use NeuronAI\Tools\ToolProperty;

class GetDeploymentsTool extends Tool
{
    protected Workspace $workspace;

    protected User $user;

    public function __construct(
        Workspace $workspace,
        User $user
    ) {
        $this->workspace = $workspace;
        $this->user = $user;
        parent::__construct(
            'get_deployments',
            '获取部署信息',
        );
    }

    /**
     * Return the list of the properties.
     */
    protected function properties(): array
    {
        return [
            new ToolProperty(
                name: 'query',
                type: PropertyType::STRING,
                description: '获取部署信息',
                required: true
            ),
        ];
    }

    /**
     * Implementing the tool logic in the __invoke magic method.
     */
    public function __invoke(string $query)
    {
        Log::info('GetDeploymentsTool', ['query' => $query]);
        try {
            $user = $this->user;
            $workspaceId = $this->workspace->id;
            $workspace = Workspace::find($workspaceId);

            if (! $workspace) {
                return '找不到指定的团队';
            }

            // 检查用户是否有权限搜索文档
            if ($workspace->owner_id !== $user->id && ! $user->hasPermissionTo('workspace:update')) {
                return '您没有权限搜索文档';
            }

            // 搜索文档
            $documents = Document::search($query)->where('workspace_id', $workspaceId)->paginate();
            // 处理文档，返回对大模型友好的纯文本格式
            $documents = $documents->map(function ($document) {
                return [
                    'id' => $document->id,
                    'title' => $document->title,
                    'content' => $document->getPlainTextContent(),
                    'created_at' => $document->created_at,
                    'updated_at' => $document->updated_at,
                ];
            });

            return $documents->toArray();
        } catch (Exception $e) {
            Log::error('GetDeploymentsTool', ['error' => $e->getMessage()]);

            return "搜索文档失败: {$e->getMessage()}";
        }
    }
}
