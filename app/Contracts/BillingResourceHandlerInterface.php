<?php

namespace App\Contracts;

interface BillingResourceHandlerInterface
{
    /**
     * 基于DTO计算价格
     *
     * @param  mixed  $usageDTO  使用量DTO
     * @param  mixed  $priceResource  价格资源模型 (Cluster, IpPool等)
     * @param  int  $minutes  计费分钟数，默认1分钟
     * @return array 价格详情
     */
    public function calculatePrice($usageDTO, $priceResource, int $minutes = 1): array;

    /**
     * 验证DTO数据是否有效
     *
     * @param  mixed  $usageDTO  使用量DTO
     */
    public function validateUsage($usageDTO): bool;

    /**
     * 获取支持的DTO类型
     */
    public function getSupportedDTOTypes(): array;

    /**
     * 获取资源类型名称
     */
    public function getResourceType(): string;
}
