<?php

namespace App\Contracts;

use App\Models\IpPool;
use App\Models\Service;

interface LoadBalancerDriverInterface
{
    /**
     * 同步 IP 池到 Kubernetes.
     * 这会创建或更新 IPAddressPool 和 L2Advertisement 等资源.
     */
    public function syncIpPool(IpPool $ipPool): void;

    /**
     * 从 Kubernetes 删除 IP 池.
     */
    public function deleteIpPool(IpPool $ipPool): void;

    /**
     * 获取 Service 的注解.
     */
    public function getServiceAnnotations(Service $service): array;

    /**
     * 获取 LoadBalancer Class 的注解.
     */
    public function getLoadBalancerClassName(Service $service): ?string;
}
