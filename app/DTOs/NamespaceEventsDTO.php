<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Namespace Events DTO
 */
class NamespaceEventsDTO
{
    /**
     * @var EventDTO[] 事件列表
     */
    protected array $events = [];

    public function __construct(array $events = [])
    {
        $this->events = $events;
    }

    /**
     * 从 Kubernetes 事件列表创建 DTO
     */
    public static function fromK8sResource(array $items): static
    {
        $events = [];

        foreach ($items as $item) {
            $events[] = EventDTO::fromK8sResource($item);
        }

        return new static($events);
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return array_map(function (EventDTO $event) {
            return $event->toArray();
        }, $this->events);
    }

    /**
     * 获取所有事件
     */
    public function getAllEvents(): array
    {
        return $this->events;
    }

    /**
     * 获取警告事件
     */
    public function getWarningEvents(): array
    {
        return array_filter($this->events, function (EventDTO $event) {
            return $event->isWarning();
        });
    }

    /**
     * 获取特定资源的事件
     */
    public function getResourceEvents(string $kind, string $name): array
    {
        return array_filter($this->events, function (EventDTO $event) use ($kind, $name) {
            return $event->involvesResource($kind, $name);
        });
    }

    /**
     * 获取最近的事件（指定分钟数内）
     */
    public function getRecentEvents(int $minutes = 30): array
    {
        return array_filter($this->events, function (EventDTO $event) use ($minutes) {
            return $event->isRecent($minutes);
        });
    }

    /**
     * 获取指定时间之后的事件
     */
    public function getEventsAfter(Carbon $time): array
    {
        return array_filter($this->events, function (EventDTO $event) use ($time) {
            return $event->isAfter($time);
        });
    }

    /**
     * 获取特定资源的最近事件
     */
    public function getRecentResourceEvents(string $kind, string $name, int $minutes = 30): array
    {
        return array_filter($this->events, function (EventDTO $event) use ($kind, $name, $minutes) {
            return $event->involvesResource($kind, $name) && $event->isRecent($minutes);
        });
    }

    /**
     * 获取事件数量
     */
    public function getEventCount(): int
    {
        return count($this->events);
    }

    /**
     * 获取警告事件数量
     */
    public function getWarningEventCount(): int
    {
        return count($this->getWarningEvents());
    }

    /**
     * 获取最近事件数量
     */
    public function getRecentEventCount(int $minutes = 30): int
    {
        return count($this->getRecentEvents($minutes));
    }

    /**
     * 按时间排序事件（最新的在前）
     */
    public function sortByTimeDesc(): static
    {
        $sortedEvents = $this->events;

        usort($sortedEvents, function (EventDTO $a, EventDTO $b) {
            $timeA = $a->getTimestampCarbon();
            $timeB = $b->getTimestampCarbon();

            if (! $timeA && ! $timeB) {
                return 0;
            }
            if (! $timeA) {
                return 1;
            }
            if (! $timeB) {
                return -1;
            }

            return $timeB->timestamp <=> $timeA->timestamp;
        });

        return new static($sortedEvents);
    }
}
