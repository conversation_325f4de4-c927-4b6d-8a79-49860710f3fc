<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Kubernetes 资源基础 DTO
 */
abstract class KubernetesResourceDTO
{
    public function __construct(
        public string $name,
        public string $namespace,
        public array $labels = [],
        public array $annotations = [],
        public ?Carbon $createdAt = null,
        public ?string $uid = null,
        public ?string $resourceVersion = null
    ) {}

    /**
     * 从 Kubernetes API 响应创建 DTO
     */
    public static function fromK8sResource(array $resource): static
    {
        $metadata = $resource['metadata'] ?? [];

        return new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
            ? Carbon::parse($metadata['creationTimestamp'])
            : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null
        );
    }

    /**
     * 过滤标签，隐藏系统标签
     */
    protected static function filterLabels(array $labels): array
    {
        $hiddenPrefixes = [
            'kubectl.kubernetes.io/',
            'app.kubernetes.io/managed-by',
            'paas.platform/',
        ];

        return array_filter($labels, function ($key) use ($hiddenPrefixes) {
            foreach ($hiddenPrefixes as $prefix) {
                if (str_starts_with($key, $prefix)) {
                    return false;
                }
            }

            return true;
        }, ARRAY_FILTER_USE_KEY);
    }

    /**
     * 过滤注解，隐藏系统注解
     */
    protected static function filterAnnotations(array $annotations): array
    {
        $hiddenPrefixes = [
            'kubectl.kubernetes.io/',
            'deployment.kubernetes.io/',
            'paas.platform/',
        ];

        return array_filter($annotations, function ($key) use ($hiddenPrefixes) {
            foreach ($hiddenPrefixes as $prefix) {
                if (str_starts_with($key, $prefix)) {
                    return false;
                }
            }

            return true;
        }, ARRAY_FILTER_USE_KEY);
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'namespace' => $this->namespace,
            'resource_version' => $this->resourceVersion,
            'uid' => $this->uid,
            'created_at' => $this->createdAt?->toISOString(),
            'labels' => $this->labels,
            'annotations' => $this->annotations,
        ];
    }
}
