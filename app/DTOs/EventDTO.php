<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Event DTO
 */
class EventDTO
{
    public function __construct(
        public string $type,
        public string $namespace,
        public string $kind,
        public string $reason,
        public string $message,
        public ?string $timestamp,
        public ?string $firstTimestamp,
        public ?string $lastTimestamp,
        public int $count,
        public string $source,
        public ?array $involvedObject = null,
        // 额外字段：用于资源变更检测与显示
        public ?string $uid = null,
        public ?string $resourceVersion = null,
        public ?string $name = null,
    ) {}

    /**
     * 从 Kubernetes Event 资源创建 DTO
     */
    public static function fromK8sResource(array $event): static
    {
        $metadata = $event['metadata'] ?? [];
        $involved = $event['involvedObject'] ?? [];

        return new static(
            type: $event['type'] ?? 'Normal',
            namespace: $metadata['namespace'] ?? ($event['namespace'] ?? ''),
            kind: $involved['kind'] ?? '',
            reason: $event['reason'] ?? '',
            message: $event['message'] ?? '',
            timestamp: $event['lastTimestamp'] ?? $event['eventTime'] ?? $event['firstTimestamp'] ?? null,
            firstTimestamp: $event['firstTimestamp'] ?? null,
            lastTimestamp: $event['lastTimestamp'] ?? null,
            count: $event['count'] ?? 1,
            source: ($event['source']['component'] ?? '') ?: ($event['reportingComponent'] ?? ''),
            involvedObject: ! empty($involved) ? [
                'kind' => $involved['kind'] ?? '',
                'name' => $involved['name'] ?? '',
                'uid' => $involved['uid'] ?? '',
            ] : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null,
            name: $involved['name'] ?? ($metadata['name'] ?? ($event['reason'] ?? '')),
        );
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'type' => $this->type,
            'namespace' => $this->namespace,
            'kind' => $this->kind,
            'reason' => $this->reason,
            'message' => $this->message,
            'timestamp' => $this->timestamp,
            'first_timestamp' => $this->firstTimestamp,
            'last_timestamp' => $this->lastTimestamp,
            'count' => $this->count,
            'uid' => $this->uid,
            'resource_version' => $this->resourceVersion,
            // 'source' => $this->source,
            // 'involved_object' => $this->involvedObject,
            'age' => $this->getAge(),
            'is_recent' => $this->isRecent(),
        ];
    }

    /**
     * 检查是否为警告事件
     */
    public function isWarning(): bool
    {
        return $this->type === 'Warning';
    }

    /**
     * 检查是否涉及特定资源
     */
    public function involvesResource(string $kind, string $name): bool
    {
        if (! $this->involvedObject) {
            return false;
        }

        return $this->involvedObject['kind'] === $kind && $this->involvedObject['name'] === $name;
    }

    /**
     * 获取事件的年龄（人类可读格式）
     */
    public function getAge(): string
    {
        if (! $this->timestamp) {
            return 'Unknown';
        }

        try {
            $eventTime = Carbon::parse($this->timestamp);

            return $eventTime->diffForHumans();
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * 检查事件是否是最近的（30分钟内）
     */
    public function isRecent(int $minutes = 30): bool
    {
        if (! $this->timestamp) {
            return false;
        }

        try {
            $eventTime = Carbon::parse($this->timestamp);

            return $eventTime->gte(now()->subMinutes($minutes));
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 检查事件是否发生在指定时间之后
     */
    public function isAfter(Carbon $time): bool
    {
        if (! $this->timestamp) {
            return false;
        }

        try {
            $eventTime = Carbon::parse($this->timestamp);

            return $eventTime->gte($time);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取事件的时间戳（Carbon实例）
     */
    public function getTimestampCarbon(): ?Carbon
    {
        if (! $this->timestamp) {
            return null;
        }

        try {
            return Carbon::parse($this->timestamp);
        } catch (\Exception $e) {
            return null;
        }
    }
}
