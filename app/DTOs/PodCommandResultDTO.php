<?php

namespace App\DTOs;

class PodCommandResultDTO
{
    public function __construct(
        public readonly string $stdout,
        public readonly string $stderr,
        public readonly ?int $exitCode,
        public readonly ?string $error,
        public readonly bool $completed,
        public readonly bool $timeout,
        public readonly float $executionTime
    ) {}

    public static function fromArray(array $data): self
    {
        return new self(
            stdout: $data['stdout'] ?? '',
            stderr: $data['stderr'] ?? '',
            exitCode: $data['exit_code'] ?? null,
            error: $data['error'] ?? null,
            completed: $data['completed'] ?? false,
            timeout: $data['timeout'] ?? false,
            executionTime: $data['execution_time'] ?? 0.0
        );
    }

    public function toArray(): array
    {
        return [
            'stdout' => $this->stdout,
            'stderr' => $this->stderr,
            'exit_code' => $this->exitCode,
            'error' => $this->error,
            'completed' => $this->completed,
            'timeout' => $this->timeout,
            'execution_time' => $this->executionTime,
        ];
    }

    public function isSuccess(): bool
    {
        // 如果有超时或明确的错误，则失败
        if ($this->timeout || $this->error) {
            return false;
        }

        // 如果有明确的退出码，则根据退出码判断
        if ($this->exitCode !== null) {
            return $this->exitCode === 0;
        }

        // 如果没有明确的退出码，但命令已完成且没有错误，则认为成功
        // 这种情况通常发生在简单命令执行时，K8s 不发送退出码信息
        return $this->completed;
    }

    public function hasOutput(): bool
    {
        return ! empty($this->stdout) || ! empty($this->stderr);
    }
}
