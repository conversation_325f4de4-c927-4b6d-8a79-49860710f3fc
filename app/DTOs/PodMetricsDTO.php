<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Pod Metrics DTO
 * 表示单个 Pod 的资源指标（CPU / 内存）
 */
class PodMetricsDTO extends KubernetesResourceDTO
{
    public function __construct(
        string $name,
        string $namespace,
        array $labels = [],
        array $annotations = [],
        ?Carbon $createdAt = null,
        ?string $uid = null,
        ?string $resourceVersion = null,
        public ?string $timestamp = null,
        public ?string $window = null,
        public array $containers = [],
    ) {
        parent::__construct($name, $namespace, $labels, $annotations, $createdAt, $uid, $resourceVersion);
    }

    /**
     * 从 Kubernetes PodMetrics 资源创建 DTO
     */
    public static function fromK8sResource(array $resource): static
    {
        $metadata = $resource['metadata'] ?? [];

        return new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
                ? Carbon::parse($metadata['creationTimestamp'])
                : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null,
            timestamp: $resource['timestamp'] ?? null,
            window: $resource['window'] ?? null,
            containers: $resource['containers'] ?? [],
        );
    }

    /**
     * 资源类型
     */
    public function getResourceType(): string
    {
        return 'Metrics';
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'type' => $this->getResourceType(),
            'timestamp' => $this->timestamp,
            'window' => $this->window,
            'containers' => $this->containers,
            'resource_version' => $this->resourceVersion,
            'created_at' => $this->createdAt?->toISOString(),
        ]);
    }
}
