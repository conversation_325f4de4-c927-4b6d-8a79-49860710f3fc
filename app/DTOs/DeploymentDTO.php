<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Deployment DTO
 */
class DeploymentDTO extends KubernetesResourceDTO
{
    public function __construct(
        string $name,
        string $namespace,
        array $labels = [],
        array $annotations = [],
        ?Carbon $createdAt = null,
        ?string $uid = null,
        ?string $resourceVersion = null,
        public array $containers = [],
        public int $replicas = 1,
        public int $readyReplicas = 0,
        public int $availableReplicas = 0,
        public string $status = 'Unknown',
        public ?string $strategy = null,
        public array $conditions = [],
        public array $volumes = [],
        public array $imagePullSecrets = [],
        public string $restartPolicy = 'Always'
    ) {
        parent::__construct($name, $namespace, $labels, $annotations, $createdAt, $uid, $resourceVersion);
    }

    /**
     * 从 Kubernetes Deployment 资源创建 DTO
     */
    public static function fromK8sResource(array $resource): static
    {
        $metadata = $resource['metadata'] ?? [];
        $spec = $resource['spec'] ?? [];
        $status = $resource['status'] ?? [];

        // 解析容器信息和其他资源
        $containers = $spec['template']['spec']['containers'] ?? [];
        $podVolumes = $spec['template']['spec']['volumes'] ?? [];
        $podImagePullSecrets = $spec['template']['spec']['imagePullSecrets'] ?? [];

        return new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
                ? Carbon::parse($metadata['creationTimestamp'])
                : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null,
            containers: static::extractContainers($containers, $podVolumes),
            replicas: $spec['replicas'] ?? 1,
            readyReplicas: $status['readyReplicas'] ?? 0,
            availableReplicas: $status['availableReplicas'] ?? 0,
            status: static::determineStatus($status),
            strategy: $spec['strategy']['type'] ?? null,
            conditions: $status['conditions'] ?? [],
            volumes: static::extractVolumes($podVolumes),
            imagePullSecrets: static::extractImagePullSecrets($podImagePullSecrets),
            restartPolicy: $spec['template']['spec']['restartPolicy'] ?? 'Always'
        );
    }

    /**
     * 确定 Deployment 状态
     */
    protected static function determineStatus(array $status): string
    {
        $conditions = $status['conditions'] ?? [];

        foreach ($conditions as $condition) {
            if ($condition['type'] === 'Progressing' && $condition['status'] === 'True') {
                if ($condition['reason'] === 'NewReplicaSetAvailable') {
                    return 'Available';
                }
                if ($condition['reason'] === 'ReplicaSetUpdated') {
                    return 'Updating';
                }
            }

            if ($condition['type'] === 'Available' && $condition['status'] === 'False') {
                return 'Unavailable';
            }
        }

        $replicas = $status['replicas'] ?? 0;
        $readyReplicas = $status['readyReplicas'] ?? 0;

        if ($replicas === 0) {
            return 'Stopped';
        }

        if ($readyReplicas === $replicas) {
            return 'Running';
        }

        if ($readyReplicas > 0) {
            return 'Partially Ready';
        }

        return 'Pending';
    }

    /**
     * 提取容器信息
     */
    protected static function extractContainers(array $containers, array $podVolumes): array
    {
        return array_map(function ($container) use ($podVolumes) {
            $envFrom = static::extractEnvFrom($container);

            return [
                'name' => $container['name'] ?? '',
                'image' => $container['image'] ?? '',
                'working_dir' => $container['workingDir'] ?? null,
                'ports' => static::extractPorts($container),
                'env' => static::extractEnvironmentVariables($container),
                'env_from_configmap' => $envFrom['configmap'],
                'env_from_secret' => $envFrom['secret'],
                'resources' => static::extractResources($container),
                'volume_mounts' => static::extractVolumeMounts($container, $podVolumes),
                'configmap_mounts' => static::extractFileMounts($container, $podVolumes, 'configmap'),
                'secret_mounts' => static::extractFileMounts($container, $podVolumes, 'secret'),
                'command' => $container['command'] ?? null,
                'args' => $container['args'] ?? null,
                'liveness_probe' => static::extractProbe($container['livenessProbe'] ?? null),
                'readiness_probe' => static::extractProbe($container['readinessProbe'] ?? null),
                'startup_probe' => static::extractProbe($container['startupProbe'] ?? null),
                'stdin' => $container['stdin'] ?? false,
                'tty' => $container['tty'] ?? false,
            ];
        }, $containers);
    }

    /**
     * 提取端口信息
     */
    protected static function extractPorts(array $container): array
    {
        $ports = $container['ports'] ?? [];

        return array_map(function ($port) {
            return [
                'name' => $port['name'] ?? 'default',
                'container_port' => $port['containerPort'] ?? 80,
                'protocol' => $port['protocol'] ?? 'TCP',
            ];
        }, $ports);
    }

    /**
     * 提取环境变量引用
     */
    protected static function extractEnvFrom(array $container): array
    {
        $configMapEnvs = [];
        $secretEnvs = [];

        // From envFrom (referencing all keys in a resource)
        if (isset($container['envFrom'])) {
            foreach ($container['envFrom'] as $envFrom) {
                if (isset($envFrom['configMapRef']['name'])) {
                    $configMapEnvs[] = [
                        'configmap_name' => $envFrom['configMapRef']['name'] ?? '',
                        'key' => null,
                        'env_name' => null,
                    ];
                }
                if (isset($envFrom['secretRef']['name'])) {
                    $secretEnvs[] = [
                        'secret_name' => $envFrom['secretRef']['name'] ?? '',
                        'key' => null,
                        'env_name' => null,
                    ];
                }
            }
        }

        // From env.valueFrom (referencing a specific key)
        if (isset($container['env'])) {
            foreach ($container['env'] as $envVar) {
                if (isset($envVar['valueFrom']['configMapKeyRef'])) {
                    $configMapEnvs[] = [
                        'configmap_name' => $envVar['valueFrom']['configMapKeyRef']['name'] ?? '',
                        'key' => $envVar['valueFrom']['configMapKeyRef']['key'] ?? '',
                        'env_name' => $envVar['name'] ?? '',
                    ];
                }
                if (isset($envVar['valueFrom']['secretKeyRef'])) {
                    $secretEnvs[] = [
                        'secret_name' => $envVar['valueFrom']['secretKeyRef']['name'] ?? '',
                        'key' => $envVar['valueFrom']['secretKeyRef']['key'] ?? '',
                        'env_name' => $envVar['name'] ?? '',
                    ];
                }
            }
        }

        return ['configmap' => $configMapEnvs, 'secret' => $secretEnvs];
    }

    /**
     * 提取环境变量
     */
    protected static function extractEnvironmentVariables(array $container): array
    {
        $env = $container['env'] ?? [];
        $result = [];

        foreach ($env as $envVar) {
            if (isset($envVar['value'])) {
                $result[] = [
                    'name' => $envVar['name'] ?? '',
                    'value' => $envVar['value'],
                ];
            }
        }

        return $result;
    }

    /**
     * 提取卷挂载
     */
    protected static function extractVolumeMounts(array $container, array $podVolumes): array
    {
        $volumeMounts = $container['volumeMounts'] ?? [];
        $pvcVolumes = [];
        foreach ($podVolumes as $volume) {
            if (isset($volume['persistentVolumeClaim'])) {
                $pvcVolumes[$volume['name']] = $volume['persistentVolumeClaim']['claimName'];
            }
        }

        $pvcMounts = [];
        foreach ($volumeMounts as $mount) {
            $mountName = $mount['name'] ?? '';
            if (array_key_exists($mountName, $pvcVolumes)) {
                $result = [
                    'mount_path' => $mount['mountPath'] ?? '',
                    'storage_name' => $pvcVolumes[$mountName],
                    'read_only' => $mount['readOnly'] ?? false,
                ];
                if (isset($mount['subPath']) && ! empty($mount['subPath'])) {
                    $result['sub_path'] = $mount['subPath'];
                } else {
                    $result['sub_path'] = null;
                }
                $pvcMounts[] = $result;
            }
        }

        return $pvcMounts;
    }

    /**
     * 提取文件挂载
     */
    protected static function extractFileMounts(array $container, array $podVolumes, string $type): array
    {
        $mounts = [];
        $volumeMounts = $container['volumeMounts'] ?? [];

        $sourceKey = $type === 'configmap' ? 'configMap' : 'secret';
        $nameKey = $type === 'configmap' ? 'configmap_name' : 'secret_name';

        // Find volumes of the specified type
        $typedVolumes = [];
        foreach ($podVolumes as $volume) {
            if (isset($volume[$sourceKey])) {
                $typedVolumes[$volume['name']] = $volume[$sourceKey];
            }
        }

        if (empty($typedVolumes)) {
            return [];
        }

        // Find mounts that use these volumes
        foreach ($volumeMounts as $volumeMount) {
            $volumeMountName = $volumeMount['name'] ?? '';
            if (array_key_exists($volumeMountName, $typedVolumes)) {
                $volumeSource = $typedVolumes[$volumeMountName];
                $mountData = [
                    $nameKey => $volumeSource['name'] ?? '',
                    'mount_path' => $volumeMount['mountPath'] ?? '',
                    'read_only' => $volumeMount['readOnly'] ?? false,
                ];

                if (isset($volumeSource['items'])) {
                    $mountData['items'] = array_map(function ($item) {
                        return [
                            'key' => $item['key'],
                            'path' => $item['path'],
                        ];
                    }, $volumeSource['items']);
                }
                if (isset($volumeSource['defaultMode'])) {
                    $mountData['default_mode'] = convertDecimalPermissionToOctal($volumeSource['defaultMode']);
                }

                $mounts[] = $mountData;
            }
        }

        return $mounts;
    }

    /**
     * 提取资源配置
     */
    protected static function extractResources(array $container): array
    {
        $resources = $container['resources'] ?? [];
        $limits = $resources['limits'] ?? [];
        // $requests = $resources['requests'] ?? [];

        if (empty($resources) || empty($resources['limits'])) {
            return [
                'cpu' => 0,
                'memory' => 0,
            ];
        }

        return [
            'cpu' => static::parseCpuValue($limits['cpu'] ?? ''),
            'memory' => static::parseMemoryValue($limits['memory'] ?? ''),
        ];
    }

    /**
     * 提取卷信息
     */
    protected static function extractVolumes(array $volumes): array
    {
        return array_map(function ($volume) {
            $result = [
                'name' => $volume['name'] ?? '',
                'type' => 'unknown',
            ];

            if (isset($volume['persistentVolumeClaim'])) {
                $result['type'] = 'pvc';
                $result['claim_name'] = $volume['persistentVolumeClaim']['claimName'] ?? '';
            } elseif (isset($volume['configMap'])) {
                $result['type'] = 'configmap';
                $result['config_map'] = $volume['configMap']['name'] ?? '';
            } elseif (isset($volume['secret'])) {
                $result['type'] = 'secret';
                $result['secret'] = $volume['secret']['secretName'] ?? '';
            }

            return $result;
        }, $volumes);
    }

    /**
     * 提取镜像拉取密钥
     */
    protected static function extractImagePullSecrets(array $imagePullSecrets): array
    {
        return array_map(function ($secret) {
            return $secret['name'] ?? '';
        }, $imagePullSecrets);
    }

    /**
     * 解析 CPU 值（转换为毫核心）
     */
    protected static function parseCpuValue(string $cpu): int
    {
        if (empty($cpu)) {
            return 0;
        }

        if (str_ends_with($cpu, 'm')) {
            return (int) str_replace('m', '', $cpu);
        }

        return (int) ((float) $cpu * 1000);
    }

    /**
     * 解析内存值（转换为 Mi）
     */
    protected static function parseMemoryValue(string $memory): int
    {
        if (empty($memory)) {
            return 0;
        }

        if (str_ends_with($memory, 'Mi')) {
            return (int) str_replace('Mi', '', $memory);
        }

        if (str_ends_with($memory, 'Gi')) {
            return (int) ((float) str_replace('Gi', '', $memory) * 1024);
        }

        if (str_ends_with($memory, 'Ki')) {
            return (int) ((float) str_replace('Ki', '', $memory) / 1024);
        }

        // 假设是字节，转换为 Mi
        return (int) ((int) $memory / 1024 / 1024);
    }

    /**
     * 提取探针信息
     */
    protected static function extractProbe(?array $probe): ?array
    {
        if (empty($probe)) {
            return null;
        }

        $result = [
            'initial_delay_seconds' => $probe['initialDelaySeconds'] ?? 0,
            'period_seconds' => $probe['periodSeconds'] ?? 10,
            'timeout_seconds' => $probe['timeoutSeconds'] ?? 1,
            'success_threshold' => $probe['successThreshold'] ?? 1,
            'failure_threshold' => $probe['failureThreshold'] ?? 3,
        ];

        // 检测探针类型
        if (isset($probe['httpGet'])) {
            $result['type'] = 'http';
            $result['http_path'] = $probe['httpGet']['path'] ?? '/';
            $result['http_port'] = $probe['httpGet']['port'] ?? 80;
            $result['http_scheme'] = $probe['httpGet']['scheme'] ?? 'HTTP';
            $result['http_headers'] = [];

            if (isset($probe['httpGet']['httpHeaders'])) {
                foreach ($probe['httpGet']['httpHeaders'] as $header) {
                    $result['http_headers'][] = [
                        'name' => $header['name'] ?? '',
                        'value' => $header['value'] ?? '',
                    ];
                }
            }
        } elseif (isset($probe['tcpSocket'])) {
            $result['type'] = 'tcp';
            $result['tcp_port'] = $probe['tcpSocket']['port'] ?? 80;
        } elseif (isset($probe['exec'])) {
            $result['type'] = 'exec';
            $result['exec_command'] = $probe['exec']['command'] ?? [];
        } else {
            return null; // 未知的探针类型
        }

        return $result;
    }

    /**
     * 获取资源类型
     */
    public function getResourceType(): string
    {
        return 'Deployment';
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        // 提取所有容器的端口信息
        $ports = [];
        foreach ($this->containers as $container) {
            $containerPorts = $container['ports'] ?? [];
            foreach ($containerPorts as $port) {
                $ports[] = [
                    'container_name' => $container['name'] ?? '',
                    'port_name' => $port['name'] ?? 'default',
                    'port' => $port['container_port'] ?? $port['port'] ?? 0,
                    'protocol' => $port['protocol'] ?? 'TCP',
                ];
            }
        }

        return array_merge(parent::toArray(), [
            'type' => $this->getResourceType(),
            'containers' => $this->containers,
            'ports' => $ports,
            'replicas' => $this->replicas,
            'ready_replicas' => $this->readyReplicas,
            'available_replicas' => $this->availableReplicas,
            'status' => $this->status,
            'strategy' => $this->strategy,
            'conditions' => $this->conditions,
            'volumes' => $this->volumes,
            'image_pull_secrets' => $this->imagePullSecrets,
            'restart_policy' => $this->restartPolicy,
            'resource_version' => $this->resourceVersion,
            'created_at' => $this->createdAt?->toISOString(),
        ]);
    }
}
