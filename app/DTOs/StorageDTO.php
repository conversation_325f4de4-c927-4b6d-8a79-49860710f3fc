<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Storage (PersistentVolumeClaim) DTO
 */
class StorageDTO extends KubernetesResourceDTO
{
    public function __construct(
        string $name,
        string $namespace,
        array $labels = [],
        array $annotations = [],
        ?Carbon $createdAt = null,
        ?string $uid = null,
        ?string $resourceVersion = null,
        public string $size = '',
        public string $storageClass = '',
        public array $accessModes = [],
        public string $status = 'Unknown',
        public ?string $volumeName = null,
        public string $capacity = '',
        public array $conditions = []
    ) {
        parent::__construct($name, $namespace, $labels, $annotations, $createdAt, $uid, $resourceVersion);
    }

    /**
     * 从 Kubernetes PVC 资源创建 DTO
     */
    public static function fromK8sResource(array $resource): static
    {
        $metadata = $resource['metadata'] ?? [];
        $spec = $resource['spec'] ?? [];
        $status = $resource['status'] ?? [];

        return new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
                ? Carbon::parse($metadata['creationTimestamp'])
                : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null,
            size: $spec['resources']['requests']['storage'] ?? '',
            storageClass: $spec['storageClassName'] ?? '',
            accessModes: $spec['accessModes'] ?? [],
            status: static::determineStatus($status),
            volumeName: $spec['volumeName'] ?? null,
            capacity: $status['capacity']['storage'] ?? '',
            conditions: $status['conditions'] ?? []
        );
    }

    /**
     * 确定 PVC 状态
     */
    protected static function determineStatus(array $status): string
    {
        $phase = $status['phase'] ?? 'Unknown';

        return match ($phase) {
            'Bound' => 'Bound',
            'Pending' => 'Pending',
            'Lost' => 'Lost',
            default => 'Unknown'
        };
    }

    /**
     * 检查是否已绑定
     */
    public function isBound(): bool
    {
        return $this->status === 'Bound';
    }

    /**
     * 检查是否支持读写
     */
    public function isReadWrite(): bool
    {
        return in_array('ReadWriteOnce', $this->accessModes) ||
               in_array('ReadWriteMany', $this->accessModes);
    }

    /**
     * 检查是否支持多节点读写
     */
    public function isReadWriteMany(): bool
    {
        return in_array('ReadWriteMany', $this->accessModes);
    }

    /**
     * 获取格式化的大小
     */
    public function getFormattedSize(): string
    {
        // 移除 K8s 的单位，只保留数字
        $sizeValue = preg_replace('/[^0-9]/', '', $this->size);

        if (empty($sizeValue)) {
            return '0 Mi';
        }

        $sizeNumber = (int) $sizeValue;

        // 判断原始单位
        if (str_contains(strtoupper($this->size), 'GI')) {
            // 如果是 Gi，转换为 Mi
            $sizeInMi = $sizeNumber * 1024;
        } elseif (str_contains(strtoupper($this->size), 'TI')) {
            // 如果是 Ti，转换为 Mi
            $sizeInMi = $sizeNumber * 1024 * 1024;
        } elseif (str_contains(strtoupper($this->size), 'KI')) {
            // 如果是 Ki，转换为 Mi
            $sizeInMi = round($sizeNumber / 1024, 2);
        } else {
            // 默认认为是 Mi
            $sizeInMi = $sizeNumber;
        }

        // 格式化显示
        if ($sizeInMi >= 1024) {
            $sizeInGi = round($sizeInMi / 1024, 2);
            if ($sizeInGi >= 1024) {
                $sizeInTi = round($sizeInGi / 1024, 2);

                return $sizeInTi.' T';
            }

            return $sizeInGi.' G';
        }

        return $sizeInMi.' M';
    }

    /**
     * 获取资源类型
     */
    public function getResourceType(): string
    {
        return 'Storage';
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'type' => $this->getResourceType(),
            'size' => $this->size,
            // 'storage_class' => $this->storageClass,
            // 'access_modes' => $this->accessModes,
            'status' => $this->status,
            // 'volume_name' => $this->volumeName,
            'capacity' => $this->capacity,
            'conditions' => $this->conditions,
            'is_bound' => $this->isBound(),
            'is_read_write' => $this->isReadWrite(),
            'is_read_write_many' => $this->isReadWriteMany(),
            'formatted_size' => $this->getFormattedSize(),
            'resource_version' => $this->resourceVersion,
            'created_at' => $this->createdAt?->toISOString(),
        ]);
    }
}
