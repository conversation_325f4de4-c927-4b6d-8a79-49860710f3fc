<?php

namespace App\DTOs;

use Carbon\Carbon;

class IngressDTO extends KubernetesResourceDTO
{
    public function __construct(
        string $name,
        string $namespace,
        array $labels = [],
        array $annotations = [],
        ?Carbon $createdAt = null,
        ?string $uid = null,
        ?string $resourceVersion = null,
        public string $className = '',
        public array $rules = [],
        public ?array $tls = null,
        public ?string $loadBalancerIngress = null,
        public string $status = 'Pending'
    ) {
        parent::__construct($name, $namespace, $labels, $annotations, $createdAt, $uid, $resourceVersion);
    }

    public static function fromK8sResource(array $resource): static
    {
        $metadata = $resource['metadata'] ?? [];
        $spec = $resource['spec'] ?? [];
        $status = $resource['status'] ?? [];

        // 获取负载均衡器信息
        $loadBalancer = $status['loadBalancer'] ?? [];
        $ingress = $loadBalancer['ingress'] ?? [];
        $loadBalancerIngress = ! empty($ingress) ? ($ingress[0]['ip'] ?? $ingress[0]['hostname'] ?? null) : null;

        // 确定状态
        $ingressStatus = 'Pending';
        if (! empty($ingress)) {
            $ingressStatus = 'Ready';
        } elseif (isset($status['conditions'])) {
            foreach ($status['conditions'] as $condition) {
                if ($condition['type'] === 'Ready' && $condition['status'] === 'True') {
                    $ingressStatus = 'Ready';
                    break;
                }
            }
        }

        return new self(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
            ? Carbon::parse($metadata['creationTimestamp'])
            : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null,
            className: $spec['ingressClassName'] ?? '',
            rules: $spec['rules'] ?? [],
            tls: $spec['tls'] ?? null,
            loadBalancerIngress: $loadBalancerIngress,
            status: $ingressStatus
        );
    }

    /**
     * 获取所有域名
     */
    public function getDomains(): array
    {
        $domains = [];
        foreach ($this->rules as $rule) {
            if (isset($rule['host'])) {
                $domains[] = $rule['host'];
            }
        }

        return array_unique($domains);
    }

    /**
     * 检查是否有 TLS
     */
    public function hasTls(): bool
    {
        return ! empty($this->tls);
    }

    /**
     * 获取 TLS 域名
     */
    public function getTlsDomains(): array
    {
        if (! $this->hasTls()) {
            return [];
        }

        $domains = [];
        foreach ($this->tls as $tlsConfig) {
            if (isset($tlsConfig['hosts'])) {
                $domains = array_merge($domains, $tlsConfig['hosts']);
            }
        }

        return array_unique($domains);
    }

    /**
     * 获取访问 URL 列表
     */
    public function getAccessUrls(): array
    {
        $urls = [];
        $domains = $this->getDomains();
        $tlsDomains = $this->getTlsDomains();

        foreach ($domains as $domain) {
            $scheme = in_array($domain, $tlsDomains) ? 'https' : 'http';
            $urls[] = "{$scheme}://{$domain}";
        }

        return $urls;
    }

    /**
     * 获取资源类型
     */
    public function getResourceType(): string
    {
        return 'Ingress';
    }

    /**
     * 转换为数组格式用于 API 响应
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'type' => $this->getResourceType(),
            'rules' => $this->rules,
            'tls' => $this->tls,
            'loadBalancerIngress' => $this->loadBalancerIngress,
            'status' => $this->status,
            'domains' => $this->getDomains(),
            'has_tls' => $this->hasTls(),
            'tls_domains' => $this->getTlsDomains(),
            'access_urls' => $this->getAccessUrls(),
            'resource_version' => $this->resourceVersion,
            'created_at' => $this->createdAt?->toISOString(),
        ]);
    }
}
