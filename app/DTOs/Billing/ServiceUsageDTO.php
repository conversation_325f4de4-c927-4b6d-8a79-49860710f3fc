<?php

namespace App\DTOs\Billing;

class ServiceUsageDTO
{
    public function __construct(
        public string $name,
        public string $namespace,
        public string $workspaceName,
        public string $serviceType,
        public array $externalIps = [],
        public ?int $poolIpId = null,
        public array $labels = [],
        public array $annotations = []
    ) {}

    /**
     * 转换为标准化使用量数据
     */
    public function toUsageArray(): array
    {
        return [
            'ip_address' => count($this->externalIps),
            'service_type' => $this->serviceType,
        ];
    }

    /**
     * 检查是否是LoadBalancer类型
     */
    public function isLoadBalancer(): bool
    {
        return $this->serviceType === 'LoadBalancer';
    }

    /**
     * 检查是否属于平台管理的资源
     */
    public function isPlatformManaged(): bool
    {
        return isset($this->labels[\App\ClusterLabel::PLATFORM->value]);
    }

    /**
     * 获取工作空间名称
     */
    public function getWorkspaceName(): ?string
    {
        return $this->labels[\App\ClusterLabel::WORKSPACE->value] ?? null;
    }

    /**
     * 从K8s Service数据创建DTO
     */
    public static function fromK8sData(array $serviceData): self
    {
        $metadata = $serviceData['metadata'] ?? [];
        $spec = $serviceData['spec'] ?? [];
        $status = $serviceData['status'] ?? [];

        $serviceType = $spec['type'] ?? 'ClusterIP';
        $externalIps = [];

        // 获取LoadBalancer的外部IP
        if ($serviceType === 'LoadBalancer') {
            $loadBalancer = $status['loadBalancer'] ?? [];
            $ingress = $loadBalancer['ingress'] ?? [];

            foreach ($ingress as $ingressItem) {
                if (isset($ingressItem['ip'])) {
                    $externalIps[] = $ingressItem['ip'];
                }
            }
        }

        $labels = $metadata['labels'] ?? [];
        $workspaceName = $labels[\App\ClusterLabel::WORKSPACE->value] ?? '';

        return new self(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            workspaceName: $workspaceName,
            serviceType: $serviceType,
            externalIps: $externalIps,
            labels: $labels,
            annotations: $metadata['annotations'] ?? []
        );
    }

    /**
     * 从数据库Service模型创建DTO
     */
    public static function fromServiceModel(\App\Models\Service $service): self
    {
        $externalIps = [];
        $poolIpId = null;

        // 如果是LoadBalancer类型且有分配的IP
        if ($service->type === 'LoadBalancer' && $service->pool_ip_id) {
            $poolIpId = $service->pool_ip_id;
            if ($service->poolIp) {
                $externalIps[] = $service->poolIp->ip_address;
            }
        }

        return new self(
            name: $service->name,
            namespace: $service->workspace->namespace,
            workspaceName: $service->workspace->name,
            serviceType: $service->type,
            externalIps: $externalIps,
            poolIpId: $poolIpId
        );
    }
}
