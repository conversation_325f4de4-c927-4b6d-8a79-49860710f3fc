<?php

namespace App\DTOs\Billing;

class StorageUsageDTO
{
    public function __construct(
        public string $name,
        public string $namespace,
        public string $workspaceName,
        public float $sizeGi,
        public string $storageClass,
        public array $labels = [],
        public array $annotations = []
    ) {}

    /**
     * 转换为标准化使用量数据
     */
    public function toUsageArray(): array
    {
        return [
            'storage_gi' => $this->sizeGi,
            'storage_class' => $this->storageClass,
        ];
    }

    /**
     * 检查是否属于平台管理的资源
     */
    public function isPlatformManaged(): bool
    {
        return isset($this->labels[\App\ClusterLabel::PLATFORM->value]);
    }

    /**
     * 获取工作空间名称
     */
    public function getWorkspaceName(): ?string
    {
        return $this->labels[\App\ClusterLabel::WORKSPACE->value] ?? null;
    }

    /**
     * 从K8s PVC数据创建DTO
     */
    public static function fromK8sData(array $pvcData): self
    {
        $metadata = $pvcData['metadata'] ?? [];
        $spec = $pvcData['spec'] ?? [];
        $status = $pvcData['status'] ?? [];

        $storageClass = $spec['storageClassName'] ?? 'default';

        // 获取存储大小
        $resources = $spec['resources'] ?? [];
        $requests = $resources['requests'] ?? [];
        $storageStr = $requests['storage'] ?? '0Gi';

        $sizeGi = self::parseStorageToGi($storageStr);

        $labels = $metadata['labels'] ?? [];
        $workspaceName = $labels[\App\ClusterLabel::WORKSPACE->value] ?? '';

        return new self(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            workspaceName: $workspaceName,
            sizeGi: $sizeGi,
            storageClass: $storageClass,
            labels: $labels,
            annotations: $metadata['annotations'] ?? []
        );
    }

    /**
     * 解析存储字符串为Gi单位
     */
    private static function parseStorageToGi(string $storageStr): float
    {
        $value = (float) preg_replace('/[^0-9.]/', '', $storageStr);

        if (str_contains($storageStr, 'Ti')) {
            return $value * 1024; // Ti to Gi
        } elseif (str_contains($storageStr, 'Gi')) {
            return $value;
        } elseif (str_contains($storageStr, 'Mi')) {
            return $value / 1024; // Mi to Gi
        } elseif (str_contains($storageStr, 'Ki')) {
            return $value / (1024 * 1024); // Ki to Gi
        } else {
            // 假设是字节
            return $value / (1024 * 1024 * 1024); // bytes to Gi
        }
    }
}
