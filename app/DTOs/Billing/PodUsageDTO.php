<?php

namespace App\DTOs\Billing;

class PodUsageDTO
{
    public function __construct(
        public string $name,
        public string $namespace,
        public string $workspaceName,
        public float $cpuRequestMillicores,
        public float $memoryRequestMi,
        public array $labels = [],
        public array $annotations = []
    ) {}

    /**
     * 转换为标准化使用量数据
     */
    public function toUsageArray(): array
    {
        return [
            'cpu_m' => $this->cpuRequestMillicores,
            'memory_mi' => $this->memoryRequestMi,
        ];
    }

    /**
     * 检查是否属于平台管理的资源
     */
    public function isPlatformManaged(): bool
    {
        return isset($this->labels[\App\ClusterLabel::PLATFORM->value]);
    }

    /**
     * 获取工作空间名称
     */
    public function getWorkspaceName(): ?string
    {
        return $this->labels[\App\ClusterLabel::WORKSPACE->value] ?? null;
    }

    /**
     * 从K8s Pod数据创建DTO
     */
    public static function fromK8sData(array $podData): self
    {
        $metadata = $podData['metadata'] ?? [];
        $spec = $podData['spec'] ?? [];
        $containers = $spec['containers'] ?? [];

        $totalCpuRequest = 0;
        $totalMemoryRequest = 0;

        foreach ($containers as $container) {
            $resources = $container['resources'] ?? [];
            $requests = $resources['requests'] ?? [];

            // CPU 请求 (格式: "100m" 或 "0.1")
            if (isset($requests['cpu'])) {
                $cpuStr = $requests['cpu'];
                if (str_ends_with($cpuStr, 'm')) {
                    $totalCpuRequest += (float) rtrim($cpuStr, 'm');
                } else {
                    $totalCpuRequest += (float) $cpuStr * 1000;
                }
            }

            // 内存请求 (格式: "128Mi", "1Gi" 等)
            if (isset($requests['memory'])) {
                $memoryStr = $requests['memory'];
                $totalMemoryRequest += self::parseMemoryToMi($memoryStr);
            }
        }

        $labels = $metadata['labels'] ?? [];
        $workspaceName = $labels[\App\ClusterLabel::WORKSPACE->value] ?? '';

        return new self(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            workspaceName: $workspaceName,
            cpuRequestMillicores: $totalCpuRequest,
            memoryRequestMi: $totalMemoryRequest,
            labels: $labels,
            annotations: $metadata['annotations'] ?? []
        );
    }

    /**
     * 解析内存字符串为Mi单位
     */
    private static function parseMemoryToMi(string $memoryStr): float
    {
        $value = (float) preg_replace('/[^0-9.]/', '', $memoryStr);

        if (str_contains($memoryStr, 'Gi')) {
            return $value * 1024; // Gi to Mi
        } elseif (str_contains($memoryStr, 'Mi')) {
            return $value;
        } elseif (str_contains($memoryStr, 'Ki')) {
            return $value / 1024; // Ki to Mi
        } else {
            // 假设是字节
            return $value / (1024 * 1024); // bytes to Mi
        }
    }
}
