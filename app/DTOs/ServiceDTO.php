<?php

namespace App\DTOs;

use App\ClusterLabel;
use Carbon\Carbon;

/**
 * Service DTO
 */
class ServiceDTO extends KubernetesResourceDTO
{
    public readonly string $serviceType;

    public readonly ?string $clusterIp;

    public readonly ?array $externalIps;

    public readonly ?string $loadBalancerIp;

    public readonly array $ports;

    public readonly array $selector;

    public readonly ?string $sessionAffinity;

    public readonly ?string $externalTrafficPolicy;

    public readonly ?string $internalTrafficPolicy;

    public readonly ?string $loadBalancerClass;

    public readonly string $status;

    public readonly ?string $workloadType;

    public readonly ?string $workloadName;

    public function __construct(
        string $name,
        string $namespace,
        string $serviceType,
        ?string $clusterIp,
        ?array $externalIps,
        ?string $loadBalancerIp,
        array $ports,
        array $selector,
        ?string $sessionAffinity,
        ?string $externalTrafficPolicy,
        ?string $internalTrafficPolicy,
        ?string $loadBalancerClass,
        string $status,
        array $labels = [],
        array $annotations = [],
        ?Carbon $createdAt = null,
        ?string $uid = null,
        ?string $resourceVersion = null,
        ?string $workloadType = null,
        ?string $workloadName = null
    ) {
        parent::__construct($name, $namespace, $labels, $annotations, $createdAt, $uid, $resourceVersion);

        $this->serviceType = $serviceType;
        $this->clusterIp = $clusterIp;
        $this->externalIps = $externalIps;
        $this->loadBalancerIp = $loadBalancerIp;
        $this->ports = $ports;
        $this->selector = $selector;
        $this->sessionAffinity = $sessionAffinity;
        $this->externalTrafficPolicy = $externalTrafficPolicy;
        $this->internalTrafficPolicy = $internalTrafficPolicy;
        $this->loadBalancerClass = $loadBalancerClass;
        $this->status = $status;
        $this->workloadType = $workloadType;
        $this->workloadName = $workloadName;
    }

    /**
     * 从 Kubernetes Service 资源创建 DTO
     */
    public static function fromK8sResource(array $resource): static
    {
        $spec = $resource['spec'] ?? [];
        $status = $resource['status'] ?? [];
        $metadata = $resource['metadata'] ?? [];

        // 处理端口信息
        $ports = array_map(function ($port) {
            return [
                'name' => $port['name'] ?? null,
                'port' => $port['port'] ?? null,
                'targetPort' => $port['targetPort'] ?? null,
                'protocol' => $port['protocol'] ?? 'TCP',
                'nodePort' => $port['nodePort'] ?? null,
            ];
        }, $spec['ports'] ?? []);

        // 处理负载均衡器状态
        $loadBalancerIp = null;
        if (isset($status['loadBalancer']['ingress'][0]['ip'])) {
            $loadBalancerIp = $status['loadBalancer']['ingress'][0]['ip'];
        }

        // 确定服务状态
        $serviceStatus = 'Active';
        if ($spec['type'] === 'LoadBalancer' && ! $loadBalancerIp) {
            $serviceStatus = 'Pending';
        }

        return new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            serviceType: $spec['type'] ?? 'ClusterIP',
            clusterIp: $spec['clusterIP'] ?? null,
            externalIps: $spec['externalIPs'] ?? null,
            loadBalancerIp: $loadBalancerIp,
            ports: $ports,
            selector: $spec['selector'] ?? [],
            sessionAffinity: $spec['sessionAffinity'] ?? 'None',
            externalTrafficPolicy: $spec['externalTrafficPolicy'] ?? 'Cluster',
            internalTrafficPolicy: $spec['internalTrafficPolicy'] ?? 'Cluster',
            loadBalancerClass: $spec['loadBalancerClass'] ?? null,
            status: $serviceStatus,
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
            ? Carbon::parse($metadata['creationTimestamp'])
            : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null,
            workloadType: $metadata['labels'][ClusterLabel::WORKLOAD_TYPE->value] ?? null,
            workloadName: $metadata['labels'][ClusterLabel::WORKLOAD_NAME->value] ?? null,
        );
    }

    /**
     * 获取外部访问地址
     */
    public function getExternalAddresses(): array
    {
        $addresses = [];

        if ($this->serviceType === 'LoadBalancer' && $this->loadBalancerIp) {
            $addresses[] = $this->loadBalancerIp;
        }

        if (! empty($this->externalIps)) {
            $addresses = array_merge($addresses, $this->externalIps);
        }

        return $addresses;
    }

    /**
     * 检查是否有外部访问
     */
    public function hasExternalAccess(): bool
    {
        return $this->serviceType === 'LoadBalancer' ||
            $this->serviceType === 'NodePort' ||
            ! empty($this->externalIps);
    }

    /**
     * 获取资源类型
     */
    public function getResourceType(): string
    {
        return 'Service';
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'type' => $this->getResourceType(),
            'service_type' => $this->serviceType,
            'cluster_ip' => $this->clusterIp,
            'external_ips' => $this->externalIps,
            'load_balancer_ip' => $this->loadBalancerIp,
            'load_balancer_class' => $this->loadBalancerClass,
            'ports' => array_map(function ($port) {
                return [
                    'name' => $port['name'] ?? null,
                    'port' => $port['port'] ?? null,
                    'target_port' => $port['targetPort'] ?? null,
                    'protocol' => $port['protocol'] ?? 'TCP',
                    'node_port' => $port['nodePort'] ?? null,
                ];
            }, $this->ports),
            // 'selector' => $this->selector,
            'session_affinity' => $this->sessionAffinity,
            'external_traffic_policy' => $this->externalTrafficPolicy,
            'status' => $this->status,
            'external_addresses' => $this->getExternalAddresses(),
            'has_external_access' => $this->hasExternalAccess(),
            'workload_type' => $this->workloadType,
            'workload_name' => $this->workloadName,
            'resource_version' => $this->resourceVersion,
            'created_at' => $this->createdAt?->toISOString(),
        ]);
    }
}
