<?php

namespace Tests\Unit;

use App\Rules\ImagePullSecretRule;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class ImagePullSecretRuleTest extends TestCase
{
    public function test_image_pull_secret_rule_rejects_empty_string()
    {
        $rule = new ImagePullSecretRule;
        $data = ['secret' => ''];
        $validator = Validator::make($data, ['secret' => ['required', $rule]]);

        $this->assertTrue($validator->fails());
        $this->assertStringContainsString('不能为空', $validator->errors()->first('secret'));
    }

    public function test_image_pull_secret_rule_rejects_null()
    {
        $rule = new ImagePullSecretRule;
        $data = ['secret' => null];
        $validator = Validator::make($data, ['secret' => $rule]);

        $this->assertTrue($validator->fails());
        $this->assertStringContainsString('镜像拉取密钥名称不能为空', $validator->errors()->first('secret'));
    }

    public function test_image_pull_secret_rule_rejects_non_string()
    {
        $rule = new ImagePullSecretRule;
        $data = ['secret' => 123];
        $validator = Validator::make($data, ['secret' => $rule]);

        $this->assertTrue($validator->fails());
        $this->assertStringContainsString('镜像拉取密钥名称必须是字符串', $validator->errors()->first('secret'));
    }

    public function test_image_pull_secret_rule_rejects_invalid_format()
    {
        $rule = new ImagePullSecretRule;

        $testCases = [
            'Invalid-Secret',    // 大写字母
            'invalid@secret',    // 特殊字符
            '-invalid-secret',   // 以连字符开头
            'invalid-secret-',   // 以连字符结尾
            '123invalid',        // 以数字开头
        ];

        foreach ($testCases as $secretName) {
            $data = ['secret' => $secretName];
            $validator = Validator::make($data, ['secret' => $rule]);

            $this->assertTrue($validator->fails(), "应该拒绝无效格式: {$secretName}");
        }
    }

    public function test_image_pull_secret_rule_accepts_valid_names()
    {
        $rule = new ImagePullSecretRule;

        $validNames = [
            'valid-secret',
            'valid-secret-123',
            'mysecret',
            'a',
            'secret1',
            'my-docker-registry-secret',
        ];

        foreach ($validNames as $secretName) {
            $data = ['secret' => $secretName];
            $validator = Validator::make($data, ['secret' => $rule]);

            $this->assertFalse($validator->fails(), "应该接受有效格式: {$secretName}");
        }
    }

    public function test_image_pull_secret_rule_rejects_too_long_name()
    {
        $rule = new ImagePullSecretRule;
        $longName = str_repeat('a', 254); // 超过253个字符的限制

        $data = ['secret' => $longName];
        $validator = Validator::make($data, ['secret' => $rule]);

        $this->assertTrue($validator->fails());
        $this->assertStringContainsString('长度不能超过253个字符', $validator->errors()->first('secret'));
    }

    public function test_image_pull_secrets_array_validation()
    {
        // 测试问题案例：包含空字符串的数组
        $data = ['image_pull_secrets' => ['']];
        $rules = [
            'image_pull_secrets' => 'nullable|array',
            'image_pull_secrets.*' => ['required', new ImagePullSecretRule],
        ];

        $validator = Validator::make($data, $rules);

        $this->assertTrue($validator->fails(), '包含空字符串的数组应该验证失败');
        $this->assertStringContainsString('不能为空', $validator->errors()->first('image_pull_secrets.0'));
    }

    public function test_mixed_valid_invalid_secrets()
    {
        $data = ['image_pull_secrets' => ['valid-secret', '', 'another-valid']];
        $rules = [
            'image_pull_secrets' => 'nullable|array',
            'image_pull_secrets.*' => ['required', new ImagePullSecretRule],
        ];

        $validator = Validator::make($data, $rules);

        $this->assertTrue($validator->fails(), '混合有效和无效密钥的数组应该验证失败');
        $this->assertStringContainsString('不能为空', $validator->errors()->first('image_pull_secrets.1'));

        // 验证有效的密钥没有错误
        $this->assertFalse($validator->errors()->has('image_pull_secrets.0'));
        $this->assertFalse($validator->errors()->has('image_pull_secrets.2'));
        // 但应该有第二个元素的错误
        $this->assertTrue($validator->errors()->has('image_pull_secrets.1'));
    }
}
