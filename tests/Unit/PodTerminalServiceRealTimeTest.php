<?php

namespace Tests\Unit;

use App\Models\Cluster;
use App\Models\Workspace;
use App\Service\PodTerminalService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PodTerminalServiceRealTimeTest extends TestCase
{
    use RefreshDatabase;

    public function test_real_time_output_callback_is_invoked()
    {
        // 创建测试数据
        $cluster = Cluster::factory()->create([
            'name' => 'test-cluster',
            'server_url' => 'https://test-cluster.example.com',
            'auth_type' => 'token',
            'token' => 'test-token',
            'insecure_skip_tls_verify' => true,
        ]);

        $workspace = Workspace::factory()->create([
            'cluster_id' => $cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'test-namespace',
        ]);

        // 模拟回调调用记录
        $callbackInvocations = [];

        $outputCallback = function (string $type, string $data) use (&$callbackInvocations) {
            $callbackInvocations[] = [
                'type' => $type,
                'data' => $data,
                'timestamp' => microtime(true),
            ];
        };

        // 测试 processMessageRealTime 方法
        $service = app(PodTerminalService::class);
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('processMessageRealTime');
        $method->setAccessible(true);

        $result = [
            'stdout' => '',
            'stderr' => '',
            'exit_code' => null,
            'error' => null,
            'completed' => false,
            'timeout' => false,
        ];

        // 测试 STDOUT 消息
        $stdoutPayload = "\x01Hello World";
        $method->invokeArgs($service, [$stdoutPayload, &$result, $outputCallback]);

        // 测试 STDERR 消息
        $stderrPayload = "\x02Error message";
        $method->invokeArgs($service, [$stderrPayload, &$result, $outputCallback]);

        // 测试退出码消息
        $exitPayload = "\x03".json_encode(['status' => 0]);
        $method->invokeArgs($service, [$exitPayload, &$result, $outputCallback]);

        // 验证回调被正确调用
        $this->assertCount(3, $callbackInvocations);

        // 验证 STDOUT 回调
        $this->assertEquals('stdout', $callbackInvocations[0]['type']);
        $this->assertEquals('Hello World', $callbackInvocations[0]['data']);

        // 验证 STDERR 回调
        $this->assertEquals('stderr', $callbackInvocations[1]['type']);
        $this->assertEquals('Error message', $callbackInvocations[1]['data']);

        // 验证退出码回调
        $this->assertEquals('exit_code', $callbackInvocations[2]['type']);
        $this->assertEquals(0, $callbackInvocations[2]['data']);

        // 验证结果数据被正确更新
        $this->assertEquals('Hello World', $result['stdout']);
        $this->assertEquals('Error message', $result['stderr']);
        $this->assertEquals(0, $result['exit_code']);
        $this->assertTrue($result['completed']);
    }

    public function test_real_time_output_handles_error_messages()
    {
        $callbackInvocations = [];

        $outputCallback = function (string $type, string $data) use (&$callbackInvocations) {
            $callbackInvocations[] = [
                'type' => $type,
                'data' => $data,
            ];
        };

        $service = app(PodTerminalService::class);
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('processMessageRealTime');
        $method->setAccessible(true);

        $result = [
            'stdout' => '',
            'stderr' => '',
            'exit_code' => null,
            'error' => null,
            'completed' => false,
            'timeout' => false,
        ];

        // 测试 K8s API 错误消息
        $errorPayload = "\x03command terminated with non-zero exit code";
        $method->invokeArgs($service, [$errorPayload, &$result, $outputCallback]);

        // 验证错误回调
        $this->assertCount(1, $callbackInvocations);
        $this->assertEquals('error', $callbackInvocations[0]['type']);
        $this->assertStringContainsString('Kubernetes API Error', $callbackInvocations[0]['data']);

        // 验证结果数据
        $this->assertStringContainsString('Kubernetes API Error', $result['error']);
        $this->assertTrue($result['completed']);
    }

    public function test_real_time_output_works_without_callback()
    {
        // 测试没有回调函数时不会出错
        $service = app(PodTerminalService::class);
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('processMessageRealTime');
        $method->setAccessible(true);

        $result = [
            'stdout' => '',
            'stderr' => '',
            'exit_code' => null,
            'error' => null,
            'completed' => false,
            'timeout' => false,
        ];

        // 测试没有回调函数的情况
        $stdoutPayload = "\x01Test output";
        $method->invokeArgs($service, [$stdoutPayload, &$result, null]);

        // 验证结果仍然被正确更新
        $this->assertEquals('Test output', $result['stdout']);

        // 不应该抛出异常
        $this->assertTrue(true);
    }
}
