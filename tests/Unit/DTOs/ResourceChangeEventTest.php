<?php

namespace Tests\Unit\DTOs;

use App\Events\ResourceChanged;
use PHPUnit\Framework\TestCase;

class ResourceChangeEventTest extends TestCase
{
    public function test_resource_changed_event_structure_matches_frontend_format()
    {
        $namespace = 'test-namespace';
        $clusterName = 'test-cluster';
        $clusterId = 1;
        $resourceType = 'deployments';
        $changes = [
            'updated' => [
                [
                    'name' => 'nginx',
                    'namespace' => 'test-namespace',
                    'uid' => '0438c8dd-a823-430b-b85d-ce38687e72ce',
                    'resourceVersion' => '3222836',
                    'previousResourceVersion' => '3222776',
                ],
            ],
        ];

        $event = new ResourceChanged($namespace, $clusterName, $clusterId, $resourceType, $changes);

        $broadcastData = $event->broadcastWith();

        // 验证事件结构与前端期望的格式匹配
        $this->assertArrayHasKey('namespace', $broadcastData);
        $this->assertArrayHasKey('cluster', $broadcastData);
        $this->assertArrayHasKey('resource_type', $broadcastData);
        $this->assertArrayHasKey('changes', $broadcastData);
        $this->assertArrayHasKey('summary', $broadcastData);
        $this->assertArrayHasKey('timestamp', $broadcastData);

        // 验证具体值
        $this->assertEquals($namespace, $broadcastData['namespace']);
        $this->assertEquals($clusterId, $broadcastData['cluster']['id']);
        $this->assertEquals($clusterName, $broadcastData['cluster']['name']);
        $this->assertEquals($resourceType, $broadcastData['resource_type']);
        $this->assertEquals($changes, $broadcastData['changes']);

        // 验证摘要信息
        $this->assertEquals(0, $broadcastData['summary']['created_count']);
        $this->assertEquals(1, $broadcastData['summary']['updated_count']);
        $this->assertEquals(0, $broadcastData['summary']['deleted_count']);
        $this->assertEquals(1, $broadcastData['summary']['total_changes']);

        // 验证时间戳格式
        $this->assertIsString($broadcastData['timestamp']);
        $this->assertNotEmpty($broadcastData['timestamp']);
    }

    public function test_resource_changed_event_with_multiple_changes()
    {
        $changes = [
            'created' => [
                [
                    'name' => 'new-deployment',
                    'namespace' => 'test-namespace',
                    'uid' => 'uid-1',
                    'resourceVersion' => '100',
                ],
            ],
            'updated' => [
                [
                    'name' => 'existing-deployment',
                    'namespace' => 'test-namespace',
                    'uid' => 'uid-2',
                    'resourceVersion' => '200',
                    'previousResourceVersion' => '150',
                ],
            ],
            'deleted' => [
                [
                    'name' => 'old-deployment',
                    'namespace' => 'test-namespace',
                    'uid' => 'uid-3',
                    'resourceVersion' => '50',
                ],
            ],
        ];

        $event = new ResourceChanged(
            'test-namespace',
            'test-cluster',
            1,
            'deployments',
            $changes
        );

        $broadcastData = $event->broadcastWith();

        // 验证摘要统计正确
        $this->assertEquals(1, $broadcastData['summary']['created_count']);
        $this->assertEquals(1, $broadcastData['summary']['updated_count']);
        $this->assertEquals(1, $broadcastData['summary']['deleted_count']);
        $this->assertEquals(3, $broadcastData['summary']['total_changes']);
    }
}
