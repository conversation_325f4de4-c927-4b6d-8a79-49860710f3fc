<?php

namespace Tests\Unit\DTOs;

use App\DTOs\PodCommandResultDTO;
use PHPUnit\Framework\TestCase;

class PodCommandResultDTOTest extends TestCase
{
    public function test_is_success_returns_true_for_completed_command_without_explicit_exit_code()
    {
        $dto = PodCommandResultDTO::fromArray([
            'stdout' => 'Hello World',
            'stderr' => '',
            'exit_code' => null,
            'error' => null,
            'completed' => true,
            'timeout' => false,
            'execution_time' => 0.5,
        ]);

        $this->assertTrue($dto->isSuccess());
    }

    public function test_is_success_returns_true_for_zero_exit_code()
    {
        $dto = PodCommandResultDTO::fromArray([
            'stdout' => 'Hello World',
            'stderr' => '',
            'exit_code' => 0,
            'error' => null,
            'completed' => true,
            'timeout' => false,
            'execution_time' => 0.5,
        ]);

        $this->assertTrue($dto->isSuccess());
    }

    public function test_is_success_returns_false_for_non_zero_exit_code()
    {
        $dto = PodCommandResultDTO::fromArray([
            'stdout' => '',
            'stderr' => 'Error message',
            'exit_code' => 1,
            'error' => null,
            'completed' => true,
            'timeout' => false,
            'execution_time' => 0.5,
        ]);

        $this->assertFalse($dto->isSuccess());
    }

    public function test_is_success_returns_false_for_timeout()
    {
        $dto = PodCommandResultDTO::fromArray([
            'stdout' => '',
            'stderr' => '',
            'exit_code' => null,
            'error' => null,
            'completed' => false,
            'timeout' => true,
            'execution_time' => 30.0,
        ]);

        $this->assertFalse($dto->isSuccess());
    }

    public function test_is_success_returns_false_for_error()
    {
        $dto = PodCommandResultDTO::fromArray([
            'stdout' => '',
            'stderr' => '',
            'exit_code' => null,
            'error' => 'Connection failed',
            'completed' => false,
            'timeout' => false,
            'execution_time' => 0.1,
        ]);

        $this->assertFalse($dto->isSuccess());
    }

    public function test_is_success_returns_false_for_incomplete_command()
    {
        $dto = PodCommandResultDTO::fromArray([
            'stdout' => '',
            'stderr' => '',
            'exit_code' => null,
            'error' => null,
            'completed' => false,
            'timeout' => false,
            'execution_time' => 0.1,
        ]);

        $this->assertFalse($dto->isSuccess());
    }

    public function test_has_output_returns_true_for_stdout()
    {
        $dto = PodCommandResultDTO::fromArray([
            'stdout' => 'Hello World',
            'stderr' => '',
            'exit_code' => null,
            'error' => null,
            'completed' => true,
            'timeout' => false,
            'execution_time' => 0.5,
        ]);

        $this->assertTrue($dto->hasOutput());
    }

    public function test_has_output_returns_true_for_stderr()
    {
        $dto = PodCommandResultDTO::fromArray([
            'stdout' => '',
            'stderr' => 'Error message',
            'exit_code' => null,
            'error' => null,
            'completed' => true,
            'timeout' => false,
            'execution_time' => 0.5,
        ]);

        $this->assertTrue($dto->hasOutput());
    }

    public function test_has_output_returns_false_for_no_output()
    {
        $dto = PodCommandResultDTO::fromArray([
            'stdout' => '',
            'stderr' => '',
            'exit_code' => null,
            'error' => null,
            'completed' => true,
            'timeout' => false,
            'execution_time' => 0.5,
        ]);

        $this->assertFalse($dto->hasOutput());
    }

    public function test_to_array_returns_correct_structure()
    {
        $data = [
            'stdout' => 'Hello World',
            'stderr' => '',
            'exit_code' => 0,
            'error' => null,
            'completed' => true,
            'timeout' => false,
            'execution_time' => 0.5,
        ];

        $dto = PodCommandResultDTO::fromArray($data);
        $result = $dto->toArray();

        $this->assertEquals($data, $result);
    }
}
