<?php

namespace Tests\Unit\Rules;

use App\Rules\NoNumbersOrSymbols;
use Tests\TestCase;

class NoNumbersOrSymbolsTest extends TestCase
{
    protected NoNumbersOrSymbols $rule;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rule = new NoNumbersOrSymbols;
    }

    public function test_valid_names_pass_validation()
    {
        $validNames = [
            'testContainer',
            'myApp',
            'webServer',
            'A',
            'a',
            'test123',
            'container1',
            'myApp2023',
            'webServer99',
        ];

        foreach ($validNames as $name) {
            $failed = false;
            $this->rule->validate('test', $name, function () use (&$failed) {
                $failed = true;
            });

            $this->assertFalse($failed, "名称 '{$name}' 应该通过验证");
        }
    }

    public function test_invalid_names_fail_validation()
    {
        $invalidNames = [
            '123container',  // 以数字开头
            '1test',         // 以数字开头
            '9webServer',    // 以数字开头
            'test-container', // 包含连字符
            'test_container', // 包含下划线
            'test@container', // 包含特殊字符
            'test.container', // 包含点
            'test container', // 包含空格
            'test/container', // 包含斜杠
            '',              // 空字符串
            '123',           // 纯数字
            '!test',         // 以特殊字符开头
            '@test',         // 以特殊字符开头
            '#test',         // 以特殊字符开头
        ];

        foreach ($invalidNames as $name) {
            $failed = false;
            $this->rule->validate('test', $name, function () use (&$failed) {
                $failed = true;
            });

            $this->assertTrue($failed, "名称 '{$name}' 应该验证失败");
        }
    }

    public function test_non_string_values_fail_validation()
    {
        $invalidValues = [
            123,
            null,
            [],
            new \stdClass,
            true,
            false,
        ];

        foreach ($invalidValues as $value) {
            $failed = false;
            $this->rule->validate('test', $value, function () use (&$failed) {
                $failed = true;
            });

            $this->assertTrue($failed, '非字符串值应该验证失败');
        }
    }

    public function test_error_message_is_in_chinese()
    {
        $errorMessage = null;
        $this->rule->validate('test', '123invalid', function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        $this->assertNotNull($errorMessage);
        $this->assertStringContainsString('必须以字母开头', $errorMessage);
        $this->assertStringContainsString('只能包含字母和数字', $errorMessage);
    }
}
