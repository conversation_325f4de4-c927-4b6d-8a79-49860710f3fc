<?php

namespace Tests\Unit;

use App\Rules\KubernetesNameRule;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class KubernetesNameRuleTest extends TestCase
{
    public function test_kubernetes_name_rule_accepts_valid_names()
    {
        $rule = new KubernetesNameRule;

        $validNames = [
            'valid-name',
            'valid-name-123',
            'myservice',
            'a',
            'service1',
            'my-awesome-service',
        ];

        foreach ($validNames as $name) {
            $data = ['name' => $name];
            $validator = Validator::make($data, ['name' => $rule]);

            $this->assertFalse($validator->fails(), "应该接受有效名称: {$name}");
        }
    }

    public function test_kubernetes_name_rule_rejects_invalid_names()
    {
        $rule = new KubernetesNameRule;

        $invalidNames = [
            'Invalid-Name',      // 大写字母
            'invalid@name',      // 特殊字符
            '-invalid-name',     // 以连字符开头
            'invalid-name-',     // 以连字符结尾
            '123invalid',        // 以数字开头
            'invalid.name',      // 包含点号
            'invalid_name',      // 包含下划线
        ];

        foreach ($invalidNames as $name) {
            $data = ['name' => $name];
            $validator = Validator::make($data, ['name' => ['required', $rule]]);

            $this->assertTrue($validator->fails(), "应该拒绝无效名称: {$name}");
        }

        // 单独测试空字符串，需要required规则来处理
        $data = ['name' => ''];
        $validator = Validator::make($data, ['name' => ['required', $rule]]);
        $this->assertTrue($validator->fails(), '应该拒绝空字符串');
    }

    public function test_kubernetes_name_rule_rejects_too_long_name()
    {
        $rule = new KubernetesNameRule;
        $longName = str_repeat('a', 64); // 超过63个字符的限制

        $data = ['name' => $longName];
        $validator = Validator::make($data, ['name' => $rule]);

        $this->assertTrue($validator->fails());
        $this->assertStringContainsString('长度不能超过63个字符', $validator->errors()->first('name'));
    }

    public function test_kubernetes_name_rule_accepts_exactly_63_chars()
    {
        $rule = new KubernetesNameRule;
        $exactlyName = str_repeat('a', 63); // 正好63个字符

        $data = ['name' => $exactlyName];
        $validator = Validator::make($data, ['name' => $rule]);

        $this->assertFalse($validator->fails(), '63个字符的名称应该被接受');
    }

    public function test_kubernetes_name_rule_rejects_null()
    {
        $rule = new KubernetesNameRule;
        $data = ['name' => null];
        $validator = Validator::make($data, ['name' => $rule]);

        $this->assertTrue($validator->fails());
        $this->assertStringContainsString('不能为空', $validator->errors()->first('name'));
    }

    public function test_kubernetes_name_rule_rejects_non_string()
    {
        $rule = new KubernetesNameRule;
        $data = ['name' => 123];
        $validator = Validator::make($data, ['name' => $rule]);

        $this->assertTrue($validator->fails());
        $this->assertStringContainsString('必须是字符串', $validator->errors()->first('name'));
    }
}
