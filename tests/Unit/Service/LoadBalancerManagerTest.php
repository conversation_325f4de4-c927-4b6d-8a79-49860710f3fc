<?php

namespace Tests\Unit\Service;

use App\Service\LoadBalancerDrivers\CiliumDriver;
use App\Service\LoadBalancerDrivers\MetallbDriver;
use App\Service\LoadBalancerDrivers\PureLbDriver;
use App\Service\LoadBalancerManager;
use InvalidArgumentException;
use Tests\TestCase;

class LoadBalancerManagerTest extends TestCase
{
    protected LoadBalancerManager $manager;

    protected function setUp(): void
    {
        parent::setUp();

        // 设置测试配置
        config([
            'loadbalancers.default' => 'metallb',
            'loadbalancers.drivers' => [
                'metallb' => [
                    'name' => 'MetalLB',
                    'class' => MetallbDriver::class,
                    'description' => 'MetalLB test driver',
                ],
                'purelb' => [
                    'name' => 'PureLB',
                    'class' => PureLbDriver::class,
                    'description' => 'PureLB test driver',
                ],
                'cilium' => [
                    'name' => 'Cilium',
                    'class' => CiliumDriver::class,
                    'description' => 'Cilium test driver',
                ],
            ],
        ]);

        $this->manager = new LoadBalancerManager;
    }

    public function test_get_default_driver_name(): void
    {
        $this->assertEquals('metallb', $this->manager->getDefaultDriverName());
    }

    public function test_get_available_drivers(): void
    {
        $drivers = $this->manager->getAvailableDrivers();

        $this->assertContains('metallb', $drivers);
        $this->assertContains('purelb', $drivers);
        $this->assertContains('cilium', $drivers);
        $this->assertCount(3, $drivers);
    }

    public function test_has_driver(): void
    {
        $this->assertTrue($this->manager->hasDriver('metallb'));
        $this->assertTrue($this->manager->hasDriver('purelb'));
        $this->assertTrue($this->manager->hasDriver('cilium'));
        $this->assertFalse($this->manager->hasDriver('nonexistent'));
    }

    public function test_get_driver_display_name(): void
    {
        $this->assertEquals('MetalLB', $this->manager->getDriverDisplayName('metallb'));
        $this->assertEquals('PureLB', $this->manager->getDriverDisplayName('purelb'));
        $this->assertEquals('Cilium', $this->manager->getDriverDisplayName('cilium'));
        $this->assertEquals('Unknown', $this->manager->getDriverDisplayName('unknown'));
    }

    public function test_driver_returns_correct_instance(): void
    {
        $metallbDriver = $this->manager->driver('metallb');
        $this->assertInstanceOf(MetallbDriver::class, $metallbDriver);

        $purelBDriver = $this->manager->driver('purelb');
        $this->assertInstanceOf(PureLbDriver::class, $purelBDriver);

        $ciliumDriver = $this->manager->driver('cilium');
        $this->assertInstanceOf(CiliumDriver::class, $ciliumDriver);
    }

    public function test_driver_returns_default_when_no_name_provided(): void
    {
        $defaultDriver = $this->manager->driver();
        $this->assertInstanceOf(MetallbDriver::class, $defaultDriver);
    }

    public function test_driver_throws_exception_for_unknown_driver(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('LoadBalancer driver [unknown] not found.');

        $this->manager->driver('unknown');
    }

    public function test_register_custom_driver(): void
    {
        $customDriver = new CiliumDriver;
        $this->manager->register('custom', $customDriver);

        $this->assertTrue($this->manager->hasDriver('custom'));
        $this->assertSame($customDriver, $this->manager->driver('custom'));
    }

    public function test_get_driver_description(): void
    {
        $this->assertEquals('MetalLB test driver', $this->manager->getDriverDescription('metallb'));
        $this->assertEquals('PureLB test driver', $this->manager->getDriverDescription('purelb'));
        $this->assertEquals('Cilium test driver', $this->manager->getDriverDescription('cilium'));
        $this->assertNull($this->manager->getDriverDescription('unknown'));
    }

    public function test_get_drivers_info(): void
    {
        $info = $this->manager->getDriversInfo();

        $this->assertArrayHasKey('metallb', $info);
        $this->assertArrayHasKey('purelb', $info);
        $this->assertArrayHasKey('cilium', $info);

        $this->assertEquals('MetalLB', $info['metallb']['name']);
        $this->assertEquals('MetalLB test driver', $info['metallb']['description']);
        $this->assertTrue($info['metallb']['available']);

        $this->assertEquals('PureLB', $info['purelb']['name']);
        $this->assertEquals('PureLB test driver', $info['purelb']['description']);
        $this->assertTrue($info['purelb']['available']);

        $this->assertEquals('Cilium', $info['cilium']['name']);
        $this->assertEquals('Cilium test driver', $info['cilium']['description']);
        $this->assertTrue($info['cilium']['available']);
    }

    public function test_invalid_driver_class_throws_exception(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('LoadBalancer driver class [NonExistentClass] not found for driver [invalid].');

        config([
            'loadbalancers.drivers' => [
                'invalid' => [
                    'name' => 'Invalid',
                    'class' => 'NonExistentClass',
                ],
            ],
        ]);

        new LoadBalancerManager;
    }

    public function test_driver_not_implementing_interface_throws_exception(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('LoadBalancer driver [invalid] must implement LoadBalancerDriverInterface.');

        config([
            'loadbalancers.drivers' => [
                'invalid' => [
                    'name' => 'Invalid',
                    'class' => \stdClass::class,
                ],
            ],
        ]);

        new LoadBalancerManager;
    }
}
