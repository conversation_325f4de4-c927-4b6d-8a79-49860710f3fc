<?php

namespace Tests\Unit\Service\LoadBalancerDrivers;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\Service;
use App\Service\LoadBalancerDrivers\PureLbDriver;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class PureLbDriverTest extends TestCase
{
    use RefreshDatabase;

    private PureLbDriver $driver;

    private Cluster $cluster;

    private IpPool $ipPool;

    protected function setUp(): void
    {
        parent::setUp();

        $this->driver = new PureLbDriver;
        $this->cluster = Cluster::factory()->create();
        $this->ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'name' => 'test-pool',
        ]);
    }

    public function test_get_service_annotations_with_allocated_ip(): void
    {
        $poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '************',
        ]);

        $service = Service::factory()->create([
            'allocated_ip' => '************',
            'pool_ip_id' => $poolIp->id,
            'allow_shared_ip' => false,
        ]);

        $annotations = $this->driver->getServiceAnnotations($service);

        $this->assertEquals([
            'purelb.io/addresses' => '************',
            'purelb.io/service-group' => 'test-pool',
        ], $annotations);
    }

    public function test_get_service_annotations_with_shared_ip(): void
    {
        $poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '************',
        ]);

        $service = Service::factory()->create([
            'allocated_ip' => '************',
            'pool_ip_id' => $poolIp->id,
            'allow_shared_ip' => true,
        ]);

        $annotations = $this->driver->getServiceAnnotations($service);

        $this->assertEquals([
            'purelb.io/addresses' => '************',
            'purelb.io/service-group' => 'test-pool',
            'purelb.io/allow-shared-ip' => 'share-ip-192-168-1-10',
        ], $annotations);
    }

    public function test_get_service_annotations_without_allocated_ip(): void
    {
        $service = Service::factory()->create([
            'allocated_ip' => null,
        ]);

        $annotations = $this->driver->getServiceAnnotations($service);

        $this->assertEquals([], $annotations);
    }

    public function test_sync_ip_pool_creates_service_group(): void
    {
        PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '************',
            'is_active' => true,
        ]);

        PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '************',
            'is_active' => true,
        ]);

        Http::fake([
            '*' => function ($request) {
                if (str_contains($request->url(), '/servicegroups/test-pool') && $request->method() === 'GET') {
                    return Http::response([], 404);
                }
                if (str_contains($request->url(), '/servicegroups') && $request->method() === 'POST') {
                    return Http::response(['created' => true], 201);
                }

                return Http::response([], 404);
            },
        ]);

        $this->driver->syncIpPool($this->ipPool);

        Http::assertSent(function ($request) {
            if ($request->method() !== 'POST') {
                return false;
            }

            $data = $request->data();

            return str_contains($request->url(), '/servicegroups') &&
                   $data['kind'] === 'ServiceGroup' &&
                   $data['metadata']['name'] === 'test-pool' &&
                   $data['spec']['local']['v4pool']['pool'] === '************-************';
        });
    }

    public function test_sync_ip_pool_updates_existing_service_group(): void
    {
        PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '************',
            'is_active' => true,
        ]);

        Http::fake([
            '*' => function ($request) {
                if (str_contains($request->url(), '/servicegroups/test-pool') && $request->method() === 'GET') {
                    return Http::response(['existing' => true], 200);
                }
                if (str_contains($request->url(), '/servicegroups/test-pool') && $request->method() === 'PUT') {
                    return Http::response(['updated' => true], 200);
                }

                return Http::response([], 404);
            },
        ]);

        $this->driver->syncIpPool($this->ipPool);

        Http::assertSent(function ($request) {
            return $request->method() === 'PUT' &&
                   str_contains($request->url(), '/servicegroups/test-pool');
        });
    }

    public function test_delete_ip_pool(): void
    {
        Http::fake([
            '*' => function ($request) {
                if (str_contains($request->url(), '/servicegroups/test-pool') && $request->method() === 'DELETE') {
                    return Http::response([], 200);
                }

                return Http::response([], 404);
            },
        ]);

        $this->driver->deleteIpPool($this->ipPool);

        Http::assertSent(function ($request) {
            return $request->method() === 'DELETE' &&
                   str_contains($request->url(), '/servicegroups/test-pool');
        });
    }

    public function test_build_ip_pool_addresses_with_consecutive_ips(): void
    {
        PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '************',
            'is_active' => true,
        ]);

        PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '************',
            'is_active' => true,
        ]);

        PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '************',
            'is_active' => true,
        ]);

        // 使用反射访问 protected 方法
        $reflection = new \ReflectionClass($this->driver);
        $method = $reflection->getMethod('buildIpPoolAddresses');
        $method->setAccessible(true);

        $result = $method->invoke($this->driver, $this->ipPool);

        $this->assertEquals('************-************', $result);
    }

    public function test_build_ip_pool_addresses_with_single_ip(): void
    {
        PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '************',
            'is_active' => true,
        ]);

        $reflection = new \ReflectionClass($this->driver);
        $method = $reflection->getMethod('buildIpPoolAddresses');
        $method->setAccessible(true);

        $result = $method->invoke($this->driver, $this->ipPool);

        $this->assertEquals('************', $result);
    }

    public function test_build_ip_pool_subnet(): void
    {
        PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '************',
            'is_active' => true,
        ]);

        $reflection = new \ReflectionClass($this->driver);
        $method = $reflection->getMethod('buildIpPoolSubnet');
        $method->setAccessible(true);

        $result = $method->invoke($this->driver, $this->ipPool);

        $this->assertEquals('***********/24', $result);
    }
}
