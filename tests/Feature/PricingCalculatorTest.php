<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PricingCalculatorTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create(['name' => 'test-cluster']);

        // 使用新的定价系统设置集群定价
        $this->cluster->createPricing([
            'unit_type' => 'memory_gb',
            'price_per_unit_per_hour' => '10.0000',
            'description' => '内存定价 10.00/GB/小时',
        ]);

        $this->cluster->createPricing([
            'unit_type' => 'cpu_core',
            'price_per_unit_per_hour' => '20.0000',
            'description' => 'CPU定价 20.00/核/小时',
        ]);

        $this->cluster->createPricing([
            'unit_type' => 'storage_gb',
            'price_per_unit_per_hour' => '5.0000',
            'description' => '存储定价 5.00/GB/小时',
        ]);

        $this->cluster->createPricing([
            'unit_type' => 'loadbalancer_service',
            'price_per_unit_per_hour' => '50.0000',
            'description' => 'LoadBalancer定价 50.00/个/小时',
        ]);
    }

    /** @test */
    public function user_can_access_pricing_calculator_on_homepage()
    {
        // 价格计算器现在集成在首页中，不需要登录
        $response = $this->get('/');

        $response->assertOk();
        // 首页通过 Vue 渲染，我们检查 Inertia 组件
        $response->assertInertia(fn ($page) => $page
            ->component('Welcome')
        );
    }

    /** @test */
    public function user_can_get_cluster_pricing_info()
    {
        $response = $this->get("/api/clusters/{$this->cluster->id}/pricing");

        $response->assertOk();
        $response->assertJsonStructure([
            'cluster' => ['id', 'name'],
            'pricing',
        ]);
    }

    /** @test */
    public function user_can_calculate_resource_price()
    {
        $response = $this->post("/api/clusters/{$this->cluster->id}/calculate-price", [
            'memory_mi' => 1024,  // 1GB
            'cpu_m' => 1000,      // 1 core
            'storage_gi' => 10,   // 10GB
            'loadbalancer_count' => 1,
        ]);

        $response->assertOk();
        $response->assertJsonStructure([
            'resources',
            'breakdown',
            'total' => [
                'per_minute',
                'per_hour',
                'per_day',
                'per_month',
            ],
            'formatted',
        ]);

        // 验证计算结果
        $data = $response->json();
        $this->assertNotEmpty($data['total']['per_minute']);
        $this->assertGreaterThan(0, (float) $data['total']['per_minute']);
    }

    /** @test */
    public function user_can_calculate_zero_resource_price()
    {
        $response = $this->post("/api/clusters/{$this->cluster->id}/calculate-price", [
            'memory_mi' => 0,
            'cpu_m' => 0,
            'storage_gi' => 0,
            'loadbalancer_count' => 0,
        ]);

        $response->assertOk();
        $data = $response->json();
        $this->assertEquals(config('pricable.default_cost'), $data['total']['per_minute']);
    }

    /** @test */
    public function price_calculation_fails_for_cluster_without_pricing()
    {
        // 创建一个没有定价的集群
        $clusterWithoutPricing = Cluster::factory()->create(['name' => 'no-pricing-cluster']);

        $response = $this->post("/api/clusters/{$clusterWithoutPricing->id}/calculate-price", [
            'memory_mi' => 1024,
            'cpu_m' => 1000,
            'storage_gi' => 10,
            'loadbalancer_count' => 1,
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'message' => '该集群未配置定价策略或计费已禁用',
        ]);
    }

    /** @test */
    public function api_returns_cluster_list_with_billing_enabled_only()
    {
        // 创建一个没有定价的集群，不应该出现在列表中
        Cluster::factory()->create(['name' => 'no-pricing-cluster']);

        $response = $this->get('/api/clusters');

        $response->assertOk();
        $clusters = $response->json();

        // 应该只包含有定价的集群
        $this->assertCount(1, $clusters);
        $this->assertEquals($this->cluster->name, $clusters[0]['name']);
    }

    /** @test */
    public function pricing_calculator_handles_validation_errors()
    {
        $response = $this->post("/api/clusters/{$this->cluster->id}/calculate-price", [
            'memory_mi' => -1,  // 负数应该失败
            'cpu_m' => 'invalid',  // 非数字应该失败
        ]);

        $response->assertStatus(422);
    }

    /** @test */
    public function pricing_calculator_handles_missing_cluster()
    {
        $response = $this->post('/api/clusters/99999/calculate-price', [
            'memory_mi' => 1024,
            'cpu_m' => 1000,
            'storage_gi' => 10,
            'loadbalancer_count' => 1,
        ]);

        $response->assertStatus(404);
    }
}
