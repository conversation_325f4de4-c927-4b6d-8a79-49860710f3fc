<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\Workspace;
use App\Service\K8sResourceCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StatefulSetCacheTest extends TestCase
{
    use RefreshDatabase;

    protected K8sResourceCacheService $cacheService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->cacheService = app(K8sResourceCacheService::class);
    }

    public function test_cache_statefulsets_command_runs_successfully()
    {
        // 创建测试数据
        $cluster = Cluster::factory()->create();
        $workspace = Workspace::factory()->create([
            'cluster_id' => $cluster->id,
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 运行命令
        $this->artisan('k8s:cache-statefulsets')
            ->assertExitCode(0);
    }

    public function test_status_command_shows_cache_info()
    {
        // 运行状态命令
        $this->artisan('k8s:statefulset-cache-status')
            ->assertExitCode(0);
    }

    public function test_clear_cache_command_works()
    {
        // 运行清除命令
        $this->artisan('k8s:statefulset-cache-status', ['--clear' => true])
            ->expectsConfirmation('确定要清除所有 StatefulSet 缓存？', 'yes')
            ->assertExitCode(0);
    }

    public function test_cache_service_can_set_and_get_data()
    {
        $clusterId = 1;
        $namespace = 'test';
        $resourceType = 'statefulsets';

        $testData = [
            'resources' => [
                ['name' => 'test-statefulset', 'replicas' => 3],
            ],
            'cached_at' => time(),
        ];

        // 设置缓存
        $result = $this->cacheService->setCachedResources($clusterId, $namespace, $resourceType, $testData);
        $this->assertTrue($result);

        // 获取缓存
        $cached = $this->cacheService->getCachedResources($clusterId, $namespace, $resourceType);
        $this->assertNotNull($cached);
        $this->assertCount(1, $cached);
        $this->assertEquals('test-statefulset', $cached[0]['name']);
    }
}
