<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\Service;
use App\Models\Workspace;
use App\Service\LoadBalancerManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class PureLbIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private Cluster $cluster;

    private IpPool $ipPool;

    private Workspace $workspace;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试集群
        $this->cluster = Cluster::factory()->create([
            'name' => 'purelb-test-cluster',
        ]);

        // 设置集群使用PureLB驱动
        $this->cluster->setSetting('loadbalancer_driver', 'purelb');

        // 创建PureLB IP池
        $this->ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'name' => 'purelb-test-pool',
            'driver' => 'purelb',
        ]);

        // 创建工作空间
        $this->workspace = Workspace::factory()->create([
            'cluster_id' => $this->cluster->id,
        ]);
    }

    public function test_purelb_driver_is_correctly_loaded(): void
    {
        $manager = new LoadBalancerManager;

        $this->assertTrue($manager->hasDriver('purelb'));
        $this->assertEquals('PureLB', $manager->getDriverDisplayName('purelb'));

        $driver = $manager->driver('purelb');
        $this->assertInstanceOf(\App\Service\LoadBalancerDrivers\PureLbDriver::class, $driver);
    }

    public function test_ip_pool_sync_creates_service_group(): void
    {
        // 添加IP到池中
        PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'is_active' => true,
        ]);

        PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'is_active' => true,
        ]);

        // 模拟K8s API响应
        Http::fake([
            '*' => function ($request) {
                if (str_contains($request->url(), '/servicegroups/purelb-test-pool') && $request->method() === 'GET') {
                    return Http::response([], 404);
                }
                if (str_contains($request->url(), '/servicegroups') && $request->method() === 'POST') {
                    return Http::response(['created' => true], 201);
                }

                return Http::response([], 404);
            },
        ]);

        // 获取LoadBalancer Manager并同步IP池
        $manager = new LoadBalancerManager;
        $driver = $manager->driver('purelb');
        $driver->syncIpPool($this->ipPool);

        // 验证请求是否正确发送
        Http::assertSent(function ($request) {
            if ($request->method() !== 'POST') {
                return false;
            }

            $data = $request->data();

            return str_contains($request->url(), '/servicegroups') &&
                   $data['kind'] === 'ServiceGroup' &&
                   $data['metadata']['name'] === 'purelb-test-pool' &&
                   str_contains($data['spec']['local']['v4pool']['pool'], '*************-*************');
        });
    }

    public function test_service_gets_correct_purelb_annotations(): void
    {
        // 创建PoolIp
        $poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'is_active' => true,
        ]);

        // 创建LoadBalancer Service
        $service = Service::factory()->create([
            'workspace_id' => $this->workspace->id,
            'type' => Service::TYPE_LOAD_BALANCER,
            'allocated_ip' => '*************',
            'pool_ip_id' => $poolIp->id,
            'allow_shared_ip' => false,
        ]);

        // 获取LoadBalancer注释
        $annotations = $service->getLoadBalancerAnnotations();

        // 验证PureLB特定的注释
        $this->assertArrayHasKey('purelb.io/addresses', $annotations);
        $this->assertArrayHasKey('purelb.io/service-group', $annotations);
        $this->assertEquals('*************', $annotations['purelb.io/addresses']);
        $this->assertEquals('purelb-test-pool', $annotations['purelb.io/service-group']);
        $this->assertArrayNotHasKey('purelb.io/allow-shared-ip', $annotations);
    }

    public function test_service_with_shared_ip_gets_sharing_annotation(): void
    {
        // 创建PoolIp
        $poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'is_active' => true,
        ]);

        // 创建允许IP共享的LoadBalancer Service
        $service = Service::factory()->create([
            'workspace_id' => $this->workspace->id,
            'type' => Service::TYPE_LOAD_BALANCER,
            'allocated_ip' => '*************',
            'pool_ip_id' => $poolIp->id,
            'allow_shared_ip' => true,
        ]);

        // 获取LoadBalancer注释
        $annotations = $service->getLoadBalancerAnnotations();

        // 验证包含IP共享注释
        $this->assertArrayHasKey('purelb.io/allow-shared-ip', $annotations);
        $this->assertEquals('share-ip-192-168-1-100', $annotations['purelb.io/allow-shared-ip']);
    }

    public function test_non_loadbalancer_service_gets_no_annotations(): void
    {
        // 创建ClusterIP Service
        $service = Service::factory()->create([
            'workspace_id' => $this->workspace->id,
            'type' => Service::TYPE_CLUSTER_IP,
        ]);

        // 获取LoadBalancer注释
        $annotations = $service->getLoadBalancerAnnotations();

        // 验证没有注释
        $this->assertEmpty($annotations);
    }

    public function test_different_drivers_produce_different_annotations(): void
    {
        // 创建MetalLB IP池
        $metallbPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'name' => 'metallb-test-pool',
            'driver' => 'metallb',
        ]);

        // 创建PoolIp
        $purelbPoolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'is_active' => true,
        ]);

        $metallbPoolIp = PoolIp::factory()->create([
            'ip_pool_id' => $metallbPool->id,
            'ip_address' => '*************',
            'is_active' => true,
        ]);

        // 创建PureLB Service
        $purelbService = Service::factory()->create([
            'workspace_id' => $this->workspace->id,
            'type' => Service::TYPE_LOAD_BALANCER,
            'allocated_ip' => '*************',
            'pool_ip_id' => $purelbPoolIp->id,
            'allow_shared_ip' => false,
        ]);

        // 创建MetalLB Service
        $metallbService = Service::factory()->create([
            'workspace_id' => $this->workspace->id,
            'type' => Service::TYPE_LOAD_BALANCER,
            'allocated_ip' => '*************',
            'pool_ip_id' => $metallbPoolIp->id,
            'allow_shared_ip' => false,
        ]);

        // 获取注释
        $purelbAnnotations = $purelbService->getLoadBalancerAnnotations();
        $metallbAnnotations = $metallbService->getLoadBalancerAnnotations();

        // 验证PureLB注释
        $this->assertArrayHasKey('purelb.io/addresses', $purelbAnnotations);
        $this->assertArrayHasKey('purelb.io/service-group', $purelbAnnotations);

        // 验证MetalLB注释
        $this->assertArrayHasKey('metallb.io/loadBalancerIPs', $metallbAnnotations);
        $this->assertArrayHasKey('metallb.io/ip-address-pool', $metallbAnnotations);

        // 验证它们的注释不同
        $this->assertNotEquals($purelbAnnotations, $metallbAnnotations);
    }

    public function test_cluster_setting_affects_ip_pool_driver_usage(): void
    {
        // 验证集群设置已正确设置
        $this->assertEquals('purelb', $this->cluster->getSetting('loadbalancer_driver'));

        // 创建PoolIp
        $poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'is_active' => true,
        ]);

        // 创建Service
        $service = Service::factory()->create([
            'workspace_id' => $this->workspace->id,
            'type' => Service::TYPE_LOAD_BALANCER,
            'allocated_ip' => '*************',
            'pool_ip_id' => $poolIp->id,
        ]);

        // 验证Service使用了正确的驱动
        $annotations = $service->getLoadBalancerAnnotations();
        $this->assertArrayHasKey('purelb.io/addresses', $annotations);
        $this->assertArrayNotHasKey('metallb.io/loadBalancerIPs', $annotations);
    }
}
