<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WorkspacePricingDisplayTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create([
            'name' => 'test-cluster',
        ]);
    }

    /** @test */
    public function it_displays_cluster_pricing_in_workspace_create_page()
    {
        // 创建集群定价配置
        $this->cluster->createPricing([
            'unit_type' => 'memory_gb',
            'price_per_unit_per_hour' => '10.0000',
            'description' => '内存定价测试',
        ]);

        $this->cluster->createPricing([
            'unit_type' => 'cpu_core',
            'price_per_unit_per_hour' => '20.0000',
            'description' => 'CPU定价测试',
        ]);

        // 访问创建工作空间页面
        $response = $this->actingAs($this->user)
            ->get(route('workspaces.create'));

        $response->assertOk();

        // 验证页面数据包含集群信息
        $response->assertInertia(function ($page) {
            $page->component('Workspaces/Create')
                ->has('clusters', function ($clusters) {
                    $clusters->where('id', $this->cluster->id);
                });
        });
    }

    /** @test */
    public function workspace_create_page_only_shows_clusters_with_pricing()
    {
        // 创建有定价的集群
        $this->cluster->createPricing([
            'unit_type' => 'memory_gb',
            'price_per_unit_per_hour' => '10.0000',
            'description' => '内存定价测试',
        ]);

        // 创建没有定价的集群
        $clusterWithoutPricing = Cluster::factory()->create([
            'name' => 'no-pricing-cluster',
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('workspaces.create'));

        $response->assertOk();

        // 验证只显示有定价的集群
        $response->assertInertia(function ($page) use ($clusterWithoutPricing) {
            $clusters = $page->toArray()['props']['clusters'];

            $this->assertCount(1, $clusters);
            $this->assertEquals($this->cluster->id, $clusters[0]['id']);
            $this->assertNotEquals($clusterWithoutPricing->id, $clusters[0]['id']);
        });
    }

    /** @test */
    public function workspace_create_shows_no_clusters_when_none_have_pricing()
    {
        // 不创建任何定价配置

        $response = $this->actingAs($this->user)
            ->get(route('workspaces.create'));

        $response->assertOk();

        // 验证没有集群显示
        $response->assertInertia(function ($page) {
            $clusters = $page->toArray()['props']['clusters'];
            $this->assertEmpty($clusters);
        });
    }
}
