<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EventControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Workspace $workspace;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $cluster = Cluster::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $cluster->id,
        ]);

        // 直接更新状态，绕过模型事件
        $this->workspace->update(['status' => 'active']);

        $this->user->setCurrentWorkspace($this->workspace);

        // 确保workspace状态和用户设置正确
        $this->workspace->refresh();
        $this->user->refresh();
    }

    public function test_can_get_events_via_api(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/events');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'type',
                'reason',
                'message',
                'timestamp',
                'first_timestamp',
                'last_timestamp',
                'count',
                'involved_object',
                'is_recent',
                'age',
            ],
        ]);
    }

    public function test_can_get_events_with_time_filter(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/events?since_minutes=60');

        $response->assertStatus(200);
    }

    public function test_validates_time_filter_parameters(): void
    {
        // 测试无效的时间参数
        $response = $this->actingAs($this->user)
            ->getJson('/api/events?since_minutes=invalid');

        $response->assertStatus(422);

        // 测试超出范围的时间参数
        $response = $this->actingAs($this->user)
            ->getJson('/api/events?since_minutes=2000');

        $response->assertStatus(422);

        // 测试负数时间参数
        $response = $this->actingAs($this->user)
            ->getJson('/api/events?since_minutes=-1');

        $response->assertStatus(422);
    }

    public function test_can_get_resource_events(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/resource-events?kind=Pod&name=test-pod');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'type',
                'reason',
                'message',
                'timestamp',
                'first_timestamp',
                'last_timestamp',
                'count',
                'involved_object',
                'is_recent',
                'age',
            ],
        ]);
    }

    public function test_can_get_resource_events_with_time_filter(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/resource-events?kind=Pod&name=test-pod&since_minutes=30');

        if ($response->status() !== 200) {
            dump('Response status: '.$response->status());
            dump('Response content: '.$response->getContent());
        }

        $response->assertStatus(200);
    }

    public function test_validates_resource_events_parameters(): void
    {
        // 测试缺少必需参数
        $response = $this->actingAs($this->user)
            ->getJson('/api/resource-events?kind=Pod');

        $response->assertStatus(422);

        $response = $this->actingAs($this->user)
            ->getJson('/api/resource-events?name=test-pod');

        $response->assertStatus(422);
    }

    public function test_requires_authentication(): void
    {
        $response = $this->getJson('/api/events');
        $response->assertStatus(401);

        $response = $this->getJson('/api/resource-events?kind=Pod&name=test-pod');
        $response->assertStatus(401);
    }

    public function test_requires_workspace(): void
    {
        $userWithoutWorkspace = User::factory()->create();

        $response = $this->actingAs($userWithoutWorkspace)
            ->getJson('/api/events');

        if ($response->status() !== 403) {
            dump('Events response status: '.$response->status());
            dump('Events response content: '.$response->getContent());
        }

        $response->assertStatus(403);

        $response = $this->actingAs($userWithoutWorkspace)
            ->getJson('/api/resource-events?kind=Pod&name=test-pod');

        if ($response->status() !== 403) {
            dump('Resource events response status: '.$response->status());
            dump('Resource events response content: '.$response->getContent());
        }

        $response->assertStatus(403);
    }
}
