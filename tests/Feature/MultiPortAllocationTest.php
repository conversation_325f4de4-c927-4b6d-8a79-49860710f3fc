<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\PortAllocation;
use App\Service\IpPoolService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MultiPortAllocationTest extends TestCase
{
    use RefreshDatabase;

    protected Cluster $cluster;

    protected IpPool $ipPool;

    protected PoolIp $poolIp;

    protected IpPoolService $ipPoolService;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试数据
        $this->cluster = Cluster::factory()->create();

        // 创建 IP 池和 IP
        $this->ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'name' => 'test-ip-pool-'.time(),
            'driver' => 'metallb',
            'is_active' => true,
        ]);

        $this->poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 30000,
            'port_range_end' => 30010, // 小范围便于测试
            'usage_count' => 0,
            'is_active' => true,
        ]);

        $this->ipPoolService = new IpPoolService;
    }

    public function test_single_port_allocation_works(): void
    {
        $port = $this->ipPoolService->allocatePort($this->poolIp, 'test-service', 'test-namespace');

        $this->assertNotNull($port);
        $this->assertGreaterThanOrEqual(30000, $port);
        $this->assertLessThanOrEqual(30010, $port);

        // 验证数据库记录
        $allocation = PortAllocation::where('service_name', 'test-service')
            ->where('namespace', 'test-namespace')
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->first();

        $this->assertNotNull($allocation);
        $this->assertEquals($port, $allocation->port);
        $this->assertEquals($this->poolIp->id, $allocation->pool_ip_id);

        // 验证使用计数
        $this->poolIp->refresh();
        $this->assertEquals(1, $this->poolIp->usage_count);
    }

    public function test_multi_port_allocation_works(): void
    {
        $allocatedPorts = $this->ipPoolService->allocatePorts(
            $this->poolIp,
            'multi-port-service',
            'test-namespace',
            3
        );

        $this->assertCount(3, $allocatedPorts);

        // 验证端口都在有效范围内
        foreach ($allocatedPorts as $port) {
            $this->assertGreaterThanOrEqual(30000, $port);
            $this->assertLessThanOrEqual(30010, $port);
        }

        // 验证端口都不相同
        $this->assertEquals(3, count(array_unique($allocatedPorts)));

        // 验证数据库记录
        $allocations = PortAllocation::where('service_name', 'multi-port-service')
            ->where('namespace', 'test-namespace')
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->get();

        $this->assertCount(3, $allocations);

        // 验证使用计数
        $this->poolIp->refresh();
        $this->assertEquals(3, $this->poolIp->usage_count);
    }

    public function test_consecutive_port_allocation_when_possible(): void
    {
        // 先占用几个端口，让连续端口分配变得有意义
        $this->ipPoolService->allocatePort($this->poolIp, 'existing-service-1', 'test-namespace');
        $this->ipPoolService->allocatePort($this->poolIp, 'existing-service-2', 'test-namespace');

        // 现在尝试分配3个连续端口
        $allocatedPorts = $this->ipPoolService->allocatePorts(
            $this->poolIp,
            'consecutive-service',
            'test-namespace',
            3
        );

        $this->assertCount(3, $allocatedPorts);

        // 验证使用计数（2个现有 + 3个新的）
        $this->poolIp->refresh();
        $this->assertEquals(5, $this->poolIp->usage_count);
    }

    public function test_port_release_works(): void
    {
        // 分配3个端口
        $allocatedPorts = $this->ipPoolService->allocatePorts(
            $this->poolIp,
            'release-test-service',
            'test-namespace',
            3
        );

        $this->assertCount(3, $allocatedPorts);
        $this->poolIp->refresh();
        $this->assertEquals(3, $this->poolIp->usage_count);

        // 释放端口
        $this->ipPoolService->releasePort('release-test-service', 'test-namespace');

        // 验证端口已释放
        $allocatedCount = PortAllocation::where('service_name', 'release-test-service')
            ->where('namespace', 'test-namespace')
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->count();
        $this->assertEquals(0, $allocatedCount);

        // 验证端口已标记为释放
        $releasedCount = PortAllocation::where('service_name', 'release-test-service')
            ->where('namespace', 'test-namespace')
            ->where('status', PortAllocation::STATUS_RELEASED)
            ->count();
        $this->assertEquals(3, $releasedCount);

        // 验证使用计数
        $this->poolIp->refresh();
        $this->assertEquals(0, $this->poolIp->usage_count);
    }

    public function test_port_conflicts_are_prevented(): void
    {
        // 分配第一个服务的端口
        $port1 = $this->ipPoolService->allocatePort($this->poolIp, 'service-1', 'test-namespace');

        // 分配第二个服务的端口
        $port2 = $this->ipPoolService->allocatePort($this->poolIp, 'service-2', 'test-namespace');

        // 验证端口不同
        $this->assertNotEquals($port1, $port2);

        // 验证使用计数
        $this->poolIp->refresh();
        $this->assertEquals(2, $this->poolIp->usage_count);
    }

    public function test_existing_allocation_is_returned(): void
    {
        // 第一次分配
        $port1 = $this->ipPoolService->allocatePort($this->poolIp, 'existing-service', 'test-namespace');

        // 再次为同一服务分配端口，应该返回现有的端口
        $port2 = $this->ipPoolService->allocatePort($this->poolIp, 'existing-service', 'test-namespace');

        $this->assertEquals($port1, $port2);

        // 验证只有一个分配记录
        $allocationCount = PortAllocation::where('service_name', 'existing-service')
            ->where('namespace', 'test-namespace')
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->count();
        $this->assertEquals(1, $allocationCount);

        // 验证使用计数只增加了一次
        $this->poolIp->refresh();
        $this->assertEquals(1, $this->poolIp->usage_count);
    }

    public function test_port_reuse_after_release(): void
    {
        // 分配端口
        $originalPort = $this->ipPoolService->allocatePort($this->poolIp, 'temp-service', 'test-namespace');

        // 释放端口
        $this->ipPoolService->releasePort('temp-service', 'test-namespace');

        // 为新服务分配端口，应该重用释放的端口
        $reusedPort = $this->ipPoolService->allocatePort($this->poolIp, 'new-service', 'test-namespace');

        $this->assertEquals($originalPort, $reusedPort);

        // 验证使用计数
        $this->poolIp->refresh();
        $this->assertEquals(1, $this->poolIp->usage_count);
    }

    public function test_port_exhaustion_throws_exception(): void
    {
        // 分配所有可用端口 (30000-30010 = 11个端口)
        for ($i = 0; $i < 11; $i++) {
            $this->ipPoolService->allocatePort($this->poolIp, "service-{$i}", 'test-namespace');
        }

        // 尝试分配另一个端口应该抛出异常
        $this->expectException(\App\Exceptions\Network\PortAllocationException::class);
        $this->ipPoolService->allocatePort($this->poolIp, 'overflow-service', 'test-namespace');
    }
}
