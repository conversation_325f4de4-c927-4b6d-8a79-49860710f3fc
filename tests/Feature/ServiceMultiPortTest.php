<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\PortAllocation;
use App\Models\Service;
use App\Models\User;
use App\Models\Workspace;
use App\Service\IpPoolService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ServiceMultiPortTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected Cluster $cluster;

    protected IpPool $ipPool;

    protected PoolIp $poolIp;

    protected IpPoolService $ipPoolService;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试数据
        $this->user = User::factory()->create();

        $this->cluster = Cluster::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
        ]);

        // 创建 IP 池和 IP
        $this->ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'is_active' => true,
        ]);

        $this->poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 30000,
            'port_range_end' => 32000,
            'usage_count' => 0,
            'is_active' => true,
        ]);

        $this->ipPoolService = new IpPoolService;
    }

    public function test_can_allocate_multiple_ports_for_service(): void
    {
        // 创建Service记录
        $service = Service::create([
            'workspace_id' => $this->workspace->id,
            'name' => 'multi-port-service',
            'type' => 'LoadBalancer',
            'ports' => [
                ['name' => 'http', 'port' => 80, 'target_port' => 8080, 'protocol' => 'TCP'],
                ['name' => 'https', 'port' => 443, 'target_port' => 8443, 'protocol' => 'TCP'],
                ['name' => 'metrics', 'port' => 9090, 'target_port' => 9090, 'protocol' => 'TCP'],
            ],
            'selector' => ['app' => 'test'],
            'pool_ip_id' => $this->poolIp->id,
            'allocated_ip' => $this->poolIp->ip_address,
            'allow_shared_ip' => true,
        ]);

        // 分配3个端口
        $allocatedPorts = $this->ipPoolService->allocatePorts(
            $this->poolIp,
            'multi-port-service',
            $this->workspace->namespace,
            3
        );

        // 验证分配成功
        $this->assertCount(3, $allocatedPorts);

        // 验证端口在有效范围内
        foreach ($allocatedPorts as $port) {
            $this->assertGreaterThanOrEqual(30000, $port);
            $this->assertLessThanOrEqual(32000, $port);
        }

        // 验证数据库记录
        $allocations = PortAllocation::where('service_name', 'multi-port-service')
            ->where('namespace', $this->workspace->namespace)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->get();

        $this->assertCount(3, $allocations);

        // 验证IP使用计数
        $this->poolIp->refresh();
        $this->assertEquals(3, $this->poolIp->usage_count);

        // 验证Service模型的getAllocatedPorts方法
        $servicePorts = $service->getAllocatedPorts($this->workspace->namespace);
        $this->assertCount(3, $servicePorts);
        $this->assertEquals($allocatedPorts, $servicePorts);
    }

    public function test_can_update_port_count(): void
    {
        $service = Service::create([
            'workspace_id' => $this->workspace->id,
            'name' => 'expandable-service',
            'type' => 'LoadBalancer',
            'ports' => [
                ['name' => 'http', 'port' => 80, 'target_port' => 8080, 'protocol' => 'TCP'],
                ['name' => 'https', 'port' => 443, 'target_port' => 8443, 'protocol' => 'TCP'],
            ],
            'selector' => ['app' => 'test'],
            'pool_ip_id' => $this->poolIp->id,
            'allocated_ip' => $this->poolIp->ip_address,
            'allow_shared_ip' => true,
        ]);

        // 首先分配2个端口
        $allocatedPorts = $this->ipPoolService->allocatePorts(
            $this->poolIp,
            'expandable-service',
            $this->workspace->namespace,
            2
        );

        $this->assertCount(2, $allocatedPorts);
        $this->assertEquals(2, $this->poolIp->fresh()->usage_count);

        // 现在模拟服务更新：先释放旧端口，再分配新端口
        $this->ipPoolService->releasePort('expandable-service', $this->workspace->namespace);

        // 验证端口已释放
        $this->assertEquals(0, $this->poolIp->fresh()->usage_count);

        // 重新分配3个端口
        $newAllocatedPorts = $this->ipPoolService->allocatePorts(
            $this->poolIp,
            'expandable-service',
            $this->workspace->namespace,
            3
        );

        $this->assertCount(3, $newAllocatedPorts);
        $this->assertEquals(3, $this->poolIp->fresh()->usage_count);

        // 验证最终只有3个活跃分配
        $activeAllocations = PortAllocation::where('service_name', 'expandable-service')
            ->where('namespace', $this->workspace->namespace)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->count();

        $this->assertEquals(3, $activeAllocations);
    }

    public function test_can_release_all_ports_on_service_deletion(): void
    {
        $service = Service::create([
            'workspace_id' => $this->workspace->id,
            'name' => 'deletable-service',
            'type' => 'LoadBalancer',
            'ports' => [
                ['name' => 'http', 'port' => 80, 'target_port' => 8080, 'protocol' => 'TCP'],
                ['name' => 'https', 'port' => 443, 'target_port' => 8443, 'protocol' => 'TCP'],
                ['name' => 'metrics', 'port' => 9090, 'target_port' => 9090, 'protocol' => 'TCP'],
            ],
            'selector' => ['app' => 'test'],
            'pool_ip_id' => $this->poolIp->id,
            'allocated_ip' => $this->poolIp->ip_address,
            'allow_shared_ip' => true,
        ]);

        // 分配3个端口
        $this->ipPoolService->allocatePorts(
            $this->poolIp,
            'deletable-service',
            $this->workspace->namespace,
            3
        );

        $this->assertEquals(3, $this->poolIp->fresh()->usage_count);

        // 释放端口（模拟服务删除）
        $this->ipPoolService->releasePort('deletable-service', $this->workspace->namespace);

        // 验证端口已释放
        $allocatedCount = PortAllocation::where('service_name', 'deletable-service')
            ->where('namespace', $this->workspace->namespace)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->count();

        $this->assertEquals(0, $allocatedCount);

        // 验证端口已标记为释放
        $releasedCount = PortAllocation::where('service_name', 'deletable-service')
            ->where('namespace', $this->workspace->namespace)
            ->where('status', PortAllocation::STATUS_RELEASED)
            ->count();

        $this->assertEquals(3, $releasedCount);

        // 验证IP使用计数重置
        $this->assertEquals(0, $this->poolIp->fresh()->usage_count);
    }
}
