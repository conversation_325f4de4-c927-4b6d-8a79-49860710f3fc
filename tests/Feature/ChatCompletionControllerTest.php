<?php

namespace Tests\Feature;

use App\Models\User;
use Guz<PERSON>Http\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class ChatCompletionControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
    }

    public function test_requires_authentication()
    {
        $response = $this->postJson('/api/chat/completions', [
            'model' => 'gpt-4',
            'messages' => [['role' => 'user', 'content' => 'Hello']],
        ]);

        $response->assertStatus(401);
    }

    public function test_validates_required_fields()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/chat/completions', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['model', 'messages']);
    }

    public function test_validates_field_types()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/chat/completions', [
                'model' => 123, // should be string
                'messages' => 'invalid', // should be array
                'max_tokens' => 'invalid', // should be integer
                'temperature' => 'invalid', // should be numeric
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['model', 'messages', 'max_tokens', 'temperature']);
    }

    public function test_successful_normal_response()
    {
        // Mock the HTTP client response
        $mockResponse = new Response(200, [], json_encode([
            'id' => 'chatcmpl-123',
            'object' => 'chat.completion',
            'created' => time(),
            'model' => 'gpt-4',
            'choices' => [
                [
                    'index' => 0,
                    'message' => [
                        'role' => 'assistant',
                        'content' => 'Hello! How can I help you today?',
                    ],
                    'finish_reason' => 'stop',
                ],
            ],
            'usage' => [
                'prompt_tokens' => 10,
                'completion_tokens' => 15,
                'total_tokens' => 25,
            ],
        ]));

        // Mock the Guzzle client
        $mock = new MockHandler([$mockResponse]);
        $handlerStack = HandlerStack::create($mock);
        $client = new Client(['handler' => $handlerStack]);

        // Bind the mock client to the container
        $this->app->bind(Client::class, function () use ($client) {
            return $client;
        });

        Log::shouldReceive('info')
            ->once()
            ->with('OpenAI API Token 消耗记录', \Mockery::type('array'));

        Log::shouldReceive('error')
            ->withAnyArgs()
            ->zeroOrMoreTimes();

        $response = $this->actingAs($this->user)
            ->postJson('/api/chat/completions', [
                'model' => 'gpt-4',
                'messages' => [
                    ['role' => 'user', 'content' => 'Hello'],
                ],
                'max_tokens' => 100,
                'temperature' => 0.7,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'object',
                'created',
                'model',
                'choices' => [
                    '*' => [
                        'index',
                        'message' => [
                            'role',
                            'content',
                        ],
                        'finish_reason',
                    ],
                ],
                'usage' => [
                    'prompt_tokens',
                    'completion_tokens',
                    'total_tokens',
                ],
            ]);
    }

    public function test_handles_openai_api_error()
    {
        // Mock an error response
        $mockResponse = new Response(400, [], json_encode([
            'error' => [
                'message' => 'Invalid request',
                'type' => 'invalid_request_error',
                'code' => 'invalid_request',
            ],
        ]));

        $mock = new MockHandler([$mockResponse]);
        $handlerStack = HandlerStack::create($mock);
        $client = new Client(['handler' => $handlerStack]);

        $this->app->bind(Client::class, function () use ($client) {
            return $client;
        });

        Log::shouldReceive('error')
            ->once()
            ->with('OpenAI API 请求失败', \Mockery::type('array'));

        $response = $this->actingAs($this->user)
            ->postJson('/api/chat/completions', [
                'model' => 'gpt-4',
                'messages' => [
                    ['role' => 'user', 'content' => 'Hello'],
                ],
            ]);

        $response->assertStatus(400)
            ->assertJsonStructure([
                'error' => [
                    'message',
                    'type',
                    'code',
                ],
            ]);
    }

    public function test_stream_response_headers()
    {
        // Mock a stream response
        $streamData = 'data: '.json_encode([
            'id' => 'chatcmpl-123',
            'object' => 'chat.completion.chunk',
            'created' => time(),
            'model' => 'gpt-4',
            'choices' => [
                [
                    'index' => 0,
                    'delta' => ['content' => 'Hello'],
                    'finish_reason' => null,
                ],
            ],
        ])."\n\ndata: [DONE]\n\n";

        $mockResponse = new Response(200, [
            'Content-Type' => 'text/event-stream',
        ], $streamData);

        $mock = new MockHandler([$mockResponse]);
        $handlerStack = HandlerStack::create($mock);
        $client = new Client(['handler' => $handlerStack]);

        $this->app->bind(Client::class, function () use ($client) {
            return $client;
        });

        $response = $this->actingAs($this->user)
            ->postJson('/api/chat/completions', [
                'model' => 'gpt-4',
                'messages' => [
                    ['role' => 'user', 'content' => 'Hello'],
                ],
                'stream' => true,
            ]);

        $response->assertStatus(200);

        // 检查响应头
        $this->assertStringContainsString('text/event-stream', $response->headers->get('Content-Type'));
        $this->assertStringContainsString('no-cache', $response->headers->get('Cache-Control'));
        $this->assertEquals('keep-alive', $response->headers->get('Connection'));
        $this->assertEquals('no', $response->headers->get('X-Accel-Buffering'));
    }

    public function test_logs_token_usage()
    {
        $mockResponse = new Response(200, [], json_encode([
            'id' => 'chatcmpl-123',
            'object' => 'chat.completion',
            'created' => time(),
            'model' => 'gpt-4',
            'choices' => [
                [
                    'index' => 0,
                    'message' => [
                        'role' => 'assistant',
                        'content' => 'Hello! How can I help you today?',
                    ],
                    'finish_reason' => 'stop',
                ],
            ],
            'usage' => [
                'prompt_tokens' => 10,
                'completion_tokens' => 15,
                'total_tokens' => 25,
            ],
        ]));

        $mock = new MockHandler([$mockResponse]);
        $handlerStack = HandlerStack::create($mock);
        $client = new Client(['handler' => $handlerStack]);

        $this->app->bind(Client::class, function () use ($client) {
            return $client;
        });

        Log::shouldReceive('info')
            ->once()
            ->with('OpenAI API Token 消耗记录', \Mockery::on(function ($data) {
                return $data['total_tokens'] === 25
                    && $data['prompt_tokens'] === 10
                    && $data['completion_tokens'] === 15
                    && $data['is_stream'] === false
                    && isset($data['user_id'])
                    && isset($data['timestamp']);
            }));

        Log::shouldReceive('error')
            ->withAnyArgs()
            ->zeroOrMoreTimes();

        $this->actingAs($this->user)
            ->postJson('/api/chat/completions', [
                'model' => 'gpt-4',
                'messages' => [
                    ['role' => 'user', 'content' => 'Hello'],
                ],
            ]);
    }
}
