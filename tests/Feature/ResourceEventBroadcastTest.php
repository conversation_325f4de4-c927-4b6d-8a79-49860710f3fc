<?php

namespace Tests\Feature;

use App\Events\ResourceChanged;
use App\Models\Cluster;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class ResourceEventBroadcastTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试集群
        $this->cluster = Cluster::factory()->create([
            'name' => 'test-cluster',
            'server_url' => 'https://test-k8s.example.com',
        ]);
    }

    /** @test */
    public function it_can_create_resource_changed_event()
    {
        $changes = [
            'created' => [
                ['name' => 'deployment-1', 'namespace' => 'app-production', 'uid' => 'uid-1'],
                ['name' => 'deployment-2', 'namespace' => 'app-production', 'uid' => 'uid-2'],
            ],
            'updated' => [
                ['name' => 'deployment-3', 'namespace' => 'app-production', 'uid' => 'uid-3', 'old_version' => '123', 'new_version' => '124'],
            ],
            'deleted' => [
                ['name' => 'deployment-4', 'namespace' => 'app-production', 'uid' => 'uid-4'],
            ],
        ];

        $event = new ResourceChanged(
            'app-production',
            'test-cluster',
            $this->cluster->id,
            'deployments',
            $changes
        );

        $this->assertEquals('app-production', $event->namespace);
        $this->assertEquals('test-cluster', $event->clusterName);
        $this->assertEquals($this->cluster->id, $event->clusterId);
        $this->assertEquals('deployments', $event->resourceType);
        $this->assertEquals($changes, $event->changes);

        // 检查汇总信息
        $this->assertEquals(2, $event->summary['created_count']);
        $this->assertEquals(1, $event->summary['updated_count']);
        $this->assertEquals(1, $event->summary['deleted_count']);
        $this->assertEquals(4, $event->summary['total_changes']);
    }

    /** @test */
    public function it_can_handle_single_resource_change()
    {
        $changes = [
            'created' => [
                ['name' => 'deployment-1', 'namespace' => 'app-production', 'uid' => 'uid-1'],
            ],
            'updated' => [],
            'deleted' => [],
        ];

        $event = new ResourceChanged(
            'app-production',
            'test-cluster',
            $this->cluster->id,
            'deployments',
            $changes
        );

        $this->assertEquals('app-production', $event->namespace);
        $this->assertEquals('test-cluster', $event->clusterName);
        $this->assertEquals($this->cluster->id, $event->clusterId);
        $this->assertEquals('deployments', $event->resourceType);

        // 检查汇总信息 - 即使只有一个变化也使用相同的格式
        $this->assertEquals(1, $event->summary['created_count']);
        $this->assertEquals(0, $event->summary['updated_count']);
        $this->assertEquals(0, $event->summary['deleted_count']);
        $this->assertEquals(1, $event->summary['total_changes']);
    }

    /** @test */
    public function it_broadcasts_on_correct_channel()
    {
        $changes = [
            'created' => [['name' => 'test-deployment', 'namespace' => 'app-production', 'uid' => 'uid-1']],
            'updated' => [],
            'deleted' => [],
        ];

        $event = new ResourceChanged(
            'app-production',
            'test-cluster',
            $this->cluster->id,
            'deployments',
            $changes
        );

        $channels = $event->broadcastOn();

        $this->assertCount(1, $channels);
        $this->assertEquals('private-workspace.app-production.resources', $channels[0]->name);
    }

    /** @test */
    public function it_broadcasts_with_correct_data()
    {
        $changes = [
            'created' => [],
            'updated' => [
                [
                    'name' => 'test-deployment',
                    'namespace' => 'app-production',
                    'uid' => 'test-uid-123',
                    'old_version' => '123',
                    'new_version' => '124',
                ],
            ],
            'deleted' => [],
        ];

        $event = new ResourceChanged(
            'app-production',
            'test-cluster',
            $this->cluster->id,
            'deployments',
            $changes
        );

        $broadcastData = $event->broadcastWith();

        $this->assertEquals('app-production', $broadcastData['namespace']);
        $this->assertEquals($this->cluster->id, $broadcastData['cluster']['id']);
        $this->assertEquals('test-cluster', $broadcastData['cluster']['name']);
        $this->assertEquals('deployments', $broadcastData['resource_type']);
        $this->assertEquals($changes, $broadcastData['changes']);
        $this->assertEquals(1, $broadcastData['summary']['updated_count']);
        $this->assertEquals(1, $broadcastData['summary']['total_changes']);
        $this->assertArrayHasKey('timestamp', $broadcastData);
    }

    /** @test */
    public function it_uses_correct_broadcast_event_name()
    {
        $changes = [
            'created' => [['name' => 'test-deployment', 'namespace' => 'app-production', 'uid' => 'uid-1']],
            'updated' => [],
            'deleted' => [],
        ];

        $event = new ResourceChanged(
            'app-production',
            'test-cluster',
            $this->cluster->id,
            'deployments',
            $changes
        );

        $this->assertEquals('resource.changed', $event->broadcastAs());
    }

    /** @test */
    public function it_handles_empty_changes()
    {
        $changes = [
            'created' => [],
            'updated' => [],
            'deleted' => [],
        ];

        $event = new ResourceChanged(
            'app-production',
            'test-cluster',
            $this->cluster->id,
            'deployments',
            $changes
        );

        $this->assertEquals(0, $event->summary['created_count']);
        $this->assertEquals(0, $event->summary['updated_count']);
        $this->assertEquals(0, $event->summary['deleted_count']);
        $this->assertEquals(0, $event->summary['total_changes']);
    }

    /** @test */
    public function it_can_fake_event_dispatching()
    {
        Event::fake();

        // 触发单个资源变化事件
        $singleChange = [
            'created' => [['name' => 'test-deployment', 'namespace' => 'app-production', 'uid' => 'uid-1']],
            'updated' => [],
            'deleted' => [],
        ];

        event(new ResourceChanged(
            'app-production',
            'test-cluster',
            $this->cluster->id,
            'deployments',
            $singleChange
        ));

        // 触发多个资源变化事件
        $multipleChanges = [
            'created' => [['name' => 'deployment-1', 'namespace' => 'app-production', 'uid' => 'uid-2']],
            'updated' => [['name' => 'deployment-2', 'namespace' => 'app-production', 'uid' => 'uid-3']],
            'deleted' => [],
        ];

        event(new ResourceChanged(
            'app-production',
            'test-cluster',
            $this->cluster->id,
            'deployments',
            $multipleChanges
        ));

        // 验证事件被触发
        Event::assertDispatched(ResourceChanged::class, function ($event) {
            return $event->namespace === 'app-production' &&
                   $event->resourceType === 'deployments' &&
                   $event->summary['created_count'] === 1 &&
                   $event->summary['total_changes'] === 1;
        });

        Event::assertDispatched(ResourceChanged::class, function ($event) {
            return $event->namespace === 'app-production' &&
                   $event->resourceType === 'deployments' &&
                   $event->summary['created_count'] === 1 &&
                   $event->summary['updated_count'] === 1 &&
                   $event->summary['total_changes'] === 2;
        });
    }

    /** @test */
    public function it_broadcasts_different_resource_types()
    {
        $resourceTypes = ['deployments', 'services', 'pods', 'ingresses'];

        foreach ($resourceTypes as $resourceType) {
            $changes = [
                'created' => [['name' => "test-{$resourceType}", 'namespace' => 'app-production', 'uid' => 'uid-1']],
                'updated' => [],
                'deleted' => [],
            ];

            $event = new ResourceChanged(
                'app-production',
                'test-cluster',
                $this->cluster->id,
                $resourceType,
                $changes
            );

            $this->assertEquals($resourceType, $event->resourceType);

            $broadcastData = $event->broadcastWith();
            $this->assertEquals($resourceType, $broadcastData['resource_type']);
        }
    }

    /** @test */
    public function it_broadcasts_different_change_types()
    {
        $changeTypes = ['created', 'updated', 'deleted'];

        foreach ($changeTypes as $changeType) {
            $changes = [
                'created' => $changeType === 'created' ? [['name' => 'test-deployment', 'namespace' => 'app-production', 'uid' => 'uid-1']] : [],
                'updated' => $changeType === 'updated' ? [['name' => 'test-deployment', 'namespace' => 'app-production', 'uid' => 'uid-1']] : [],
                'deleted' => $changeType === 'deleted' ? [['name' => 'test-deployment', 'namespace' => 'app-production', 'uid' => 'uid-1']] : [],
            ];

            $event = new ResourceChanged(
                'app-production',
                'test-cluster',
                $this->cluster->id,
                'deployments',
                $changes
            );

            $this->assertEquals($changes, $event->changes);

            $broadcastData = $event->broadcastWith();
            $this->assertEquals($changes, $broadcastData['changes']);

            // 验证对应的变化类型计数正确
            $this->assertEquals($changeType === 'created' ? 1 : 0, $broadcastData['summary']['created_count']);
            $this->assertEquals($changeType === 'updated' ? 1 : 0, $broadcastData['summary']['updated_count']);
            $this->assertEquals($changeType === 'deleted' ? 1 : 0, $broadcastData['summary']['deleted_count']);
        }
    }
}
