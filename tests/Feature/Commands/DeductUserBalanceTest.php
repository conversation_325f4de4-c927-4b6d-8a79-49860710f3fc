<?php

namespace Tests\Feature\Commands;

use App\Models\User;
use App\Service\BalanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DeductUserBalanceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected BalanceService $balanceService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => '测试用户',
            'current_balance' => 0,
        ]);

        $this->balanceService = app(BalanceService::class);
    }

    public function test_can_deduct_balance_with_sufficient_funds()
    {
        // 先给用户充值
        $this->balanceService->addBalance(
            $this->user,
            1000.0,
            'manual',
            null,
            ['remark' => '测试充值']
        );

        $this->user->refresh();
        $initialBalance = $this->user->current_balance;

        // 执行扣除命令
        $this->artisan('user:deduct-balance', [
            'user' => $this->user->id,
            'amount' => 500.0,
            '--reason' => '测试扣除',
            '--force' => true,
        ])
            ->expectsOutput('✅ 扣除成功！')
            ->assertExitCode(0);

        // 验证余额变化
        $this->user->refresh();
        $this->assertEquals(500.0, $this->user->current_balance);
        $this->assertEquals($initialBalance - 500.0, $this->user->current_balance);
    }

    public function test_can_deduct_balance_by_email()
    {
        // 先给用户充值
        $this->balanceService->addBalance(
            $this->user,
            800.0,
            'manual',
            null,
            ['remark' => '测试充值']
        );

        // 使用邮箱执行扣除命令
        $this->artisan('user:deduct-balance', [
            'user' => $this->user->email,
            'amount' => 300.0,
            '--reason' => '邮箱扣除测试',
            '--force' => true,
        ])
            ->assertExitCode(0);

        // 验证余额变化
        $this->user->refresh();
        $this->assertEquals(500.0, $this->user->current_balance);
    }

    public function test_fails_with_insufficient_balance()
    {
        // 给用户少量余额
        $this->balanceService->addBalance(
            $this->user,
            100.0,
            'manual',
            null,
            ['remark' => '测试充值']
        );

        // 尝试扣除超过余额的金额
        $this->artisan('user:deduct-balance', [
            'user' => $this->user->id,
            'amount' => 500.0,
            '--reason' => '超额扣除测试',
            '--force' => true,
        ])
            ->expectsOutput('用户余额不足')
            ->assertExitCode(1);

        // 验证余额没有变化
        $this->user->refresh();
        $this->assertEquals(100.0, $this->user->current_balance);
    }

    public function test_fails_with_zero_or_negative_amount()
    {
        $this->artisan('user:deduct-balance', [
            'user' => $this->user->id,
            'amount' => 0,
            '--force' => true,
        ])
            ->expectsOutput('扣除金额必须大于0')
            ->assertExitCode(1);

        $this->artisan('user:deduct-balance', [
            'user' => $this->user->id,
            'amount' => -100,
            '--force' => true,
        ])
            ->expectsOutput('扣除金额必须大于0')
            ->assertExitCode(1);
    }

    public function test_fails_with_nonexistent_user()
    {
        $this->artisan('user:deduct-balance', [
            'user' => 999999,
            'amount' => 100,
            '--force' => true,
        ])
            ->expectsOutput('用户不存在')
            ->assertExitCode(1);

        $this->artisan('user:deduct-balance', [
            'user' => '<EMAIL>',
            'amount' => 100,
            '--force' => true,
        ])
            ->expectsOutput('用户不存在')
            ->assertExitCode(1);
    }

    public function test_requires_confirmation_without_force_flag()
    {
        // 先给用户充值
        $this->balanceService->addBalance(
            $this->user,
            1000.0,
            'manual',
            null,
            ['remark' => '测试充值']
        );

        // 拒绝确认
        $this->artisan('user:deduct-balance', [
            'user' => $this->user->id,
            'amount' => 100,
            '--reason' => '需要确认的扣除',
        ])
            ->expectsQuestion('确认执行扣除操作？', false)
            ->expectsOutput('操作已取消')
            ->assertExitCode(0);

        // 验证余额没有变化
        $this->user->refresh();
        $this->assertEquals(1000.0, $this->user->current_balance);
    }

    public function test_executes_with_confirmation()
    {
        // 先给用户充值
        $this->balanceService->addBalance(
            $this->user,
            1000.0,
            'manual',
            null,
            ['remark' => '测试充值']
        );

        // 接受确认
        $this->artisan('user:deduct-balance', [
            'user' => $this->user->id,
            'amount' => 200,
            '--reason' => '需要确认的扣除',
        ])
            ->expectsQuestion('确认执行扣除操作？', true)
            ->expectsOutput('✅ 扣除成功！')
            ->assertExitCode(0);

        // 验证余额变化
        $this->user->refresh();
        $this->assertEquals(800.0, $this->user->current_balance);
    }

    public function test_uses_default_reason()
    {
        // 先给用户充值
        $this->balanceService->addBalance(
            $this->user,
            1000.0,
            'manual',
            null,
            ['remark' => '测试充值']
        );

        // 不指定原因执行扣除
        $this->artisan('user:deduct-balance', [
            'user' => $this->user->id,
            'amount' => 100,
            '--force' => true,
        ])
            ->expectsOutputToContain('管理员手动扣除')
            ->assertExitCode(0);
    }

    public function test_displays_balance_information()
    {
        // 先给用户充值
        $this->balanceService->addBalance(
            $this->user,
            1000.0,
            'manual',
            null,
            ['remark' => '测试充值']
        );

        // 执行扣除并检查显示的信息
        $this->artisan('user:deduct-balance', [
            'user' => $this->user->id,
            'amount' => 300,
            '--reason' => '信息显示测试',
            '--force' => true,
        ])
            ->expectsOutputToContain('即将扣除用户余额:')
            ->expectsOutputToContain($this->user->email)
            ->expectsOutputToContain($this->user->name)
            ->expectsOutputToContain('信息显示测试')
            ->assertExitCode(0);
    }

    public function test_handles_precision_correctly()
    {
        // 先给用户充值
        $this->balanceService->addBalance(
            $this->user,
            1000.12345678,
            'manual',
            null,
            ['remark' => '精度测试充值']
        );

        // 执行精确金额扣除
        $this->artisan('user:deduct-balance', [
            'user' => $this->user->id,
            'amount' => 500.87654321,
            '--reason' => '精度测试扣除',
            '--force' => true,
        ])
            ->assertExitCode(0);

        // 验证精确计算结果
        $this->user->refresh();
        $expectedBalance = bcsub('1000.12345678', '500.87654321', 8);
        $this->assertEquals($expectedBalance, number_format($this->user->current_balance, 8, '.', ''));
    }
}
