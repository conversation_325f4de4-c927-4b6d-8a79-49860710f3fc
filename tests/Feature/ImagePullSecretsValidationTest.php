<?php

namespace Tests\Feature;

use App\Http\Requests\BaseWorkloadValidation;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class ImagePullSecretsValidationTest extends TestCase
{
    public function test_image_pull_secrets_rejects_empty_strings()
    {
        // 测试数据 - 这是导致TypeError的原始数据
        $data = [
            'image_pull_secrets' => [''],
        ];

        // 获取验证规则
        $request = new BaseWorkloadValidation;
        $rules = $request->rules();

        // 创建验证器
        $validator = Validator::make($data, $rules);

        // 验证应该失败
        $this->assertTrue($validator->fails(), '空字符串应该被验证器拒绝');

        // 检查错误信息
        $errors = $validator->errors()->all();
        $this->assertNotEmpty($errors, '应该有验证错误信息');

        echo "测试通过 - 空字符串被正确拒绝\n";
        echo '错误信息: '.implode(', ', $errors)."\n";
    }

    public function test_image_pull_secrets_accepts_valid_names()
    {
        $data = [
            'image_pull_secrets' => ['valid-secret-name', 'another-valid-123'],
        ];

        $request = new BaseWorkloadValidation;
        $rules = $request->rules();

        $validator = Validator::make($data, $rules);

        $this->assertFalse($validator->fails(), '有效的密钥名称应该通过验证');

        echo "测试通过 - 有效的密钥名称被接受\n";
    }

    public function test_image_pull_secrets_rejects_invalid_format()
    {
        $data = [
            'image_pull_secrets' => ['invalid@secret', 'Invalid-Secret', ''],
        ];

        $request = new BaseWorkloadValidation;
        $rules = $request->rules();

        $validator = Validator::make($data, $rules);

        $this->assertTrue($validator->fails(), '无效格式的密钥名称应该被拒绝');

        echo "测试通过 - 无效格式被正确拒绝\n";
        echo '错误信息: '.implode(', ', $validator->errors()->all())."\n";
    }
}
