<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\PortAllocation;
use App\Models\Service;
use App\Models\User;
use App\Models\Workspace;
use App\Service\IpPoolService;
use App\Service\LoadBalancerManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Tests\TestCase;

class IpPoolSystemTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Cluster $cluster;

    protected Workspace $workspace;

    protected IpPoolService $ipPoolService;

    protected LoadBalancerManager $loadBalancerManager;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create(['name' => 'test-cluster']);
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'namespace' => 'test-namespace',
        ]);

        $this->ipPoolService = app(IpPoolService::class);
        $this->loadBalancerManager = app(LoadBalancerManager::class);
    }

    /** @test */
    public function test_create_shared_ip_pool_with_command(): void
    {
        $exitCode = Artisan::call('ippool:create', [
            'range' => '************60-**************',
            '--name' => 'test-shared-pool',
            '--cluster-id' => $this->cluster->id,
            '--desc' => 'Test shared IP pool',
            '--driver' => 'metallb',
            '--sharing' => 'shared',
            '--allocation' => 'least_used',
            '--port-start' => 10000,
            '--port-end' => 30000,
            '--subnet-v4' => '************/24',
            '--gateway-v4' => '************',
        ]);

        $this->assertEquals(0, $exitCode);

        // 验证 IP 池创建
        $ipPool = IpPool::where('name', 'test-shared-pool')->first();
        $this->assertNotNull($ipPool);
        $this->assertEquals('shared', $ipPool->sharing_strategy);
        $this->assertEquals('metallb', $ipPool->driver);
        $this->assertEquals('************/24', $ipPool->subnet_v4);
        $this->assertEquals('************', $ipPool->gateway_v4);

        // 验证 IP 地址创建 (************60-200 = 41个IP)
        $poolIps = $ipPool->poolIps;
        $this->assertCount(41, $poolIps);

        // 验证共享模式的端口范围
        foreach ($poolIps as $poolIp) {
            $this->assertEquals(10000, $poolIp->port_range_start);
            $this->assertEquals(30000, $poolIp->port_range_end);
        }

        // 验证第一个和最后一个 IP
        $firstIp = $poolIps->sortBy('ip_address')->first();
        $lastIp = $poolIps->sortBy('ip_address')->last();
        $this->assertEquals('************60', $firstIp->ip_address);
        $this->assertEquals('**************', $lastIp->ip_address);
    }

    /** @test */
    public function test_create_dedicated_ip_pool_with_command(): void
    {
        $exitCode = Artisan::call('ippool:create', [
            'range' => '*************-*************',
            '--name' => 'test-dedicated-pool',
            '--cluster-id' => $this->cluster->id,
            '--sharing' => 'dedicated',
            '--allocation' => 'round_robin',
            '--subnet-v4' => '************/24',
            '--gateway-v4' => '************',
        ]);

        $this->assertEquals(0, $exitCode);

        $ipPool = IpPool::where('name', 'test-dedicated-pool')->first();
        $this->assertNotNull($ipPool);
        $this->assertEquals('dedicated', $ipPool->sharing_strategy);

        // 验证独享模式的端口范围 (固定为80-80)
        foreach ($ipPool->poolIps as $poolIp) {
            $this->assertEquals(80, $poolIp->port_range_start);
            $this->assertEquals(80, $poolIp->port_range_end);
        }
    }

    /** @test */
    public function test_shared_ip_allocation_and_port_management(): void
    {
        // 创建共享模式 IP 池
        $ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'sharing_strategy' => 'shared',
            'allocation_strategy' => 'least_used',
        ]);

        $poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 20000,
            'port_range_end' => 20005, // 6个端口用于测试
            'usage_count' => 0,
        ]);

        // 测试共享模式下的端口分配
        $port1 = $this->ipPoolService->allocatePort($poolIp, 'service-1', $this->workspace->namespace);
        $port2 = $this->ipPoolService->allocatePort($poolIp, 'service-2', $this->workspace->namespace);
        $port3 = $this->ipPoolService->allocatePort($poolIp, 'service-3', $this->workspace->namespace);

        // 验证端口分配
        $this->assertEquals(20000, $port1);
        $this->assertEquals(20001, $port2);
        $this->assertEquals(20002, $port3);

        // 验证 IP 使用计数
        $poolIp->refresh();
        $this->assertEquals(3, $poolIp->usage_count);

        // 验证端口分配记录
        $allocations = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->get();
        $this->assertCount(3, $allocations);

        // 测试端口释放
        $this->ipPoolService->releasePort('service-2', $this->workspace->namespace);
        $poolIp->refresh();
        $this->assertEquals(2, $poolIp->usage_count);

        // 测试端口重用
        $port4 = $this->ipPoolService->allocatePort($poolIp, 'service-4', $this->workspace->namespace);
        $this->assertEquals(20001, $port4); // 应该重用释放的端口
    }

    /** @test */
    public function test_dedicated_ip_allocation(): void
    {
        // 创建独享模式 IP 池
        $ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'sharing_strategy' => 'dedicated',
            'allocation_strategy' => 'least_used',
        ]);

        $poolIp1 = PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 80,
            'port_range_end' => 80,
            'usage_count' => 0,
        ]);

        $poolIp2 = PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 80,
            'port_range_end' => 80,
            'usage_count' => 0,
        ]);

        // 在独享模式下，每个服务应该获得不同的 IP
        $allocatedIp1 = $this->ipPoolService->allocateIp($ipPool, false); // 不允许共享

        // 模拟标记第一个IP为已使用（在实际应用中，这会在Service创建时发生）
        $allocatedIp1->increment('usage_count');

        $allocatedIp2 = $this->ipPoolService->allocateIp($ipPool, false);

        $this->assertNotNull($allocatedIp1);
        $this->assertNotNull($allocatedIp2);
        $this->assertNotEquals($allocatedIp1->ip_address, $allocatedIp2->ip_address);

        // 验证使用计数
        $this->assertEquals(1, $allocatedIp1->fresh()->usage_count); // 已标记为使用
        $this->assertEquals(0, $allocatedIp2->usage_count); // 新分配的还未标记
    }

    /** @test */
    public function test_service_creation_with_shared_ip(): void
    {
        // 创建共享模式 IP 池
        $ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'sharing_strategy' => 'shared',
        ]);

        $poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 25000,
            'port_range_end' => 25010,
        ]);

        // 创建允许共享 IP 的服务
        $service = Service::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'shared-service',
            'type' => 'LoadBalancer',
            'allow_shared_ip' => true,
            'pool_ip_id' => $poolIp->id,
            'allocated_ip' => $poolIp->ip_address,
        ]);

        // 分配端口
        $port = $this->ipPoolService->allocatePort($poolIp, $service->name, $this->workspace->namespace);

        $this->assertNotNull($port);
        $this->assertGreaterThanOrEqual(25000, $port);
        $this->assertLessThanOrEqual(25010, $port);
    }

    /** @test */
    public function test_service_creation_with_dedicated_ip(): void
    {
        // 创建独享模式 IP 池
        $ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'sharing_strategy' => 'dedicated',
        ]);

        $poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 80,
            'port_range_end' => 80,
        ]);

        // 创建不允许共享 IP 的服务
        $service = Service::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'dedicated-service',
            'type' => 'LoadBalancer',
            'allow_shared_ip' => false,
            'pool_ip_id' => $poolIp->id,
            'allocated_ip' => $poolIp->ip_address,
        ]);

        $this->assertEquals('*************', $service->allocated_ip);

        // 验证独享模式下服务可以获取分配的端口
        $allocatedPorts = $service->getAllocatedPorts($this->workspace->namespace);
        $this->assertIsArray($allocatedPorts);
    }

    /** @test */
    public function test_different_loadbalancer_drivers(): void
    {
        $drivers = ['metallb', 'purelb', 'cilium'];

        foreach ($drivers as $driver) {
            // 创建使用不同驱动的 IP 池
            $ipPool = IpPool::factory()->create([
                'cluster_id' => $this->cluster->id,
                'driver' => $driver,
                'sharing_strategy' => 'shared',
            ]);

            $poolIp = PoolIp::factory()->create([
                'ip_pool_id' => $ipPool->id,
                'ip_address' => "192.168.10.{$this->getDriverIpSuffix($driver)}",
                'port_range_start' => 30000,
                'port_range_end' => 30010,
            ]);

            // 创建服务
            $service = Service::factory()->create([
                'workspace_id' => $this->workspace->id,
                'name' => "test-service-{$driver}",
                'type' => 'LoadBalancer',
                'allow_shared_ip' => true,
                'pool_ip_id' => $poolIp->id,
                'allocated_ip' => $poolIp->ip_address,
            ]);

            // 测试驱动的注解生成
            $driverInstance = $this->loadBalancerManager->driver($driver);
            $annotations = $driverInstance->getServiceAnnotations($service);

            // 验证每个驱动都有正确的注解
            $this->assertNotEmpty($annotations);

            // 验证 IP 共享注解 (只有在共享模式且允许共享且为IPv4时才设置)
            if ($ipPool->isShared() && $service->allow_shared_ip) {
                switch ($driver) {
                    case 'metallb':
                        $this->assertArrayHasKey('metallb.io/allow-shared-ip', $annotations);
                        break;
                    case 'purelb':
                        $this->assertArrayHasKey('purelb.io/allow-shared-ip', $annotations);
                        break;
                    case 'cilium':
                        $this->assertArrayHasKey('lbipam.cilium.io/sharing-key', $annotations);
                        break;
                }
            }
        }
    }

    /** @test */
    public function test_ip_pool_stats(): void
    {
        // 创建共享模式 IP 池
        $ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'sharing_strategy' => 'shared',
        ]);

        // 创建多个 IP
        $poolIp1 = PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 30000,
            'port_range_end' => 30002, // 3个端口
            'usage_count' => 2,
        ]);

        $poolIp2 = PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 30000,
            'port_range_end' => 30002, // 3个端口
            'usage_count' => 1,
        ]);

        $poolIp3 = PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 30000,
            'port_range_end' => 30002, // 3个端口
            'usage_count' => 0,
            'is_active' => false, // 不活跃
        ]);

        // 获取统计信息
        $stats = $ipPool->stats;

        $this->assertEquals(3, $stats['total_ips']);
        $this->assertEquals(2, $stats['active_ips']);
        $this->assertEquals(1, $stats['inactive_ips']);
        $this->assertEquals(3, $stats['total_usage']); // 2 + 1 + 0
        $this->assertEquals(1.5, $stats['average_usage']); // 3 / 2 active IPs
    }

    /**
     * 获取驱动对应的 IP 后缀
     */
    private function getDriverIpSuffix(string $driver): int
    {
        return match ($driver) {
            'metallb' => 100,
            'purelb' => 101,
            'cilium' => 102,
            default => 199,
        };
    }
}
