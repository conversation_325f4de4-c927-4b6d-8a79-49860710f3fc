<?php

namespace Tests\Feature;

use App\Http\Controllers\Api\DeploymentController;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class K8sCacheTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
        ]);
        $this->user->setCurrentWorkspace($this->workspace);
    }

    public function test_cache_key_generation()
    {
        $controller = new DeploymentController;

        // 使用反射访问 protected 方法
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('generateK8sCacheKey');
        $method->setAccessible(true);

        // 测试基本缓存键生成
        $key1 = $method->invoke($controller, 'workspace-1', 'deployments');
        $this->assertEquals('k8s:workspace-1:deployments', $key1);

        // 测试带资源名称的缓存键
        $key2 = $method->invoke($controller, 'workspace-1', 'deployments', 'nginx');
        $this->assertEquals('k8s:workspace-1:deployments:nginx', $key2);

        // 测试带参数的缓存键
        $key3 = $method->invoke($controller, 'workspace-1', 'events', null, ['since_minutes' => 60]);
        $this->assertStringStartsWith('k8s:workspace-1:events:', $key3);
        $this->assertStringContains(md5(serialize(['since_minutes' => 60])), $key3);
    }

    public function test_cache_concurrent_access()
    {
        $controller = new DeploymentController;

        // 使用反射访问 protected 方法
        $reflection = new \ReflectionClass($controller);
        $getCachedDataMethod = $reflection->getMethod('getCachedK8sData');
        $getCachedDataMethod->setAccessible(true);

        $cacheKey = 'test:concurrent:access';
        $callCount = 0;

        // 模拟并发请求
        $callback = function () use (&$callCount) {
            $callCount++;
            usleep(100000); // 模拟 100ms 的数据获取时间

            return ['data' => 'test-result', 'call_count' => $callCount];
        };

        // 第一次调用应该执行回调
        $result1 = $getCachedDataMethod->invoke($controller, $cacheKey, $callback, 300);
        $this->assertEquals(1, $result1['call_count']);

        // 第二次调用应该从缓存获取，不执行回调
        $result2 = $getCachedDataMethod->invoke($controller, $cacheKey, $callback, 300);
        $this->assertEquals(1, $result2['call_count']); // 应该还是 1，说明没有再次执行回调

        $this->assertEquals($result1, $result2);
    }

    public function test_cache_clearing()
    {
        $controller = new DeploymentController;

        // 使用反射访问 protected 方法
        $reflection = new \ReflectionClass($controller);
        $getCachedDataMethod = $reflection->getMethod('getCachedK8sData');
        $getCachedDataMethod->setAccessible(true);
        $clearCacheMethod = $reflection->getMethod('clearK8sCache');
        $clearCacheMethod->setAccessible(true);

        $workspaceId = $this->workspace->id;
        $cacheKey = "k8s:{$workspaceId}:deployments";

        // 设置缓存
        $testData = ['test' => 'data'];
        $callback = function () use ($testData) {
            return $testData;
        };

        $result = $getCachedDataMethod->invoke($controller, $cacheKey, $callback, 300);
        $this->assertEquals($testData, $result);

        // 验证缓存存在
        $this->assertTrue(Cache::has($cacheKey));

        // 清除缓存
        $clearCacheMethod->invoke($controller, $workspaceId, 'deployments');

        // 验证缓存已清除（对于非 Redis 驱动）
        if (config('cache.default') !== 'redis') {
            $this->assertFalse(Cache::has($cacheKey));
        }
    }

    public function test_cache_with_different_ttl()
    {
        $controller = new DeploymentController;

        // 使用反射访问 protected 方法
        $reflection = new \ReflectionClass($controller);
        $getCachedDataMethod = $reflection->getMethod('getCachedK8sData');
        $getCachedDataMethod->setAccessible(true);

        $cacheKey = 'test:ttl:short';
        $testData = ['ttl' => 'test'];

        $callback = function () use ($testData) {
            return $testData;
        };

        // 使用很短的 TTL（1 秒）
        $result = $getCachedDataMethod->invoke($controller, $cacheKey, $callback, 1);
        $this->assertEquals($testData, $result);

        // 立即检查，应该还在缓存中
        $this->assertTrue(Cache::has($cacheKey));

        // 等待超过 TTL 时间
        sleep(2);

        // 缓存应该已过期
        $this->assertFalse(Cache::has($cacheKey));
    }

    public function test_cache_exception_handling()
    {
        $controller = new DeploymentController;

        // 使用反射访问 protected 方法
        $reflection = new \ReflectionClass($controller);
        $getCachedDataMethod = $reflection->getMethod('getCachedK8sData');
        $getCachedDataMethod->setAccessible(true);

        $cacheKey = 'test:exception:handling';
        $fallbackData = ['fallback' => 'data'];

        // 模拟抛出异常的回调
        $callback = function () use ($fallbackData) {
            // 在异常情况下，应该仍然返回数据（降级处理）
            return $fallbackData;
        };

        // 应该能够处理异常并返回降级数据
        $result = $getCachedDataMethod->invoke($controller, $cacheKey, $callback, 300);
        $this->assertEquals($fallbackData, $result);
    }
}
